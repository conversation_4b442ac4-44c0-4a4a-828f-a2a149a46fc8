"""
Constants
"""

from outbound.views.orders.order_data import get_order_details_api
from outbound.views.picklist.print_picklist import get_picklist_details
from outbound.views.picklist.cancel_picklist import cancel_picklist_callback
from outbound.views.invoice.integrations import (
    get_invoice_callback_data, cancel_invoice_callback, get_invoice_packing_callback_data_with_packing_service,
    get_invoice_packing_callback_data_without_packing_service
)
from outbound.views.shipment.helpers import prepare_shipment_creation_payload
from outbound.views.staging_lanes.integrations import get_staging_area_callback_data
from outbound.views.sales_return.sales_return_data import get_sales_return_callback_data

from inbound.views.grn.common import get_grn_sku_wise_sr_data
from inbound.views.grn.asn import asn_call_back_data
from inventory.views.locator.stock_detail import get_inventory_callback
from inbound.views.purchase_order.create_po import send_po_callback
from inbound.views.rtv.common import send_rtv_callback

#defining constants
DISPENSE_FILTER = 'SKU Dispensing'
ORDER_TYPE_FILTER = "Order Type"
CLIENT_INTEGRATIONS = "Client Integrations"
ASN_TRIGGER_NAME = "ASN Callback"
inventory_callback_detail_view = "Inventory Callback Detail View"

class TPIntegration():
    """
    3P Integrations
    """
    DATA_FORMAT_TO_FUNCATION_MAPPING = {
            "order_callback": get_order_details_api,
            "order_update":  get_order_details_api,
            "cancel_order": get_order_details_api,
            "picklist_detail": get_picklist_details,
            "picklist_detail_with_zero_quantity": get_picklist_details,
            "cancel_picklist": cancel_picklist_callback,
            "cancel_jo_picklist": cancel_picklist_callback,
            "cancel_invoice": cancel_invoice_callback,
            "inventory_callback": get_inventory_callback,
            # "invoice": old_asn_creation_payload,
            # "load_share": prepare_load_share_creation_payload,
            "shipment": prepare_shipment_creation_payload,
            # "grn": prepare_asn_creation_payload,
            "invoice_callback": get_invoice_callback_data,
            "invoice_callback_with_packing": get_invoice_packing_callback_data_without_packing_service,
            "invoice_callback_with_packing_service": get_invoice_packing_callback_data_with_packing_service,
            "stgaing_area_callback": get_staging_area_callback_data,
            "sales_return_callback": get_sales_return_callback_data,
            "sales_return_grn_callback": get_grn_sku_wise_sr_data,
            "po_callback": send_po_callback,
            "rtv_callback": send_rtv_callback,
            "asn_callback": asn_call_back_data,
            "asn_cancellation": asn_call_back_data,
        }

    inventory_and_order_callbacks = {
            'Inventory Callback': 'inventory_callback',
            'Order Callback': 'order_callback'
        }
    INTEGRATIONS_3P = {
        "TRIGGERS": {
            'PO Creation': 'po_creation',
            'Invoice Creation': 'invoice_creation',
            'Cancel Invoice': 'cancel_invoice',
            'Order Creation': 'order_creation',
            'Picklist Generation': 'picklist_generation',
            'Picklist Confirmation': 'picklist_confirmation',
            'Allocation': 'allocation',
            'Cancel Order': 'cancel_order',
            'Cancel Picklist': 'cancel_picklist',
            'Inventory Changes': 'inventory_changes',
            'Order Updates': 'order_updates',
            'E-Invoice': 'einvoice',
            'Eway-bill':'ewaybill',
            'Consolidated Eway-bill': 'consolidated_ewaybill',
            'E-Invoice Cancel': 'einvoice_cancel',
            'E-Creditnote': 'ecreditnote',
            'E-Creditnote Cancel': 'ecreditnote_cancel',
            'E-Debitnote': 'edebitnote',
            'E-Debitnote Cancel': 'edebitnote_cancel',
            'Shipment':'shipment',
            'Sales Return':'sales_return',
            'Sales Return Status Update': 'sales_return_update',
            'Sales Return Cancellation': 'sales_return_cancellation',
            'Sales Return GRN' : 'sales_return_grn',
            'GRN Creation' : 'grn_creation',
            'Zone Updation': 'zone_updation',
            'ASN Creation' : 'asn_creation',
            'ASN Cancellation': 'asn_cancellation',
            'Gate-IN': 'gate_in',
            'Gate-OUT': 'gate_out',
            'Putaway' : 'putaway',
            'QC-Done':'qc_done',
            'RTV Creation': 'rtv_creation',
            'Sales Return Putaway':'sales_return_putaway',
            'CPPutaway':'cp_putaway',
            'Move Inventory' : 'move_inventory',
            'Inventory Adjustment': 'inventory_adjustment',
            'Cycle Count': 'cycle_count',
            'JO GRN Creation': 'jo_grn_creation',
            'JO Picklist Completion': 'jo_picklist_completion',
            'Material to Material': 'material_to_material',
            'JO Dispense' : 'jo_dispense',
            'Cancel JO Picklist': 'cancel_jo_picklist',
            'Cancel GRN': 'cancel_grn',
            'Cancel Dispense': 'cancel_jo_dispense',
            'Staging Area': 'staging_area',
            'SKU': 'sku_creation',
            'Async API Updates' : 'async_api_updates', 
            'PO Callback' : 'po_callback',
            'RTV Callback' : 'rtv_callback',
            'Invoice Updation': 'invoice_updation',
            'QC Callback': 'qc_callback',
        },
        "ACTIONS": {
            "po_creation":{
                "PO":"po",
                "SO":"so",
                "CallBack":"callback",
            },
            "invoice_creation": {
                "Invoice":"invoice",
                "GRN":"grn",
                "Order Update":"order_update",
                "Shipment":"shipment",
                "Invoice Callback": "invoice_callback",
                "Invoice Callback With Packing Details": "invoice_callback_with_packing",
                "Invoice Callback With Packing Service": "invoice_callback_with_packing_service",
                "Inventory Callback": "inventory_callback",
            },
            "cancel_invoice": {
                "Callback": "cancel_invoice",
                "Inventory Callback": "inventory_callback",
            },
            "order_creation": inventory_and_order_callbacks,
            "cancel_order": inventory_and_order_callbacks,
            "order_updates": inventory_and_order_callbacks,
            'picklist_generation': inventory_and_order_callbacks,
            'picklist_confirmation': {**inventory_and_order_callbacks, **{
                "Callback": "picklist_detail",
                "Callback With Zero Quantity": "picklist_detail_with_zero_quantity"
            }},
            'cancel_picklist': {**inventory_and_order_callbacks, **{
                "Callback": "cancel_picklist"
            }},
            'allocation':  inventory_and_order_callbacks,
            "einvoice":{
                "MasterGst":"mastergst",
                "GenrateIRN":"generateirn"
            },
            "ewaybill":{
                "MasterGst":"mastergst",
                "WithoutIrn":"withoutirn",
                "CancelEwayBill": "cancel_ewaybill",
                "EwayBill": "ewaybill",
                "EwayBillWithoutIrn": "ewaybillwithoutirn",
            },
            "consolidated_ewaybill": {
                "MasterGst": "mastergst",
                "CONSOLIDATED_EWAYBILL": "consolidated_ewaybill",
            },
            "einvoice_cancel":{
                "MasterGst":"mastergst",
                "CancelIRN":"cancelirn"
            },
            "ecreditnote":{
                "MasterGst":"mastergst",
                "GenerateECreditNote":"generateecreditnote"
            },
            "ecreditnote_cancel":{
                "MasterGst":"mastergst",
                "CancelECreditNote":"cancelecreditnote"
            },
            "edebitnote":{
                "MasterGst":"mastergst"
            },
            "edebitnote_cancel":{
                "MasterGst":"mastergst"
            },
            "sales_return":{
                "Sales Return Callback":"sales_return_callback",
            },
            "sales_return_cancellation": {
                "sales Return Callback": "sales_return_callback",
            },
            "sales_return_update": {
                 "sales Return Callback": "sales_return_callback",
            },
            "sales_return_putaway":{
                "Sales Return Callback":"sales_return_callback",
            },
            "sales_return_grn":{
                "Sales Return GRN Callback":"sales_return_grn_callback",
                "Sales Return Callback":"sales_return_callback",
                'Inventory Callback': 'inventory_callback'
            },
            "cp_putaway":{
                "Callback":"callback",
            },
            "shipment":{
                "Recommendation":"recommendation",
                "Shipment Creation":"shipment_creation",
                "Shipment Callback":"shipment_callback",
                "Manifest Creation Callback":"manifest_creation_callback",
                "Manifest Closure Callback":"manifest_closure_callback",
            },
            "zone_updation": {
                "Inventory Callback": "inventory_callback",
            },
            "grn_creation":{
                "CallBack":"callback",
                ASN_TRIGGER_NAME: "asn_callback",
                'Inventory Callback': 'inventory_callback',
            },
            "asn_creation":{
                "ASN CallBack Old": "callback",
                ASN_TRIGGER_NAME: "asn_callback",
                "Inventory Callback": "inventory_callback",
            },
            "asn_cancellation":
            {
                "ASN Cancellation": "asn_callback",
                "Inventory Callback": "inventory_callback",
            },
            'gate_in':{
                ASN_TRIGGER_NAME: "asn_callback",
                "SR CallBack": "sales_return_callback",
            },
            'gate_out':{
                ASN_TRIGGER_NAME: "asn_callback",
                "SR CallBack": "sales_return_callback",
            },
            "putaway":{
                "CallBack":"callback",
                'Inventory Callback': 'inventory_callback',
            },
            "qc_done":{
                "CallBack":"callback",
                "Inventory Callback": "inventory_callback",
                ASN_TRIGGER_NAME: "asn_callback",
            },
            "rtv_creation":{
                "Callback":"callback",
                "Inventory Callback": "inventory_callback",
            },
            "move_inventory":{
                "Callback":"callback",
                "Inventory Callback": "inventory_callback",
            },
            "inventory_adjustment":{
                "Callback":"callback",
                "Inventory Callback": "inventory_callback",
            },
            "jo_grn_creation":{
                "Callback":"callback"
            },
            "jo_picklist_completion": {
                "Callback": "picklist_detail"
            },
            "material_to_material": {
                "Callback":"callback"
            },
            "jo_dispense": {
                "Callback":"callback"
            },
            'cancel_jo_picklist': {
                "Callback":"cancel_jo_picklist"
            },
            'cancel_grn': {
                "Callback":"callback",
                "Inventory Callback": "inventory_callback",
            },
            'cancel_jo_dispense': {
                "Callback":"callback"
            },
            'staging_area': {
                "Callback": "stgaing_area_callback"
            },
            'sku_creation': {
                "Callback": "callback"
            },
            'async_api_updates': {
                "Callback": "callback"
            },
            'po_callback': {
                "Callback":"po_callback"
            },
            'rtv_callback' :{
                "Callback":"rtv_callback"
            },
            'invoice_updation': {
                'Invoice Callback': 'invoice_callback',
            },
            'cycle_count' : {
                "Inventory Callback": "inventory_callback",
            }
        },
        "FILTER_FIELDS":{
            'po_creation': {'': '', 'Supplier': 'supplier'},
            'invoice_creation': {'': '', 'Marketplace': 'marketplace', ORDER_TYPE_FILTER : 'order_type', 'Customer Type': 'customer_type', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'order_creation': {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'picklist_generation' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'picklist_confirmation' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', DISPENSE_FILTER: 'sku__dispensing_enabled', inventory_callback_detail_view: 'detail_view'},
            'jo_picklist_completion': {'': '', DISPENSE_FILTER : 'sku__dispensing_enabled', inventory_callback_detail_view: 'detail_view'},
            'allocation' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'cancel_order' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', 'Order Cancellation Filter': 'order_cancellation_filter', inventory_callback_detail_view: 'detail_view'},
            'cancel_picklist' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', DISPENSE_FILTER : 'sku__dispensing_enabled', inventory_callback_detail_view: 'detail_view'},
            'order_updates' : {'': '', ORDER_TYPE_FILTER : 'order_type', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'inventory_changes': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'zone_updation': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'putaway': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'sales_return_putaway': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'rtv_creation': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'shipment' : {'': '', ORDER_TYPE_FILTER : 'order_type', inventory_callback_detail_view: 'detail_view'},
            'move_inventory' : {'': '', "Movement Type": 'movement_type', "Exclude Zones": 'move_inv_exclude_zones', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'inventory_adjustment': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'cycle_count': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'cancel_jo_picklist': {'': '', DISPENSE_FILTER : 'sku__dispensing_enabled', inventory_callback_detail_view: 'detail_view'},
            'po_callback': {'': '', 'Status': 'status'},
            'qc_done': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'rtv_callback': {'':'', 'Status': 'status'},
            'asn_creation': {'':'', 'ASN Status': 'asn_status', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view', 'PO Type': 'po_type'},
            'asn_cancellation': {'':'', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'grn_creation': {'':'', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'cancel_grn': {'':'', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'sales_return_grn': {'':'', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
            'cancel_invoice': {'': '', 'Zone': 'zone', inventory_callback_detail_view: 'detail_view'},
        }
    }
