# package imports
import copy
import json
from collections import OrderedDict
import os

# django imports
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
import requests
from oauth2_provider.models import Application


# wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

# core_operations imports
from core_operations.views.common.main import (
    WMSListView, frame_datatable_column_filter, get_local_date_known_timezone,
    get_related_users_filters, get_sister_warehouse,
    get_user_time_zone, get_warehouse, get_warehouses_list
)

# core imports
from core.models.integration import (
    UserIntegrationAPIS,
    UserIntegrationCalls, 
    UserIntegrations
)

# core_operation views import
from core_operations.views.integration.run_3pintegration import ( 
    run_3p_int_func
)
from core_operations.views.integration.constants import TPIntegration

int_log = init_logger('logs/integration.log')

STATUS_DICT = {"success": 0, "pending": 1, "failed": 2}

def get_user_integrations_data(start_index, stop_index, temp_data, search_trm, order_term, col_num, request, warehouse: User, filters):
    request_data = request.GET
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)

    if "username" == order_data:
        order_data = "user__first_name"
    if sort_type == '1':
        order_data = '-%s' % order_data

    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    if search_term:
        master_data = UserIntegrations.objects.filter(
            Q(user__first_name__icontains=search_term) | Q(name__icontains=search_term) |
            Q(auth_type__icontains=search_term), user__in=user_ids).order_by(order_data)
    elif column_headers:
        column_filters_dict = {}
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {"username__icontains":"user__first_name__icontains"}
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            else:
                column_filters_dict[key] = value
        master_data = UserIntegrations.objects.filter(user__in=user_ids, **column_filters_dict).order_by(order_data)
    else:
        master_data = UserIntegrations.objects.filter(user__in=user_ids).order_by(order_data)

    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    time_zone = get_user_time_zone(warehouse)

    for data in master_data[start_index: stop_index]:
        creation_date = get_local_date_known_timezone(time_zone, data.creation_date) if  data.creation_date else ''
        temp_data['aaData'].append(
            OrderedDict((('id', data.id),
                         ('username', data.user.username),
                         ('user', data.user.id),
                         ('name', data.name),
                         ('auth_type', data.auth_type),
                         ('creation_date', creation_date),
                         ('DT_RowId', data.id), ('DT_RowClass', 'results'),
                         )))

def prepare_user_integration_api_column_filters(column_headers):
    ''' Prepare Column Filters for integration api'''
    column_filters_dict = {}
    column_filters = frame_datatable_column_filter(column_headers)
    custom_filter_dict = {"user__icontains":"user_integration__user__username__icontains",
                            "user_integration_name__icontains": "user_integration__name__icontains"}
    for key, value in column_filters.items():
        if key in custom_filter_dict:
            column_filters_dict[custom_filter_dict.get(key)] = value
        elif value.lower() == 'inactive':
            column_filters_dict['status__icontains'] = 0
        elif value.lower() == 'active':
            column_filters_dict['status__icontains'] = 1
        else:
            column_filters_dict[key] = value

    return column_filters_dict



def get_filter_list(filters):
    ''' Prepare Filter List for integration api'''
    filter_mapping = {
        'order__order_type__in': 'order_type',
        'location__zone_id__in': 'zone',
        'movement_type': 'movement_type',
        'sku__dispensing_enabled__in': 'sku__dispensing_enabled',
        'exclude_zones': 'move_inv_exclude_zones',
        'detail_view': 'detail_view',
        'asn_status': 'asn_status',
        'order__customer_identifier__customer_type__in':'customer_type',
        'order_cancellation_filter': 'order_cancellation_filter',
        'po_type': 'po_type',
    }

    filter_list = []
    if filters:
        filters_dict = json.loads(filters.replace("'", "\""))
        for key, value in filters_dict.items():
            filter_dict = {}
            #{filter_key: ["movement_type"], filter_value: ["Intra Zone"]}
            if key in filter_mapping:
                filter_dict['filter_key'] = filter_mapping[key]
                filter_dict['filter_value'] = value
                filter_list.append(filter_dict)

    return filter_list


def get_request_data(request):
    ''' Get Request Data for integration api '''
    request_data = request.GET
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)

    return column_headers, search_term, order_data, sort_type



def get_user_integrations_api_data(start_index, stop_index, temp_data, request_search_term, order_term, col_num, request, warehouse: User, filter):

    column_headers, search_term, order_data, sort_type = get_request_data(request)

    order_data_dict = {
        "user": "user_integration__user__username",
        "user_integration_name": "user_integration__name"
    }

    order_data_value = order_data_dict.get(order_data,None)
    if order_data_value:
        order_data = order_data_value

    if sort_type == '1':
        order_data = '-%s' % order_data
    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    if search_term:
        master_data = UserIntegrationAPIS.objects.filter(
            Q(user_integration__user__first_name__icontains=search_term) |
            Q(user_integration__name__icontains=search_term) |
            Q(api_url__icontains=search_term) |
            Q(trigger__icontains=search_term) |
            Q(data_format__icontains=search_term) |
            Q(api_method__icontains=search_term) |
            Q(data_params__icontains=search_term), user_integration__user__in=user_ids).order_by(order_data)
    elif column_headers:
        column_filters_dict = prepare_user_integration_api_column_filters(column_headers)
        master_data = UserIntegrationAPIS.objects.filter(user_integration__user__in=user_ids, **column_filters_dict).order_by(order_data)
    else:
        master_data = UserIntegrationAPIS.objects.filter(user_integration__user__in=user_ids).order_by(order_data)

    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    time_zone = get_user_time_zone(warehouse)

    for data in master_data[start_index: stop_index]:
        status = 'Active' if data.status else 'Inactive'
        filters, filter_field, filter_vals = "", "", ""
        filters = data.filters
        filter_list = get_filter_list(filters)

        creation_date = get_local_date_known_timezone(time_zone, data.creation_date) if  data.creation_date else ''
        updation_date = get_local_date_known_timezone(time_zone, data.updation_date) if  data.updation_date else ''
        temp_data['aaData'].append(
            OrderedDict((('id', data.id), 
                         ('user', data.user_integration.user.username),
                         ('user_integration', data.user_integration.id),
                         ('user_integration_name', data.user_integration.name),
                         ('send_token', data.send_token), ('api_url', data.api_url),
                         ('is_token_url', data.is_token_url),
                         ('access_token', data.access_token),
                         ('trigger', data.trigger), ('data_format', data.data_format),
                         ('api_method', data.api_method), ('data_params', data.data_params),
                         ('status', status),
                         ('retain_updates', data.retain_updates),
                         ('attach_request_id',data.attach_request_id),
                         ('batch_level_integration', data.batch_level_integration),
                         ('creation_date', creation_date),
                         ('updation_date', updation_date),
                         ('token_data', data.token_data),
                         ('filters', filter_list),
                         ('retry_attempts', data.retry_attempts),
                         ('is_async', data.is_async),
                         ('DT_RowId', data.id),
                         ('DT_RowClass', 'results'),
                         ('filter_field', filter_field),
                         ('filter_vals', filter_vals)
                         )))
        
def get_status_value(status):
    status_int = None
    if status:
        status_int = STATUS_DICT.get(status.lower(), None)
    return status_int

def prepare_user_integration_calls_column_filters(column_headers):
    ''' Prepare Column Filters for integration calls'''
    column_filters_dict = {}
    column_filters = frame_datatable_column_filter(column_headers)
    custom_filter_dict = {"user__icontains": "user_integrationapis__user_integration__user__username__icontains",
                    "user_integration_name__icontains": "user_integrationapis__user_integration__name__icontains",
                    "api_url__icontains": "user_integrationapis__api_url__icontains", "api_method__icontains":"user_integrationapis__api_method__icontains",
                    "trigger__icontains":"user_integrationapis__trigger__icontains", "data_format__icontains":"user_integrationapis__data_format__icontains",
                    "retry_attempts__icontains":"user_integrationapis__retry_attempts__icontains",
                    "reference_number__icontains":"api_data__icontains"}
    for key, value in column_filters.items():
        if key in custom_filter_dict:
            column_filters_dict[custom_filter_dict.get(key)] = value
        elif key == 'status__icontains':
            value = get_status_value(value)
            if value:
                column_filters_dict['status'] = value
        else:
            column_filters_dict[key] = value
    return column_filters_dict

def prepare_reference_numbers(api_data):
    ''' Prepare Reference Numbers list '''
    reference_numbers = ""
    if isinstance(api_data, dict) and api_data.get("data"):
        api_data = api_data.get("data")
    if isinstance(api_data, dict):
        reference_numbers = api_data.get("order_reference", "")
        if not reference_numbers and 'invoice_number' in  api_data:
            reference_numbers = api_data.get('invoice_number','')
        async_id = api_data.get('async_id', '')
        if async_id:
            reference_numbers = async_id
    elif isinstance(api_data, list):
        for each_row in api_data:
            if "order_reference" in each_row:
                reference_numbers+=  each_row["order_reference"] + ","
            elif "sku" in each_row:
                reference_numbers+= each_row["sku"] + ","
            elif "invoice_number" in each_row:
                reference_numbers += each_row["invoice_number"]+ ","
            elif "reference_number" in each_row and each_row.get("transaction_type",'') == "jo_dispense":
                reference_numbers += each_row["reference_number"]+ ","
    return reference_numbers

def get_user_integrations_calls_data(start_index, stop_index, temp_data, request_search_term, order_term, col_num, request, warehouse: User, filters):
    request_data = request.GET
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    order_data_dict = {"user": "user_integrationapis__user_integration__user__username",
                       "user_integration_name": "user_integrationapis__user_integration__name",
                       "api_url": "user_integrationapis__api_url", "api_method":"user_integrationapis__api_method",
                       "trigger":"user_integrationapis__trigger", "data_format":"user_integrationapis__data_format",
                       "retry_attempts":"user_integrationapis__retry_attempts",
                       "reference_number": "api_data"}
    if order_data_dict.get(order_data):
        order_data = order_data_dict.get(order_data) 
    if sort_type == '1':
        order_data = '-%s' % order_data
    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    if column_headers:
        column_filters_dict = prepare_user_integration_calls_column_filters(column_headers)
        master_data = UserIntegrationCalls.objects\
            .select_related('user_integrationapis__user_integration__user')\
            .filter(user_integrationapis__user_integration__user__in=user_ids, **column_filters_dict).order_by(order_data)[start_index: stop_index]
    else:
        master_data = UserIntegrationCalls.objects\
            .select_related('user_integrationapis__user_integration__user')\
            .filter(user_integrationapis__user_integration__user__in=user_ids).order_by(order_data)[start_index: stop_index]
    temp_data['recordsTotal'] = master_data.count()
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    time_zone = get_user_time_zone(warehouse)
    status_dict= {0:"Success", 1:"Pending", 2:"Failed"}
    for data in master_data:        
        reference_numbers = prepare_reference_numbers(data.api_data) or data.integration_reference
        reference_numbers = reference_numbers.strip(",")
        creation_date = get_local_date_known_timezone(time_zone, data.creation_date) if  data.creation_date else ''
        updation_date = get_local_date_known_timezone(time_zone, data.updation_date) if  data.updation_date else ''
        temp_data['aaData'].append(
            OrderedDict((('id', data.id), 
                        ('user', data.user_integrationapis.user_integration.user.username),
                        ('retry_completed', data.retry_counter),
                        ('status', status_dict.get(data.status, "")),
                        ('user_integration_name', data.user_integrationapis.user_integration.name),
                        ('integration_reference', data.integration_reference),
                        ('reference_number', reference_numbers),
                        ('api_url', data.user_integrationapis.api_url),
                        ('api_method', data.user_integrationapis.api_method),
                        ('creation_date', creation_date),
                        ('updation_date', updation_date),
                        ('retry_attempts', data.user_integrationapis.retry_attempts),
                        ('trigger', data.user_integrationapis.trigger), 
                        ('data_format', data.user_integrationapis.data_format),
                        ('DT_RowId', data.id), ('DT_RowClass', 'results'),
                        )))

@get_warehouse
def view_integration_call_data(request, warehouse: User):
    """ view integration calls

    Args:
        request : django request object
        user (str, optional): user.

    Returns:
        HttpResponse: list of all integration APIs related to the current user
    """

    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    search_params ={}
    search_fields = ['id', 'status']
    for field in search_fields:
        value = request.GET.get(field, '') or ''
        if value:
            if field == 'status':
                value = STATUS_DICT.get(value.lower(), 0)
                if value:
                    search_params[field] = value
            else:
                search_params[field] = request.GET.get(field)
        
    master_data = UserIntegrationCalls.objects.filter(user_integrationapis__user_integration__user__in=user_ids, **search_params)
    output_dict= {}
    status_dict= {0:"Success", 1:"Pending", 2:"Failed"}
    user_integrationapis_values=['user_integration__name', 'id', 'api_url', 'trigger']
    user_integratoinapis= UserIntegrationAPIS.objects.filter(user_integration__user__in=user_ids).order_by("-creation_date").values(*user_integrationapis_values)
    user_integration = []
    for int_api_data in user_integratoinapis:
        user_integratoinapis_dict = {
            "name": int_api_data.get('user_integration__name', ''),
            "id": int_api_data.get('id', ''),
            "api_url": int_api_data.get('api_url', ''),
            "trigger": int_api_data.get('trigger', ''),
        }
        user_integration.append(user_integratoinapis_dict)

    if not search_params:
        return HttpResponse(json.dumps({"data": output_dict, "user_integration_apis": user_integration}))
    
    for data in master_data:
        api_data= data.api_data if data.api_data else {}
        try:
            api_response= json.loads(data.api_response) if data.api_response else {}
        except Exception as e:
            api_response= data.api_response
        output_dict={ 
                            "id": data.id,
                            "api_id": data.user_integrationapis.id,
                            "trigger": data.user_integrationapis.trigger,
                            "user": data.user_integrationapis.user_integration.user.username,
                            "retry_completed": data.retry_counter,
                            "name": data.user_integrationapis.user_integration.name,
                            "status": status_dict.get(data.status, ""),
                            #"reference_number": api_data.get("order_reference", ""),
                            "api_data": api_data,
                            "api_url": data.user_integrationapis.api_url,
                            "retry_attempts": data.user_integrationapis.retry_attempts,
                            "api_method": data.user_integrationapis.api_method,
                            "api_response": api_response
                        }
    return HttpResponse(json.dumps({"data": output_dict, "user_integration_apis": user_integration}))



@get_warehouse
def create_update_integration_call(request, warehouse: User):
    """ Used to create or update existing intergation calls

    Args:
        request : django request object
        user (str, optional): user.

    Returns:
        HttpResponse: success/failed
    """
    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    status_dict= { 'Pending': 1, 'Success': 0, "Failed": 2}
    search_dict = {
        'user_integrationapis__user_integration__user__in': user_ids
    }
    insert_update_dict ={}
    id = None
    try:
        input_data = json.loads(request.POST.get("data"))
        if request.POST.get("data"):
            if input_data.get("integratoin_call_id", None):
                insert_update_dict["id"] = input_data.get("integratoin_call_id", None)
                id= insert_update_dict["id"]
            if input_data.get("integratoin_api_id", None):
                insert_update_dict["user_integrationapis_id"] = input_data.get("integratoin_api_id", None)
            if input_data.get("status", None):
                insert_update_dict["status"] = status_dict[input_data.get("status")]
            if input_data.get("api_data", None):
                insert_update_dict["api_data"] =  input_data.get("api_data", {})
            if input_data.get("current_status", None):
                search_dict["status"] = status_dict[input_data["current_status"]]
            insert_update_dict["retry_counter"] = input_data.get("retry_counter", 0)
            if id: 
                search_dict['id'] = id
                insert_update_dict["api_response"] = json.dumps(input_data.get("api_response", {}))
                master_data = UserIntegrationCalls.objects.filter(**search_dict).update(**insert_update_dict)
            else:
                insert_update_dict["account_id"] = request.user.userprofile.id
                created_obj= UserIntegrationCalls.objects.create(**insert_update_dict)
    except Exception as e:
        return HttpResponse("Failed")
    return HttpResponse("Success")



@get_warehouse
def execute_3p_integration(request, warehouse: User):
    if warehouse.userprofile.warehouse_type == 'ADMIN':
        users = get_related_users_filters(warehouse.id)
    else:
        users = User.objects.filter(id=warehouse.id)
    user_ids = users.values_list('id', flat=True)
    filters ={}
    try:
        data = json.loads(request.body)
        ids = data.get("ids", [])
        filters ={"id__in": ids}
        run_3p_int_func(filters)
    except Exception as e:
        int_log.info('Integration call error statement is %s' % (str(e)))
        return JsonResponse({"status": "Failed"})
    return JsonResponse({"status": "Success", "message" : "Call triggered successfully"})

@get_warehouse
def get_warehouse_list_for_integration(request, warehouse: User):
    warehouse_list, unique_users = [], []
    sister_whs1 = get_sister_warehouse(warehouse).values('user__username', 'user__id', 'user__first_name')
    for wh in sister_whs1:
        user_name = wh.get('user__username')
        if user_name not in unique_users:
            warehouse_list.append({
                'warehouse_id': wh.get('user__id'),
                'warehouse_name': user_name,
                'warehouse_first_name': wh.get('user__first_name')
            })
            unique_users.append(user_name)
    sister_whs2_ids, sister_whs2 = get_warehouses_list(warehouse)
    if sister_whs2:
        for wh in sister_whs2:
            user_name = wh.username
            if user_name not in unique_users:
                warehouse_list.append({
                    'warehouse_id': wh.id,
                    'warehouse_name': user_name,
                    'warehouse_first_name': wh.first_name
                })
                unique_users.append(user_name)
    return HttpResponse(json.dumps({'warehouses': warehouse_list}))


class IntegrationTriggerActionSet(WMSListView):

    '''3p Integrations triggers and actions get api function'''

    def get(self, *args, **kwargs):
        final_data, integrations_val = {}, {}
        params = self.kwargs
        '''check if primary string parameter exist return integrations json data'''
        if params and  params.get('primary_string', '').upper() == "INTEGRATIONS_3P":
            integrations_val = copy.deepcopy(TPIntegration.INTEGRATIONS_3P)
            final_data[params.get('primary_string', '').upper()] = integrations_val

            '''if primary string  and secondary string exist return secondary json data'''
            if params.get('secondary_string', '').upper() in list(integrations_val.keys()):
                final_data.clear()
                final_data[params.get('secondary_string').upper()] = integrations_val.get(params.get('secondary_string').upper())

                '''if primary and secondary strings and third string exist return json data'''
                if params.get('third_string', '') in list(final_data[params.get('secondary_string').upper()].keys()):
                    final_data.clear()
                    final_data[params.get('third_string')] = integrations_val.get(params.get('secondary_string', '').upper()).get(params.get('third_string', ''))
            final_data['message'] = 'Success'
            return JsonResponse(final_data, status=200)
        else:
            return {'error': [{'message':'Optional Parameter Not Exist'}], 'status': 400}

class Link(WMSListView):

    def get(self,*args,**kwargs):
        request = self.request
        response = {}
        self.set_user_credientials()
        wh_user = self.warehouse
        try:
            link_url = os.environ.get('LINK_URL')
            uname = os.environ.get('LINK_UNAME')
            pswd = os.environ.get('LINK_PWD')
            url = link_url+'api-token-auth/'

            link_response = requests.post(url, data = {'username':uname,'password':pswd})
            if link_response.status_code != 200:
                return JsonResponse({'message': 'Request to integration layer failed'}, status=400)

            link_response = link_response.json()
            token = link_response.get('token')
            response['link_token'] = token
            response['link_url'] = link_url
            oauth_data = Application.objects.filter(name=wh_user.username)

            if oauth_data.exists():
                oauth_data = oauth_data.first()
                response['client_id'] = oauth_data.client_id
                response['client_secret'] = oauth_data.client_secret
            else:
                return JsonResponse({'message': 'Contact Stockone Admin to Enable Integration'}, status=400)

        except Exception as e:
            import traceback
            int_log.debug(traceback.format_exc())
            int_log.info('Call to link failed for %s and error statement is %s' % (str(request.user.username), str(e)))
            return JsonResponse({'message': 'Internal Server Error'}, status=500)
        return response