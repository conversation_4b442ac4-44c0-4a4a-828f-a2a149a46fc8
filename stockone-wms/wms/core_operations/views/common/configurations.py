from collections import OrderedDict
import copy
import datetime
import json
import time
import ast
from collections import defaultdict
import pandas as pd

from django.http import HttpResponse, JsonResponse
from django.views import View
from django.db.models import F, Count
from core.models.common import IncrementalTable, IncrementalSegregationMaster

from inventory.models.locator import LocationGroups, ZoneMaster

from wms_base.models import User, UserProfile

from production.models.job_order import JobOrder, ProductionStages, StatusTracking
from inbound.models import PutawayMapping, ASNSummary, SupplierMaster

from core.models import (
    InvoiceForms, MiscDetail, MiscDetailOptions, WaveCriteria,
    PurchaseApprovalConfig,SKUGroups, MasterEmailMapping,
    SKUMaster, UserPrefixes
    )

from core_operations.views.common.main import (
    WMSListView, config_multiple_misc_values, get_invoice_formats, get_local_date,
    get_marketplace_names, get_misc_value, get_multiple_misc_value_split,
    get_multiple_misc_values, get_permission_based_sub_users_emails,
    get_related_users, get_user_attributes, get_warehouse, sla_detail_validation,
    filter_or_none, delete_sla_entries, get_sla_misc_details,
    get_ars_replenishment_strategy
)

from wms_base.wms_utils import (
    CONFIG_DEF_DICT, CONFIG_INPUT_DICT, CONFIG_SWITCHES_DICT, CONFIG_ORDER_TYPE_DICT,
    MAIL_REPORTS_DATA, REPORTS_DATA, SKU_GROUP_FIELDS, STAGES_FIELDS,
    get_permission, init_logger, signed_public_dashboard
    )
from wms_base.redis_wrapper import RedisJSONWrapper
from django.db.models import Q
from django.core.cache import cache


log = init_logger('logs/configurations.log')

exception_message = "Something Went Wrong"

def get_misc_detail_options(config_dict, warehouse):
    '''
    Get Misc Detail Options
    '''
    config_dict['all_order_field_options'] = {}
    filter_params = {"misc_detail__user": warehouse.id, "status": 1}
    misc_details = list(MiscDetailOptions.objects.filter(**filter_params).values(
        'misc_key', 'misc_value', 'json_data', 'id', 'misc_detail__misc_type'
    ))
    misc_detail_options = {}
    grn_weighing_options = []
    stock_selection_strategy = []
    for misc in misc_details:
        #Frame Order Extra Fields
        if misc['misc_detail__misc_type'] == 'extra_order_fields':
            misc_list = misc.get('misc_value').split(',')
            config_dict['all_order_field_options'][misc.get('misc_key')]=[]
            for misc_value in misc_list :
                temp_dict = {}
                temp_dict['field_name'] = misc_value
                config_dict['all_order_field_options'][misc.get('misc_key')].append(temp_dict)
        #Frame GRN Weighing Options
        elif misc['misc_value'] in ['full_weighing', 'sample_weighing']:
            json_data = misc['json_data'] or {}
            grn_weighing_options.append({
                'id': misc['id'],
                'misc_key': misc['misc_key'],
                'misc_value': misc['misc_value'],
                'weighing_scale':json_data.get('weighing_scale', '')
            })
        elif misc['misc_detail__misc_type'] == 'stock_selection_strategy':
            stock_selection_strategy.append({
                'id': misc['id'],
                'misc_key': misc['misc_key'],
                'misc_value': ast.literal_eval(misc['misc_value'])         
            })
        #Frame Misc Detail Options
        else:
            misc_detail_options.setdefault(misc['misc_key'], [])
            misc_detail_options[misc['misc_key']].append(misc['misc_value'])
    config_dict.update(misc_detail_options)
    config_dict['grn_weighing_options'] = grn_weighing_options
    config_dict['stock_selection_strategy'] = stock_selection_strategy

def get_invoice_form_templates(config_dict, warehouse):
    """
    Retrieves the invoice form templates based on the given warehouse and populates the config_dict with the results.
    """
    document_tuple = tuple(InvoiceForms.objects\
                .filter(Q(warehouse_id__isnull=True)|Q(warehouse_id_id=warehouse.id), status=1)\
                .values("document_type", "invoice_format").distinct())
    document_types = {}
    for doc in document_tuple:
        if doc["document_type"] in document_types:
            document_types[doc["document_type"]].append(doc["invoice_format"])
        else:
            document_types[doc["document_type"]] = [doc["invoice_format"]]
    config_dict['document_types'] = document_types
    config_dict['selected_invoices'] = get_multiple_misc_values(document_types.keys(), warehouse.id)
    return config_dict

@get_warehouse
def save_misc_json(request, warehouse: User):
    """
    Save Misc JSON Data
    """
    try:
        data = json.loads(request.body)
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=data['misc_type'])
        if misc_detail.exists():
            misc_detail = misc_detail.first()
            misc_detail.json_data = data['json_data']
        else:
            misc_detail = MiscDetail(
                user=warehouse.id,
                misc_type=data['misc_type'],
                json_data=data['json_data'],
                account_id = warehouse.userprofile.id
            )
        misc_detail.save()
        return HttpResponse(json.dumps({'status': 'success'}))
    except Exception as e:
        log.error(f"Error in save_misc_json: {str(e)}")
        return HttpResponse(json.dumps({'status': 'failure', 'message': exception_message}))

@get_warehouse
def configurations(request, warehouse: User):
    NOW = datetime.datetime.now()
    config_dict = copy.deepcopy(CONFIG_DEF_DICT)
    sku_attributes = get_user_attributes(warehouse, 'sku')

    
    sku_attribute_names = {
        sku_attr['attribute_name']: f'attr-{sku_attr["attribute_name"]}' for sku_attr in sku_attributes
    }
    for key in ['receive_po_mandatory_fields', 'receive_po_editable_fields', 'sr_grn_mandatory_fields', 'sr_grn_editable_fields']:
        config_dict[key].update(sku_attribute_names)

    config_dict['display_none'] = 'display: block;'

    extra_misc_types = {
        'grn_fields': "grn_fields",
        'po_fields': "po_fields", 'po_types': "po_types", 'rtv_reasons': "rtv_reasons",
        'discrepancy_reasons': "discrepancy_reasons",
        'grn_rejection_reasons': "grn_rejection_reasons", 'sales_return_reasons': "sales_return_reasons",
        'lms_integrations': "lms_integrations", 'move_inventory_reasons':"move_inventory_reasons",
        'inventory_adjustment_reasons': "inventory_adjustment_reasons", 'job_order_types':"job_order_types",
        'supplier_payment_terms': "supplier_payment_terms", 'spoc_warehouse':"spoc_warehouse",
        'sale_order_types': "sale_order_types",'issuance_dept_types': "issuance_dept_types", 
        'sku_types': "sku_types", 'mobile_version_number': "mobile_version_number", "sku_invoice_groups":"sku_invoice_groups",
        'membership_discount_value': "membership_discount_value", 'prefill_batch_number': "prefill_batch_number",
        'return_types':"return_types","extra_order_sku_fields":"extra_order_sku_fields","extra_order_fields":"extra_order_fields",
        'return_consumed_quantity_reasons': "return_consumed_quantity_reasons",
        'allow_inter_zone_movement': 'allow_inter_zone_movement', "restricted_adjustment_status" : "restricted_adjustment_status",
        'ba_to_sa_reasons': 'ba_to_sa_reasons', 'nte_reasons': 'nte_reasons', 'gate_pass_file_types': 'gate_pass_file_types', 
        'po_line_extra_fields': 'po_line_extra_fields', 'extra_batch_attributes': 'extra_batch_attributes', 
        'gatepass_return_reasons': 'gatepass_return_reasons', 'asn_return_reasons': 'asn_return_reasons',
        'asn_line_extra_fields': 'asn_line_extra_fields',
        'vertical_groups': 'vertical_groups',
        'nte_selected_source_zones': 'nte_selected_source_zones',
        'nte_selected_destination_zones': 'nte_selected_destination_zones',
        'expired_selected_source_zones': 'expired_selected_source_zones',
        'expired_selected_destination_zones': 'expired_selected_destination_zones',
    }
    all_misc_types = set(CONFIG_SWITCHES_DICT.values()) | set(CONFIG_INPUT_DICT.values())
    all_misc_types.update(extra_misc_types)
    all_misc_values = config_multiple_misc_values(all_misc_types, warehouse.id)

    config_dict = prepare_config_dict(config_dict, all_misc_values, extra_misc_types)

    #response expected with diffrent key
    misc_type_value_dict = {
        "return_types": "all_return_type_fields",
        "extra_order_sku_fields": "all_order_sku_fields",
        "extra_order_fields": "all_order_fields"
    }
    for misc_key, change_misc_key in misc_type_value_dict.items():
        config_dict[change_misc_key] = all_misc_values.get(misc_key, '')
    
    #response expected with diffrent key adn value is list
    misc_type_split_dict = {
        "receive_po_mandatory_fields": "selected_receive_po_mandatory",
        "additional_batch_attributes": "selected_additional_batch_attribute",
        "display_batch_attributes": "selected_display_batch_attribute",
        "auto_confirm_po_options": "selected_auto_po_options",
        "receive_po_editable_fields": "selected_receive_po_editable",
        "sr_grn_mandatory_fields": "sr_grn_mandatory_fields",
        "sr_grn_editable_fields": "sr_grn_editable_fields",
        "inco_terms":"selected_inco_terms",
        "sku_category_list_pf": "sku_category_list_ba",
        "non_mandatory_batch_attributes": "selected_non_mandatory_batch_attribute",
        "batch_key_validation" : "batch_key_validation",
        "po_header_fields": "selected_po_header_fields",
        "po_mandatory_fields": "selected_po_mandatory_fields",
        "picking_screen_attributes": "selected_picking_screen_attributes",
        "picking_summary_attributes": "selected_picking_summary_attributes",
        "asn_fields": "selected_asn_fields",
        "asn_mandatory_fields": "selected_asn_mandatory_fields",
        "mobile_asn_summary_views" : "selected_mobile_asn_summary_views",
        "order_hold_options": "selected_order_hold_options",
        "replenishment_options": "selected_replenishment_options",
        "blind_grn_validations": "selected_blind_grn_validations",
        "warehouse_notifications":"warehouse_notifications",
        "restrict_picklist_on_cycle_count_creation_options": "selected_restrict_picklist_on_cycle_count_creation_options",
        "restrict_picklist_on_cycle_count_pending_approval_options": "selected_restrict_picklist_on_cycle_count_pending_approval_options",
        "warehouse_notifications":"warehouse_notifications",
        "hide_grn_fields": "selected_grn_fields",
    }
    split_misc_values = get_multiple_misc_value_split(list(misc_type_split_dict.keys()), warehouse.id)
    for misc_key, change_misc_key in misc_type_split_dict.items():
        config_dict[change_misc_key] = split_misc_values.get(misc_key, [])

    putaway_configs = list(MiscDetail.objects.filter(
            user=warehouse.id, misc_value= True, 
            misc_type__in=['restrict_location_to_one_sku', 'restrict_sku_to_one_location']
        ).values_list('misc_type', flat=True))
    config_dict['putaway_config'] = putaway_configs
    
    weight_integration_fields = get_misc_value('weight_integration_name', request.user.id)
    if weight_integration_fields == 'false' :
        config_dict['weight_integration_name'] = ''
    else:
        config_dict['weight_integration_name'] = weight_integration_fields

    misc_detail_list = []
    all_groups = SKUGroups.objects.filter(user=warehouse.id).values_list('group', flat=True)
    config_dict['all_groups'] = str(','.join(all_groups))

    if not get_permission(request.user, 'add_qualitycheck'):
        del config_dict['receive_options']['One step Receipt + Qc']

    config_dict['view_order_status'] = config_dict['view_order_status'].split(',')
    config_dict['style_headers'] = config_dict['style_headers'].split(',')

    if config_dict['stock_display_warehouse'] and config_dict['stock_display_warehouse'] != "false":
        config_dict['stock_display_warehouse'] = config_dict['stock_display_warehouse'].split(',')
        config_dict['stock_display_warehouse'] = list(map(int, config_dict['stock_display_warehouse']))
    else:
        config_dict['stock_display_warehouse'] = []

    # Invoice Marketplaces list and selected Option
    config_dict['marketplaces'] = get_marketplace_names(warehouse, 'all_marketplaces')
    config_dict['prefix_data'] = list(IncrementalTable.objects\
                            .filter(user=warehouse.id, account_id=warehouse.userprofile.id, type_name='invoice')\
                            .exclude(prefix='')\
                            .values('prefix', 'interfix', 'date_type'))
    config_dict['actual_pr_conf_names'] = list(PurchaseApprovalConfig.objects\
                                    .filter(user=warehouse, purchase_type='PR')\
                                    .values_list('name', 'product_category'))
    config_dict['actual_pr_approvals_conf_data'] = get_pr_approvals_configuration_data(warehouse, purchase_type='PR')
    config_dict['actual_pr_permissive_emails'] = get_permission_based_sub_users_emails(warehouse, permission_name='pending_pr')

    all_stages = ProductionStages.objects.filter(user=warehouse.id).order_by('order').values_list('stage_name', flat=True)
    config_dict['all_stages'] = str(','.join(all_stages))

    #miscdetail creation keys
    create_misc_type_dict = {
        "receive_process": "2-step-receive",
        "auto_credit_note": "true",
        "auto_grn_for_salesreturn": "true",
        "validate_unique_id_in_packing": "true",
        "scan_options_in_packing": "scan_first_sku_mandatory",
        "prefill_qty_in_picking": "true",
        "order_wise_inv" : "true",
        "mobile_invoice_type": "default",
        "packing_data_in_packing_screen": "lpn",
        "lpn_scan_in_picking": "lpn",
        "invoice_preview_view": "lpn",
        "packing_in_progress_data_in_packing_screen": "dont_show_sku",
        "invoice_type": "one_order_one_invoice",
        "picking_screen_attributes": CONFIG_DEF_DICT.get('pre_selected_picking_screen_attributes', []),
        "picking_summary_attributes": list(CONFIG_DEF_DICT.get('picking_summary_attributes', {}).values()),
        "scan_fixed_number_of_sku_in_picking": 0,
        "restricts_number_of_skus_in_a_lpn": 0,
        "max_skus_count_in_packing": 0,
        "restrict_invoice_at_picking": "true",
        "enable_order_type_selection_in_mobile": "true",
        "show_picklist_data": "Task",
        "allow_close_manifest": "manifest",
        "invoice_split_on_invoice_group": "false",
        "show_summary_in_picking": "true",
        'material_request': "true",
    }
    created_objs =  dict(MiscDetail.objects.filter(user=warehouse.id,misc_type__in=create_misc_type_dict).values_list('misc_type', 'misc_value'))
    return_list = {'user':warehouse.id, 'creation_date':NOW, 'updation_date' :NOW, 'account': warehouse.userprofile}
    for key, value in create_misc_type_dict.items():
        if created_objs.get(key) == None:
            return_list["misc_type"] = key
            return_list["misc_value"] = value
            if key in misc_type_split_dict:
                return_list["misc_value"] = ','.join(value)
            misc_detail_list.append(MiscDetail(**return_list))
            config_key = misc_type_split_dict.get(key, key)
            config_dict[config_key] = value

    if config_dict['mail_alerts'] == 'false':
        config_dict['mail_alerts'] = 0
    if config_dict['production_switch'] == 'false':
        config_dict['display_none'] = 'display:none;'
    config_dict['mail_inputs'] = []

    misc_values = {}
    mail_report_dict = list(MAIL_REPORTS_DATA.values())
    mail_misc_data = MiscDetail.objects.filter(user=warehouse.id, misc_type__in=mail_report_dict)
    for item in mail_misc_data:
        misc_values[item.misc_type] = item.misc_value
    
    config_dict['mail_inputs'] = [key for key, val in MAIL_REPORTS_DATA.items() if misc_values.get(val) == 'true']

    if config_dict['online_percentage'] == "false":
        config_dict['online_percentage'] = 0
    if config_dict['idle_timeout'] == "false":
        config_dict['idle_timeout'] = 0

    user_profile = UserProfile.objects.filter(user_id=warehouse.id).only('prefix').first()
    config_dict['prefix'] = user_profile.prefix if user_profile else ''

    batch_character_mapping = MiscDetail.objects.filter(user=warehouse.id, misc_type='batch_character_mapping')
    config_dict['batch_character_mapping'] = batch_character_mapping[0].json_data if batch_character_mapping else {}

    enabled_reports = MiscDetail.objects.filter(misc_type__contains='report', misc_value='true', user=request.user.id)
    config_dict['reports_data'] = []
    for reports in enabled_reports:
        config_dict['reports_data'].append(str(reports.misc_type.replace('report_', '')))
    try:
        all_related_warehouse_id = get_related_users(warehouse.id)
    except Exception as e:
        all_related_warehouse_id = [warehouse.id]
    config_dict['all_related_warehouse'] = dict(
        User.objects.filter(id__in=all_related_warehouse_id).exclude(id=warehouse.id).values_list('first_name', 'id'))
    config_dict['all_related_warehouse'].update({"Intransit of Current Warehouse": warehouse.id})

    config_dict['display_pos'] = ''
    if config_dict['pos_switch'] == 'false':
        config_dict['display_pos'] = 'display:none'
    config_dict['zones_list'] = list(ZoneMaster.objects.filter(user=warehouse.id).values_list('zone', flat=True))
    config_dict['sub_warehouses'] = []
    if warehouse.userprofile.company:
        config_dict['sub_warehouses'] = list(User.objects\
            .filter(userprofile__company_id=warehouse.userprofile.company.id, userprofile__warehouse_type="DEPT", userprofile__warehouse_level=3)\
            .values_list('username', flat=True))

    invoice_formats = get_invoice_formats(request, warehouse)
    if invoice_formats.get("invoice_formats", {}):
        config_dict["invoice_options"] = invoice_formats.get("invoice_formats", {})
        config_dict["invoice_templates"] = invoice_formats.get("data", {})

    config_dict = get_invoice_form_templates(config_dict, warehouse)

    #Get Misc Detail Options
    get_misc_detail_options(config_dict, warehouse)

    # Framing default list for order type configs
    for order_type_config in CONFIG_ORDER_TYPE_DICT:
        if config_dict.get(order_type_config) in [None, '', 'false']:
            config_dict[order_type_config] = []
    if "PRE_INVOICE" not in config_dict.get('outbound_staging_lanes', []):
        config_dict['outbound_staging_lanes'].append("PRE_INVOICE")

    MiscDetail.objects.bulk_create_with_rounding(misc_detail_list)
    return HttpResponse(json.dumps(config_dict))

def prepare_config_dict(config_dict, all_misc_values, extra_misc_types):
    for key, value in CONFIG_SWITCHES_DICT.items():
        config_dict[key] = all_misc_values.get(value, '')

    for key, value in CONFIG_INPUT_DICT.items():
        config_dict[key] = all_misc_values.get(value, '')

    for key, value in extra_misc_types.items():
        config_dict[key] = all_misc_values.get(value, '')
    
    return config_dict

def validate_invoice_level_config(toggle_field, selection, data, warehouse):
    """Validate Invoice level config """
    if toggle_field == 'invoice_level' and selection != data[0].misc_value:
        if ASNSummary.objects.filter(asn_user=warehouse.id, status__in=[1,2, 5]).exists():
            return "Config cannot be changed. There are a few ASNs in the 'In-Transit' state. Please either receive or cancel the In-Transit ASNs"
    return ''

@get_warehouse
def switches(request, warehouse: User):
    account_id = warehouse.userprofile.id
    log_message = (("Request Configurations Changes for User %s, with params are %s") % (str(request.user.username), str(request.GET.dict())))
    log.info(log_message)
    try:
        NOW = datetime.datetime.now()
        toggle_data = {'batch_switch': 'batch_switch',
                       'send_message': 'send_message',
                       'show_image': 'show_image',
                       'back_order': 'back_order',
                       'online_percentage': 'online_percentage',
                       'idle_timeout': 'idle_timeout',
                       'use_imei': 'use_imei',
                       'pallet_switch': 'pallet_switch',
                       'production_switch': 'production_switch',
                       'mail_alerts': 'mail_alerts',
                       'invoice_prefix': 'invoice_prefix',
                       'pos_switch': 'pos_switch',
                       'auto_po_switch': 'auto_po_switch',
                       'no_stock_switch': 'no_stock_switch',
                       'float_switch': 'float_switch',
                       'automate_invoice': 'automate_invoice',
                       'async_picklist_generation': 'async_picklist_generation',
                       'picking_location_copy': 'picking_location_copy',
                       'async_order_creation': 'async_order_creation',
                       'show_distributer': 'show_distributer',
                       'show_order_type': 'show_order_type',
                       'show_mrp': 'show_mrp',
                       'decimal_limit': 'decimal_limit',
                       'print_no_rows': 'print_no_rows',
                       'decimal_limit_price':'decimal_limit_price',
                       'picklist_sort_by': 'picklist_sort_by',
                       'stock_sync': 'stock_sync',
                       'async_picklist_confirmation': 'async_picklist_confirmation',
                       'sku_sync': 'sku_sync',
                       'auto_generate_picklist': 'auto_generate_picklist',
                       'auto_generate_backorder': 'auto_generate_backorder',
                       'order_headers': 'order_headers',
                       'detailed_invoice': 'detailed_invoice',
                       'scan_picklist_option': 'scan_picklist_option',
                       'stock_display_warehouse': 'stock_display_warehouse',
                       'view_order_status': 'view_order_status',
                       'style_headers': 'style_headers',
                       'seller_margin': 'seller_margin',
                       'receive_process': 'receive_process',
                       'tally_config': 'tally_config',
                       'hsn_summary': 'hsn_summary',
                       'gst_summary':'gst_summary',
                       'display_customer_sku': 'display_customer_sku',
                       'label_generation': 'label_generation',
                       'marketplace_model': 'marketplace_model',
                       'barcode_generate_opt': 'barcode_generate_opt',
                       'grn_scan_option': 'grn_scan_option',
                       'invoice_titles': 'invoice_titles',
                       'show_imei_invoice': 'show_imei_invoice',
                       'priceband_sync': 'priceband_sync',
                       'display_remarks_mail': 'display_remarks_mail',
                       'create_seller_order': 'create_seller_order',
                       'invoice_remarks': 'invoice_remarks',
                       'invoice_declaration':'invoice_declaration',
                       'pos_remarks':'pos_remarks',
                       'show_disc_invoice': 'show_disc_invoice',
                       'serial_limit': 'serial_limit',
                       'increment_invoice': 'increment_invoice',
                       'create_shipment_type': 'create_shipment_type',
                       'dashboard_order_level': 'dashboard_order_level',
                       'auto_allocate_stock': 'auto_allocate_stock',
                       'generic_wh_level': 'generic_wh_level',
                       'auto_confirm_po': 'auto_confirm_po',
                       'customer_pdf_remarks': 'customer_pdf_remarks',
                       'tax_inclusive' : 'tax_inclusive',
                       'create_order_po': 'create_order_po',
                       'calculate_customer_price': 'calculate_customer_price',
                       'shipment_sku_scan': 'shipment_sku_scan',
                       'extra_view_order_status':'extra_view_order_status',
                       'bank_option_fields':'bank_option_fields',
                       'disable_brands_view':'disable_brands_view',
                       'invoice_types': 'invoice_types',
                       'sellable_segregation': 'sellable_segregation',
                       'display_styles_price': 'display_styles_price',
                       'picklist_display_address': 'picklist_display_address',
                       'shelf_life_ratio': 'shelf_life_ratio',
                       'mode_of_transport': 'mode_of_transport',
                       'terms_of_payment': 'terms_of_payment',
                       'show_purchase_history': 'show_purchase_history',
                       'display_sku_cust_mapping': 'display_sku_cust_mapping',
                       'disable_categories_view': 'disable_categories_view',
                       'is_portal_lite': 'is_portal_lite',
                       'auto_raise_stock_transfer': 'auto_raise_stock_transfer',
                       'inbound_supplier_invoice': 'inbound_supplier_invoice',
                       'invoice_based_payment_tracker': 'invoice_based_payment_tracker',
                       'customer_dc': 'customer_dc',
                       'auto_expire_enq_limit': 'auto_expire_enq_limit',
                       'sales_return_reasons': 'sales_return_reasons',
                       'central_order_mgmt': 'central_order_mgmt',
                       'receive_po_invoice_check': 'receive_po_invoice_check',
                       'mark_as_delivered': 'mark_as_delivered',
                       'order_exceed_stock': 'order_exceed_stock',
                       'block_min_shelf_life_stock' : 'block_min_shelf_life_stock',
                       'receive_po_mandatory_fields': 'receive_po_mandatory_fields',
                       'additional_batch_attributes': 'additional_batch_attributes',
                       'display_batch_attributes': 'display_batch_attributes',
                       'receive_po_editable_fields': 'receive_po_editable_fields',
                       'sr_grn_mandatory_fields': 'sr_grn_mandatory_fields',
                       'sr_grn_editable_fields': 'sr_grn_editable_fields',
                       'sku_pack_config': 'sku_pack_config',
                       'location_types': 'location_types',
                       'central_order_reassigning':'central_order_reassigning',
                       'sno_in_invoice':'sno_in_invoice',
                       'po_sub_user_prefix': 'po_sub_user_prefix',
                       'combo_allocate_stock': 'combo_allocate_stock',
                       'block_expired_batches_picklist': 'block_expired_batches_picklist',
                       'sku_less_than_threshold':'sku_less_than_threshold',
                       'dispatch_qc_check': 'dispatch_qc_check',
                       'unique_mrp_putaway': 'unique_mrp_putaway',
                       'generate_delivery_challan_before_pullConfiramation':'generate_delivery_challan_before_pullConfiramation',
                       'rtv_prefix_code': 'rtv_prefix_code',
                       'discrepency_prefix':'discrepency_prefix',
                       'non_transacted_skus': 'non_transacted_skus',
                       'allow_rejected_serials':'allow_rejected_serials',
                       'update_mrp_on_grn': 'update_mrp_on_grn',
                       'mandate_sku_supplier':'mandate_sku_supplier',
                       'weight_integration_name': 'weight_integration_name',
                       'repeat_po':'repeat_po',
                       'loc_serial_mapping_switch':'loc_serial_mapping_switch',
                       'brand_categorization':'brand_categorization',
                       'purchase_order_preview':'purchase_order_preview',
                       'picklist_sort_by_sku_sequence': 'picklist_sort_by_sku_sequence',
                       'stop_default_tax':'stop_default_tax',
                       'delivery_challan_terms_condtions': 'delivery_challan_terms_condtions',
                       'order_prefix':'order_prefix',
                       'supplier_mapping':'supplier_mapping',
                       'show_mrp_grn': 'show_mrp_grn',
                       'display_dc_invoice': 'display_dc_invoice',
                       'display_order_reference': 'display_order_reference',
                       'mrp_discount':'mrp_discount',
                       'enable_pending_approval_pos':'enable_pending_approval_pos',
                       'mandate_invoice_number':'mandate_invoice_number',
                       'display_parts_allocation': 'display_parts_allocation',
                       'auto_generate_receive_qty':'auto_generate_receive_qty',
                       'mandate_ewaybill_number':'mandate_ewaybill_number',
                       'allow_partial_picklist': 'allow_partial_picklist',
                       'order_all_items_picklist_generation': 'order_all_items_picklist_generation',
                       'sku_packs_invoice':'sku_packs_invoice',
                       'enable_pending_approval_prs': 'enable_pending_approval_prs',
                       'auto_allocate_sale_order':'auto_allocate_sale_order',
                       'lpn_level_drop_in_picking': 'lpn_level_drop_in_picking',
                       'po_or_pr_edit_permission_approver': 'po_or_pr_edit_permission_approver',
                       'stock_auto_receive':'stock_auto_receive',
                       'attributes_sync': 'attributes_sync',
                       'tax_master_sync': 'tax_master_sync',
                       'st_po_prefix':'st_po_prefix',
                       'supplier_sync': 'supplier_sync',
                       'enable_margin_price_check':'enable_margin_price_check',
                       'receive_po_inv_value_qty_check':'receive_po_inv_value_qty_check',
                       'central_admin_level_po': 'central_admin_level_po',
                       'sku_attribute_grouping_key': 'sku_attribute_grouping_key',
                       'pending_pr_prefix': 'pending_pr_prefix',
                       'auto_putaway_grn': 'auto_putaway_grn',
                       'eom_consumption_configuration_plant': 'eom_consumption_configuration_plant',
                       'location_sku_mapping': 'location_sku_mapping',
                       'slotted_order':'slotted_order',
                       'location_min_norm': 'location_min_norm',
                       'bulk_zones_list': 'bulk_zones_list',
                       'pick_zones_list': 'pick_zones_list',
                       'short_close_order':'short_close_order',
                       'alternative_location_for_partial_pick': 'alternative_location_for_partial_pick',
                       'allow_reserved_in_move_inventory': 'allow_reserved_in_move_inventory',
                       'create_cycle_count_for_short_close': 'create_cycle_count_for_short_close',
                       'sourcesku_sync':'sourcesku_sync',
                       'mobile_version_number': 'mobile_version_number',
                       'view_orders_alert' : 'view_orders_alert',
                       'picklist_reasons' : 'picklist_reasons',
                       'membership_discount_value': 'membership_discount_value',
                       'stock_allocate' : 'stock_allocate',
                       'return_po_qty': 'return_po_qty',
                       'einvoice': 'einvoice',
                       'trigger_invoice_callback' : 'trigger_invoice_callback',
                       'AUDIT LOGS': 'AUDIT LOGS',
                       'document_invoice' : 'document_invoice',
                       'packing_switch': 'packing_switch',
                       'bulk_picking':'bulk_picking',
                       'sorting':'sorting',
                       'staging_area': 'staging_area',
                       'update_order_check': 'update_order_check',
                       'carton_label':'carton_label',
                       'ba_to_sa_logic': 'ba_to_sa_logic',
                       'ba_to_sa_type': 'ba_to_sa_type',
                       'express_putaway': 'express_putaway',
                       'putaway_strategy': 'putaway_strategy',
                       'network_type': 'network_type',
                       'receive_higher_quantity' : 'receive_higher_quantity',
                       'bag_label':'bag_label',
                       'stock_supplier_id':'stock_supplier_id',
                       'invoice_type' : 'invoice_type',
                       'inbound_packing': 'inbound_packing',
                       'asn_packing': 'asn_packing',
                       'draft_asn': 'draft_asn',
                       'sku_category_list_pf': 'sku_category_list_pf',
                       'sku_category_list_ba': 'sku_category_list_ba',
                       'multi_session': 'multi_session',
                       'enable_dispense': 'enable_dispense',
                       'putaway_sa_time_from':'putaway_sa_time_from',
                       'putaway_sa_time_to':'putaway_sa_time_to',
                       'cycle_count_allowance_value':'cycle_count_allowance_value',
                       'saved_weighing_scale': 'saved_weighing_scale',
                       'ignore_packing_instock': 'ignore_packing_instock',
                       'cancel_po': 'cancel_po' ,
                       'cancel_picklist_packlist': 'cancel_picklist_packlist',
                       'auto_invoice' : 'auto_invoice',
                       'invoice_price_as_per_buyprice' : 'invoice_price_as_per_buyprice',
                       'show_picklist_data' : 'show_picklist_data',
                       'export_invoice': 'export_invoice',
                       'bin_picking': 'bin_picking',
                       'scan_mandatory_while_picking' : 'scan_mandatory_while_picking',
                       'scan_first_sku_mandatory' : 'scan_first_sku_mandatory',
                       'restrict_location_to_one_sku': 'restrict_location_to_one_sku',
                       'assign_task_count': 'assign_task_count',
                       'assign_task_count_type': 'assign_task_count_type',
                       'group_replenishment_task_count': 'group_replenishment_task_count',
                       'customer_shelf_life': 'customer_shelf_life',
                       'expense_item_putaway': 'expense_item_putaway',
                       'enable_fast_picking': 'enable_fast_picking',
                       'sku_pack_create_order': 'sku_pack_create_order',
                       'apparel_ship_label': 'apparel_ship_label',
                       'mrp_based_picking': 'mrp_based_picking',
                       'sku_inv_price_tolerance': 'sku_inv_price_tolerance',
                       'grn_inv_price_tolerance': 'grn_inv_price_tolerance',
                       'same_supplier_invoice_check': 'same_supplier_invoice_check',
                       'picklist_priority': 'picklist_priority',
                       'additional_cost_in_wac': 'additional_cost_in_wac',
                       'spoc_warehouse': 'spoc_warehouse',
                       'full_carton_picking': 'full_carton_picking',
                       'sort_by': 'sort_by',
                       'scan_sku_mandatory':'scan_sku_mandatory',
                       'mandate_first_sku_scan':'mandate_first_sku_scan',
                       'show_mrp_raise_po': 'show_mrp_raise_po',
                       'prefill_batch_number': 'prefill_batch_number',
                       'inco_terms': 'inco_terms',
                       'auto_grn_for_salesreturn' : 'auto_grn_for_salesreturn',
                       'nodocument_salesreturn' : 'nodocument_salesreturn',
                       'auto_credit_note' : 'auto_credit_note',
                       'auto_confirm_po_options': 'auto_confirm_po_options',
                       'replenishment_options': 'replenishment_options',
                       'split_by_pick_group':'split_by_pick_group',
                       'free_qty_in_grn': 'free_qty_in_grn',
                       'merge_picking':'merge_picking',
                       'outbound_staging_area': 'outbound_staging_area',
                       'auto_shipment' : 'auto_shipment',
                       'auto_po_frequency': 'auto_po_frequency',
                       'min_max_planning': 'min_max_planning',
                       'back_to_back_po': 'back_to_back_po',
                       'stock_ledger': 'stock_ledger',
                       'manual_assignment':'manual_assignment',
                       'auto_trigger_jo': 'auto_trigger_jo',
                       'gate_management': 'gate_management',
                       'location_based_move_inventory': 'location_based_move_inventory',
                       'lpn_movement': 'lpn_movement',
                       'drop_multiple_lpns_putaway': 'drop_multiple_lpns_putaway',
                       'prefill_suggested_location_in_putaway': 'prefill_suggested_location_in_putaway',
                       'combo_details_in_inventory_callback': 'combo_details_in_inventory_callback',
                       'enable_inventory_approval': 'enable_inventory_approval',
                       'enable_empty_location_cycle_count' : 'enable_empty_location_cycle_count',
                       'schedule_cycle_past_picking': 'schedule_cycle_past_picking',
                       'schedule_cycle_past_putaway': 'schedule_cycle_past_putaway',
                       'reuse_carton':'reuse_carton',
                       'release_carton': 'release_carton',
                       'closing_stock_zones': 'closing_stock_zones',
                       'inbound_staging_lanes' : 'inbound_staging_lanes',
                       'batosa_assign_task_count': 'batosa_assign_task_count',
                       'create_cycle_count_for_short_close_batosa': 'create_cycle_count_for_short_close_batosa',
                       'order_wise_inv':'order_wise_inv',
                       'invoice_price_from_pricing_master': 'invoice_price_from_pricing_master',
                       'inbound_scan_sku_options': 'inbound_scan_sku_options',
                       'jo_batch_level_picking': 'jo_batch_level_picking',
                       'subject_to_retest': 'subject_to_retest',
                       'po_tolerance' : 'po_tolerance',
                       'maximum_takeoff_weight': 'maximum_takeoff_weight',
                       'restrict_stock_creation': 'restrict_stock_creation',
                       'picklist_tolerance': 'picklist_tolerance',
                       'restrict_partial_invoice': 'restrict_partial_invoice',
                       'order_cancel_on_invoice':  'order_cancel_on_invoice',
                       'picklist_tolerance_type': 'picklist_tolerance_type',
                       'dispensing_short_close': 'dispensing_short_close',
                       'rtv_for_blocked_stock': 'rtv_for_blocked_stock',
                       'complete_batch_mapping': 'complete_batch_mapping',
                       'dispensing_tolerance_enabled': 'dispensing_tolerance_enabled',
                       'non_mandatory_batch_attributes': 'non_mandatory_batch_attributes',
                       'zone_mandatory_for_picklist_generation': 'zone_mandatory_for_picklist_generation',
                       'restrict_movement_in_wip': 'restrict_movement_in_wip',
                       'INVOICE' : 'INVOICE',
                       'STOCK TRANSFER': 'STOCK TRANSFER',
                       'EWAYBILL' : 'EWAYBILL',
                       'PENDING PUTAWAY' : 'PENDING PUTAWAY',
                       'PO' : 'PO',
                       'PACKLIST': 'PACKLIST',
                       'PICKLIST': 'PICKLIST',
                       "GRN" : "GRN",
                       'DELIVERY CHALLAN': 'DELIVERY CHALLAN',
                       'CONSOLIDATED_EWAYBILL': 'CONSOLIDATED_EWAYBILL',
                       'DEBIT NOTE': 'DEBIT NOTE',
                       'consumble_quantity_while_grn': 'consumble_quantity_while_grn',
                       'receive_higher_fg_quantity': 'receive_higher_fg_quantity',
                       'enable_weighing': 'enable_weighing',
                       'enable_purchase_uom': 'enable_purchase_uom',
                       'pack_uom_based_lpn_split': 'pack_uom_based_lpn_split',
                       'enable_sales_uom': 'enable_sales_uom',
                       'scan_carton_in_invoice': 'scan_carton_in_invoice',
                       'mobile_invoice_type': 'mobile_invoice_type',
                       'packing_data_in_packing_screen': 'packing_data_in_packing_screen',
                       'packing_in_progress_data_in_packing_screen': 'packing_in_progress_data_in_packing_screen',
                       'invoice_type': 'invoice_type',
                       'order_reference_as_lpn_number': 'order_reference_as_lpn_number',
                       'picklist_strategies' : 'picklist_strategies',
                       'material_return_after_jogrn' : 'material_return_after_jogrn',
                       'consume_extra_stock_while_rm_consumption': 'consume_extra_stock_while_rm_consumption',
                       'location_scan_mandatory' : 'location_scan_mandatory',
                       'rm_return_logic' : 'rm_return_logic',
                       'multi_scanner': 'multi_scanner',
                       'replenishment_task_creation': 'replenishment_task_creation',
                       'group_by_dest_details': 'group_by_dest_details',
                       'nte_picklist_strategy': 'nte_picklist_strategy',
                       'replenishment_pick_drop_user': 'replenishment_pick_drop_user',
                       'nte_pick_drop_user': 'nte_pick_drop_user',
                       'replenishment_picklist_strategy': 'replenishment_picklist_strategy',
                       'restricted_adjustment_status': 'restricted_adjustment_status',
                       'style_based_asn_summary': 'style_based_asn_summary',
                       'min_max_po_type' : 'min_max_po_type',
                       'blind_grn': 'blind_grn',
                       'lpn_restriction' : 'lpn_restriction',
                       'invoice_level' : 'invoice_level',
                       'allow_unitprice_gt_mrp' : 'allow_unitprice_gt_mrp',
                       'allow_future_manufactured_dates' : 'allow_future_manufactured_dates',
                       'allow_past_expiry_dates' : 'allow_past_expiry_dates',
                       'lpn_putaway_execution' : 'lpn_putaway_execution',
                       'show_grn_details_in_putaway' : 'show_grn_details_in_putaway', 
                       'bulk_putaway': 'bulk_putaway',
                       'qr_code_format': 'qr_code_format',
                       'prefill_qty_in_picking': 'prefill_qty_in_picking',
                       'stock_selection_strategy' : 'stock_selection_strategy',
                       'ars_replenishment_strategy': 'ars_replenishment_strategy',
                       'restrict_putaway_override': 'restrict_putaway_override',
                       'grn_rejected_docs_mandatory': 'grn_rejected_docs_mandatory',
                       'allow_batch_edit_in_asn_to_grn': 'allow_batch_edit_in_asn_to_grn',
                       'enable_batch_key' : 'enable_batch_key',
                       'batch_key_prefix': 'batch_key_prefix',
                       'batch_key_validation' : 'batch_key_validation',
                       'auto_po_cancellation': 'auto_po_cancellation',
                       'po_header_fields': 'po_header_fields',
                       'po_mandatory_fields': 'po_mandatory_fields',
                       'hide_asn_rejected_accepted_qty': 'hide_asn_rejected_accepted_qty',
                       'asn_receive_qty_as_invoiced_qty': 'asn_receive_qty_as_invoiced_qty',
                       'asn_fields': 'asn_fields',
                       'asn_mandatory_fields': 'asn_mandatory_fields',
                       'lpn_scan_in_picking':'lpn_scan_in_picking',
                       'validate_unique_id_in_packing': 'validate_unique_id_in_packing',
                       'remove_button_in_packing': 'remove_button_in_packing',
                       'scan_options_in_packing': 'scan_options_in_packing',
                       'task_assignment_priority': 'task_assignment_priority',
                       'nte_selected_source_zone': 'nte_selected_source_zone',
                       'nte_selected_destination_zone': 'nte_selected_destination_zone',
                       'allow_ba_to_sa_partial_picklist': 'allow_ba_to_sa_partial_picklist',
                       'ba_to_sa_block_expired_batches_picklist': 'ba_to_sa_block_expired_batches_picklist',
                       'file_mandatory_for_reject_qty': 'file_mandatory_for_reject_qty',
                       'scandit_license_key': 'scandit_license_key',
                       'lpn_wise_invoice_preview': 'lpn_wise_invoice_preview',
                       'picking_screen_attributes' : 'picking_screen_attributes',
                       'picking_summary_attributes': 'picking_summary_attributes',
                       'move_inventory_task_count' : 'move_inventory_task_count',
                       'mobile_asn_summary_views' : 'mobile_asn_summary_views',
                       'auto_pick_jo' : 'auto_pick_jo',
                       'price_application': 'price_application',
                       'order_expiration_date': 'order_expiration_date',
                       'order_hold_options': 'order_hold_options',
                       'auto_cancel_open_expired_orders': 'auto_cancel_open_expired_orders',
                       'subzone_mapping' : 'subzone_mapping',
                       'asn_approval' : 'asn_approval',
                       'print_saved_invoice': 'print_saved_invoice',
                       'single_scan_location' : 'single_scan_location',
                       'restrict_override_drop_location' : 'restrict_override_drop_location',
                       'create_cycle_count_for_short_close_joborder' : 'create_cycle_count_for_short_close_joborder',
                       'scan_fixed_number_of_sku_in_picking' : 'scan_fixed_number_of_sku_in_picking',
                       'enable_route_master': 'enable_route_master',
                       'update_lpn_details':'update_lpn_details',
                       'lpn_based_move_inventory':'lpn_based_move_inventory',
                       'restricts_number_of_skus_in_a_lpn': 'restricts_number_of_skus_in_a_lpn',
                       'default_filter_in_invoice_and_packing': 'default_filter_in_invoice_and_packing',
                       'auto_generate_einvoice': 'auto_generate_einvoice',
                       'user_sub_zone_mapping': 'user_sub_zone_mapping',
                       'force_zone_removal': 'force_zone_removal',
                       'one_user_one_zone_restriction': 'one_user_one_zone_restriction',
                       'blind_grn_validations': 'blind_grn_validations',
                       'default_filter_in_invoice_and_packing': 'default_filter_in_invoice_and_packing',
                       'auto_generate_einvoice': 'auto_generate_einvoice',
                       'max_skus_count_in_packing': 'max_skus_count_in_packing',
                       'update_lpn_details':'update_lpn_details',
                       'auto_generate_eway_bill': 'auto_generate_eway_bill',
                       'auto_calculate_short_quantity': 'auto_calculate_short_quantity',
                       'sku_limit_for_invoice': 'sku_limit_for_invoice',
                       'allow_po_update_with_open_asn_grn': 'allow_po_update_with_open_asn_grn',
                       'MANIFEST': 'MANIFEST',
                       'enable_inbound_staging_lanes': 'enable_inbound_staging_lanes',
                       'max_skus_count_in_packing': 'max_skus_count_in_packing',
                       'consolidate_packing': 'consolidate_packing',
                       'dynamic_bin_mapping_level': 'dynamic_bin_mapping_level',
                       'dynamic_replenishment_mapping_level': 'dynamic_replenishment_mapping_level',
                       'auto_generate_consolidated_eway_bill': 'auto_generate_consolidated_eway_bill',
                       'restrict_invoice_at_picking': 'restrict_invoice_at_picking',
                       'enable_invoice_value_in_asn': 'enable_invoice_value_in_asn',
                       'enable_invoice_value_in_grn': 'enable_invoice_value_in_grn',
                       'asn_inv_price_tolerance': 'asn_inv_price_tolerance',
                       'batch_attr_prefill_date': 'batch_attr_prefill_date',
                       'override_picklist_priority': 'override_picklist_priority',
                       'grn_tax_source': 'grn_tax_source',
                       'grn_trigger_count': 'grn_trigger_count',
                       'close_manifest': 'close_manifest',
                       'allow_close_manifest': 'allow_close_manifest',
                       'save_lpn_details_in_invoice': 'save_lpn_details_in_invoice',
                       'mandate_sku_scan_at_picking': 'mandate_sku_scan_at_picking',
                       'cycle_time_in_picking': 'cycle_time_in_picking',
                       'location_single_scan_in_picking': 'location_single_scan_in_picking',
                       'unique_id_scan_in_asn': 'unique_id_scan_in_asn',
                       'enable_order_type_selection_in_mobile': 'enable_order_type_selection_in_mobile',
                       'eff_rate_in_rtv': 'eff_rate_in_rtv',
                       'enable_control_tower': 'enable_control_tower',
                       'incremental_batch_prefix': 'incremental_batch_prefix',
                       'show_create_manifest': 'show_create_manifest',
                       'MOBILE INVOICE': 'MOBILE INVOICE',
                       'CREDIT NOTE': 'CREDIT NOTE',
                       'manifest_filters': 'manifest_filters',
                       'disable_location_scan': 'disable_location_scan',
                       'tab_view_in_mobile': 'tab_view_in_mobile',
                       'all_batch_cycle_count_on_short_pick': 'all_batch_cycle_count_on_short_pick',
                       'show_inventory_in_cycle_count': 'show_inventory_in_cycle_count',
                       'allow_skip_cycle_count': 'allow_skip_cycle_count',
                       'mandate_cycle_count_assignment_by_zone': 'mandate_cycle_count_assignment_by_zone',
                       'mandate_tripid_for_picklist' : 'mandate_tripid_for_picklist',
                       'invoice_split_on_invoice_group': 'invoice_split_on_invoice_group',
                       'staging_location_routing': 'staging_location_routing',
                       'show_summary_in_picking': 'show_summary_in_picking',
                       'rapid_picklist_confirmation': 'rapid_picklist_confirmation',
                       'warehouse_notifications': 'warehouse_notifications',
                       'connect_electron': 'connect_electron',
                       'allowed_special_characters': 'allowed_special_characters',
                       'restrict_picklist_on_cycle_count_creation_options': 'restrict_picklist_on_cycle_count_creation_options',
                       'restrict_picklist_on_cycle_count_pending_approval_options': 'restrict_picklist_on_cycle_count_pending_approval_options',
                       'sr_grn_packing': 'sr_grn_packing',
                       'hold_grn_po_types': 'hold_grn_po_types',
                       'inspection_lot_inv_hold_after_putaway': 'inspection_lot_inv_hold_after_putaway',
                       'customer_level_packing': 'customer_level_packing',
                       'material_request': 'material_request',
                       'restrict_expired_orders' : 'restrict_expired_orders',
                       'warehouse_notifications': 'warehouse_notifications',
                       'skip_location_suggestions_in_lpn_drop': 'skip_location_suggestions_in_lpn_drop',
                       'show_inventory_in_picking': 'show_inventory_in_picking',
                       'cancel_task_in_picking': 'cancel_task_in_picking',
                       'manifest_creation_grouping': 'manifest_creation_grouping',
                       'gate_pass_validation_during_grn': 'gate_pass_validation_during_grn',
                       'margin_value_validation': 'margin_value_validation',
                       'direct_dispatch_of_orders': 'direct_dispatch_of_orders',
                       'gate_pass_file_upload': 'gate_pass_file_upload',
                       'cancel_open_order_at_picklist_generation' : 'cancel_open_order_at_picklist_generation',
                       'cancel_open_order_at_picklist_confirmation' : 'cancel_open_order_at_picklist_confirmation',
                       'invoice_callback_at_order_level': 'invoice_callback_at_order_level',
                       'allow_grn_for_rtv_qty': 'allow_grn_for_rtv_qty',
                       'update_batch_details_config' : 'update_batch_details_config',
                       'intermediate_drop_in_pick_and_pass': 'intermediate_drop_in_pick_and_pass',
                       'allocation_options' : 'allocation_options',
                       'allow_jo_partial_picklist': 'allow_jo_partial_picklist',
                       'sku_serialisation': 'sku_serialisation',
                       'location_capacity_calculation': 'location_capacity_calculation',
                       'enable_seller': 'enable_seller',
                       'invoice_to_picklist_quantity': 'invoice_to_picklist_quantity',
                       'po_type_level_packing': 'po_type_level_packing',
                       'new_dashboard_apis':'new_dashboard_apis',
                       'reason_for_cycle_count': 'reason_for_cycle_count',
                       'allow_negative_inventory': 'allow_negative_inventory',
                       'negative_inventory_threshold': 'negative_inventory_threshold',
                       'allow_negative_inventory_in_zones': 'allow_negative_inventory_in_zones',
                       'cycle_count_variance_ledger': 'cycle_count_variance_ledger',
                       'positive_variance_ledger_location': 'positive_variance_ledger_location',
                       'negative_variance_ledger_location': 'negative_variance_ledger_location',
                       'hide_grn_fields': 'hide_grn_fields',
                       'sr_grn_lpn_restriction': 'sr_grn_lpn_restriction',
                       'seller_usage_calculation': 'seller_usage_calculation',
                       'serial_number_mapping': 'serial_number_mapping',
                       'enable_standard_dashboards': 'enable_standard_dashboards',
                       'validate_doc_num_per_fy': 'validate_doc_num_per_fy',
                       'dock_scheduling': 'dock_scheduling',
                       'dock_scheduling_handling_unit': 'dock_scheduling_handling_unit', 
                       'mandate_serial_scan_at_invoice': 'mandate_serial_scan_at_invoice',
                       'auto_drop_after_picking': 'auto_drop_after_picking',
                       'full_lpn_invoice_at_order_fulfill': 'full_lpn_invoice_at_order_fulfill',
                       'mandate_allocation_for_picklist_generation': 'mandate_allocation_for_picklist_generation',
                       'minimum_doc_time': 'minimum_doc_time',
                       'check_digit_limit':'check_digit_limit',
                       'image_expansion':'image_expansion',
                       'sales_return_check_in': 'sales_return_check_in',
                       'merge_stock_ids_in_invoice': 'merge_stock_ids_in_invoice',
                       'validate_customer_po_number' : 'validate_customer_po_number',
                       'max_days_for_material_transactions': 'max_days_for_material_transactions',
                       'enable_invoice_value_tolerance': 'enable_invoice_value_tolerance',
                       'manual_stock_selection_for_picklist' : 'manual_stock_selection_for_picklist',
                       "sorting_for_external_system": "sorting_for_external_system",
                       'picklist_label_content': 'picklist_label_content',
                       'allow_grn_line_cancel': 'allow_grn_line_cancel',
                       'asn_grn_from_po_header': 'asn_grn_from_po_header',
                       'hide_asn_fields': 'hide_asn_fields',
                       'validate_order_type': 'validate_order_type',
                       'mrp_tolerance': 'mrp_tolerance',
                       'allow_partial_grn_in_asn': 'allow_partial_grn_in_asn',
                       'auto_putaway': 'auto_putaway',
                       'image_expansion': 'image_expansion',
                       'cancel_open_delivery_lines': 'cancel_open_delivery_lines',
                       'manual_wave' : 'manual_wave',
                       'zone_mandatory_for_jo_picklist_generation': 'zone_mandatory_for_jo_picklist_generation',
                       'enable_staging_locator_in_grn': 'enable_staging_locator_in_grn',
                       'source_lpn_as_destination_lpn_in_picking': 'source_lpn_as_destination_lpn_in_picking',
                       'order_approval': 'order_approval',
                       'enable_outbound_qc' : 'enable_outbound_qc',
                       'sku_quantity_in_qc' : 'sku_quantity_in_qc',
                       'enable_check_digit': 'enable_check_digit',
                       'pigeon_hole_sorting': 'pigeon_hole_sorting',
                       'invoice_preview_view': 'invoice_preview_view',
                       'allow_asn_creation_without_batch': 'allow_asn_creation_without_batch',
                       'check_digit_delimiter': 'check_digit_delimiter',
                       'lpn_type_suggestion': 'lpn_type_suggestion',
                       'task_based_move_inventory': 'task_based_move_inventory',
                       'move_inventory_approval': 'move_inventory_approval',
                       'restrict_sku_batch_mixing': 'restrict_sku_batch_mixing',
                       'allow_multi_customer_lpn_scan_at_invoice': 'allow_multi_customer_lpn_scan_at_invoice',
                       'cluster_type_filtering': 'cluster_type_filtering',
                       'sort_picklist_by_scanned_location': 'sort_picklist_by_scanned_location',
                       'update_inventory_expiry_status': 'update_inventory_expiry_status',
                       'approval_order_types': 'approval_order_types',
                       'old_putaway_suggestions': 'old_putaway_suggestions',
                       'enable_skip_task': 'enable_skip_task',
                       'update_inventory_expiry_status': 'update_inventory_expiry_status',
                       'enable_order_view_v2': 'enable_order_view_v2', # Temporary: Will be removed once v2 is stable
                       'restrict_to_single_order_invoice': 'restrict_to_single_order_invoice',
                       'putaway_class_category': 'putaway_class_category',
                       'zone_wise_manual_assignment' : 'zone_wise_manual_assignment',
                       'restrict_default_location_suggestion_for_pigeon_hole_sorting': 'restrict_default_location_suggestion_for_pigeon_hole_sorting',
                       'suspend_cancel_orders': 'suspend_cancel_orders',
                       'line_level_picklist_cancellation': 'line_level_picklist_cancellation',
                       'flag_order_level_discrepancy' : 'flag_order_level_discrepancy',
                       'line_level_order_cancellation': 'line_level_order_cancellation',
                       'allowed_serial_number_special_characters': 'allowed_serial_number_special_characters',
                    }
        toggle_field, selection = "", ""
        invalid_keys = []
        for key, value in request.GET.items():
            if key in toggle_data:
                toggle_field = toggle_data[key]
                selection = value
            else:    
                invalid_keys.append(key)
        
        if invalid_keys:
            return JsonResponse({"message":"Invalid Config Keys : " + ', '.join(invalid_keys)}, status=400)

        user_id = warehouse.id
        if toggle_field == 'incremental_batch_prefix':
            prefix = selection if selection else 'BATCH'
            user_prefix = UserPrefixes.objects.filter(user_id=user_id, type_name='incremental_batch')
            if user_prefix:
                setattr(user_prefix[0], 'prefix', prefix)
                user_prefix[0].save()
        if key in ['customer_portal_prefered_view','weight_integration_name', 'mobile_version_number', 'membership_discount_value', 'tab_view_in_mobile']:
            data = MiscDetail.objects.filter(misc_type=key, user=request.user.id, account__user_id=warehouse.id)
            if not data:
                misc_detail = MiscDetail(user=request.user.id, misc_type=key, misc_value=selection,
                                         creation_date=NOW, updation_date=NOW, account=warehouse.userprofile)
                misc_detail.save()
            else:
                setattr(data[0], 'misc_value', selection)
                data[0].save(update_fields=['misc_value', 'updation_date'])
        else:
            document_invoice = request.GET.get('document_invoice', '')
            if not toggle_field and document_invoice == 'true':
                toggle_field = key
            data = MiscDetail.objects.filter(misc_type=toggle_field, user=user_id)
            if not data:
                misc_detail = MiscDetail(user=user_id, misc_type=toggle_field, misc_value=selection,
                                         creation_date=NOW, updation_date=NOW, account=warehouse.userprofile)
                
                misc_detail.save()
            else:
                error_message = validate_invoice_level_config(toggle_field, selection, data, warehouse)
                if error_message:
                    return JsonResponse({"message": error_message}, status=400)
                setattr(data[0], 'misc_value', selection)
                data[0].save()
            if key in ['decimal_limit']:
                update_configs_in_cache(account_id, toggle_field, selection)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info("Update Configurations failed for params " + str(request.GET.dict()) + " on " + \
                 str(get_local_date(warehouse, datetime.datetime.now())) + "and error statement is " + str(e))
        return JsonResponse({"message":"Updation Failed"}, status=400)

    return JsonResponse({'message':'Success'}, status=200)

def update_configs_in_cache(account_id, misc_key, misc_value):
    redis_wrapper = RedisJSONWrapper()
    '''
    Saving in Cache in this format : {account_id : {configurations : {misc_key: misc_value}}}
    '''
    # Get the current configuration data from the cache (if it exists)
    user_config = redis_wrapper.get(account_id) or {}

    # Use setdefault to either create or update the 'decimal_limit' key
    user_config['configurations'] = user_config.get('configurations', {})
    user_config['configurations'][misc_key] = misc_value

    # Set the updated data back in the cache
    redis_wrapper.set(account_id, user_config)

def get_pr_approvals_configuration_data(warehouse: User, purchase_type='PO'):
    if purchase_type == 'PO':
        master_type = 'pr_approvals_conf_data'
    elif purchase_type == 'PR':
        master_type = 'actual_pr_approvals_conf_data'
    pr_conf_obj = PurchaseApprovalConfig.objects.filter(user=warehouse, purchase_type=purchase_type).order_by('creation_date')
    pr_conf_data = pr_conf_obj.values('id', 'name', 'product_category', 'sku_category', 'department_type',
                                      'min_Amt', 'max_Amt', 'level')
    mailsMap = {}
    totalConfigData = OrderedDict()
    for eachConfData in pr_conf_data:
        name = eachConfData['name']
        prod_catg = eachConfData['product_category']
        sameLevelMailIds = MasterEmailMapping.objects.filter(master_id=eachConfData['id'],
                                    master_type=master_type, user=warehouse).values_list('email_id', flat=True)
        commaSepMailIds = ','.join(sameLevelMailIds)
        eachConfData['mail_id'] = {str(eachConfData['level']):commaSepMailIds}
        if name not in totalConfigData:
            totalConfigData[name] = eachConfData
        else:
            totalConfigData[name]['mail_id'][str(eachConfData['level'])] = commaSepMailIds
    return list(totalConfigData.values())

@get_warehouse
def all_purchase_approval_config_data(request, warehouse:User):
    config_dict = {}
    config_dict['pr_approvals_conf_data'] = get_pr_approvals_configuration_data(warehouse, purchase_type='PO')
    config_dict['actual_pr_approvals_conf_data'] = get_pr_approvals_configuration_data(warehouse, purchase_type='PR')
    return HttpResponse(HttpResponse(json.dumps({'config_data': config_dict})))

@get_warehouse
def save_groups(request, warehouse: User):
    groups = request.GET.get('sku_groups')
    groups = groups.split(',')
    all_groups = SKUGroups.objects.filter(user=warehouse.id).values_list('group', flat=True)
    for group in groups:
        group_obj = SKUGroups.objects.filter(group=group, user=warehouse.id)
        if not group_obj:
            group_dict = copy.deepcopy(SKU_GROUP_FIELDS)
            group_dict['group'] = group
            group_dict['user'] = warehouse.id
            group_dict['account'] = warehouse.userprofile
            new_group = SKUGroups(**group_dict)
            new_group.save()
    deleted_groups = set(all_groups) - set(groups)
    for group in deleted_groups:
        SKUGroups.objects.get(group=group, user=warehouse.id).delete()
        skus = SKUMaster.objects.filter(sku_group=group, user=warehouse.id).update(sku_group='')
        LocationGroups.objects.filter(group=group, location__zone__user=warehouse.id).delete()
    return HttpResponse("Saved Successfully")

@get_warehouse
def save_order_extra_fields(request, warehouse: User):
    #order extra fields configs
    order_extra_fields = request.GET.get('extra_order_fields', '')
    misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type='extra_order_fields')
    try:
        if not misc_detail.exists():
             MiscDetail.objects.create(user=warehouse.id,misc_type='extra_order_fields',misc_value=order_extra_fields, account=warehouse.userprofile)
        else:
            misc_order_option_list = list(MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_detail__misc_type='extra_order_fields').values_list('misc_key',flat=True))
            order_extra_list = order_extra_fields.split(',')
            diff_list = list(set(misc_order_option_list)- set(order_extra_list))
            if len(diff_list) > 0 :
                for key in diff_list :
                    misc_records = MiscDetailOptions.objects.filter(misc_detail__user= warehouse.id, misc_detail__misc_type='extra_order_fields', misc_key = key)
                    for record in misc_records :
                        record.delete()
            misc_detail_obj = misc_detail[0]
            misc_detail_obj.misc_value = order_extra_fields
            misc_detail_obj.save()
    except:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Issue for order extra fields' + str(request))
        return HttpResponse(exception_message)

    return HttpResponse("Saved Successfully")

@get_warehouse
def save_return_types(request, warehouse: User):
    return_type_str = request.GET.get('return_types', '')
    misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type='return_types')
    try:
        if not misc_detail.exists():
            MiscDetail.objects.create(user=warehouse.id,misc_type='return_types',misc_value=return_type_str, account=warehouse.userprofile)
        else:
            misc_return_type_list = list(MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_detail__misc_type='return_types').values_list('misc_key',flat=True))
            return_types_list = return_type_str.split(',')
            diff_list = list(set(misc_return_type_list)- set(return_types_list))
            if len(diff_list) > 0 :
                MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_key__in=diff_list, misc_detail__misc_type='return_types').delete()
            misc_detail_obj = misc_detail[0]
            misc_detail_obj.misc_value = return_type_str
            misc_detail_obj.save()
    except Exception:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Issue for save return types' + str(request))
        return HttpResponse(exception_message)

    return HttpResponse("Saved Successfully")

@get_warehouse
def send_mail_reports(request, warehouse: User):
    mail_report_obj = MailReports()
    email = request.GET.get('mails', '')
    if email:
        add_misc_email(warehouse, email)
    misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type='email')
    if misc_detail and misc_detail[0].misc_value:
        mail_report_obj.send_reports_mail(warehouse, mail_now=True)
        return HttpResponse('Success')
    return HttpResponse('Email ids not found')

def add_misc_email(user, email):
    misc_detail = MiscDetail.objects.filter(user=user.id, misc_type='email')
    if misc_detail:
        misc_detail[0].misc_value = email
        misc_detail[0].save()
    else:
        misc_detail = MiscDetail(user=user.id, misc_type='email', misc_value=email)
        misc_detail.save()

@get_warehouse
def update_mail_configuration(request, warehouse: User):
    data = {}
    selected = request.POST.getlist('selected[]')
    removed = request.POST.getlist('removed[]')
    frequency = request.POST.get('frequency')
    data_range = request.POST.get('range')
    email = request.POST.get('email', '')
    date_val = request.POST.get('date_val')

    for select_data in selected:
        misc_type = 'report_' + REPORTS_DATA[select_data.strip()]
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=misc_type)
        if misc_detail:
            misc_detail[0].misc_value = 'true'
            misc_detail[0].save()
            continue

        misc_detail = MiscDetail(user=warehouse.id, misc_type=misc_type, misc_value='true')
        misc_detail.save()

    for select_data in removed:
        misc_type = 'report_' + REPORTS_DATA[select_data.strip()]
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=misc_type)
        if misc_detail:
            misc_detail[0].misc_value = 'false'
            misc_detail[0].save()
            continue

        misc_detail = MiscDetail(user=warehouse.id, misc_type=misc_type, misc_value='false')
        misc_detail.save()

    if frequency:
        data_dict = {'user': warehouse.id, 'misc_type': 'report_frequency'}
        if date_val:
            date_val = time.strptime(date_val, '%d/%M/%Y')
            date_val = time.strftime('%Y-%d-%M', date_val)
        misc_detail = MiscDetail.objects.filter(**data_dict)
        if misc_detail:
            misc_detail[0].misc_value = frequency
            if date_val:
                misc_detail[0].creation_date = date_val
            misc_detail[0].save()
        else:
            if date_val:
                data_dict['creation_date'] = date_val
            misc_detail = MiscDetail(**data_dict)
            misc_detail.misc_value = frequency
            misc_detail.save()

    if data_range:
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type='report_data_range')
        if misc_detail:
            misc_detail[0].misc_value = data_range
            misc_detail[0].save()
        else:
            misc_detail = MiscDetail(user=warehouse.id, misc_type='report_data_range', misc_value=data_range)
            misc_detail.save()

    existing_emails = MiscDetail.objects.filter(misc_type='email', user=warehouse.id).values_list('misc_value', flat=True)

    add_misc_email(warehouse, email)

    return HttpResponse('Success')

@get_warehouse
def save_stages(request, warehouse: User):
    stages = request.GET.get('stage_names', '')
    stages = stages.split(',')
    all_stages = ProductionStages.objects.filter(user=warehouse.id).values_list('stage_name', flat=True)
    index = 1
    for stage in stages:
        if not stage:
            continue
        stage_obj = ProductionStages.objects.filter(stage_name=stage, user=warehouse.id)
        if not stage_obj:
            stage_dict = copy.deepcopy(STAGES_FIELDS)
            stage_dict['stage_name'] = stage
            stage_dict['user'] = warehouse.id
            stage_dict['order'] = index
            stage_dict['account'] = warehouse.userprofile
            new_stage = ProductionStages(**stage_dict)
            new_stage.save()
        elif stage_obj:
            stage_obj[0].order = index
            stage_obj[0].save()
        index += 1
    deleted_stages = set(all_stages) - set(stages)
    for stage in deleted_stages:
        ProductionStages.objects.get(stage_name=stage, user=warehouse.id).delete()
        job_ids = JobOrder.objects.filter(product_code__user=warehouse.id,
                                          status__in=['grn-generated', 'pick_confirm']).values_list('id', flat=True)
        StatusTracking.objects.filter(status_value=stage, status_id__in=job_ids, status_type='JO').delete()
    return HttpResponse("Saved Successfully")

@get_warehouse
def save_misc_detail_options(request, warehouse: User):
    misc_type = request.GET.get('misc_type', '')
    misc_detail_options = request.GET.get('misc_detail_options', '')
    misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=misc_type)
    try:
        if not misc_detail.exists():
            misc_detail_obj = MiscDetail.objects.create(user=warehouse.id,misc_type=misc_type,misc_value='true', account=warehouse.userprofile)
        else:
            misc_detail_obj = misc_detail[0]
        misc_options = dict(MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_detail__misc_type=misc_type).values_list('misc_value', 'status'))
        misc_option_list = set(misc_options.keys())
        given_list = set(misc_detail_options.split(',') if misc_detail_options else [])
        if misc_type == 'outbound_staging_lanes':
            given_list.add("PRE_INVOICE")
        extra_list = list(misc_option_list- given_list)
        add_list = list(given_list- misc_option_list)
        update_list = []
        for key in given_list:
            if not misc_options.get(key):
                update_list.append(key)
        if len(extra_list) > 0:
            MiscDetailOptions.objects.filter(misc_detail = misc_detail_obj, misc_value__in = extra_list).update(status=0)
        add_objs = []
        if add_list:
            for add_val in add_list:
                add_objs.append(MiscDetailOptions(misc_detail = misc_detail_obj, misc_key=misc_type, misc_value=add_val, account=warehouse.userprofile))
        if add_objs:
            MiscDetailOptions.objects.bulk_create_with_rounding(add_objs)
        if update_list:
            MiscDetailOptions.objects.filter(misc_detail = misc_detail_obj, misc_value__in = update_list).update(status=1)
        if misc_detail_options:
            misc_detail_obj.misc_value = 'true'
        else:
            misc_detail_obj.misc_value = 'false'
        misc_detail_obj.save()
        return HttpResponse("Success")
    except:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Issue for save miscdetail options' + str(request))
        return HttpResponse(exception_message)
    
class Wavepicking(WMSListView):
    '''Wave Picking Configurations'''
    def get(self, *args, **kwargs):
        '''Get the wave picking configurations'''
        try:
            self.set_user_credientials()
            request_params = self.request.GET
            filters = {'warehouse_id':self.warehouse.id,'status':1}
            if request_params.get('name'):
                filters.update({'name':request_params.get('name')})
            values_list = [
                'id', 'name', 'order_type', 'frequency', 'start_time', 'end_time', 'no_of_orders', 'picklist_strategy',
                'status', 'json_data', 'order_creation_days', 'full_open_order', 'action_type', 'customer_category', 'route_name'
            ]
            wave_picking_data = list(WaveCriteria.objects.filter(**filters).values(*values_list))
            for wave in wave_picking_data:
                wave['wave_name'] = wave.pop('name')
                wave['start_time'] = wave['start_time'].strftime("%H:%M")
                wave['end_time'] = wave['end_time'].strftime("%H:%M")
                wave['frequency'] = wave['frequency'].replace('H',':').replace('T','')
                wave['pick_type'] = wave.pop('picklist_strategy')
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Get Wave picking options failed for %s and params are %s and error statement is %s' % (
            str(self.warehouse), str(self.request.GET), str(e)))
            return JsonResponse({"message":exception_message},status=400)        
        return JsonResponse({'data':wave_picking_data})

    def post(self, *args, **kwargs):
        '''Save the wave picking configurations'''
        try:
            start_hr, end_hr = '', ''
            update_objs, create_objs = [], []
            self.set_user_credientials()
            request_data = json.loads(self.request.body)
            wave_objs = WaveCriteria.objects.filter(warehouse_id=self.warehouse.id,status=1)
            for request in request_data:
                misc_type = request.get('misc_type','wave_picking')
                wave_name = request.get('wave_name','')
                order_type = request.get('order_type','')
                frequency = request.get('frequency','')
                no_of_orders = request.get('no_of_orders',0)
                wave_id = request.get('id',0)
                if frequency :
                    start_hr = request.get('start_time','')
                    end_hr = request.get('end_time','')
                pick_type = request.get('pick_type','default')
                action_type = request.get('action_type', 'picklist_generation')
                status = int(request.get('status',1))
                full_open_order = request.get('full_open_order', False)
                customer_category = request.get('customer_category', '')
                route_name = request.get('route_name', '')

                hours, minutes = frequency.split(':')
                frequency = hours + 'H' + minutes + 'T'
                order_days = request.get('order_creation_days', 0)
                if misc_type !='wave_picking' :
                    return JsonResponse({'message':'Invalid Misc Type'},status=400)
                if not wave_name :
                    return JsonResponse({'message':'Name should not be empty'},status=400)
                if status:
                    errors, start_hr, end_hr = self.validate_request_data(order_type, frequency, start_hr, end_hr)
                    if errors:
                        return errors
                    
                    existing_wave_data = wave_objs.filter(name=wave_name,id=wave_id)
                    if existing_wave_data.exists():
                        existing_wave_data = existing_wave_data[0]
                        if existing_wave_data.status == 1:
                            existing_wave_data.status = 0
                            existing_wave_data.save()
                    existing_wave_data = wave_objs.filter(status=1)
                    errors = self.validate_wave_availability(existing_wave_data, create_objs, order_type, start_hr, end_hr)
                    if errors:
                        return errors
                    wave_dict = {
                                'name' : wave_name,
                                'order_type': order_type, 
                                'frequency' : frequency,
                                'start_time' : start_hr, 
                                'end_time' : end_hr, 
                                'no_of_orders' : no_of_orders, 
                                'picklist_strategy' : pick_type,
                                'action_type' : action_type,
                                'status' : status,
                                'warehouse_id' : self.warehouse.id, 
                                'order_creation_days' : order_days,
                                'full_open_order' : full_open_order,
                                'account_id' : self.warehouse.userprofile.id,
                                'customer_category' : customer_category,
                                'route_name' : route_name
                                }
                    create_objs.append(WaveCriteria(**wave_dict))
                else:
                    wave_obj = WaveCriteria.objects.filter(warehouse_id=self.warehouse.id,name=wave_name,id=wave_id)
                    if wave_obj.exists():
                        wave_obj = wave_obj[0]
                        wave_obj.status = status
                        update_objs.append(wave_obj)
            
            if create_objs:
                WaveCriteria.objects.bulk_create_with_rounding(create_objs)
            if update_objs:
                WaveCriteria.objects.bulk_update_with_rounding(update_objs, ['status'])
        
        except Exception:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Issue for save wave picking config' + str(request))
            return JsonResponse({'message':'Something Went Wrong'},status=400)
        
        return JsonResponse({'message':'Success'},status=200) 
            
    def validate_request_data(self, order_type, frequency, start_hr, end_hr):
        '''Validate the request data for wave picking configurations'''
        if not order_type:
            return JsonResponse({'message':'Order Type is Mandatory'},status=400), start_hr, end_hr
        if frequency and not (start_hr and end_hr):
            return JsonResponse({'message':'Start/End time is Mandatory'},status=400), start_hr, end_hr
        else:
            try:
                start_hr = datetime.datetime.strptime(start_hr, "%H:%M")
                end_hr = datetime.datetime.strptime(end_hr, "%H:%M")

                if start_hr > end_hr:
                    return JsonResponse({'message':'Start Time should be greater End Time'},status=400), start_hr, end_hr
            except Exception: 
                return JsonResponse({'message':'Invalid Start/End Time format'},status=400), start_hr, end_hr
    
        return None, start_hr, end_hr

    def validate_wave_availability(self, existing_wave_data, create_objs, order_type, start_hr, end_hr):
        '''Validate the wave availability for the given order type and time interval'''
        start_hr = start_hr.time()
        start_seconds = start_hr.hour * 3600 + start_hr.minute * 60 + start_hr.second
        end_hr = end_hr.time()
        end_seconds = end_hr.hour * 3600 + end_hr.minute * 60 + end_hr.second
        range_list = [(start_seconds, end_seconds)]
        if create_objs:
            existing_wave_data = list(existing_wave_data)
            existing_wave_data.extend(create_objs)
        req_order_types = set(order_type.split(","))
        for wave_entry in existing_wave_data:
            # Extract relevant information from the wave entry
            wave_order_types = set(wave_entry.order_type.split(","))
            
            # Check if the order type matches
            if not(wave_order_types & req_order_types):
                continue
            
            try:
                wave_start_hr = wave_entry.start_time.time()
                wave_end_hr = wave_entry.end_time.time()
            except Exception:
                wave_start_hr = wave_entry.start_time
                wave_end_hr = wave_entry.end_time
            
            wave_start_hr_seconds = wave_start_hr.hour * 3600 + wave_start_hr.minute * 60 + wave_start_hr.second
            wave_end_hr_seconds = wave_end_hr.hour * 3600 + wave_end_hr.minute * 60 + wave_end_hr.second 
            range_list.append((wave_start_hr_seconds, wave_end_hr_seconds))
            
            try:
                # Check if start time is before end time
                if start_hr >= end_hr:
                    return JsonResponse({'message':'End Time is before Start time'},status=400) 
            
                # Check if the given time interval overlaps with the existing wave
                range_list = sorted(range_list)
                for i in range(len(range_list) - 1):
                    if range_list[i][1] > range_list[i + 1][0]:
                        return JsonResponse({'message':'Time interval overlaps with existing wave'},status=400)
            except ValueError:
                # Handle invalid time format
                return JsonResponse({'message':'Invalid time format'},status=400) 
        return False

@get_warehouse
def save_multiple_misc_options(request, warehouse: User):
    """
    Save and Update multiple misc options for a warehouse.
    """
    request_data = json.loads(request.body)
    warehouse_id = warehouse.id
    bulk_updates, bulk_creates = [], []
    warehouse_profile = warehouse.userprofile
    misc_type = request_data.get('misc_type', '')
    multi_misc_options = request_data.get('multi_misc_options', [])

    misc_detail = MiscDetail.objects.filter(user=warehouse_id, misc_type=misc_type)
    if not misc_detail.exists():
        misc_detail_obj = MiscDetail.objects.create(user=warehouse.id,misc_type=misc_type,misc_value='true', account=warehouse.userprofile)
    else:
        misc_detail_obj = misc_detail[0]
    try:
        # Check if the request is for deleting an entry
        if request_data.get('id'):
            misc_detail_options = MiscDetailOptions.objects.filter(id=request_data['id'])
            if not misc_detail_options.exists():
                return JsonResponse({"message": "Invalid ID"}, status=400)
            misc_detail_options = misc_detail_options[0]
            misc_detail_options.status = False
            misc_detail_options.save()
            return JsonResponse({"message": "Success"}, status=200)

        request_misc_keys = [each_data['misc_key'] for each_data in multi_misc_options]

        if not request_misc_keys and misc_detail_obj.misc_type == 'ars_replenishment_strategy':
            # Remove all the existing entries
            misc_detail_opt_objs = MiscDetailOptions.objects.filter(misc_detail=misc_detail_obj)
            for misc_detail_opt_obj in misc_detail_opt_objs:
                misc_detail_opt_obj.status = False
                bulk_updates.append(misc_detail_opt_obj)

        # Iterate through each data and update/create the entry
        for each_data in multi_misc_options:
            misc_j_data = each_data.get('data') or {}
            json_data = {
                each_data['misc_key']: each_data['misc_value']
            }
            json_data.update(misc_j_data)
            misc_detail_options = list(MiscDetailOptions.objects.filter(misc_key=each_data['misc_key'], misc_detail=misc_detail_obj).order_by('-status'))
            other_misc_detail_options = list(MiscDetailOptions.objects.filter(misc_value=each_data['misc_value'], misc_detail=misc_detail_obj).exclude(misc_key=each_data['misc_key']).order_by('-status'))

            if misc_detail_options:
                misc_obj = misc_detail_options[0]
                misc_obj.status = True
                misc_obj.misc_key = each_data['misc_key']
                misc_obj.misc_value = each_data['misc_value']
                misc_obj.json_data = json_data
                bulk_updates.append(misc_obj)
                # Update the status of other entries with the same misc_value, if replaced.
                for misc_obj in other_misc_detail_options:
                    if misc_obj.misc_key != each_data['misc_key'] and misc_obj.misc_key not in request_misc_keys and misc_obj.status:
                        misc_obj.status = False
                        bulk_updates.append(misc_obj)
            else:
                # Create a new entry
                new_entry = MiscDetailOptions(
                    misc_detail=misc_detail_obj,
                    misc_key=each_data['misc_key'],
                    misc_value=each_data['misc_value'],
                    json_data=json_data,
                    status = True,
                    account=warehouse_profile
                )
                bulk_creates.append(new_entry)

        # Perform bulk update
        if bulk_updates:
            MiscDetailOptions.objects.bulk_update(bulk_updates, ['misc_key','misc_value','status', 'json_data'])

        # Perform bulk create
        if bulk_creates:
            MiscDetailOptions.objects.bulk_create(bulk_creates)

        return JsonResponse({"message": "Success"}, status=200)
    except Exception:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Issue for save multiple miscdetail options' + str(request))
        return JsonResponse({"message": "Something went wrong"}, status=400)

@get_warehouse
def fetch_multiple_misc_options(request, warehouse:User):
    config_dict = {}
    misc_type = request.GET.get('misc_type', '')
    filter_params = {
        "misc_detail__user": warehouse.id,
        "status": 1
    }
    if misc_type == 'ars_replenishment_strategy':
        return get_ars_replenishment_strategy(misc_type, warehouse)

    if misc_type:
        filter_params['misc_detail__misc_type'] = misc_type

    misc_details = list(MiscDetailOptions.objects.filter(**filter_params).values(
        'misc_key', 'misc_value', 'json_data', 'id', 'misc_detail__misc_type'
    ))
    misc_detail_options = {}
    stock_selection_strategy = []
    for misc in misc_details:
        #Frame Stock Selection Strategy
        if misc['misc_detail__misc_type'] == 'stock_selection_strategy':
            stock_selection_strategy.append({
                'id': misc['id'],
                'misc_key': misc['misc_key'],
                'misc_value': ast.literal_eval(misc['misc_value'])         
            })
        else: #Frame Misc Detail Options
            misc_detail_options.setdefault(misc['misc_key'], [])
            misc_detail_options[misc['misc_key']].append(misc['misc_value'])

    config_dict.update(misc_detail_options)
    if misc_type == 'stock_selection_strategy':
        config_dict['stock_selection_strategy'] = stock_selection_strategy

    return config_dict

class MiscOptions(WMSListView):

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data_validation()
        self.misc_type = self.request.GET.get('misc_type', '')
        self.misc_value = self.request.GET.get('misc_value', '')
        self.misc_key = self.request.GET.get('misc_key', '')
        self.misc_status = self.request.GET.get('status', 1)
        self.misc_id = self.request.GET.get('id', '')

        misc_filters = {
            'misc_detail__user' : self.user.id,
            'misc_detail__misc_value' : 'true', 
            'status' : 1}
        if 'status' in self.request.GET:
            misc_filters.update(status = self.misc_status)

        if self.misc_type:
            misc_filters['misc_detail__misc_type'] = self.misc_type
        
        if self.misc_value:
            misc_filters['misc_value'] = self.misc_value
        if self.misc_key:
            misc_filters['misc_key'] = self.misc_key
        if self.misc_id:
            misc_filters['id'] = self.misc_id

        misc_detail_list = list(MiscDetailOptions.objects.filter(**misc_filters)
                                        .values('id','misc_key', 'status', 'misc_value', 'json_data', misc_type =F('misc_detail__misc_type')))
        misc_detail_options = {}
        for each_row in misc_detail_list:
            json_data = each_row.get('json_data', {}) or {}
            if 'default' in json_data:
                each_row['default'] = json_data['default']
            misc_detail_options[each_row['misc_key']] = each_row

            #Modifying the response in case of Dashboards
            if each_row['misc_type'] in ['operational_dashboards', 'executive_dashboards']:
                 each_row['dashboard_url'] = signed_public_dashboard(
                     self.warehouse.id, each_row['misc_value'], send_params=False
                    )

        return JsonResponse(misc_detail_options, status=200)
    
    def remove_checklist_config(self, checklist_name):
        misc_options = MiscDetailOptions.objects.filter(misc_detail__user=self.user.id, misc_value=checklist_name)
        if misc_options.exists():
            misc_options.delete()
            return "", "Updated Successfully"
        else:
            return "No Config Found", ""

    def validate_supplier_csv_mapping(self, misc_type, misc_options, misc_detail_options_keys):
        if misc_type == 'supplier_csv_mapping':
            if misc_options.filter(misc_key__in=misc_detail_options_keys, status = True).exists():
                    return 'Supplier CSV Mapping already exists!'
        return ''
    
    def validate_invoice_group_priority(self, misc_type, misc_options, misc_detail_options):
        error_message = ''
        invoice_groups = list(MiscDetail.objects.filter(user=self.user.id, misc_type='sku_invoice_groups').values_list('misc_value', flat=True))
        if invoice_groups:
            invoice_groups = invoice_groups[0].split(',')
        else:
            return 'Invoice Groups not found!'
        self.priorities = set()
        for key, value in misc_detail_options.items():
            if key not in invoice_groups:
                error_message = 'Invalid Invoice Group'
                break
            error_message = self.validate_priorities(key, value)
            if error_message:
                break
            
        return error_message
    
    def validate_priorities(self, key, value):
        """
        Validates the priority value for a given key.
        Args:
            key (str): The key for which the priority is being validated.
            value (dict): A dictionary containing the priority value with the key 'misc_value'.
        Returns:
            str: An error message if the validation fails, otherwise an empty string.
        """
        
        error_message = ''
        misc_value = value.get('misc_value', '') or ''
        if not misc_value:
            error_message = f'Priority is mandatory for {key}'
        elif not misc_value.isdigit():
            error_message = f'Priority should be a number for {key}'
        elif misc_value in self.priorities:
            error_message = f'Priority {misc_value} is already assigned to another key'
        else:
            self.priorities.add(misc_value)

        return error_message
        
    
    def validate_price_application_priority(self, misc_detail_options):
        """
        Validates the price application priority based on the provided miscellaneous detail options.
        Args:
            misc_detail_options (dict): A dictionary containing miscellaneous detail options where
                                        keys are the option names and values are the option values.
        Returns:
            str: An error message if validation fails, otherwise an empty string.
        """

        error_message = ''
        self.priorities = set()
        for key, value in misc_detail_options.items():
            if key not in ['customer_master', 'order_type_configuration']:
                error_message = 'Invalid price application priority key'
                break
            error_message = self.validate_priorities(key, value)
            if error_message:
                break
        
        return error_message
    

    def post(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data_validation()
        request_data = self.request.POST
        if not request_data:
            request_data = json.loads(self.request.body)
        message = "Misc Options successfully created!"
        misc_type = request_data.get('misc_type', '')
        misc_detail_options = request_data.get('misc_detail_options', {})
        usage = request_data.get('usage', '')

        action = request_data.get('action', '')
        checklist_name = request_data.get('checklist_name', '')
        if usage == "checklist" and action == "remove_config" and checklist_name:
            error, message = self.remove_checklist_config(checklist_name)
            if error:
                return JsonResponse({'message': error}, status=400)
            else:
                return JsonResponse({'message': message}, status=200)

        if not misc_type:
            return JsonResponse({'message': 'Misc type is mandatory!'}, status=400)

        #get MiscDetail changes
        misc_detail = MiscDetail.objects.filter(user=self.user.id, misc_type=misc_type)
        try:
            if not misc_detail.exists():
                misc_detail_obj = MiscDetail.objects.create(user=self.user.id,misc_type=misc_type,misc_value='true', account=self.user.userprofile)
            else:
                misc_detail_obj = misc_detail[0]

            #update misc detail options
            misc_options = MiscDetailOptions.objects.filter(misc_detail__user=self.user.id, misc_detail__misc_type=misc_type, misc_detail_id=misc_detail_obj.id)
            misc_detail_options_keys, misc_options_list = misc_detail_options.keys(), []
            error_message = self.validate_supplier_csv_mapping(misc_type, misc_options, misc_detail_options_keys)
            error_message = ('', self.validate_invoice_group_priority(misc_type, misc_options, misc_detail_options))[misc_type=='invoice_group_priority']
            error_message = ('', self.validate_price_application_priority(misc_detail_options))[misc_type=='price_application_priority']
            if error_message:
                return JsonResponse({"message": error_message}, status=400)
            if misc_type == 'supplier_csv_mapping':
                message = "Supplier Invoice Mapping created successfully"
                misc_options = misc_options.filter(status = True, misc_key__in=misc_detail_options_keys)
            for misc_obj in misc_options:
                misc_key = misc_obj.misc_key
                
                misc_dict = misc_detail_options.get(misc_key) or {}
                misc_value = misc_dict.get('misc_value')
                misc_json = misc_dict.get('misc_json')
                misc_options_list.append(misc_key)
                if usage == "checklist" and (misc_key.split('__')[0] != misc_value):
                    continue
                elif usage != "checklist" and misc_key not in misc_detail_options_keys:
                    misc_obj.status = 0
                else:
                    if misc_value != misc_obj.misc_value:
                        misc_obj.misc_value = misc_value
                    if misc_json != misc_obj.json_data:
                        misc_obj.json_data = misc_json
                    misc_obj.status = 1
            MiscDetailOptions.objects.bulk_update_with_rounding(misc_options, ['misc_value', 'status', 'json_data'])

            #new misc options list
            new_misc_options_list = []
            new_list = list(set(misc_detail_options.keys()) - set(misc_options_list))
            for new_key in new_list:
                misc_dict = misc_detail_options.get(new_key)
                misc_value = misc_dict.get('misc_value')
                misc_json = misc_dict.get('misc_json')
                new_misc_options_list.append(
                    MiscDetailOptions(
                        misc_detail_id = misc_detail_obj.id,
                        misc_key=new_key, misc_value=misc_value,
                        json_data = misc_json,
                        account_id=self.user.userprofile.id
                        ))
            if new_misc_options_list:
                MiscDetailOptions.objects.bulk_create_with_rounding(new_misc_options_list)

            if misc_detail_options:
                misc_detail_obj.misc_value = 'true'
            else:
                misc_detail_obj.misc_value = 'false'
            misc_detail_obj.save()
            return JsonResponse({"message": message}, status=200)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Issue for misc options creation' + str(self.request) + 'error is' + str(e))
            return JsonResponse({"message": exception_message}, status=400)

    def put(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data_validation()
        request_data = json.loads(self.request.body)

        misc_type = request_data.get('misc_type', '')
        misc_option_id = request_data.get('id', '')
        misc_detail_options = request_data.get('misc_detail_options', {})
        error = ''
        message = "Misc Options successfully updated!"
        if misc_type == 'supplier_csv_mapping':
            message = "Supplier Invoice Mapping updated successfully"

        if not misc_type:
            return JsonResponse({'message': 'Misc type is mandatory!'}, status=400)

        # Get MiscDetail changes
        misc_detail = MiscDetail.objects.filter(user=self.user.id, misc_type=misc_type).values('id', 'miscdetailoptions__status', 'miscdetailoptions__misc_key')
        if not misc_detail:
            return JsonResponse({'message': 'No record exists to update!'}, status=400)
        
        available_misc_status = defaultdict(list)
        for misc in misc_detail:
            available_misc_status[misc['miscdetailoptions__misc_key']].append(misc['miscdetailoptions__status'])

        try:
            # Update misc detail options
            misc_detail_obj = misc_detail[0].get('id')
            filter_dict = {'misc_detail__user': self.user.id, 'misc_detail__misc_type': misc_type, 'misc_detail_id':misc_detail_obj}
            if misc_option_id:
                filter_dict['id'] = misc_option_id
            misc_options = MiscDetailOptions.objects.filter(**filter_dict)
            misc_detail_options_keys = misc_detail_options.keys()
            misc_update_options = []
            for misc_obj in misc_options:
                misc_key = misc_obj.misc_key

                if misc_key not in misc_detail_options_keys:
                    continue

                misc_dict = misc_detail_options.get(misc_key) or {}
                misc_value = misc_dict.get('misc_value')
                misc_json = misc_dict.get('misc_json')
                status = misc_dict.get('status')
                if available_misc_status and status and status in available_misc_status.get(misc_obj.misc_key) and misc_obj.status != status:
                    error = "Cannot update status because another record with same supplier is already active."
                    break
                if misc_value != misc_obj.misc_value:
                    misc_obj.misc_value = misc_value
                if misc_json != misc_obj.json_data:
                    misc_obj.json_data = misc_json
                if misc_obj.status != status and status in [0, 1]:
                    misc_obj.status = status
                misc_update_options.append(misc_obj)
            MiscDetailOptions.objects.bulk_update_with_rounding(misc_update_options, ['misc_value', 'status', 'json_data'])
            if error:
                return JsonResponse({"message": error}, status=400)
            return JsonResponse({"message": message}, status=200)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Issue for misc options update' + str(self.request) + 'error is' + str(e))
            return JsonResponse({"message": 'An error occurred while updating the record.'}, status=400)

    def request_data_validation(self):
        if self.warehouse:
            self.user = self.warehouse
        else:
            self.error_message.append({'error':'Invalid Warehouse ID'})

@get_warehouse
def validate_availble_misc_data(request, warehouse: User):
    misc_type = request.GET.get('misc_type', '')
    misc_key = request.GET.get('misc_key', '')
    misc_data = MiscDetail.objects.filter(user=warehouse.id, misc_type=misc_type, miscdetailoptions__misc_key = misc_key, miscdetailoptions__status = 1)
    if misc_data.exists():
        return JsonResponse({'message': 'Active Supplier Invoice Mapping already exists for this supplier'}, status=400)
    return JsonResponse({'message': 'Supplier Invoice Mapping Not Found'}, status=200)

@get_warehouse
def save_order_sku_extra_fields(request, warehouse: User):
    extra_order_sku_fields = request.GET.get('extra_order_sku_fields', '')
    misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type='extra_order_sku_fields')
    try:
        if not misc_detail.exists():
             MiscDetail.objects.create(user=warehouse.id,misc_type='extra_order_sku_fields',misc_value=extra_order_sku_fields, account=warehouse.userprofile)
        else:
            misc_order_option_list = list(MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_detail__misc_type='extra_order_fields').values_list('misc_key',flat=True))
            order_extra_list = extra_order_sku_fields.split(',')
            diff_list = list(set(misc_order_option_list)- set(order_extra_list))
            if len(diff_list) > 0 :
                for key in diff_list :
                    misc_records = MiscDetailOptions.objects.filter(misc_detail__user= warehouse.id, misc_detail__misc_type='extra_order_fields', misc_key = key)
                    for record in misc_records :
                        record.delete()
            misc_detail_obj = misc_detail[0]
            misc_detail_obj.misc_value = extra_order_sku_fields
            misc_detail_obj.save()
    except:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Issue for save order sku extra fields' + str(request))
        return HttpResponse(exception_message)

    return HttpResponse("Saved Successfully")

@get_warehouse
def save_extra_order_options(request, warehouse: User):
    try:
        data_dict = json.loads(request.POST.get('data'))
        field = data_dict.get('field','')
        options_list = data_dict.get('order_field_options')
        message = save_extra_field_options_fun(warehouse,field,options_list)
    except Exception as e:
       import traceback
       log.debug(traceback.format_exc())
       log.info('Issue for save extra order options' + str(request))
       log.info('extra options Insert failed for %s and params are %s and error statement is %s' % (
       str(warehouse), str(request.POST), str(e)))
       return HttpResponse(exception_message)
    return HttpResponse(json.dumps({'message': message}))

def save_extra_field_options_fun(warehouse: User,field,options_list):
    options_string = ",".join(options_list)
    misc_obj = MiscDetail.objects.filter(user=warehouse.id,misc_type='extra_order_fields')
    if misc_obj.exists():
        misc_obj = misc_obj[0]
        misc_options = MiscDetailOptions.objects.filter(misc_detail= misc_obj,misc_key = field)
        if misc_options.exists():
            misc_options =  misc_options[0]
            misc_options.misc_value = options_string
            misc_options.save()
        else:
            MiscDetailOptions.objects.create(misc_detail= misc_obj,misc_key = field,misc_value = options_string)
        message = "Success"
        return message
    else:
        message = "Please Enter Extra Fields"
        return message 

@get_warehouse
def enable_mail_reports(request, warehouse: User):
    data = request.GET.get('data').split(',')
    data_enabled = []
    data_disabled = []
    for d in data:
        if d:
            data_enabled.append(MAIL_REPORTS_DATA[d])

    data_disabled = set(MAIL_REPORTS_DATA.values()) - set(data_enabled)
    for d in data_disabled:
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=d)
        if misc_detail:
            misc_detail[0].misc_value = 'false'
            misc_detail[0].save()
            continue
        data_obj = MiscDetail(user=warehouse.id, misc_type=d, misc_value='false', account = warehouse.userprofile)

    for d in data_enabled:
        misc_detail = MiscDetail.objects.filter(user=warehouse.id, misc_type=d)
        if misc_detail:
            misc_detail[0].misc_value = 'true'
            misc_detail[0].save()
            continue
        data_obj = MiscDetail(user=warehouse.id, misc_type=d, misc_value='true', account = warehouse.userprofile)
        data_obj.save()

    return HttpResponse('Success')

@get_warehouse
def save_config_extra_fields(request, warehouse: User):
    user = warehouse
    field_type = request.GET.get('field_type', '')
    fields = request.GET.get('config_extra_fields', '')
    field_type = field_type.strip('.')
    source = request.GET.get('source', '')
    source = 'APP' if source == 'APP' else ''
    json_data = {}
    field_types = [
        'move_inventory_reasons', 'discrepancy_reasons',
        'inventory_adjustment_reasons', 'picklist_reasons',
        'grn_rejection_reasons', 'lms_integrations',
        'sales_return_reasons', 'sale_order_types',
        'issuance_dept_types', 'selected_cluster_names',
        'selected_order_types', 'print_types',
        'rtv_reasons', 'supplier_payment_terms',
        'job_order_types', 'supplier_mandatory_fields',
        'sku_types', 'return_consumed_quantity_reasons',
        'po_types', 'allow_inter_zone_movement',
        'ba_to_sa_reasons', 'nte_reasons', 'sku_invoice_groups',
        'gate_pass_file_types', 'po_line_extra_fields', 
        'extra_batch_attributes', 'gatepass_return_reasons',
        'asn_return_reasons', 'vertical_groups',
        'customer_mandatory_fields',
        'nte_selected_source_zones',
        'nte_selected_destination_zones',
        'expired_selected_source_zones',
        'expired_selected_destination_zones',
    ]
    sub_user_warehouse_field_types = ['selected_order_types', 'selected_cluster_names']

    if (len(fields.split(',')) <= 4 or field_type in field_types) and (not source or (source == 'APP' and field_type in ['selected_order_types', 'selected_cluster_names'])):
        if source == 'APP':
            user = request.user

        account_id = user.userprofile.id
        if field_type in sub_user_warehouse_field_types:
            account_id = warehouse.userprofile.id

        misc_detail = MiscDetail.objects.filter(user=user.id, misc_type=field_type, account_id=account_id)
        try:
            if not misc_detail.exists():
                MiscDetail.objects.create(user=user.id, misc_type=field_type, misc_value=fields, account_id=account_id)
            else:
                misc_detail_obj = misc_detail[0]
                misc_detail_obj.misc_value = fields
                misc_detail_obj.account_id = account_id
                misc_detail_obj.save()
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Issue for {} withe exception {}'.format(request.GET.dict(), str(e)))
            message = exception_message
            json_data['status'] = 400
            json_data['message'] = message
            return JsonResponse(json_data)
    else:
        message = "Limit Exceeded Enter only Four Fields"
        if source:
            json_data['status'] = 400
            json_data['message'] = message
            return JsonResponse(json_data)
        else:
            return HttpResponse(message)

    message = "Saved Successfully"
    if source:
        json_data['status'] = 200
        json_data['message'] = message
        return JsonResponse(json_data)
    return HttpResponse(message)

class ConfigFields(WMSListView):

    def get(self, request, *args, **kwargs):
        self.set_user_credientials()
        user = self.warehouse
        data = json.loads(request.body)
        filter_params = {'user': user.id}
        if data.get('id'):
            filter_params['id'] = data.get('id')
        misc_objects = dict(MiscDetail.objects.filter(**filter_params).\
            values_list('misc_type', 'misc_value'))
        
        return JsonResponse({"message": misc_objects}, status=200)

    def post(self, request, *args, **kwargs):
        self.set_user_credientials()
        payload = request.body
        data = json.loads(payload)
        account_id = self.warehouse.userprofile.id

        #getting data
        field_type = data.get("field_key", '')
        field_value = data.get("field_value", '')

        #checking for user config
        misc_detail = MiscDetail.objects.filter(user=self.warehouse.id, misc_type=field_type)
        if not misc_detail.exists():
            #creating new config
            misc_detail_dict = {
                "user": self.warehouse.id,
                "misc_type": field_type,
                "misc_value": field_value,
                "account_id": account_id
            }
            misc_obj = MiscDetail.objects.create(**misc_detail_dict)
            misc_id = misc_obj.id
        else:
            #updating config
            misc_detail_obj = misc_detail[0]
            misc_detail_obj.misc_value = field_value
            misc_detail_obj.account_id = account_id
            misc_detail_obj.save()
            misc_id = misc_detail_obj.id
        
        self.update_cache_with_configurations(account_id, field_type, field_value, misc_id)

        message = "Updated Successfully"
        return JsonResponse({"message": message}, status=200)
    
    def update_cache_with_configurations(self, account_id, field_type, field_value, misc_id):
        field_value = field_value.split(',')
        result_dict = {key: misc_id for key in field_value if key}

        redis_wrapper = RedisJSONWrapper()
        '''
        Saving in Cache in this format : {account_id : {configurations : {misc_key: misc_value}}}
        '''
        # Get the current configuration data from the cache (if it exists)
        user_config = redis_wrapper.get(account_id) or {}

        # Use setdefault to either create or update the 'decimal_limit' key
        user_config['configurations'] = user_config.get('configurations', {})
        user_config['configurations'][field_type] = result_dict

        # Set the updated data back in the cache
        redis_wrapper.set(account_id, user_config)

@get_warehouse
def save_grn_weighing_options(request, warehouse: User):
    """
    Save and Update GRN weighing options for a warehouse.
    """
    request_data = json.loads(request.body)
    warehouse_id = warehouse.id
    misc_detail = MiscDetail.objects.filter(misc_type='enable_weighing', user=warehouse_id)

    if not misc_detail.exists():
        return JsonResponse({"message": "Weighing is not enabled for this warehouse"}, status=400)

    # Check if the request is for deleting an entry
    if not isinstance(request_data, list) and request_data.get('id'):
        misc_detail_options = MiscDetailOptions.objects.filter(misc_detail_id=misc_detail[0].id, id=request_data['id'])
        if not misc_detail_options.exists():
            return JsonResponse({"message": "Invalid ID"}, status=400)
        misc_detail_options = misc_detail_options[0]
        misc_detail_options.status = 0
        misc_detail_options.save()
        return JsonResponse({"message": "Success"}, status=200)

    misc_detail_id = misc_detail[0].id
    bulk_updates = []
    bulk_creates = []
    warehouse_profile = warehouse.userprofile
    # Iterate through each data and update/create the entry
    for each_data in request_data:
        json_data = {'weighing_scale': each_data['weighing_scale']}
        misc_detail_options = MiscDetailOptions.objects.filter(misc_detail_id=misc_detail_id, misc_key=each_data['misc_key'])

        if misc_detail_options.exists():
            misc_obj = misc_detail_options[0]
            # Update existing entry
            misc_obj.misc_value = each_data['misc_value']
            misc_obj.status = 1
            misc_obj.json_data = json_data
            bulk_updates.append(misc_obj)
        else:
            # Create a new entry
            new_entry = MiscDetailOptions(
                misc_detail_id=misc_detail_id,
                misc_key=each_data['misc_key'],
                misc_value=each_data['misc_value'],
                json_data=json_data,
                status = 1,
                account=warehouse_profile
            )
            bulk_creates.append(new_entry)

    # Perform bulk update
    if bulk_updates:
        MiscDetailOptions.objects.bulk_update(bulk_updates, ['misc_value','status', 'json_data'])

    # Perform bulk create
    if bulk_creates:
        MiscDetailOptions.objects.bulk_create(bulk_creates)

    return JsonResponse({"message": "Success"}, status=200)


class SlaConfig(WMSListView):

    def post(self, *args, **kwargs):
        self.set_user_credientials()
        try:
            request_data = json.loads(self.request.body)
        except Exception:
            return JsonResponse({"message": "Invalid JSON"}, status=400)
        misc_type = request_data.get("misc_type", '')
        request_data = self.sort_request_data(request_data.get('items', []))
        for sla_config in request_data:
            misc_key = sla_config.get('color', '')
            name = sla_config.get('name', '')
            value = sla_config.get('time', '')
            validation_sts = sla_detail_validation(self.warehouse, misc_key, value, name)
            if validation_sts:
                return JsonResponse({'status':400,'errors':[validation_sts]},status=400)
            existing_block_data = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type': misc_type,'misc_key': misc_key, 'misc_detail__user': self.warehouse.id, 'status': 1})
            if existing_block_data.exists():
                # update already existing entry.
                existing_block_data.update(misc_value=value, json_data={'name':name})
                continue

            misc_det, created= MiscDetail.objects.get_or_create(user=self.warehouse.id, misc_type=misc_type, misc_value='true', account=self.warehouse.userprofile)
            MiscDetailOptions.objects.create(misc_detail=misc_det, misc_key=misc_key, misc_value=value, status=1, json_data={'name':name}, account=self.warehouse.userprofile)
        return JsonResponse({'status':200,'message':'Success'},status=200)


    def sort_request_data(self, request_data):
        '''
        Sort the request data based on color
        '''
        color_order = {"Red": 0, "Yellow": 1, "Green": 2}
        request_data = sorted(request_data, key=lambda x: color_order[x['color']])
        return request_data

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        sla_data, _, _ = get_sla_misc_details(self.warehouse, api=True)
        data = []
        for each_sla in sla_data:
            json_data = {}
            if each_sla['json_data']:
                json_data=each_sla['json_data']
            sla_dict = {'id': each_sla['id'], 'color':each_sla['misc_key'],'time':each_sla['misc_value'],'name':json_data.get('name')}
            data.append(sla_dict)
        return JsonResponse({'data':data}, status=200)

    def delete(self, *args, **kwargs):
        self.set_user_credientials()
        sla_config_id = self.request.GET.get('id', '')
        sla_config = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type':'sla', 'misc_detail__user':self.warehouse.id,'status':1, 'id':sla_config_id})
        if not sla_config.exists():
            return JsonResponse({'status':400,'errors':['Invalid ID']},status=400)
        if sla_config[0].misc_key == 'Yellow':
            red_entry = filter_or_none(MiscDetailOptions,{'misc_detail__misc_type':'sla','misc_key':'Red','misc_detail__user':self.warehouse.id,'status':1})
            if red_entry.exists():
                return JsonResponse({'status':400,'errors':['Please delete Red Entry and try again']},status=400)
        delete_sla_entries(self.warehouse, 'sla', sla_config[0].misc_key)
        return JsonResponse({'status':200,'message':'Success'},status=200)

@get_warehouse
def putaway_config(request, warehouse: User):
    request_data = json.loads(request.body)

    misc_key = request_data.get('misc_key')
    misc_value = request_data.get('misc_value')

    if misc_key not in ['restrict_sku_to_one_location', 'restrict_location_to_one_sku']:
        return JsonResponse({"message": 'Invalid Options'}, status=400)

    warehouse_id = warehouse.id
    misc_obj = \
            MiscDetail.objects.filter(user = warehouse_id, misc_type=misc_key)

    putaway_mappings = PutawayMapping.objects.filter(warehouse = warehouse_id, status=1, strategy='fixed_bin_mapping')
    error_message = ''
    if misc_key == 'restrict_sku_to_one_location':
        multiple_mappings = putaway_mappings.values('entity_name').annotate(num_locations=Count('location')).filter(num_locations__gt=1)
        error_message = "Found SKU with Multiple Location Mappings"

    elif misc_key == 'restrict_location_to_one_sku':
        multiple_mappings = putaway_mappings.values('location').annotate(num_skus=Count('entity_name')).filter(num_skus__gt=1)
        error_message = "Found Location with Multiple SKU Mappings"

    if misc_value and multiple_mappings.exists():
        return JsonResponse({"message": error_message}, status=400)

    if misc_obj.exists():
        misc_obj.update(misc_value=misc_value)
    else:
        MiscDetail.objects.create(
            user = warehouse_id,
            misc_type=misc_key,
            misc_value = misc_value,
            account_id = warehouse.userprofile.id
            )
    return JsonResponse({"message": "Success"}, status=200)

@get_warehouse
def get_supplier_csv_mapping_details(request, warehouse:User):
    """Get all active supplie csv mapping list"""
    supplier_ids = list(MiscDetailOptions.objects.filter(
        misc_detail__user=warehouse.id, misc_detail__misc_type='supplier_csv_mapping',
        status=1
    ).values_list('misc_key', flat=True))

    supplier_data = []
    if supplier_ids:
        supplier_data = SupplierMaster.objects.filter(supplier_id__in=supplier_ids, user=warehouse.id).values('supplier_id', 'name')

    data_list = []
    for each_data in supplier_data:
        data_list.append({'label': str(each_data['supplier_id']) + ' : ' + str(each_data['name']), 'value': str(each_data['supplier_id'])})

    return JsonResponse({'data': data_list}, status=200)

class IncrementalConfig(WMSListView):
    '''
    Incremental configutation for warehouse
    '''
    
    def validate_post_request_data(self):
        '''
        Validatet the post request data
        '''
        ref_sub_type_dict = {
            'order_type': self.wh_order_types
        }
        validate_data = lambda i, data: [
            errors.append('Invalid Reference Type') if data.get('reference_type', '') not in ['invoice', 'delivery_challan'] else None,
            errors.append('Invalid Reference Sub Type') if data.get('reference_sub_type','') not in ['order_type'] else None,
            errors.append('Reference Value is mandatory') if not data.get('reference_value','') else None, # errors.append('Invalid Reference Value') if not (data.get('reference_value','').lower() == 'all' or data.get('reference_value') in ref_sub_type_dict.get(data.get('reference_sub_type'),[])) else None,
            errors.append('Invalid Status') if data.get('status') not in [True, False] else None,
            self.error_message.update({i: errors}) if errors else None
        ]
        for i, data in enumerate(self.request_data):
            errors = []
            validate_data(i, data)
            max_length = data.get('max_length', 15)
            max_prefix_length = sum(len(data.get(key, '')) for key in ['prefix', 'suffix']) + 2* len(data.get('delimiter', '')) + 5
            max_prefix_length += 5 if data.get('date_type') else 0
            if max_length < 10 or  max_length < max_prefix_length:
                errors.append('Invalid Max Length')
                self.error_message.update({i: errors})
        
    def get_inc_table_record_check(self, prefix, reference_type):
        '''
        Get the incremental table record
        '''
        check_value = False
        if (self.inc_table_df.empty or self.inc_table_df[(self.inc_table_df['type_name'] == f'{reference_type}_{prefix}') & (self.inc_table_df['prefix'] == prefix)].empty):
            check_value = True
        return check_value
    
    def create_or_update_inc_master_data(self,data):
        
        inc_master_id = data.get('id',0)
        prefix = data.get('prefix','')
        reference_type = data.get('reference_type','')
        reference_sub_type = data.get('reference_sub_type','')
        reference_value = data.get('reference_value','')
        suffix = data.get('suffix', '')
        delimiter = data.get('delimiter', '')
        date_type = data.get('date_type', '')
        max_length = data.get('max_length', 15)
        if date_type.isdigit():
            date_type = int(date_type)
        else:
            date_type = None
        status = data.get('status', True)
        
        inc_tbl_check = self.get_inc_table_record_check(prefix, reference_type)
        inc_record = pd.DataFrame()
        if inc_master_id:
            inc_record =  self.inc_master_df[self.inc_master_df['id'] == inc_master_id]      
        elif not self.inc_master_df.empty:
            inc_record = self.inc_master_df[(self.inc_master_df['reference_type'] == reference_type) & (self.inc_master_df['reference_sub_type'] == reference_sub_type) & (self.inc_master_df['reference_value'] == reference_value)]
        if inc_record.empty:
            if inc_tbl_check:
                inc_master_obj = IncrementalSegregationMaster(
                    account_id = self.warehouse.userprofile.id,
                    warehouse = self.warehouse,
                    reference_type = reference_type,
                    reference_sub_type = reference_sub_type,
                    reference_value = reference_value,
                    status = status
                )
                log.info(F"Incremental Seggregation Master Creation with reference_type: {reference_type}, reference_sub_type: {reference_sub_type}, reference_value: {reference_value}")
                log.info(F"Incremental Table Creation of {reference_type}_{prefix} with prefix: {prefix}, suffix: {suffix}, delimiter: {delimiter}, date_type: {date_type}")
                self.new_inc_master_objs[(reference_type,prefix,suffix,delimiter,date_type,max_length)].append(inc_master_obj)
            else:
                inc_table = self.inc_table_df[(self.inc_table_df['type_name'] == f'{reference_type}_{prefix}')&(self.inc_table_df['prefix'] == prefix)]
                inc_table_obj = inc_table['inc_table_obj'].iloc[0]
                update_check = inc_table[(inc_table['suffix'] == suffix) & (inc_table['delimiter'] == delimiter) & (inc_table['date_type'] == date_type) & (inc_table['max_length'] == max_length)]
                if update_check.empty:
                    log.info(F"Incremental Table Updation of {reference_type}_{prefix} with prefix: {prefix}, suffix: {suffix}, delimiter: {delimiter}, date_type: {date_type}")
                    self.update_inc_table[(reference_type,prefix,suffix,delimiter,date_type,max_length)] = inc_table_obj
                inc_obj = IncrementalSegregationMaster(
                    account_id = self.warehouse.userprofile.id,
                    warehouse = self.warehouse,
                    reference_type = reference_type,
                    incremental_record = inc_table_obj,
                    reference_sub_type = reference_sub_type,
                    reference_value = reference_value,
                    status = status
                )
                log.info(F"Incremental Seggregation Master Creation with reference_type: {reference_type}, reference_sub_type: {reference_sub_type}, reference_value: {reference_value}")
                self.new_inc_objs.append(inc_obj)
        else:
            status_check = False
            unq_key = (reference_type, reference_sub_type, reference_value)
            if unq_key in self.update_inc_master_objs and not status:
                return
            inc_master_obj = inc_record.iloc[0]['inc_master_obj']
            if status != inc_master_obj.status:
                inc_master_obj.status = status
                status_check = True
            if not inc_tbl_check:
                prefix_id = inc_record.iloc[0]['incremental_record_id']
                prefix_check = self.inc_table_df[(self.inc_table_df['id'] == prefix_id)&(self.inc_table_df['prefix'] == prefix)]    
                if prefix_check.empty:
                    inc_table_record = self.inc_table_df[(self.inc_table_df['type_name'] == f'{reference_type}_{prefix}')&(self.inc_table_df['prefix'] == prefix)]
                    inc_table_obj = inc_table_record['inc_table_obj'].iloc[0]
                    inc_master_obj.incremental_record = inc_table_obj
                else:
                    update_check = prefix_check[(prefix_check['suffix'] == suffix) & (prefix_check['delimiter'] == delimiter) & (prefix_check['date_type'] == date_type) & (prefix_check['max_length'] == max_length)]
                    if update_check.empty:
                        log.info(F"Incremental Table Updation of {reference_type}_{prefix} with prefix: {prefix}, suffix: {suffix}, delimiter: {delimiter}, date_type: {date_type}")
                        self.update_inc_table[(reference_type,prefix,suffix,delimiter,date_type,max_length)] = prefix_check['inc_table_obj'].iloc[0]
                if status_check or prefix_check.empty:
                    log.info(F"Incremental Seggregation Master Updation with reference_type: {reference_type}, reference_sub_type: {reference_sub_type}, reference_value: {reference_value}, status: {status}")
                    self.update_inc_master_objs[unq_key] = inc_master_obj
            else:
                self.update_new_inc_tbl[(reference_type,prefix,suffix,delimiter,date_type,max_length)].append(inc_master_obj)
                    
    def prepare_df_list(self, table_data, obj_column):
        obj_column = obj_column or 'obj'
        temp_list = []
        for obj in table_data:
            obj_dict = obj.__dict__.copy()
            obj_dict.pop('_state')
            obj_dict[obj_column] = obj
            temp_list.append(obj_dict)       
        return temp_list  
    
    def update_incremental_table_objs(self, data):
        for key, obj in data.items():
            ref_type,prefix,suffix,delimiter,date_type,max_length = key
            obj.suffix = suffix
            obj.delimiter = delimiter
            obj.date_type = date_type
            obj.max_length = max_length
            self.incremental_tables_to_update.append(obj)
    
    def prepare_incremental_table_objs(self, objects_dict):
        for key, objs in objects_dict.items():
            ref_type,prefix,suffix,delimiter,date_type,max_length  = key
            inc_table_obj = IncrementalTable(
                account_id = self.warehouse.userprofile.id,
                user = self.warehouse,
                prefix=prefix,
                type_name=f'{ref_type}_{prefix}',
                value = 0,
                suffix = suffix,
                delimiter = delimiter,
                max_length = max_length,
                date_type = date_type   
            )
            self.incremental_tables_to_create.append(inc_table_obj)
            self.incremental_table_mapping[key] = inc_table_obj
             
                
    def post(self, *args, **kwargs):
        '''
        Save the incremental configuration for warehouse
        '''
        try:
            self.request_data = json.loads(self.request.body)
        except Exception:
            return JsonResponse({"message": "Invalid JSON"}, status=400)
        
        self.set_user_credientials()
        self.error_message = defaultdict(list)
        
        misc_types = ['sale_order_types', 'decimal_limit']
        self.misc_values_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        self.wh_order_types = self.misc_values_dict.get('sale_order_types','').split(',')
        self.new_inc_master_objs, self.update_new_inc_tbl, self.update_inc_master_objs, self.new_inc_objs, self.update_inc_table = defaultdict(list), defaultdict(list), {}, [], {}
        self.decimal_limit = self.misc_values_dict.get('decimal_limit',0)
        if self.decimal_limit and isinstance(self.decimal_limit, str) and self.decimal_limit.isdigit():
            self.decimal_limit = int(self.decimal_limit)
        
        self.validate_post_request_data()
        if self.error_message:
            return JsonResponse({'message': self.error_message}, status=400)
        
        
        values = ['id', 'reference_type', 'reference_sub_type', 'reference_value', 'status', 'incremental_record__id']
        inc_master_data = IncrementalSegregationMaster.objects.filter(warehouse_id = self.warehouse.id).select_related('incremental_record').only(*values)
        inc_table = IncrementalTable.objects.filter(user = self.warehouse.id).only('id', 'prefix', 'type_name', 'suffix', 'date_type', 'delimiter', 'max_length')
        inc_master_data_list, inc_table_data_list = [], []
        inc_master_data_list = self.prepare_df_list(inc_master_data, 'inc_master_obj')
        inc_table_data_list = self.prepare_df_list(inc_table, 'inc_table_obj')
        
  
        self.inc_table_df = pd.DataFrame(inc_table_data_list, )
        self.inc_master_df = pd.DataFrame(inc_master_data_list)
    
        try:
            for data in self.request_data:
                self.create_or_update_inc_master_data(data)
            
            if self.update_inc_master_objs:
                IncrementalSegregationMaster.objects.bulk_update_with_rounding(list(self.update_inc_master_objs.values()), ['incremental_record','reference_value', 'status'], decimal_places=self.decimal_limit)
                
            if self.new_inc_objs:
                IncrementalSegregationMaster.objects.bulk_create_with_rounding(self.new_inc_objs)
            
            self.incremental_tables_to_create, self.incremental_tables_to_update = [], []
            self.incremental_table_mapping = {}
            
            self.prepare_incremental_table_objs(self.new_inc_master_objs)
            self.prepare_incremental_table_objs(self.update_new_inc_tbl)
            self.update_incremental_table_objs(self.update_inc_table)
            
            if self.incremental_tables_to_update:
                IncrementalTable.objects.bulk_update_with_rounding(self.incremental_tables_to_update, ['suffix', 'delimiter', 'date_type', 'max_length'], decimal_places=self.decimal_limit)
                
            if self.incremental_tables_to_create:
                IncrementalTable.objects.bulk_create_with_rounding(self.incremental_tables_to_create)
            
            new_inc_master, update_inc_tbl = [], []
            for key, objs in self.new_inc_master_objs.items():
                for obj in objs:
                    obj.incremental_record = self.incremental_table_mapping[key]
                    new_inc_master.append(obj)
            for key, objs in self.update_new_inc_tbl.items():
                for obj in objs:
                    obj.incremental_record = self.incremental_table_mapping[key]
                    update_inc_tbl.append(obj)
            if self.new_inc_master_objs:
                IncrementalSegregationMaster.objects.bulk_create_with_rounding(new_inc_master)
            
            if self.update_new_inc_tbl:
                IncrementalSegregationMaster.objects.bulk_update_with_rounding(update_inc_tbl, ['incremental_record', 'reference_value', 'status'], decimal_places=self.decimal_limit)
            
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Issue for incremental configuration' + str(self.request) + 'error is' + str(e))
            return JsonResponse({"message": "incremental config save was not successful"}, status=400)
        
        return JsonResponse({"message": "Success"}, status=200)
       
    def get(self, *args, **kwargs):
        '''
        Get the incremental configuration for warehouse
        '''
        self.set_user_credientials()
        values = ['id', 'reference_type', 'reference_sub_type', 'reference_value', 'status']
        values_dict = {
            'prefix': F('incremental_record__prefix'),
            'suffix': F('incremental_record__suffix'),
            'delimiter': F('incremental_record__delimiter'),
            'date_type': F('incremental_record__date_type'),
            'max_length': F('incremental_record__max_length')
        }
        inc_master_data = list(IncrementalSegregationMaster.objects.filter(warehouse = self.warehouse.id, status = 1).values(*values, **values_dict))
        for data in inc_master_data:
            if not data.get('date_type'):
                data['date_type'] = ''
            else:
                data['date_type'] = str(data['date_type'])
        return JsonResponse({"data": inc_master_data}, status=200)
