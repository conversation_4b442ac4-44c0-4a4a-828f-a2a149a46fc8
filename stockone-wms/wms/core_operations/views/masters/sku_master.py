#package imports
from collections import OrderedDict, defaultdict
import copy
import datetime
from datetime import date, timedelta
from itertools import chain
import json
import operator
from dateutil import parser
from django.utils import timezone

#django imports
from django.db.models import Q
from django.db import DataError
from django.http import HttpResponse, JsonResponse
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models.fields import <PERSON>r<PERSON><PERSON>
from django.db.models.functions import Cast
from django.core.cache import cache

#wms imports
from wms_base.wms_utils import (LOAD_UNIT_HANDLE_DICT,
                                SKU_DATA, SKU_HEADERS,
                                SKU_MASTER_API_MAPPING, SUB_CATEGORIES,
                                get_permission)
from wms_base.wms_utils import init_logger, folder_check
from wms_base.models import POChoices, User

#core imports
from core.models.masters import (EANNumbers, SKUAttributes, 
                                 SKUFields, SKUGroups, SizeMaster, 
                                 TaxMaster, UOMDetail)
from core.models.common import MasterDocs, AsyncAPIExecution
from core.models import SKUMaster, HSNMaster
from core_operations.views.common.main import (get_warehouse,
    WMSListView, build_search_term_query, check_update_hot_release, filter_or_none,
    frame_datatable_column_filter, get_company_id, get_local_date_known_timezone, 
    get_misc_value, get_multiple_misc_values, get_or_none, get_related_users, 
    get_sister_warehouse, get_user_attributes, get_warehouses_list,
    get_user_time_zone, get_extra_fields_for_upload,
    save_json_data, scroll_data, update_ean_sku_mapping, update_error_message,
    update_sku_attributes, update_volume,
    upload_sku_image, get_sku_master, validate_drop_down_values,
    get_custom_sku_attributes_and_values,
)
from core_operations.views.common.user_attributes import validate_mandatory_and_type_of_attribute_value
from core_operations.views.masters.tax_master import save_tax_master

#inventory imports
from inventory.models.locator import StockDetail, ZoneMaster, SKUPackMaster

#outbound imports
from outbound.models.orders import OrderDetail

#inbound imports
from inbound.models import SupplierMaster

#sync masters imports
from core_operations.views.masters.sync_masters import sync_masters_across_warehouses

#auth imports
from auth.utils import get_warehouse_objects

from wms.celery import app as celery_app

from core.models import (
    UserIntegrationAPIS,
    UserIntegrationCalls
)
from core_operations.views.integration.run_3pintegration import async_run_3p_int_func

##request user storaging middleware
from wms_base.middleware.current_user import CurrentUserMiddleware

# inbound models
from inbound.models import ASNSummary, SellerPOSummary

log = init_logger('logs/sku_master.log')
upload_log =  init_logger('logs/sku_upload.log')

MIX_SKU_MAPPING = {'no mix': 'no_mix', 'mix within group': 'mix_group'}
LOAD_UNIT_HANDLE_DICT = {'enable': 'pallet', 'disable': 'unit'}
SUB_CATEGORIES = OrderedDict((('mens_polo', 'MENS POLO'), ('ladies_polo', 'LADIES POLO'),
                              ('round_neck', 'ROUND NECK'), ('hoodie', 'HOODIE'), ('jackets', 'JACKETS'),
                              ('henley', 'HENLEY'), ('laptop_bags', 'LAPTOP BAGS'),
                              ('gym_bags', 'GYM BAGS'), ('pant', 'PANT'),
                              ('belts', 'BELTS'), ('ear_phone', 'EAR PHONE'),
                              ('v_neck', 'V NECK'), ('polo', 'POLO'), ('chinese_collar', 'CHINESE COLLAR'),
                              ('bags', 'BAGS')
                              ))
SCAN_OPTIONS = {'scannable':1, 'non-scannable':0}
sku_keys_list = [
    'status', 'scan_picking', 'qc_eligible', 'qc_check', 'batch_based', 'dispensing_enabled', 
    'enable_serial_based', 'is_barcode_required', 'pick_and_sort'
]

error_dict = {
    'sku_code': 'Enter a Valid SKU CODE',
    'hsn_code': 'HSN Code is not Mapped to Tax Master',
    'serialized': 'Serialized flag can’t be changed when stock >0',
    'batch_based': 'Batch Based Flag Cannot be Changed when stock >0'

}

CONFIG_VALUES = ['enable', 'disable']
BOOLEAN_CONFIG_VALUES = ['true', 'false']
BLOCK_FOR_PO_CONFIG_VALUES  = ['yes', 'no']
LOCK_EXPIRE = 60

SKU_ERROR_DICT = {
    "1": "Min Norm Quantity Can Not Be Greater Than Max Norm Quantity",
    "2": "Duplicate SKU Code Found in File",
    "3": "SKU Group Does not Exists",
    "4": "Invalid Zone",
    "5": "EAN Number Length Should be less than 20",
    "6": "HSN Code Should be Numeric",
    "7": "Product Type Should Match With Tax Master Product Type",
    "8": "Invalid Scan Status",
    "9": "Invalid Quantity",
    "10":"Quantity Should not be Negative",
    "11":"Invalid Min Norm Quantity",
    "12":"Invalid Max Norm Quantity",
    "13":"Receipt Tolerance Must Be of Type Float",
    "14":"Invalid Price",
    "15":"Invalid option for Mix SKU",
    "16":"Load Unit Handling Should be Either Enable or Disable",
    "17":"Invalid Sub Category",
    "18":"Hot Release Should be Either Enable or Disable",
    "19":"Batch Based Should be Enable or Disable",
    "20":"Dispensing Enabled Should be true or false",
    "21":"Serialized Should be Enable or Disable",
    "22":"Sequence should be Numeric",
    "23":"Block For PO should be Yes/No",
    "24":"GL Code should be Numeric",
    "25":"Customer Shelf Life should be Numeric",
    "26":"Minimum Shelf Life should be Number",
    "27":"Please define UOM in UOM Master",
    "28":"UOM should not be Numeric",
    "29":"Product Shelf Life should be Numeric",
    "30":"QC Required Should be Yes or No",
    "31":"Product Shelf life must be greater than  Customer Shelf life",
    "32":"Customer Shelf life must be greater than  Minimum Shelf life",
    "33":"Min Norm Quantity Should Be greater than Zero",
    "34":"Max Norm Quantity Should Be greater than Zero",
    "35":"Height Value should be numeric",
    "36":"Length Value should be numeric",
    "37":"Breadth Value should be numeric",
    "38":"Weight Value should be numeric",
    "39":"Cost Price Value Should be numeric",
    "40":"MRP Price Value Should be numeric",
    "41":"MRP should be greater than Price",
    "42":"MRP Should not be negative",
    "43":"Price Should not be negative",
    "44":"Barcoding Required Should be Yes or No",
    "45": "Invalid Invoice Group",
    "46": "Invoice Group cannot be updated as the SKU is present in open orders",
    "47": "Pick and Sort Should be Enable or Disable",
    "48": "Mandate Scan Should be Enable or Disable",
}

#constant key values
SELLING_PRICE = 'Selling Price'
PRODUCT_SHELF_LIFE = 'Product Shelf Life'
DISPENSING = 'Dispensing Enabled(Options: true, false)'
BLOCK_FOR_PO = 'Block For PO'
SKU_CODE = 'SKU Code'


def get_sku_results(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse: User, filters):
    """
    Retrieves SKU results based on the provided parameters.

    Args:
        start_index (int): The starting index of the SKU results.
        stop_index (int): The stopping index of the SKU results.
        temp_data (dict): The dictionary to store the temporary data.
        search_term (str): The search term for filtering SKU results.
        order_term (str): The term for ordering SKU results.
        col_num (int): The number of columns.
        request (HttpRequest): The HTTP request object.
        warehouse (User): The warehouse user object.
        filters (dict): Additional filters for SKU results.

    Returns:
        dict: The dictionary containing the SKU results.
    """
    request_data = request.GET
    count = True if request_data.get('count', '') == 'true' else False
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    
    sort_by_column = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "sku_code"
    sort_type = request_data.get('sort_type', 0)
    sku_values = [
        "id", "status", "creation_date", "updation_date", "customer_shelf_life", "minimum_shelf_life",
        "zone__zone", "relation_type", "sku_type", "scan_picking", "json_data", "wms_code", "image_url",
        "block_options", "sku_code", "sku_desc", "sku_group", "sku_category", "sku_class", "sku_brand",
        "style_name", "sku_size", "cost_price", "mrp", "threshold_quantity", "max_norm_quantity", "color",
        "batch_based", "hsn_code", "sub_category", "length", "breadth", "height", "weight", "product_type",
        "shelf_life", "sku_reference", "make_or_buy", "enable_serial_based", "qc_eligible", "measurement_type",
        "dispensing_enabled", "price", "gl_code", "receipt_tolerance", "is_barcode_required", "invoice_group",
        "seller_id", "pick_and_sort", "mandate_scan"
    ]

    sku_master, _ = get_sku_master(warehouse, instance_name=SKUMaster)
    order_data_dict = {"created_by":"json_data__created_by", "updated_by":"json_data__updated_by", "zone":"zone__zone"}
    order_data = ""
    if sort_by_column in order_data_dict:
        order_data = order_data_dict.get(sort_by_column)
    
    elif sort_by_column in sku_values:
        order_data = sort_by_column

    #Framing Order By Params
    if sort_type == '1' and order_data:
        order_data = '-%s' % order_data

    master_data = []
    column_filters_dict = {}
    if column_headers:
        formatted_column_headers = format_sku_column_headers(column_headers)
        column_filters = frame_datatable_column_filter(formatted_column_headers)
        custom_filter_dict = {"zone__icontains":"zone__zone__icontains"}
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            elif key == "dispensing_enabled__icontains":
                if value.lower() == "enable":
                    column_filters_dict["dispensing_enabled"] = 1
                elif value.lower() == "disable":
                    column_filters_dict["dispensing_enabled"] = 0
            else:
                column_filters_dict[key] = value

    sku_master = sku_master.filter(**column_filters_dict)

    if order_data:
        sku_master = sku_master.order_by(order_data)
    if count:
        temp_data['count'] = sku_master.count()
    
    master_data = sku_master[start_index:stop_index].values(*sku_values)

    sku_ids = master_data.values('id')

    ean_numbers = EANNumbers.objects.filter(sku_id__in=sku_ids).values("ean_number","sku")
    sku_ean_dict = {}
    for ean in ean_numbers:
        if ean["sku"] in sku_ean_dict:
            sku_ean_dict[ean["sku"]].append(str(ean["ean_number"]))
        else:
            sku_ean_dict[ean["sku"]] = [str(ean["ean_number"])]
    temp_data = sku_master_results_preparation(master_data, warehouse, sku_ean_dict, temp_data)
    return temp_data

def format_sku_column_headers(column_headers):
    """
    Format the column headers for SKU results.

    Args:
        column_headers (dict): A dictionary containing the column headers.

    Returns:
        dict: The formatted column headers.
    """
    custom_headers = {"created_by": "json_data__created_by",
                      "updated_by": "json_data__updated_by"}
    formatted_column_headers = {}
    for key, value in column_headers.items():
        formatted_column_headers[custom_headers.get(key, key)] = value
    return formatted_column_headers

def get_sku_image_url(sku_image_upload_path, data):
    """
    Get the image URL for a SKU based on the provided data.

    Args:
        sku_image_upload_path (dict): A dictionary containing SKU image upload paths.
        data (dict): A dictionary containing SKU data.

    Returns:
        str: The image URL for the SKU.
    """
    image_url = ''
    if sku_image_upload_path.get(data.get("wms_code")):
        image_url = sku_image_upload_path.get(data.get("wms_code"))
    elif data.get("image_url", ""):
        image_url = data.get("image_url")
    return image_url

def get_sku_log_data(data):
    """
    Get the created_by and updated_by values from the provided data dictionary.

    Args:
        data (dict): The data dictionary containing the json_data.

    Returns:
        tuple: A tuple containing the created_by and updated_by values.
    """
    created_by, updated_by = '', ''
    if data.get("json_data", {}):
        json_dict = data.get("json_data", {}) if data.get("json_data", {}) else {}
        created_by = json_dict.get('created_by', '')
        updated_by = json_dict.get('updated_by', '')
    return created_by, updated_by

def get_sku_image_upload_path(master_data, warehouse):
    """
    Get the image upload path for each SKU in the master data.

    Args:
        master_data (list): List of dictionaries containing SKU master data.
        warehouse (object): Warehouse object.

    Returns:
        dict: A dictionary mapping SKU compressed IDs to their corresponding image upload paths.
    """
    sku_compressed_ids = [sku.get("wms_code", "") for sku in master_data]
    sku_image_upload_path = {image_log.extra_flag:image_log.uploaded_file.url for image_log in MasterDocs.objects.filter(user_id = warehouse.id , master_type = 'SKUMaster', extra_flag__in=sku_compressed_ids )}
    return sku_image_upload_path

def sku_master_results_preparation(master_data, warehouse: User, sku_ean_dict, temp_data):
    """
    Prepare the results for the SKU master data.

    Args:
        master_data (list): List of SKU master data.
        warehouse (User): Warehouse object.
        sku_ean_dict (dict): Dictionary mapping SKU IDs to EAN numbers.
        temp_data (dict): Temporary data dictionary.

    Returns:
        dict: Updated temporary data dictionary with SKU master results.

    """
    sku_type_list, sku_invoice_groups, misc_data = get_required_misc_values(warehouse)
    sku_image_upload_path = get_sku_image_upload_path(master_data, warehouse)
    user_timezone = get_user_time_zone(warehouse)
    sku_ids = [data["id"] for data in master_data]
    #sku attributes dict preparation
    sku_attributes_dict = defaultdict(dict)
    sku_attributes = SKUAttributes.objects.filter(sku__user=warehouse.id, sku_id__in=sku_ids)\
                            .values("sku_id", 'attribute_value', 'attribute_name')
    for sku_attr in sku_attributes:
        sku_attributes_dict[sku_attr["sku_id"]].update({sku_attr["attribute_name"] : sku_attr["attribute_value"]})
    enable_seller = misc_data.get('enable_seller', 'false')
    seller_master_dict = {}
    seller_master_dict = fetch_seller_master_data(enable_seller, warehouse, master_data, seller_master_dict)
    for data in master_data:
        creation_date = get_local_date_known_timezone(user_timezone, data.get("creation_date"), send_date=True).strftime('%Y-%m-%d %I:%M %p') if data.get("creation_date", "") else ""
        updation_date = get_local_date_known_timezone(user_timezone, data.get("updation_date"), send_date=True).strftime('%Y-%m-%d %I:%M %p') if data.get("updation_date", "") else ""
        created_by, updated_by = get_sku_log_data(data)
        image_url = get_sku_image_url(sku_image_upload_path, data)
        make_or_by = POChoices.labels[data.get("make_or_buy")] if data.get("make_or_buy","") else ""
        sku_attrs = sku_attributes_dict.get(data.get("id"), {})
        data_dict = OrderedDict(
            (('sku_code', data.get("sku_code", "")),
             ('sku_desc', data.get("sku_desc", "")), 
             ('sku_group', data.get("sku_group", "")),
             ('sku_type', data.get("sku_type") if data.get("sku_type", "") in sku_type_list else ""),
             ('invoice_group', data.get("invoice_group", "") if data.get("invoice_group", "") in sku_invoice_groups else ""),
             ('sku_category', data.get("sku_category", "")),
             ('sku_class',data.get("sku_class", "")),
             ('sku_brand', data.get("sku_brand", "")),
             ('style_name', data.get("style_name", "")),
             ('sku_size', data.get("sku_size", "")),
             ('size_type', ""),
             ('zone', data.get('zone__zone') if data.get('zone__zone', "") else ""),
             ('seller_id', seller_master_dict.get(data.get("seller_id", ""), "")),
             ('price', data.get("price", "")),
             ('cost_price', data.get("cost_price", "")),
             ('mrp', data.get("mrp", "")),
             ('image_url', image_url),
             ('min_norm_quantity', data.get("threshold_quantity", "")),
             ('max_norm_quantity', data.get("max_norm_quantity", "")),
             ('measurement_type', data.get("measurement_type", "")),
             ('color', data.get("color", "")),
             ('ean_number', sku_ean_dict[data.get("id")] if sku_ean_dict.get(data.get("id"),False) else ""), 
             ('batch_based', data.get("batch_based", "")),
             ('hsn_code', data.get("hsn_code", "")),
             ('sub_category', data.get("sub_category", "")),
             ('combo_flag', 'Yes' if data.get("relation_type", "") == 'combo' else 'No'),
             ('block_options', "Yes" if data.get("block_options", "") == 'PO' else "No"),
             ('status', 'Active' if data.get("status") else 'Inactive'),
             ('length', data.get("length", "")),
             ("breadth", data.get("breadth", "")),
             ('height', data.get("height", "")), 
             ('weight', data.get("weight","")),
             ('tax', data.get("product_type", "")),
             ("product_shelf_life", data.get("shelf_life", "")), 
             ("customer_shelf_life", data.get("customer_shelf_life").total_seconds() / timedelta(days=1).total_seconds() if data.get("customer_shelf_life", "") else ""),
             ("sku_reference", data.get("sku_reference", "")), 
             ('scan_picking', 'Scannable' if data.get("scan_picking", "") else 'Non-Scannable'),
             ("pick_and_sort", data.get("pick_and_sort", "")),
             ('minimum_shelf_life', data.get("minimum_shelf_life").total_seconds() / timedelta(days=1).total_seconds() if data.get("minimum_shelf_life", "") else ""),
             ('make_or_by', make_or_by),
             ('serialized', data.get("enable_serial_based", "")), 
             ('qc_required', data.get("qc_eligible", "")),
             ('is_barcode_required', data.get("is_barcode_required", "")),
             ('invoice_group', data.get("invoice_group", "")),
             ('mandate_scan', data.get('mandate_scan', '')),
             ('dispensing_enabled', 'Enable'if data.get("dispensing_enabled", "") else "Disable"),
             ('creation_date', creation_date),
             ('updation_date', updation_date),
             ('created_by',created_by),
             ('updated_by',updated_by),
             ('gl_code', data.get("gl_code", "")),
             ('receipt_tolerance', data.get("receipt_tolerance", "")),
             ('DT_RowAttr', {'data-id': data.get("id", "")})))
        if sku_attrs:
            data_dict.update(sku_attrs)
        temp_data['aaData'].append(data_dict)
    return temp_data

def get_required_misc_values(warehouse: User):
    misc_dict = get_multiple_misc_values(['sku_types', 'sku_invoice_groups', 'enable_seller'], warehouse.id)
    if misc_dict.get('sku_types', ""):
        sku_type_list = misc_dict.get('sku_types').split(",")
        sku_type_list.extend(['FG', 'RM', 'Expense', 'WIP', 'Consumables', 'Spare Parts'])
    else:
        sku_type_list = ['FG', 'RM', 'Expense', 'WIP', 'Consumables', 'Spare Parts']
    sku_invoice_groups = []
    if misc_dict.get('sku_invoice_groups', ""):
        sku_invoice_groups = misc_dict.get('sku_invoice_groups').split(",")
    return sku_type_list, sku_invoice_groups, misc_dict

def get_open_orders_sku_list(warehouse, sku_codes):
    return list(OrderDetail.objects.exclude(status__in = [3, 5]).filter(
        user = warehouse.id, sku__sku_code__in = sku_codes
    ).values_list('sku_code', flat=True))

@get_warehouse
def insert_sku(request, warehouse: User):
    """Insert SKU through request"""
    sku_details = request.POST.dict()
    sku_details['warehouse_id'] = warehouse.id
    sku_details['user_id'] = request.user.id
    image_file = request.FILES.get('file', '')
    if image_file:
        image_file_dict = {}
        image_file_dict[sku_details.get('sku_code')] = request.FILES.get('file', '')
        sku_details['file_dict'] = image_file_dict
    message = warehouse_sku_creation(sku_details)
    if message == 'Success':
        return JsonResponse({'message': 'New SKU Code Added'}, status=200)
    else:
        return JsonResponse({'message': message}, status=400)
    
@celery_app.task
def create_sku_in_bulk(sku_details_arr=[]):
    """Create SKUS in bulk"""
    for sku_details in sku_details_arr:
        warehouse_sku_creation(sku_details)    

def create_master_doc_object_for_master_id(sku_details, warehouse_id, master_id):
    """create master doc object for master id"""
    if sku_details.get('master_doc_id'):
        master_doc_obj = MasterDocs.objects.filter(
            id=sku_details.get('master_doc_id'), master_type='SKUMaster'
            )
        if master_doc_obj.exists():
            obj_dict = master_doc_obj[0].__dict__
            obj_dict.pop('id')
            obj_dict.pop('_state', '')
            obj_dict.update({'user_id': warehouse_id, 'master_id':master_id})
            MasterDocs.objects.create(**obj_dict)

def validate_and_get_wms_description(sku_details):
    """
    Validate and retrieve WMS code and description from SKU details.
    """
    error_message = ''
    # Determine whether it's a test SKU or not
    is_test_sku = sku_details.get('is_test') == 'true'

    # Get WMS code based on test SKU flag
    wms = sku_details['test_code'] if is_test_sku else sku_details['sku_code']
    # Get description based on test SKU flag
    description = sku_details['test_name'] if is_test_sku else sku_details['sku_desc']

    # Strip whitespace from WMS code
    if wms:
        wms = wms.strip()

    # Check for mandatory WMS code
    if not wms:
        error_message = error_dict['sku_code']
        return wms, error_message

    # Check for mandatory description
    if not description:
        error_message = 'Product Description is Mandatory'
        return wms, error_message

    return wms, error_message

def validate_sku_thresholds(sku_details):
    """
    Validate SKU details for shelf life, minimum shelf life, and quantity thresholds.
    """

    if sku_details.get("shelf_life") and sku_details.get("customer_shelf_life"):
        if float(sku_details.get("shelf_life", 0)) < float(sku_details.get("customer_shelf_life", 0)):
            error_message = 'Product Shelf life must be greater than Customer Shelf life'
            return error_message

    if sku_details.get("shelf_life") and sku_details.get("minimum_shelf_life"):
        if float(sku_details.get("shelf_life", 0)) < float(sku_details.get("minimum_shelf_life", 0)):
            error_message = 'Product Shelf life must be greater than Minimum Shelf life'
            return error_message

    if sku_details.get('threshold_quantity') and sku_details.get('max_norm_quantity'):
        if float(sku_details.get('threshold_quantity', 0)) > float(sku_details.get('max_norm_quantity', 0)):
            error_message = SKU_ERROR_DICT["1"]
            return error_message

    return None

def validate_hsn_code_and_product_type(sku_details, available_hsn_codes):
    """Validate HSN Code and Product Type."""
    hsn_code = sku_details.get('hsn_code', '')
    if available_hsn_codes and hsn_code:
        hsn_code = str(hsn_code)
        if not available_hsn_codes.get(hsn_code):
            error_message = 'HSN Code is not mapped to any Tax'
            return error_message, hsn_code
        #validate product type
        if sku_details.get('product_type'):
            product_type = sku_details.get('product_type')

            if hsn_code in available_hsn_codes.keys():
                available_product_type = available_hsn_codes.get(hsn_code)

                if product_type.lower() != available_product_type.lower():
                    error_message = 'HSN Code and Tax Name are not mapped'
                    return error_message, hsn_code
            else:
                error_message = error_dict.get('hsn_code', '')
                return error_message, hsn_code

    return '', 0

def validate_sku_zone_id(key, value,warehouse_id):
    if key == 'zone_id':
        if value not in ['', 'undefined']:
            value = get_or_none(ZoneMaster, {'zone': value, 'user': warehouse_id})
            if value:
                value = value.id
        else:
            value = None
    return value


def validate_seller_mater_id(seller_master, seller_master_dict, enable_seller):
    #validating seller master id
    if enable_seller == 'true' and not seller_master:
        return 'Seller Master ID is Mandatory for SKU Creation', ''
    if not seller_master_dict.get(seller_master, ''):
        return 'Seller Master ID does not exist/Inactive', ''
    return '', seller_master_dict.get(seller_master)

def validate_sku_key_list(value):
    value = 1 if value in ['1', 1, 'true'] else 0
    return value

def validate_product_type(value, hsn_code, available_hsn_codes):
    if available_hsn_codes:
        value = available_hsn_codes.get(int(hsn_code)) if available_hsn_codes.get(int(hsn_code)) else value
    return value

def validate_dates_for_sku_data(key, value):
    if key in ['service_start_date', 'service_end_date']:
        try:
            value = datetime.datetime.strptime(value, '%d-%m-%Y')
        except ValueError:
            value = None
    return value

def validate_each_sku_data(key, value, warehouse_id, data_dict, sku_keys_list, available_hsn_codes, hsn_code, load_unit_dict, sku_details, seller_master_dict, enable_seller):
    """Validate each sku field data """
    if isinstance(value, str) and 'undefined:undefined' in value:
        return data_dict, ''
    if key == 'zone_id':
        value = validate_sku_zone_id(key, value, warehouse_id)
    elif key == 'seller_id':
        message, value = validate_seller_mater_id(value, seller_master_dict, enable_seller)
        if message:
            return {}, message 
    elif key in sku_keys_list:
        value = validate_sku_key_list(value)
    elif key == 'product_type':
        value = validate_product_type(value, hsn_code, available_hsn_codes)
    elif key == 'load_unit_handle':
        value = load_unit_dict.get(value.lower(), 'unit')
    elif key == 'block_options':
        value = 'PO' if value == 'Yes' else ''
    elif key == 'receipt_tolerance':
        value = float(value) if value and isinstance(value, (str, float)) and value not in ['null'] else 0
    elif key == 'customer_shelf_life' or key == 'minimum_shelf_life':
        if sku_details.get(key):
            value = timedelta(float(sku_details[key]))
        else:
            value = timedelta(0)
    elif key == 'image_url':
        if value and len(value) > 200:
            return {}, 'image url length should be less than 200'

    elif key == 'sku_code' and sku_details.get("sku_code"):
        value = str(value).strip()

    elif key in ['qc_eligible', 'mandate_scan']:
        value = True if value == 'true' else False

    if value != '':
        value = validate_dates_for_sku_data(key, value)
        data_dict[key] = value

    return data_dict, ''


def fetch_seller_master_data(enable_seller, warehouse, sku_details, seller_master_dict, source=None):
    # If seller is not enabled, return an empty dictionary
    if enable_seller != 'true':
        return seller_master_dict  # Keep the initial value unchanged

    # Get seller data from request
    seller_data = get_seller_from_request(sku_details, source=source)
    
    # Store the fetched seller master data in a new variable
    updated_seller_master_dict = get_supplier_data(warehouse, seller_data)

    return updated_seller_master_dict  
    

def warehouse_sku_creation(sku_details):
    """
    Insert new SKU details into the warehouse
    """
    # Get user and warehouse objects using the IDs
    user_id = sku_details.get('user_id')
    warehouse_id = sku_details.get('warehouse_id')
    user_object_dict = get_warehouse_objects([user_id, warehouse_id])
    # Check if user and warehouse objects are retrieved successfully
    user = user_object_dict.get(user_id)
    warehouse = user_object_dict.get(warehouse_id)
    if not user or not warehouse:
        return ""
    # Log the request for SKU Master
    log_message = (("Request Sku Master for User %s, request params are %s") % (str(user.username), (str(sku_details))))
    log.info(log_message)
   
    load_unit_dict = LOAD_UNIT_HANDLE_DICT
    misc_data = get_multiple_misc_values(['sku_serialisation', 'enable_seller'], warehouse_id)
    enable_seller = misc_data.get('enable_seller', 'false')
    seller_master_dict = {}
    seller_master_dict = fetch_seller_master_data(enable_seller, warehouse, [sku_details], seller_master_dict)
    try:
        size_type = sku_details.get('size_type', '')
        hot_release = sku_details.get('hot_release', '')
        #validate sku code and description
        wms, error_message = validate_and_get_wms_description(sku_details)
        if error_message:
            return error_message
        #validate sku thresholds
        error_message = validate_sku_thresholds(sku_details)
        if error_message:
            return error_message
        available_hsn_codes = dict(HSNMaster.objects.filter(warehouse = warehouse_id).values_list('hsn_code','product_type'))
        error_message, hsn_code = validate_hsn_code_and_product_type(sku_details, available_hsn_codes)
        if error_message:
            return error_message
        error_message = validate_sku_serialisation(sku_details.get('enable_serial_based'), misc_data)
        if error_message:
            return error_message
        
        error_message = validate_lbhw_fields(sku_details, "", "", skumaster_ui = True)
        if error_message:
            return error_message

        if sku_details.get('image_url') and 'http' not in sku_details.get('image_url'):
            return error_message
        filter_params = {'wms_code__iexact': wms, 'user': warehouse_id}
        data = filter_or_none(SKUMaster, filter_params)
        if data:
            return 'SKU already exists'
        data_dict = copy.deepcopy(SKU_DATA)
        data_dict['user'] = warehouse_id
        for key, value in sku_details.items():
            if key in data_dict.keys():
                data_dict, error_message = validate_each_sku_data(
                    key, value, warehouse_id, data_dict, sku_keys_list, available_hsn_codes,
                    hsn_code, load_unit_dict, sku_details, seller_master_dict, enable_seller
                )
                if error_message:
                    return error_message
        combo_qtys = sku_details.get('combo_quantity', [])
        if '0' in combo_qtys:
            error_message = 'combo quantity should be greater than 0'
            return error_message
        if sku_details.get('is_test', '') == 'true':
            data_dict['wms_code'] = data_dict['test_code']
            data_dict['sku_desc'] = data_dict['test_name']
        data_dict['wms_code'] = data_dict['sku_code']
        data_dict = save_json_data(user,data_dict)
        data_dict['account'] = warehouse.userprofile
        sku_master = SKUMaster(**data_dict)
        sku_master.save()
        update_volume(sku_master)
        #update sku attributes
        update_sku_attributes(sku_master, sku_details, user)

        #create master doc if master id exits
        create_master_doc_object_for_master_id(sku_details, warehouse.id, sku_master.id)

        #save sku file data
        if sku_details.get('file_dict'):
            image_file_dict = sku_details.pop('file_dict')
            upload_sku_image(image_file_dict,warehouse,sku_master,"SKUMaster")
            master_obj = MasterDocs.objects.filter(
                master_id=sku_master.id, user_id=warehouse.id, master_type='SKUMaster'
            ).values('id').first()
            if master_obj:
                sku_details['master_doc_id'] = master_obj['id']

        if size_type:
            check_update_size_type(sku_master, size_type)
        if hot_release:
            value = 1 if (value.lower() == 'enable') else 0
            check_update_hot_release(sku_master, value)

        ean_numbers = sku_details.get('ean_numbers', '')
        if ean_numbers:
            ean_numbers = ean_numbers.split(',')
            update_ean_sku_mapping(warehouse, ean_numbers, sku_master)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Insert New SKU failed for %s and params are %s and error statement is %s' % (str(user.username), \
                                                                                               str(sku_details),
                                                                                               str(e)))
        if sku_details.get('is_test') == 'true':
            status_msg = 'Insert Test Failed'
        else:
            status_msg = 'Insert SKU Failed'
        return status_msg
    log_message = (("Request Sku Master for User %s, SKU code added is %s") % (str(user.username), (str(sku_details['sku_code']))))
    log.info(log_message)

    #Sync SKU Master for spoke warehouses
    sync_masters_across_warehouses.apply_async(args=[user_id, warehouse_id, [sku_details], 'sku_master'], link=create_sku_in_bulk.s())

    return 'Success'

def validate_hsn_codes(sku_details, hsn_code, available_hsn_codes):
    """Validates HSN codes against available entries."""
    error_message = ''
    if available_hsn_codes and hsn_code not in ['null', '']:
        if not available_hsn_codes.get(str(hsn_code)):
            error_message = 'HSN Code is not mapped to any Tax'
        if sku_details.get('product_type'):
            product_type = sku_details.get('product_type')
            hsn_code = str(hsn_code)
            if hsn_code in available_hsn_codes.keys():
                available_product_type = available_hsn_codes.get(hsn_code)
                if product_type.lower() != available_product_type.lower():
                    error_message = 'HSN Code and Tax Name is Not mapped'
            else:
                error_message = error_dict['hsn_code']
    else:
        hsn_code = 0
    return hsn_code, error_message

def validate_sku_shelf_lives(sku_details):
    """
    Validates customer shelf life and minimum shelf life.
    """
    error_message = ''

    variable_map = {
        'shelf_life': 'product_shelf_life',
        'customer_shelf_life': 'customer_shelf_life',
        'threshold_quantity': 'threshold_quantity',
        'minimum_shelf_life': 'minimum_shelf_life',
        'max_norm_quantity': 'max_norm_quantity',
    }

    shelf_lives = {}
    for key, value in variable_map.items():
        shelf_lives[value] = float(sku_details.get(key, 0)) if sku_details.get(key, 0) not in ['null', ''] else 0

    if shelf_lives['product_shelf_life'] < shelf_lives['customer_shelf_life']:
        error_message = 'Product Shelf life must be greater than Customer Shelf life'

    if shelf_lives['product_shelf_life'] < shelf_lives['minimum_shelf_life']:
        error_message = 'Product Shelf life must be greater than Minimum Shelf life'

    if shelf_lives['threshold_quantity'] > shelf_lives['max_norm_quantity']:
        error_message = SKU_ERROR_DICT["1"]

    return shelf_lives, error_message

def get_zone_for_sku_update(value, warehouse):
    """
    Validates and returns the zone for SKU update.
    """
    if value not in ['', 'undefined']:
        zone = get_or_none(ZoneMaster, {'zone': value, 'user': warehouse.id})
        if zone:
            value = zone.id
    else:
        value = None
    return value

def validate_hsn_code_for_sku_update(value):
    """
    Validates the HSN code for SKU update.
    """
    error_message = ""
    hsn_code = value
    if value:
        try:
            int(hsn_code)
            value = str(value)
        except (ValueError, TypeError):
            value = 0
            error_message = 'HSN code should be numeric'
    else:
        value = 0

    return value, error_message

def valid_numeric_string(value):
    """
    Validates and converts a value to a numeric string.
    """
    try:
        if isinstance(value, (str, float, int)):
            if value not in ['null', '']:
                value = float(value)
            else:
                value = 0
        else:
            value = 0
    except (ValueError, TypeError):
        # Handle the exception if the conversion to float fails
        value = 0
    return value

def process_timedelta_field(field_value):
    """
    Process the given field value as a timedelta.
    """
    if field_value:
        field_value = timedelta(float(field_value))
    else:
        field_value = timedelta(0)
    return field_value

def process_boolean_field(key, value):
    """
    Process a boolean field based on the given key and value.
    """
    field_value = ''
    if key in sku_keys_list:
        field_value = 1 if value in ['1', 1, 'true'] else 0
    elif key == 'make_or_buy':
        field_value = value if value else 0
    elif key == 'block_options':
        field_value = 'PO' if value == 'Yes' else ''
    return field_value

def validate_sku_data(key, value, warehouse, data, hsn_code, available_hsn_codes, shelf_lives, load_unit_dict, seller_master_dict, enable_seller):
    """
    Validates the SKU data based on the given key and value.
    """
    time_fields = ['customer_shelf_life', 'minimum_shelf_life']
    boolean_fields = ['make_or_buy', 'block_options']
    number_fields = ['threshold_quantity', 'cost_price', 'price', 'mrp', 'max_norm_quantity',
                          'shelf_life', 'gl_code', 'liquidation_shelf_life', 'receipt_tolerance']

    if 'attr_' in key:
        return '', True, value

    if key in time_fields:
        # Process time fields
        value = process_timedelta_field(shelf_lives[key])
    elif key in sku_keys_list or key in boolean_fields:
        # Process SKU keys or boolean fields
        value = process_boolean_field(key, value)
    elif key == 'zone_id' and value:
        # Get zone for SKU update
        value = get_zone_for_sku_update(value, warehouse)
    elif key == 'seller_id':
        #validating seller master id
        error_message, value=validate_seller_mater_id(value, seller_master_dict, enable_seller)
        if error_message:
            return error_message, False, value
    elif key == 'ean_numbers':
        # Update EAN-SKU mapping
        ean_numbers = value.split(',')
        update_ean_sku_mapping(warehouse, ean_numbers, data, True)
    elif key == 'hsn_code':
        # Validate HSN code for SKU update
        value, error_message = validate_hsn_code_for_sku_update(value)
        if error_message:
            return error_message, False, value
    elif key == 'product_type':
        if available_hsn_codes and hsn_code:
            # Get product type based on available HSN codes and HSN code
            value = available_hsn_codes.get(int(hsn_code), "")
    elif key == 'load_unit_handle':
        # Get load unit handle
        value = load_unit_dict.get(value.lower(), 'unit')
    elif key == 'size_type':
        # Check and update size type
        check_update_size_type(data, value)
        return '', True, value
    elif key == 'hot_release':
        # Check and update hot release
        value = 1 if value == '1' else 0
        check_update_hot_release(data, value)
        return '', True, value
    elif key in number_fields:
        # Validate numeric fields
        value = valid_numeric_string(value)
    elif key == 'block_options':
        # Map block options
        value = {'Yes': 'PO'}.get(value, '')
    #Validate boolean fields
    elif key in ['qc_eligible', 'mandate_scan']:
        value = True if value == 'true' else False

    elif key == 'image_url':
        # Return image URL
        return '', False, value
    return '', False, value

@get_warehouse
def update_sku(request, warehouse: User):
    """Insert SKU through request"""
    sku_details = request.POST.dict()
    sku_details['warehouse_id'] = warehouse.id
    sku_details['user_id'] = request.user.id
    image_file = request.FILES.get('file', '')
    if image_file:
        image_file_dict = {}
        image_file_dict[sku_details.get('sku_code')] = request.FILES.get('file', '')
        sku_details['file_dict'] = image_file_dict
    message = warehouse_sku_updation(sku_details)
    if message == 'Success':
        return JsonResponse({'message': 'Updated Successfully'}, status=200)
    else:
        return JsonResponse({'message': message}, status=400)

@celery_app.task
def update_sku_in_bulk(sku_details_arr=[]):
    """Create SKUS in bulk"""
    for sku_details in sku_details_arr:
        warehouse_sku_updation(sku_details)

def warehouse_sku_updation(sku_details):
    """ Update SKU Details """
    load_unit_dict = LOAD_UNIT_HANDLE_DICT
    try:
        error_message = ""
        wms = sku_details['sku_code'].strip()
        #Check for SKU Code
        if not wms:
            error_message = error_dict['sku_code']
            return error_message
        user_id = sku_details.get('user_id')
        warehouse_id = sku_details.get('warehouse_id')
        user_object_dict = get_warehouse_objects([user_id, warehouse_id])
        # Check if user and warehouse objects are retrieved successfully
        user = user_object_dict.get(user_id)
        warehouse = user_object_dict.get(warehouse_id)
        if not user or not warehouse:
            return ""

        data = get_or_none(SKUMaster, {'sku_code__iexact': wms, 'user': warehouse.id})
        if not data:
            error_message = 'SKU does not exists'
            return error_message

        #Validate SKU Shelf Life
        shelf_lives, error_message = validate_sku_shelf_lives(sku_details)
        if error_message:
            return error_message

        #Validate HSN Codes
        hsn_code = sku_details.get('hsn_code')
        available_hsn_codes = dict(HSNMaster.objects.filter(warehouse = warehouse.id).values_list('hsn_code','product_type'))
        hsn_code, error_message = validate_hsn_codes(sku_details, hsn_code, available_hsn_codes)
        if error_message:
            return error_message
        
        stock_data = get_available_stock_data([{SKU_CODE:data.sku_code}], warehouse)

        found, not_found = get_open_sku_stock_details([data.sku_code], warehouse.id)

        #Validate Serialized flag        
        misc_dict = get_multiple_misc_values(['sku_serialisation', 'enable_seller'], warehouse.id)
        enable_seller = misc_dict.get('enable_seller', 'false')
        seller_master_dict = {}
        # fetch seller master data from request
        seller_master_dict=get_supplier_data(warehouse, {'supplier_id__in':[sku_details.get('seller_id','')]})
        sku_serial_config = sku_details.get('enable_serial_based', '0') or '0'
        
        old_serial_flag = data.enable_serial_based

        if sku_serial_config != str(old_serial_flag) and stock_data:
            error_message = error_dict['serialized']
            return error_message
        
        if sku_serial_config != str(old_serial_flag):
            error_message = validate_sku_serialisation(sku_serial_config, misc_dict, found)
            if error_message:
                return error_message

            
        old_batch_based_flag = data.batch_based
        batch_based = sku_details.get('batch_based', '0') or '0'
        if batch_based != str(old_batch_based_flag) and stock_data:
            error_message = error_dict['batch_based']
            return error_message
        
        old_invoice_group = data.invoice_group
        invoice_group = sku_details.get('invoice_group', '') or ''
        if invoice_group and invoice_group != old_invoice_group:
            #GET Open Orders
            open_orders_sku_list = get_open_orders_sku_list(warehouse, [data.sku_code])
            if open_orders_sku_list:
                return 'Invoice Group cannot be updated as the SKU is present in open orders'

        #iterate through the request data and update the data
        for key, value in sku_details.items():
            error_message, continue_check, value = validate_sku_data(
                key, value, warehouse, data, hsn_code,
                available_hsn_codes, shelf_lives, load_unit_dict, seller_master_dict, enable_seller
                )
            if error_message:
                return error_message
            elif continue_check:
                continue
            try:
                setattr(data, key, value)
            except Exception as e:
                pass
        data.json_data = data.json_data or {}
        data.json_data['updated_by'] = user.username

        update_sku_attributes(data, sku_details, user)
        data.save()

        #create master doc if master id exits
        create_master_doc_object_for_master_id(sku_details, warehouse.id, data.id)

        #save sku file data
        if sku_details.get('file_dict'):
            image_file_dict = {}
            image_file_dict[wms] = sku_details.pop('file_dict')
            upload_sku_image(image_file_dict,warehouse,data,"SKUMaster")
            master_obj = MasterDocs.objects.filter(
                master_id=data.id, user_id=warehouse.id, master_type='SKUMaster'
            ).values('id').first()
            if master_obj:
                sku_details['master_doc_id'] = master_obj['id']
        update_volume(data)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Update SKU Data failed for %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(sku_details), str(e)))
        error_message = 'Update SKU Failed'
        return error_message

    sync_masters_across_warehouses.apply_async(args=[user_id, warehouse_id, [sku_details], 'sku_master'], link=update_sku_in_bulk.s())

    return 'Success'

@get_warehouse
def get_sku_data(request, warehouse: User):
    """ Get SKU Details """
    market_data = []
    combo_data = []
    data_id = request.GET['data_id']

    filter_params = {'id': data_id, 'user': warehouse.id}
    instance_name = SKUMaster
    data = get_or_none(instance_name, filter_params)
    filter_params = {'user': warehouse.id}
    all_groups = list(SKUGroups.objects.filter(user=warehouse.id).values_list('group', flat=True))
    sku_image_upload_path = {image_obj.extra_flag: image_obj.uploaded_file.url for image_obj in MasterDocs.objects.filter(user_id = warehouse.id , master_type = 'SKUMaster', extra_flag=data.wms_code)}
    load_unit_dict = {'unit': 0, 'pallet': 1}
    sku_type_list, sku_invoice_groups , misc_dict = get_required_misc_values(warehouse)
    enable_seller = misc_dict.get('enable_seller', 'false')
    sku_type =  data.sku_type if data.sku_type in sku_type_list else ""
    invoice_group = data.invoice_group if data.invoice_group in sku_invoice_groups else ""
    zone_name = ''
    if data.zone:
        zone_name = data.zone.zone

    company_id = get_company_id(warehouse)
    measure_type = data.measurement_type
    uom_dat = list(UOMDetail.objects.filter(company_id=company_id).values_list('uom_code',flat=True))
    uom_data = []

    hsn_data = HSNMaster.objects.filter(warehouse = warehouse.id)
    hsn_master_data = True if hsn_data else False
    seller_master_name = ''
    sku_data = {}
    sku_data['sku_code'] = data.sku_code
    sku_data['sku_desc'] = data.sku_desc
    sku_data['sku_group'] = data.sku_group
    sku_data['sku_type'] = sku_type
    sku_data['sku_category'] = data.sku_category
    sku_data['sku_class'] = data.sku_class
    sku_data['sku_brand'] = data.sku_brand
    sku_data['style_name'] = data.style_name
    sku_data['sku_size'] = data.sku_size
    sku_data['product_type'] = data.product_type
    sku_data['zone'] = zone_name
    sku_data['threshold_quantity'] = data.threshold_quantity
    sku_data['max_norm_quantity'] = data.max_norm_quantity
    sku_data['online_percentage'] = data.online_percentage
    if sku_image_upload_path.get(data.wms_code):
        sku_data['image_url'] = sku_image_upload_path.get(data.wms_code)
    else:
        sku_data['image_url'] = data.image_url
    sku_data['qc_check'] = data.qc_check
    sku_data['status'] = data.status
    sku_data['cost_price'] = data.cost_price
    sku_data['price'] = data.price
    sku_data['mrp'] = data.mrp
    sku_data['scan_picking'] = data.scan_picking
    sku_data['pick_and_sort'] = data.pick_and_sort
    sku_data['qc_eligible'] = data.qc_eligible
    sku_data['is_barcode_required'] = data.is_barcode_required
    sku_data['invoice_group'] = invoice_group
    sku_data['mandate_scan'] = data.mandate_scan
    sku_data['make_or_buy'] = data.make_or_buy
    sku_data['size_type'] = ''
    sku_data['mix_sku'] = data.mix_sku
    sku_data['ean_number'] = data.ean_number
    sku_data['height'] = data.height
    sku_data['weight'] = data.weight
    sku_data['breadth'] = data.breadth
    sku_data['length'] = data.length
    sku_data['volume'] = data.volume
    sku_data['article_no'] = data.article_no
    sku_data['sku_reference'] = data.sku_reference
    if enable_seller == 'true' and data.seller_id:
        sku_data['seller_id'] = data.seller.supplier_id
        sku_data['cost_category'] = data.cost_category

    ean_number = ''
    ean_numbers_list = list(data.eannumbers_set.filter().annotate(str_eans=Cast('ean_number', CharField())). \
                                values_list('str_eans', flat=True))
    if ean_numbers_list:
        ean_number = ean_numbers_list
    sku_data['ean_numbers'] = ean_number
    sku_data['color'] = data.color
    sku_data['load_unit_handle'] = load_unit_dict.get(data.load_unit_handle, 'unit')
    hsn_code = ''
    hsn_code = data.hsn_code
    purchase_uom, sales_uom = 0, 0
    sku_pack_obj = SKUPackMaster.objects.filter(Q(purchase_uom=1)|Q(sales_uom=1),sku__sku_code=data.sku_code, sku__user=warehouse.id)
    for sku_pack in sku_pack_obj:
        if sku_pack.purchase_uom:
            purchase_uom = sku_pack.purchase_uom
        if sku_pack.sales_uom:
            sales_uom = sku_pack.sales_uom

    sku_data['purchase_uom'] = purchase_uom
    sku_data['sales_uom'] = sales_uom
    sku_data['hsn_code'] = hsn_code
    sku_data['sub_category'] = data.sub_category
    sku_data['pick_group'] = data.pick_group
    sku_data['hot_release'] = 0
    sku_data['shelf_life'] = data.shelf_life
    customer_shelf_life = data.customer_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
    sku_data['customer_shelf_life'] = customer_shelf_life
    minimum_shelf_life = data.minimum_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
    sku_data['minimum_shelf_life'] = minimum_shelf_life
    sku_data['batch_based'] = data.batch_based
    sku_data['measurement_type'] = measure_type
    sku_data['youtube_url'] = data.youtube_url
    sku_data['enable_serial_based'] = data.enable_serial_based
    sku_data['dispensing_enabled'] = data.dispensing_enabled
    sku_data['block_options'] = 'No'
    sku_data['gl_code'] = data.gl_code
    sku_data['uoms'] = uom_dat
    sku_data['receipt_tolerance'] = data.receipt_tolerance
    sku_data['allow_price_override'] = data.allow_price_override
    if data.block_options == 'PO':
        sku_data['block_options'] = 'Yes'

    sku_fields = SKUFields.objects.filter(field_type='size_type', sku_id=data.id)
    if sku_fields:
        sku_data['size_type'] = sku_fields[0].field_value

    sku_fields = SKUFields.objects.filter(field_type='hot_release', sku_id=data.id)
    if sku_fields:
        sku_data['hot_release'] = sku_fields[0].field_value

    size_names = SizeMaster.objects.filter(user=warehouse.id)
    sizes_list = []
    for sizes in size_names:
        sizes_list.append({'size_name': sizes.size_name, 'size_values': (sizes.size_value).split('<<>>')})
    
    product_types = list(TaxMaster.objects.filter(user_id=warehouse.id).values_list('product_type',
                                                                                   flat=True).distinct())
    attributes = get_user_attributes(warehouse, 'sku')
    sku_attribute_objs = data.skuattributes_set.filter()
    sku_attributes = OrderedDict()
    for sku_attribute_obj in sku_attribute_objs:
        sku_attributes.setdefault(sku_attribute_obj.attribute_name, [])
        if sku_attribute_obj.attribute_value:
            if ',' in sku_attribute_obj.attribute_value:
                sku_attributes[sku_attribute_obj.attribute_name]=(sku_attribute_obj.attribute_value.split(','))
            else:
                sku_attributes[sku_attribute_obj.attribute_name].append(sku_attribute_obj.attribute_value)
    


    return HttpResponse(
        json.dumps({'sku_data': sku_data, 'groups': all_groups,
                    'market_data': market_data, 'combo_data': combo_data, 'sizes_list': sizes_list,
                    'sub_categories': SUB_CATEGORIES, 'product_types': product_types, 'attributes': list(attributes),
                    'sku_attributes': sku_attributes, 'uom_data': uom_data,
                    'hsn_master_data':hsn_master_data}, cls=DjangoJSONEncoder))


@get_warehouse
def get_po_choices(request, warehouse:User):
    po_choices = POChoices.labels
    return JsonResponse({'po_choices': po_choices})

def check_and_return_mapping_id(sku_code, warehouse: User):
    sku_id = ''
    sku_master = SKUMaster.objects.filter(sku_code=sku_code, user=warehouse.id)
    if sku_master:
        sku_id = sku_master[0].id

    return sku_id

def get_sku_id(sku_code, warehouse):
    sku_id = check_and_return_mapping_id(sku_code, warehouse)
    if not sku_id: #Checking scanned sku first, if not present then checking based on configuration.
        sku_code = ''#(CHECK_LATER)
        sku_id = check_and_return_mapping_id(sku_code, warehouse)
    if not sku_id:
        try:
            sku_ean_objs = SKUMaster.objects.filter(ean_number=sku_code, user=warehouse.id).only('ean_number', 'sku_code')
            if sku_ean_objs.exists():
                sku_id = sku_ean_objs[0].id
            else:
                ean_obj = EANNumbers.objects.filter(sku__user=warehouse.id, ean_number=sku_code)
                if ean_obj.exists():
                    sku_id = ean_obj[0].sku_id
        except Exception:
            sku_id = ''

    return sku_id

@get_warehouse
def check_sku(request, warehouse: User):
    data = {}
    sku_code = request.GET.get('sku_code')
    sale_return = request.GET.get('sale_return','false')
    customer_tax_type = request.GET.get('customer_tax_type', None)
    if sale_return == 'true':
        sales_return_reasons = get_misc_value('sales_return_reasons', warehouse.id)
        if sales_return_reasons:
            sales_return_reasons = sales_return_reasons.split(',')

    sku_id = get_sku_id(sku_code, warehouse)
   
    if sku_id:
        tax_dict = {}
        sku_data = SKUMaster.objects.get(id=sku_id)

        if not data:
            data = {"status": 'confirmed', 'sku_code': sku_data.sku_code, 'description': sku_data.sku_desc,
                    'order_id': '', 'ship_quantity': '', 'unit_price': sku_data.price, 'return_quantity': 1,
                    'mrp': sku_data.mrp, 'buy_price': sku_data.cost_price, 'weight': sku_data.weight, 'is_serialised': sku_data.enable_serial_based}
            tax_dict = {'igst': 0, 'cess': 0,'cgst': 0, 'sgst': 0, 'tax_percent': 0}
            if customer_tax_type in ['inter_state', 'intra_state']:
                tax_rates = {
                        'inter_state': 1,
                        'intra_state': 0
                    }
                item_tax_type = tax_rates.get(customer_tax_type)
                tax_masters = list(TaxMaster.objects.filter(user_id=warehouse.id, inter_state=item_tax_type, product_type=sku_data.product_type)\
                            .values('igst_tax', 'cgst_tax', 'sgst_tax', 'cess_tax', 'inter_state'))
                if tax_masters:
                    igst_tax = tax_masters[0].get('igst_tax', 0)
                    sgst_tax = tax_masters[0].get('sgst_tax', 0)
                    cgst_tax = tax_masters[0].get('cgst_tax', 0)
                    cess_tax = tax_masters[0].get('cess_tax', 0)
                    if tax_masters[0].get('inter_state'):
                        tax_dict = {'igst': igst_tax, 'cess':cess_tax,'cgst': 0, 'sgst':0, 'tax_percent': igst_tax}
                    else:
                        tax_dict = {'igst': 0, 'cess': cess_tax,'cgst': cgst_tax, 'sgst': sgst_tax, 'tax_percent': sgst_tax+cgst_tax+cess_tax}
            data.update(**tax_dict)
            if sale_return == 'true':
                data['sales_return_reasons'] = sales_return_reasons
                data['shelf_life'] = sku_data.shelf_life
        return HttpResponse(json.dumps(data))
    barcode_res = {"status":False, "data":"No Templates are present"} #(CHECK_LATER)
    if(barcode_res["status"]):
        return HttpResponse(json.dumps(barcode_res["data"]))
    return HttpResponse("%s not found" % sku_code)


def sku_form(user, extra_params={}):
    log.info('SKU Master Form for %s with extra params %s' % ((str(user.username)), str(extra_params)))
    extra_fields = get_extra_fields_for_upload(user, 'sku')
    sku_headers=copy.deepcopy(SKU_HEADERS)
    # validating seller master summary is enabled or not
    misc_types = ['enable_seller']
    misc_dict = get_multiple_misc_values(misc_types, user.id)
    if misc_dict.get('enable_seller', '') != 'true':
        sku_headers.remove('Seller Id')
    return sku_headers + extra_fields

#SKU UPLOAD
def sku_upload(request, user, data_list, extra_params={}):
    log.info('SKU Master Upload for %s with extra params %s' % ((str(user.username)), str(extra_params)))
    try:
        data_to_integrate = data_list
        attribute_data = get_user_attributes(user, 'sku')
        attributes_dict = {}
        for attr_data in attribute_data:
            attributes_dict[attr_data["attribute_name"]] = attr_data
        zones_data = get_zone_data(user)
        extra_params = {
            'attributes' : attributes_dict,
            'zones_data' : zones_data
        }
        status = validate_sku_form(user, data_to_integrate, extra_params)
        if status != 'Success':
            return status
        sku_excel_upload(request, user, data_to_integrate, extra_params)
    except Exception as e:
        import traceback
        upload_log.debug(traceback.format_exc())
        upload_log.info('SKU Master Upload failed for %s and error statement is %s' % (
        str(user.username), str(e)))
        return data_list

    return 'Success'

def get_available_product_types(user):
    return list(TaxMaster.objects.filter(user_id=user.id).values_list('product_type', flat=True).distinct())

def get_available_hsn_codes(user):
    return dict(HSNMaster.objects.filter(warehouse=user.id).values_list('hsn_code', 'product_type'))

def get_available_uoms(user):
    return list(UOMDetail.objects.filter(company_id=get_company_id(user)).values_list('uom_code', flat=True))

def get_available_sku_groups(user):
    available_sku_group = SKUGroups.objects.filter(user=user.id).values_list('group',flat = True)
    available_sku_group = [str(group).upper() for group in available_sku_group]
    return available_sku_group

def get_available_invoice_groups(user):
    misc_dict = get_multiple_misc_values(['sku_invoice_groups'], user.id)
    sku_invoice_groups = []
    if misc_dict.get('sku_invoice_groups', ""):
        sku_invoice_groups = misc_dict.get('sku_invoice_groups').split(",")
    return sku_invoice_groups

def validate_sku_attributes_data(key, cell_data, attribute_data, status):
    if cell_data == "" and attribute_data['is_mandatory']:
        status.append('%s is a Mandatory Field'%(key))
    elif cell_data != "":
        status = validate_attribute_values(cell_data, attribute_data, status)
    return status

def validate_attribute_values(cell_data, attr_dataa, status):
    attr_data = str(cell_data).split(',') if cell_data else []
    attribute_type = attr_dataa['attribute_type']
    for data in attr_data:
        if str(data).strip() not in attr_dataa.get('attribute_values').split(',') and attribute_type == "Dropdown":
            status.append('Select a valid drop down data for %s'%(data))
        elif (attribute_type == "Number Integer") and not (cell_data.replace('.', '', 1).isdigit() and float(cell_data).is_integer()):
            status.append('%s Value Should Be Numeric without Decimals'%(cell_data))
        elif (attribute_type == "Number Float") and not cell_data.replace('.', '', 1).isdigit():
            status.append('%s Value Should Be Numeric'%(cell_data))
    return status

def validate_sku_code_data(status, upload_file_skus, cell_data):

    sku_code = cell_data
    if isinstance(cell_data, float):
        sku_code = str(int(cell_data))
        sku_code = str(sku_code).strip()
    if sku_code in upload_file_skus:
        status.append(SKU_ERROR_DICT["2"])
    else:
        upload_file_skus.append(sku_code)
    if not str(sku_code).strip():
        status.append('%s %s' % (error_dict['sku_code'], sku_code))

    return status, upload_file_skus, sku_code

def validate_sku_group_data(cell_data, status, available_sku_groups):
    if cell_data and str(cell_data).upper() not in available_sku_groups:
        status.append(SKU_ERROR_DICT["3"])
    return status

def validate_sku_put_zone_data(cell_data, status, zones_list):
    if cell_data:
        if isinstance(cell_data, (int, float)):
            cell_data = str(int(cell_data))
        if str(cell_data).upper() not in zones_list:
            status.append(SKU_ERROR_DICT["4"])
    return status

def validate_sku_ean_number(cell_data, status, user):
    if cell_data:
        try:
            ean_numbers = str(cell_data).split(',') if ',' in str(cell_data) else [str(cell_data)]
            for temp_ean in ean_numbers:
                if not temp_ean:
                    continue
                temp_ean = str(temp_ean)
                if len(temp_ean) > 20:
                    status.append(SKU_ERROR_DICT["5"])
        except Exception as e:
            upload_log.info(f'SKU Master Upload failed for {user.username} and error statement is {e}')
    return status

def validate_hsn_code(cell_data, status):
    hsn_code = ''
    if cell_data:
        if isinstance(cell_data, (int,float)):
            cell_data = str(int(cell_data))
        cell_data = cell_data.strip()
        if isinstance(cell_data, str) and not cell_data.isnumeric():
            status.append(SKU_ERROR_DICT["6"])
        else:
            hsn_code = cell_data
    return status, hsn_code

def validate_tax_type(cell_data, status, product_types, hsn_code, available_hsn_codes):
    product_type = ''
    if cell_data:
        if cell_data not in product_types:
            status.append(SKU_ERROR_DICT["7"])
        else:
            product_type = cell_data
    if available_hsn_codes and hsn_code:
        status = validate_available_hsn_with_uploaded_hsn(hsn_code, product_type, available_hsn_codes, status)
    
    return status

def validate_scan_status(cell_data, status):
    if cell_data and cell_data.lower() not in SCAN_OPTIONS:
        status.append(SKU_ERROR_DICT["8"])
    return status

def validate_min_norm_quantity(cell_data, status):
    min_norm_quantity = 0
    try:
        if cell_data:
            cell_data = float(cell_data)
    except Exception:
        status.append(SKU_ERROR_DICT['11'])
        return status, min_norm_quantity
    
    if not isinstance(cell_data, (int, float, str)) and cell_data:
        status.append(SKU_ERROR_DICT['11'])
    else:
        min_norm_quantity = cell_data
        
    if cell_data and isinstance(cell_data, (int, float)) and int(cell_data) < 0:
        status.append(SKU_ERROR_DICT['33'])

    return status, min_norm_quantity

def validate_max_norm_quantity(cell_data, status, min_norm_quantity):
    try:
        if cell_data:
            cell_data = float(cell_data)
    except Exception:
        status.append(SKU_ERROR_DICT['12'])
        return status
    
    if not isinstance(cell_data, (int, float, str)) and cell_data:
        status.append(SKU_ERROR_DICT['12'])
    else:
        if  min_norm_quantity and cell_data:
            if not isinstance(cell_data, (str)) and not isinstance(min_norm_quantity, (str)):
                if float(cell_data)< float(min_norm_quantity):
                    status.append(SKU_ERROR_DICT["1"])
    if cell_data and isinstance(cell_data, (int, float)) and int(cell_data) < 0:
        status.append(SKU_ERROR_DICT['34'])
    return status
    
def validate_config_values(cell_data, status, key, value_list, sku_code, additional_params = {}):
    error_key_dict = {
        "Hot Release": SKU_ERROR_DICT["18"],
        "Batch Based(Options: Enable, Disable)": SKU_ERROR_DICT["19"],
        "Serialized(Options: Enable, Disable)": SKU_ERROR_DICT["21"],
        "Dispensing Enabled(Options: true, false)": SKU_ERROR_DICT["20"],
        "Block For PO": SKU_ERROR_DICT["23"],
        "Load Unit Handling(Options: Enable, Disable)":SKU_ERROR_DICT["16"],
        "Pick and Sort(Options: Enable, Disable)": SKU_ERROR_DICT["47"],
        "Mandate Scan(Options: Enable, Disable)": SKU_ERROR_DICT["48"],

    }
    if cell_data and str(cell_data).lower() not in value_list:
        status.append(error_key_dict[key])

    if key == "Batch Based(Options: Enable, Disable)" and cell_data:
        existing_batch_based = additional_params.get(str(sku_code).lower())
        if existing_batch_based and existing_batch_based != cell_data.lower():
            status.append('Batch Based cannot be changed, Stock is available For this SKU')

    return status

def validate_receipt_tolerance_value(cell_data, status):
    if cell_data:
        try:
            cell_data = float(cell_data)
        except Exception:
            status.append(SKU_ERROR_DICT['13'])
    return status

def validate_numeric_values(cell_data, status, key):
    error_key_dict = {
        SELLING_PRICE : SKU_ERROR_DICT["14"],
        "Sequence": SKU_ERROR_DICT["22"],
        "GL Code": SKU_ERROR_DICT["24"],
        "Weight": SKU_ERROR_DICT["38"],
        "Height": SKU_ERROR_DICT["35"],
        "Breadth": SKU_ERROR_DICT["37"],
        "Length": SKU_ERROR_DICT["36"],
        "Cost Price": SKU_ERROR_DICT["39"],
        "MRP Price": SKU_ERROR_DICT["40"],
    }
    try:
        if cell_data:
            cell_data = float(cell_data)
    except Exception:
        status.append(error_key_dict[key])
        return status

    if not isinstance(cell_data, (int, float, str)) and cell_data:
        status.append(error_key_dict[key])
    return status

def validate_mix_sku_attribute(cell_data, status):
    if cell_data and cell_data.lower() not in MIX_SKU_MAPPING.keys():
        status.append(SKU_ERROR_DICT['15'])
    return status

def validate_sku_sub_categories_values(cell_data, status):
    if cell_data and str(cell_data).upper() not in SUB_CATEGORIES.values():
        status.append(SKU_ERROR_DICT['17'])
    return status

def validate_sku_uom_data(cell_data, status, uoms_list):
    if cell_data and not uoms_list:
        status.append(SKU_ERROR_DICT['27'])
    if cell_data and isinstance(cell_data, (int, float)):
        status.append(SKU_ERROR_DICT['28'])
    if cell_data and uoms_list and cell_data not in uoms_list:
        status.append('Available choices for UOMs: %s'%(','.join(uoms_list)))
    return status

def validate_available_hsn_with_uploaded_hsn(hsn_code, product_type, available_hsn_codes, status):
    if not available_hsn_codes.get(hsn_code):
        status.append(error_dict['hsn_code'])
        return status
    if product_type and available_hsn_codes.get(hsn_code):
        available_product_type = available_hsn_codes.get(hsn_code)
        if product_type.lower() != available_product_type.lower():
            status.append('HSN Code is Mapped to other Tax Type- ' +available_product_type)
    return status

def validate_shelf_life(cell_data, status, key):
    error_key_dict = {"Customer Shelf Life": SKU_ERROR_DICT["25"],
                      "Minimum Shelf Life": SKU_ERROR_DICT["26"],
                      "Product Shelf Life": SKU_ERROR_DICT["29"]}
    shel_life = 0
    if cell_data:
        
        try:
            if cell_data:
                cell_data = float(cell_data)
        except Exception:
            status.append(error_key_dict[key])

        if not isinstance(cell_data, (int,float, str)):
            status.append(error_key_dict[key]) 
            return status, shel_life
        else:
            try:
                if key == PRODUCT_SHELF_LIFE:
                    shel_life = float(cell_data)
                else:
                    shel_life= timedelta(float(cell_data))
            except Exception:
                status.append(error_key_dict[key])
    else:
        if key != PRODUCT_SHELF_LIFE:
            shel_life=timedelta(0)
    return status, shel_life

def validate_shelf_lives(self_life, customer_self_life, minimum_shelf_life, status):
    try:
        if self_life < customer_self_life.total_seconds() / timedelta(days=1).total_seconds():
            status.append('Product Shelf life must be greater than  Customer Shelf life')
        if self_life < minimum_shelf_life.total_seconds() / timedelta(days=1).total_seconds():
            status.append('Customer Shelf life must be greater than  Minimum Shelf life')
    except Exception:
        pass
    return status

def validate_qc_field_value(cell_data, status):
    if cell_data and str(cell_data).lower() not in ['yes', 'no']:
        status.append(SKU_ERROR_DICT["30"])
    return status

def validate_barcoding_required_field_value(cell_data, status):
    if cell_data and str(cell_data).lower() not in ['yes', 'no']:
        status.append(SKU_ERROR_DICT["44"])
    return status

def validate_sku_invoice_group(cell_data, status, available_invoice_groups, open_orders_sku_list, existing_invoice_group_details, sku_code):
    if cell_data:
        if cell_data not in available_invoice_groups:
            status.append(SKU_ERROR_DICT["45"])
        if sku_code in open_orders_sku_list and existing_invoice_group_details.get(sku_code) != cell_data:
            status.append(SKU_ERROR_DICT["46"])
    return status

def extract_key(key):
    return str(key.split('(options :')[0]).strip()

def validate_multiple_config_values(cell_data, status, key, sku_code, additional_params = {}, sku_serial=None, found=None, existing_data=None):
    enable_serial_based = ''
    if key == 'Serialized(Options: Enable, Disable)' and cell_data:
        if existing_data:
            existing_data_dict = dict(existing_data)
            enable_serial_based = existing_data_dict.get(sku_code, 0)
        value = 1 if str(cell_data).lower()=="enable" else 0
        if enable_serial_based != value:
            status = validate_sku_serialisation(value,sku_serial, found)
            if additional_params.get(str(sku_code)) and not status:
                status = error_dict['serialized']
    if key == DISPENSING:
        status = validate_config_values(cell_data, status, key, BOOLEAN_CONFIG_VALUES, sku_code)
    elif key == BLOCK_FOR_PO:
        status = validate_config_values(cell_data, status, key, BLOCK_FOR_PO_CONFIG_VALUES, sku_code)
    else:
        status = validate_config_values(cell_data, status, key, CONFIG_VALUES, sku_code, additional_params = additional_params)
    return status

def validate_sku_details(status, cell_data, key, available_sku_groups = {},uoms_list = [], seller_master_dict = {}, enable_seller = 'false'):
    if key == "SKU Group":
        status = validate_sku_group_data(cell_data, status, available_sku_groups)
    elif key == 'Scan Status(Options: Scannable, Non-Scannable)':
        status = validate_scan_status(cell_data, status)
    elif key == 'UOM':
        status = validate_sku_uom_data(cell_data, status, uoms_list)
    elif key == 'Receipt Tolerance':
        status = validate_receipt_tolerance_value(cell_data, status)
    elif key == "Seller Id":
        message, cell_data = validate_seller_mater_id(cell_data, seller_master_dict, enable_seller)
        update_status(status, message)
    return status

def get_available_stock_data(data_to_integrate, user):
    sku_code_list = [row_data.get(SKU_CODE) for row_data in data_to_integrate]
    stock_data_dict = {}
    stock_data = StockDetail.objects.filter(sku__user=user.id, sku__sku_code__in=sku_code_list,quantity__gt = 0).values('sku__sku_code', 'sku__batch_based')
    for data in stock_data:
        stock_data_dict[str(data['sku__sku_code']).lower()] = "enable" if data['sku__batch_based'] else "disable"
    return stock_data_dict

def get_extra_params(extra_params):
    if extra_params is None:
        extra_params = {}
    return extra_params

def get_sku_inv_group_details(data_to_integrate):
    input_sku_codes_inv_group_data, sku_code_list = {}, []
    for row_data in data_to_integrate:
        if row_data.get('Invoice Group'):
            input_sku_codes_inv_group_data[str(row_data.get(SKU_CODE))] = row_data.get('Invoice Group')
        sku_code_list.append(row_data.get(SKU_CODE))
    return input_sku_codes_inv_group_data, sku_code_list

def get_existing_invoice_group_details(user, input_sku_inv_group_data):
    sku_inv_group_data = dict(SKUMaster.objects.filter(user=user.id, sku_code__in=input_sku_inv_group_data).values_list('sku_code', 'invoice_group'))
    update_inv_group_data = {}
    for sku_code, inv_group in sku_inv_group_data.items():
        if inv_group != input_sku_inv_group_data.get(sku_code):
            update_inv_group_data[sku_code] = inv_group
    return update_inv_group_data

def update_status(status, message):
    #Update the status list with the message
    if message:
        status.append(message)

#SKU Upload Validation
def validate_sku_form(user, data_to_integrate, extra_params=None):
    log.info('SKU Master Upload Validation Started for %s' % user.username)
    extra_params = get_extra_params(extra_params)
    upload_file_skus = []
    error_status = False
    error_data_list = []
    #Get all the available product types, hsn codes, uoms and zones
    product_types = get_available_product_types(user)
    available_hsn_codes = get_available_hsn_codes(user)
    uoms_list = get_available_uoms(user)
    zones_list, _ = extra_params.get('zones_data', ([], []))
    attributes_dict = extra_params.get('attributes', {})
    available_sku_groups = get_available_sku_groups(user)
    available_invoice_groups = get_available_invoice_groups(user)
    available_stock_data = get_available_stock_data(data_to_integrate, user)
    input_sku_codes_inv_group_data, sku_codes_list = get_sku_inv_group_details(data_to_integrate)
    existing_invoice_group_details = get_existing_invoice_group_details(user, input_sku_codes_inv_group_data)
    open_orders_sku_list = get_open_orders_sku_list(user, existing_invoice_group_details) if existing_invoice_group_details else []
    sku_serial = get_multiple_misc_values(['sku_serialisation', 'enable_seller'], user.id)
    enable_seller = sku_serial.get('enable_seller', 'false')
    seller_master_dict = {}
    seller_master_dict= fetch_seller_master_data(enable_seller, user, data_to_integrate, seller_master_dict, source='upload')
    extra_params['seller_master_dict'] = seller_master_dict
    found, not_found = get_open_sku_stock_details(sku_codes_list, user.id)
    data = SKUMaster.objects.filter(sku_code__in=sku_codes_list, user=user.id).values_list('sku_code','enable_serial_based')
    
    for row_data in data_to_integrate:
        sku_code , hsn_code  = '', ''
        self_life , customer_self_life, minimum_shelf_life = 0, 0, 0
        min_norm_quantity = 0
        status = []
        for key, value in row_data.items():
            cell_data = value
            key = extract_key(key)
            if key[-1] == '*' and key[:-1] in attributes_dict:
                key = key[:-1]
            if key == SKU_CODE:
                status, upload_file_skus, sku_code = validate_sku_code_data(status, upload_file_skus, cell_data)
                
            elif key == 'Put Zone':
                status = validate_sku_put_zone_data(cell_data, status, zones_list)

            elif key == 'EAN Number':
                status = validate_sku_ean_number(cell_data, status, user)
                
            elif key == 'HSN Code':
                status, hsn_code = validate_hsn_code(cell_data, status)
                
            elif key == 'Tax Name':
                status = validate_tax_type(cell_data, status, product_types, hsn_code, available_hsn_codes)
                
            elif key == 'Min Norm Quantity':
                status, min_norm_quantity = validate_min_norm_quantity(cell_data, status)
                
            elif key == 'Max Norm Quantity':
                status = validate_max_norm_quantity(cell_data, status, min_norm_quantity)
                
            elif key in [SELLING_PRICE,'Sequence', 'GL Code', 'Length', 'Breadth', 'Height', 'Weight', 'Cost Price', 'MRP Price']:
                status = validate_numeric_values(cell_data, status, key)
               
            elif key == 'Mix SKU Attribute(Options: No Mix, Mix within Group)':
                status = validate_mix_sku_attribute(cell_data, status)
                
            elif key in [
                'Hot Release', 'Batch Based(Options: Enable, Disable)', 
                'Serialized(Options: Enable, Disable)','Load Unit Handling(Options: Enable, Disable)',
                'Pick and Sort(Options: Enable, Disable)',  'Mandate Scan(Options: Enable, Disable)',
                DISPENSING, BLOCK_FOR_PO
            ]:
                found_sku = {sku_code: found.get(sku_code)} if sku_code in found else {}
                status = validate_multiple_config_values(cell_data, status, key, sku_code, additional_params = available_stock_data, sku_serial=sku_serial, found=found_sku, existing_data=data)

            elif key == 'QC Required(Options: Yes, No)':
                status = validate_qc_field_value(cell_data, status)

            elif key == 'Barcoding Required(Options: Yes, No)':
                status = validate_barcoding_required_field_value(cell_data, status)

            elif key == 'Invoice Group':
                status = validate_sku_invoice_group(cell_data, status, available_invoice_groups, open_orders_sku_list, existing_invoice_group_details, sku_code)
               
            elif key == 'Customer Shelf Life':
                status, customer_self_life = validate_shelf_life(cell_data, status, key)
                
            elif key == 'Minimum Shelf Life':
                status, minimum_shelf_life = validate_shelf_life(cell_data, status, key)
               
            elif key == PRODUCT_SHELF_LIFE:
                status, self_life = validate_shelf_life(cell_data, status, key)
            
            elif key in attributes_dict:
                status = validate_sku_attributes_data(key, cell_data, attributes_dict[key], status)

            status = validate_sku_details(
                status, cell_data, key, available_sku_groups = available_sku_groups, uoms_list= uoms_list, 
                seller_master_dict=seller_master_dict, enable_seller=enable_seller
            )
                      
        status =  validate_shelf_lives(self_life, customer_self_life, minimum_shelf_life, status)
        
        if status:
            error_status = True
            row_data['error_status'] = status
        error_data_list.append(row_data)
    if not error_status:
        return 'Success'
    else:
        return error_data_list

def get_zone_data(user):
    zone_master = ZoneMaster.objects.filter(user=user.id).values('id', 'zone')
    zones = [str(d['zone']).upper() for d in zone_master]
    zone_ids = [str(d['id']).upper() for d in zone_master]
    return zones, zone_ids

def get_supplier_data(user, filters = {}, values = None):
    # Fetching Supplier Master data
    if not values:
        values = ['supplier_id', 'id']
    supplier_data = dict(SupplierMaster.objects.filter(user=user.id, **filters, status=1).values_list(*values))
    return supplier_data

def get_seller_from_request(request, source):
    # defining the correct key based on the source
    key = "Seller Id" if source == "upload" else "seller_id"
    # Extract seller names from request
    seller_data = [data.get(key) for data in request if data.get(key, '') is not None]

    return {'supplier_id__in': seller_data}


def get_existing_ean_details(user):
    exist_sku_eans = dict(
        SKUMaster.objects.filter(user=user.id, status=1).exclude(ean_number='').\
        only('ean_number', 'sku_code').values_list('ean_number', 'sku_code')
    )
    exist_ean_list = dict(
        EANNumbers.objects.filter(sku__user=user.id, sku__status=1).\
        only('ean_number', 'sku__sku_code').values_list('ean_number', 'sku__sku_code')
    )
    return exist_sku_eans, exist_ean_list

def update_sku_code(user, data_dict, each_row):
    sku_code = each_row.get(SKU_CODE)
    if isinstance(sku_code, (int, float)):
        sku_code = int(sku_code)

    wms_code = str(sku_code).strip()
    data_dict['sku_code'] = wms_code
    data_dict['wms_code'] = wms_code
    if wms_code:
        sku_data = SKUMaster.objects.filter(user=user.id, sku_code__iexact=wms_code).first()
    
    return sku_data


def update_put_zone(each_row, zones, zone_ids, sku_data, data_dict):
    zone_id = None
    put_zone = each_row.get('Put Zone')
    if put_zone:
        put_zone = put_zone.upper()
        if put_zone in zones:
            zone_id = zone_ids[list(zones).index(put_zone)]
        if sku_data and put_zone:
            sku_data.zone_id = zone_id
        data_dict['zone_id'] = zone_id
    return sku_data


def update_seller_name(each_row, sku_data, data_dict, seller_master_dict):
    #update seller master id in sku data
    seller_id = each_row.get('Seller Id')
    if seller_id:
        seller_id = seller_master_dict.get(seller_id)
        data_dict['seller_id'] = seller_id
        if sku_data:
            sku_data.seller_id = seller_id
    return sku_data

def update_sku_status(each_row, sku_data, data_dict):
    sku_status = each_row.get('Status')
    if sku_status.lower() == 'inactive':
        status = 0
    else:
        status = 1
    if sku_data and sku_status:
        setattr(sku_data, 'status', status)
    data_dict['status'] = status
    return sku_data

def update_sku_type(each_row, sku_type_list, sku_data, data_dict):
    sku_type = each_row.get('SKU Type(Options: FG, RM, Expense, WIP,Consumables,Spare Parts)')
    if sku_type and sku_type in sku_type_list:
        if sku_data:
            setattr(sku_data, 'sku_type', sku_type)
        data_dict['sku_type'] = sku_type
    
    return sku_data

def update_invoice_group(each_row, sku_invoice_groups, sku_data, data_dict):
    sku_type = each_row.get('Invoice Group')
    if sku_type and sku_type in sku_invoice_groups:
        if sku_data:
            setattr(sku_data, 'invoice_group', sku_type)
        data_dict['invoice_group'] = sku_type
    return sku_data

def update_make_or_buy(each_row, sku_data, data_dict):
    make_or_buy = each_row.get('Make Or Buy')
    if make_or_buy:
        choice = make_or_buy.upper()
    if sku_data and make_or_buy:
        sku_data.make_or_buy = POChoices[choice]
    elif sku_data and not make_or_buy:
        choice = POChoices.labels[sku_data.make_or_buy]
        choice = choice.upper()
    elif not sku_data and not make_or_buy:
        choice = 'DEFAULT'
    if not choice:
        choice = 'DEFAULT'
    make_or_buy = POChoices[choice]
    data_dict['make_or_buy'] = make_or_buy
    return sku_data

def update_sku_desc(each_row, sku_data, data_dict):
    sku_desc = each_row.get('SKU Description')
    if isinstance(sku_desc, (int, float)):
        sku_desc = int(sku_desc)
    if sku_data and sku_desc:
        setattr(sku_data, 'sku_desc', sku_desc)
    data_dict['sku_desc'] = sku_desc
    return sku_data

def update_sku_category(each_row, sku_data, data_dict):
    sku_category = each_row.get('SKU Category')
    if sku_category and isinstance(sku_category, (int, float)):
        sku_category = str(int(sku_category))
    if sku_data and sku_category:
        setattr(sku_data, 'sku_category', sku_category)
    data_dict['sku_category'] = sku_category
    return sku_data

def update_sku_sub_category(each_row, sku_data, data_dict):
    sku_sub_category = each_row.get('Sub Category')
    if sku_sub_category and isinstance(sku_sub_category, (int, float)):
        sku_sub_category = str(int(sku_sub_category))
    if sku_data and sku_sub_category:
        setattr(sku_data, 'sub_category', sku_sub_category.upper())
    data_dict['sub_category'] = sku_sub_category.upper()
    return sku_data

def update_sku_size(each_row, sku_data, data_dict):
    cell_data = each_row.get('SKU Size')
    try:
        cell_data = str(int(cell_data))
    except Exception:
        cell_data = str(cell_data)
    if sku_data and cell_data:
        setattr(sku_data, 'sku_size', cell_data)
    data_dict['sku_size'] = cell_data
    return sku_data

def update_sku_quantity_fields(each_row, sku_data, data_dict):
    quantity_fields = {
        'Min Norm Quantity': 'threshold_quantity', 'Max Norm Quantity': 'max_norm_quantity',
        'Selling Price': 'price', 'Cost Price': 'cost_price', 'MRP Price': 'mrp',
        'Receipt Tolerance': 'receipt_tolerance'
    }
    for key, value in quantity_fields.items():
        cell_data = each_row.get(key)
        if not cell_data:
            cell_data = 0
        if sku_data and cell_data:
            setattr(sku_data, value, cell_data)
        data_dict[value] = cell_data
    return sku_data

def update_shelf_life(each_row, sku_data, data_dict):
    shelf_life_fields = {
        PRODUCT_SHELF_LIFE : 'shelf_life', 'Customer Shelf Life': 'customer_shelf_life',
        'Minimum Shelf Life': 'minimum_shelf_life'
    }
    for key, value in shelf_life_fields.items():
        cell_data = each_row.get(key)
        if key == 'Product Shelf Life':
            if str(cell_data):
                cell_data = float(cell_data)
            if sku_data and str(cell_data):
                setattr(sku_data, value, cell_data)
            elif sku_data and not str(cell_data):
                cell_data = getattr(sku_data, value)
            elif not str(cell_data):
                cell_data = 0
            data_dict[value] = cell_data
        else:
            if str(cell_data):
                cell_data = timedelta(float(cell_data))
            if sku_data and str(cell_data):
                setattr(sku_data, value, cell_data)
            elif sku_data and not str(cell_data):
                if key == "Customer Shelf Life":
                    cell_data = sku_data.customer_shelf_life
                else:
                    cell_data = sku_data.minimum_shelf_life
            elif not str(cell_data):
                cell_data = timedelta(0)
            data_dict[value] = cell_data

    return sku_data

def update_sku_dimensions(each_row, sku_data, data_dict):
    attributes_dict = {
        'UOM': 'measurement_type', 'Image Url': 'image_url', 'Length': 'length', 
        'Breadth': 'breadth', 'Height': 'height',
        'Weight': 'weight', 'Color': 'color', 'SKU Class': 'sku_class', 
        'Style Name': 'style_name', 'SKU Reference': 'sku_reference'
    }
    for key, value in attributes_dict.items():
        cell_data = each_row.get(key)
        if cell_data:
            if sku_data:
                setattr(sku_data, value, cell_data)
            data_dict[value] = cell_data
    return sku_data

def update_sku_mix_attribute(each_row, sku_data, data_dict):
    mix_sku = each_row.get('Mix SKU Attribute(Options: No Mix, Mix within Group', '')
    if mix_sku:
        mix_sku = MIX_SKU_MAPPING[mix_sku.lower()]
    if sku_data and mix_sku:
        setattr(sku_data, 'mix_sku', mix_sku)
    data_dict['mix_sku'] = mix_sku
    return sku_data

def update_qc(each_row, sku_data, data_dict):
    qc_update = each_row.get('QC Required(Options: Yes, No)')
    if qc_update:
        if str(qc_update).lower() == 'yes':
            qc_update = True
        else:       
            qc_update = False
        data_dict['qc_eligible'] = qc_update
        if sku_data:
            setattr(sku_data,'qc_eligible', qc_update)
    return sku_data

def update_barcoding_required(each_row, sku_data, data_dict):
    barcode_update = each_row.get('Barcoding Required(Options: Yes, No)')
    if barcode_update:
        if str(barcode_update).lower() == 'yes':
            barcode_update = True
        else:
            barcode_update = False
        data_dict['is_barcode_required'] = barcode_update
        if sku_data:
            setattr(sku_data,'is_barcode_required', barcode_update)
    return sku_data

def validate_mandate_scan_flag(each_row, sku_data, data_dict):
    mandate_scan = each_row.get('Mandate Scan(Options: Enable, Disable)')
    if mandate_scan:
        if str(mandate_scan).lower() == 'enable':
            mandate_scan = True
        else:
            mandate_scan = False
        data_dict['mandate_scan'] = mandate_scan
        if sku_data:
            setattr(sku_data,'mandate_scan', mandate_scan)
    return sku_data

def update_serial_batch_based(each_row, sku_data, data_dict):
    enable_dict = {
        'Serialized(Options: Enable, Disable)': 'enable_serial_based',
        'Batch Based(Options: Enable, Disable)': 'batch_based',
        'Pick and Sort(Options: Enable, Disable)': 'pick_and_sort',
    }
    for key, value in enable_dict.items():
        cell_data = each_row.get(key)
        toggle_value = str(cell_data).lower()
        if toggle_value == "enable":
            cell_data = 1
        if toggle_value == "disable":
            cell_data = 0
        if not toggle_value:
            cell_data = 0
        if toggle_value and sku_data:
            setattr(sku_data, value, cell_data)
        data_dict[value] = cell_data
    
    return sku_data

def update_dispense_check(each_row, sku_data, data_dict):
    cell_data = each_row.get('Dispensing Enabled(Options: true, false)', 'false')
    saved_value = str(cell_data).lower()
    if saved_value == "true":
        cell_data = 1
    else:
        cell_data = 0
    data_dict['dispensing_enabled'] = cell_data
    if saved_value and sku_data:
        setattr(sku_data, 'dispensing_enabled', cell_data)
    return sku_data

def update_load_unit_handling(each_row, sku_data, data_dict):
    cell_data = each_row.get('Load Unit Handling(Options: Enable, Disable)')
    cell_data = LOAD_UNIT_HANDLE_DICT[cell_data.lower()] if cell_data else "unit"
    if sku_data and cell_data:
        setattr(sku_data, 'load_unit_handle', cell_data)
    data_dict['load_unit_handle'] = cell_data
    return sku_data

def update_tax_name(each_row, sku_data, data_dict, available_hsn_codes):
    cell_data = each_row.get('Tax Name')
    if available_hsn_codes and cell_data and data_dict.get('hsn_code'):
        product_type = available_hsn_codes.get(str(data_dict.get('hsn_code',0)),'')
        data_dict['product_type'] = product_type
        if sku_data:
            setattr(sku_data, 'product_type', product_type)
    else:
        if cell_data:
            data_dict['product_type'] = cell_data
            if sku_data:
                setattr(sku_data, 'product_type', cell_data)
    return sku_data

def update_ean_number(each_row):
    ean_numbers = []
    cell_data = each_row.get('EAN Number')
    if cell_data:
        if ',' in str(cell_data):
            ean_numbers = [x.strip() for x in str(cell_data).split(',')]
        else:
            if isinstance(cell_data, (str, int, float)):
                cell_data = str(cell_data)
            ean_numbers = [str(cell_data)]
    return ean_numbers

def update_block_for_po(each_row, sku_data, data_dict):
    cell_data = each_row.get('Block For PO')
    if cell_data:
        if str(cell_data).lower() == 'yes':
            cell_data = 'PO'
        if str(cell_data).lower() in ['no', '']:
            cell_data = ''
        if sku_data:
            setattr(sku_data, 'block_options', cell_data)
        data_dict['block_options'] = cell_data
    return sku_data

def update_gl_code(each_row, sku_data, data_dict):
    cell_data = each_row.get('GL Code')
    if cell_data:
        try:
            cell_data = int(cell_data)
        except ValueError:
            cell_data = 0
        if sku_data:
            setattr(sku_data, 'gl_code', cell_data)
        data_dict['gl_code'] = cell_data

    return sku_data

def update_hsn_code(each_row, sku_data, data_dict, available_hsn_codes):
    cell_data = each_row.get('HSN Code')
    if cell_data:
        if isinstance(cell_data, (int, float)):
            cell_data = str(int(cell_data))
        cell_data = cell_data.strip()
        data_dict['hsn_code'] = cell_data
        data_dict['product_type']= available_hsn_codes.get(int(data_dict.get('hsn_code',0)),'')
        if sku_data:
            setattr(sku_data, 'hsn_code', cell_data)
            setattr(sku_data, 'product_type', available_hsn_codes.get(int(data_dict.get('hsn_code',0)),''))

    return sku_data

def update_scan_status(each_row, sku_data, data_dict):
    cell_data = each_row.get('Scan Status(Options: Scannable, Non-Scannable)')
    if cell_data and cell_data.lower() in SCAN_OPTIONS:
        cell_data = SCAN_OPTIONS[cell_data.lower()]
        data_dict['scan_picking'] = cell_data
        if sku_data:
            setattr(sku_data, 'scan_picking', cell_data)
    return sku_data

def update_sku_brand(each_row, sku_data, data_dict):
    """Create or Update SKU Brand """
    sku_brand = each_row.get('SKU Brand')
    if sku_brand and isinstance(sku_brand, (int, float)):
        sku_brand = str(int(sku_brand))
    if sku_data and sku_brand:
        setattr(sku_data, 'sku_brand', sku_brand)
    data_dict['sku_brand'] = sku_brand
    return sku_data

def create_or_update_skus(user, each_row, zones, zone_ids, sku_type_list, available_hsn_codes, sku_invoice_groups, seller_master_dict):
    data_dict = copy.deepcopy(SKU_DATA)
    data_dict['user'] = user.id
    sku_data=  None
    #Update SKU Code
    sku_data = update_sku_code(user, data_dict, each_row)

    #Update SKU Description
    sku_data = update_sku_desc(each_row, sku_data, data_dict)

    #Update SKU Category
    sku_data = update_sku_category(each_row, sku_data, data_dict)

    #Update Sub Category
    sku_data = update_sku_sub_category(each_row, sku_data, data_dict)

    #Update SKU Brand
    sku_data = update_sku_brand(each_row, sku_data, data_dict)

    #Update SKU Size
    sku_data = update_sku_size(each_row, sku_data, data_dict)

    #Update SKU Type
    sku_data = update_sku_type(each_row, sku_type_list, sku_data, data_dict)

    #Update Invoice Types
    sku_data = update_invoice_group(each_row, sku_invoice_groups, sku_data, data_dict)

    #Update SKU Status
    sku_data = update_sku_status(each_row, sku_data, data_dict)

    #Update Put Zone
    sku_data = update_put_zone(each_row, zones, zone_ids, sku_data, data_dict)

    #update Seller Name
    sku_data = update_seller_name(each_row, sku_data, data_dict, seller_master_dict)

    #Update Make or Buy
    sku_data = update_make_or_buy(each_row, sku_data, data_dict)

    #Update All Quantity Fields
    sku_data = update_sku_quantity_fields(each_row, sku_data, data_dict)

    #Update Shelf Life
    sku_data = update_shelf_life(each_row, sku_data, data_dict)

    #Update Dimension
    sku_data = update_sku_dimensions(each_row, sku_data, data_dict)

    #Update Mix SKU Attribute
    sku_data = update_sku_mix_attribute(each_row, sku_data, data_dict)

    #Update QC Update
    sku_data = update_qc(each_row, sku_data, data_dict)

    #Update Barcoding Required
    sku_data = update_barcoding_required(each_row, sku_data, data_dict)

    #Update Mandate Scan
    sku_data = validate_mandate_scan_flag(each_row, sku_data, data_dict)

    #Update Serial Based and Batch Based
    sku_data = update_serial_batch_based(each_row, sku_data, data_dict)

    #Update Dispense Check
    sku_data = update_dispense_check(each_row, sku_data, data_dict)

    #Update Load Unit Handling
    sku_data = update_load_unit_handling(each_row, sku_data, data_dict)

    #Update HSN Code
    sku_data = update_hsn_code(each_row, sku_data, data_dict, available_hsn_codes)

    #Update Tax Name
    sku_data = update_tax_name(each_row, sku_data, data_dict, available_hsn_codes)

    #Update EAN Number
    ean_numbers = update_ean_number(each_row)

    #Update Block For PO
    sku_data = update_block_for_po(each_row, sku_data, data_dict)

    #Update GL Code
    sku_data = update_gl_code(each_row, sku_data, data_dict)

    sku_data = update_scan_status(each_row, sku_data, data_dict)

    return sku_data, data_dict, ean_numbers

def check_and_create_skus(user, new_skus, exist_ean_list, exist_sku_eans, all_sku_masters):
    if new_skus:
        new_ean_objs = []
        new_sku_objs =  [sku_dict['sku_obj'] for sku_dict in new_skus.values()]

        #SKU Bulk Creation
        SKUMaster.objects.bulk_create_with_rounding(new_sku_objs)
        new_sku_master = SKUMaster.objects.filter(user=user.id, sku_code__in=new_skus.keys())
        all_sku_masters = list(chain(all_sku_masters, new_sku_master))
        sku_key_map = OrderedDict(new_sku_master.values_list('sku_code', 'id'))

        for sku_code, sku_id in sku_key_map.items():
            sku_data = SKUMaster.objects.get(id=sku_id)
            if new_skus[sku_code].get('ean_numbers', ''):
                ean_numbers = new_skus[sku_code].get('ean_numbers', '')
                sku_data, new_ean_objs, update_sku_obj = prepare_ean_bulk_data(
                    sku_data, ean_numbers, exist_ean_list,
                    exist_sku_eans, new_ean_objs=new_ean_objs
                )
                log.info(("EAN Numbers for SKU %s are %s SKU Obj %s" % (sku_code, str(ean_numbers), str(update_sku_obj))))
        if new_ean_objs:
            EANNumbers.objects.bulk_create_with_rounding(new_ean_objs)
    return all_sku_masters

#SKU upload Insertion
def sku_excel_upload(request, user, data_list, extra_params={}):
    log.info("SKU Master Excel Upload for data list %s, extra params %s and user %s" % (str(data_list), str(extra_params), str(user.username)))

    all_sku_masters, new_skus = [], OrderedDict()
    #Existing Zone Info
    zones, zone_ids = extra_params.get('zones_data', ([], []))

    #SKU Attributes
    attributes_dict = extra_params.get('attributes', {})
    
    #Existing Seller Info
    seller_master_dict = extra_params.get('seller_master_dict', {})
    
    #Available HSN Codes
    available_hsn_codes = dict(
        HSNMaster.objects.filter(warehouse = user.id).values_list('hsn_code','product_type')
    )

    #Existing EAN Details
    exist_sku_eans, exist_ean_list = get_existing_ean_details(user)

    #SKU Type Misc Values
    sku_type_list, sku_invoice_groups, _ = get_required_misc_values(user)

    for row_data in data_list:

        sku_data, data_dict, ean_numbers = create_or_update_skus(
            user, row_data, zones, zone_ids, sku_type_list, available_hsn_codes, sku_invoice_groups, seller_master_dict
        )
        if sku_data:
            if sku_data.json_data:
                sku_data.json_data['updated_by']=request.username
            else:
                sku_data.json_data = {'updated_by': request.username}
            sku_data.save()

            update_volume(sku_data)
            all_sku_masters.append(sku_data)

            if ean_numbers:
                update_ean_sku_mapping(user, ean_numbers, sku_data, remove_existing=True)

        if not sku_data:
            data_dict['sku_code'] = data_dict['wms_code']
            data_dict['account_id'] = user.userprofile.id
            data_dict = save_json_data(request,data_dict)    
            sku_master = SKUMaster(**data_dict)
            new_skus[data_dict['sku_code']] = {'sku_obj': sku_master}
            if ean_numbers:
                new_skus[data_dict['sku_code']]['ean_numbers'] = ean_numbers

    #Create SKUs and EANs
    all_sku_masters = check_and_create_skus(user, new_skus, exist_ean_list, exist_sku_eans, all_sku_masters)
    create_or_update_sku_attributes(user, data_list, all_sku_masters, attributes_dict)
    return 'success'

def create_or_update_sku_attributes(user, data_list, all_sku_masters, attributes_dict):
    sku_data = {sku.sku_code : sku.id for sku in all_sku_masters}
    sku_attrs_data = {}
    sku_attrs = SKUAttributes.objects.select_related('sku').filter(sku_id__in=sku_data.values())
    sku_attrs_update_objs, sku_attrs_create_objs = [], []
    for sku_attr in sku_attrs:
        sku_code = sku_attr.sku.sku_code
        attribute_name = sku_attr.attribute_name
        sku_attrs_data[(attribute_name, sku_code)] = sku_attr
    
    for row_data in data_list:
        for key, value in row_data.items():
            sku_code = row_data.get(SKU_CODE)
            if isinstance(sku_code, (int, float)):
                sku_code = int(sku_code)

            wms_code = str(sku_code).strip()
            if key[-1] == '*' and key[:-1] in attributes_dict:
                key = key[:-1]
            if key not in attributes_dict or value == '' or wms_code not in sku_data:
                continue
            if (key, wms_code) in sku_attrs_data:
                sku_attr = sku_attrs_data[(key, wms_code)]
                sku_attr.attribute_value = value
                sku_attr.updation_date = datetime.datetime.now()
                sku_attrs_update_objs.append(sku_attr)
            else:
                sku_id = sku_data[wms_code]
                sku_attr = SKUAttributes(**{
                    'sku_id': sku_id, 'attribute_name': key, 'attribute_value': value,
                    'account_id': user.userprofile.id  
                })
                sku_attrs_create_objs.append(sku_attr)
    
    if sku_attrs_update_objs:
        SKUAttributes.objects.bulk_update_with_rounding(sku_attrs_update_objs, ['attribute_value', 'updation_date'])
    if sku_attrs_create_objs:
        SKUAttributes.objects.bulk_create_with_rounding(sku_attrs_create_objs)
    
def update_volume(obj):
    try:
        obj.volume = float(obj.length) * float(obj.breadth) * float(obj.height)
        obj.save()
    except Exception:
        pass

def check_update_size_type(data, value):
    NOW = datetime.datetime.now()
    sku_fields = SKUFields.objects.filter(sku_id=data.id, field_type='size_type')
    size_master = SizeMaster.objects.filter(user=data.user, size_name=value)
    if not size_master:
        return
    size_master = size_master[0]
    _value = size_master.size_name
    if not sku_fields:
        SKUFields.objects.create(sku_id=data.id, field_id=size_master.id, field_type='size_type', field_value=_value,
                                 creation_date=NOW, account_id=data.user.userprofile.id)
    else:
        sku_fields[0].field_value = _value
        sku_fields[0].field_id = size_master.id
        sku_fields[0].save()

def check_update_hot_release(data, value):
    NOW = datetime.datetime.now()
    sku_fields = SKUFields.objects.filter(sku_id=data.id, field_type='hot_release')
    if not sku_fields:
        SKUFields.objects.create(sku_id=data.id, field_type='hot_release', field_value=value,
                                 creation_date=NOW, account_id=data.user.userprofile.id)
    else:
        if sku_fields[0].field_value != value:
            sku_fields[0].field_value = value
            sku_fields[0].save()

def prepare_ean_bulk_data(sku_master, ean_numbers, exist_ean_list, exist_sku_eans, new_ean_objs=''):
    update_sku_obj = False
    try:
        exist_eans = list(sku_master.eannumbers_set.exclude(ean_number='').\
                      values_list('ean_number', flat=True))
        
        rem_eans = set(exist_eans) - set(ean_numbers)
        create_eans = set(ean_numbers) - set(exist_eans)
        if rem_eans:
            rem_ean_objs = sku_master.eannumbers_set.filter(ean_number__in=rem_eans)
            if rem_ean_objs.exists():
                rem_ean_objs.delete()
            for rem_ean in rem_eans:
                if exist_ean_list.get(rem_ean, ''):
                    del exist_ean_list[rem_ean]
                if exist_sku_eans.get(rem_ean, ''):
                    del exist_sku_eans[rem_ean]
        if str(sku_master.ean_number) in rem_eans:
            sku_master.ean_number = ''
            update_sku_obj = True
            #sku_master.save()
        for ean in create_eans:
            if not ean:
                continue
            try:
                sku_ean_check= EANNumbers.objects.filter(**{'ean_number': ean, 'sku_id': sku_master.id}, sku__user = sku_master.user)
                if not sku_ean_check:
                    new_ean_objs.append(EANNumbers(**{'ean_number': ean, 'sku_id': sku_master.id, 'account_id': sku_master.user}))
                    ean_found = False
                    if exist_ean_list.get(ean, ''):
                        exist_ean_list[ean] = sku_master.sku_code
                        ean_found = True
                    elif exist_sku_eans.get(ean, ''):
                        exist_sku_eans[ean] = sku_master.sku_code
                        ean_found = True
                    if not ean_found:
                        exist_ean_list[ean] = sku_master.sku_code
            except Exception:
                pass
        #update_ean_sku_mapping(user, ean_numbers, sku_master, True)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(e)
    return sku_master, new_ean_objs, update_sku_obj

def update_ean_sku_mapping(warehouse: User, ean_numbers, data, remove_existing=False):
    ean_status = 'Success'
    exist_ean_list = list(data.eannumbers_set.filter().annotate(str_eans=Cast('ean_number', CharField())).\
                          values_list('str_eans', flat=True))
    rem_ean_list = []
    if remove_existing:
        rem_ean_list = list(set(exist_ean_list) - set(ean_numbers))
    for ean_number in ean_numbers:
        if ean_number :
            ean_dict = {'ean_number': ean_number, 'sku_id': data.id}
            sku_ean_check= EANNumbers.objects.filter(**ean_dict,sku__user = warehouse.id)
            if not sku_ean_check:
                ean_dict.update({"account_id": warehouse.userprofile.id})
                EANNumbers.objects.create(**ean_dict)
    for rem_ean in rem_ean_list:
        if str(data.ean_number) == str(rem_ean):
            data.ean_number = ''
            data.save()
        else:
            EANNumbers.objects.filter(sku_id=data.id, ean_number=rem_ean).delete()
    return ean_status

def save_json_data(user,data_dict):
    try:
        if data_dict['json_data']:
            data = data_dict['json_data']
            if data.get('created_by',''):
                data['updated_by'] = user.username
            else:
                data['created_by'] = user.username
        else:
            data_dict['json_data']={'created_by':user.username}
    except Exception:
        data_dict['json_data']={'created_by':user.username}
    return data_dict

def all_size_list(user):
    all_sizes = []
    size_objs = SizeMaster.objects.filter(user=user.id)
    if size_objs:
        sizes_items = size_objs.values_list('size_value', flat=True)
        for sizes in sizes_items:
            all_sizes.extend(sizes.split('<<>>'))

    all_sizes = list(set(all_sizes))

    return all_sizes

def sort_get_skus(model, sort_option):
    order_by = 'creation_date'
    sort_check = sort_option.split('-')
    sort_check = sort_check[-1]
    if sort_check in model:
        order_by = sort_option
    return order_by

def validate_sku_serialisation(isSerialise, sku_serial, found=None):
    status = ''
    sku_prefix_code = sku_serial.get('sku_serialisation', 'false')
    if isSerialise and (not sku_prefix_code or sku_prefix_code == 'false'):
       status = 'Inventory Serialisation is not enabled'
    elif found:
        status = f"SKU has open transaction, can't change serial flag"
    return status

def get_open_sku_stock_details(skus, warehouse_id):
    found = {}
    not_found = set(skus)

    asn_sku_numbers = ASNSummary.objects.filter(
        asn_user_id=warehouse_id,
        purchase_order__open_po__sku__sku_code__in=skus,
        quantity__gt=0, status__in=[0,1,2,5,6]
    ).values_list('purchase_order__open_po__sku__sku_code', 'asn_number')

    asn_mapping = {sku_code: asn_number for sku_code, asn_number in asn_sku_numbers}

    for sku, asn_number in asn_mapping.items():
        found[sku] = asn_number
        not_found.discard(sku)

    remaining_skus = not_found.copy()

    if remaining_skus:
        # Query SellerPOSummary for SKUs without ASN
        sellerpo_sku_numbers = SellerPOSummary.objects.filter(
            user_id=warehouse_id,
            sku__sku_code__in=remaining_skus,
            quantity__gt=0
        ).exclude(status=9).values_list('sku__sku_code', 'grn_number')

        sellerpo_mapping = {sku_code: grn_number for sku_code, grn_number in sellerpo_sku_numbers}

        for sku, grn_number in sellerpo_mapping.items():
            found[sku] = grn_number
            not_found.discard(sku)

    return found, list(not_found)



class ItemsDetailsSet(WMSListView):
    #products api
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None

    def search_params_preparation(self, request_data, sku_model, search_params):
        for key, value in request_data.items():
            if key in sku_model:
                if type(request_data[key]) == list:
                    search_params[key + '__in'] = request_data[key]
                else:
                    search_params[key] = request_data[key]
        return search_params

    def prepare_attributes_ids(self, user, request_data, search_params):
        attr_list = []
        attributes = get_user_attributes(user, 'sku')
        if attributes:
            attr_list = list(attributes.values_list('attribute_name', flat=True))
        if attr_list:
            attr_filter_ids = []
            attr_found = False
            for key, value in request_data.items():
                if key in attr_list:
                    attr_found = True
                    attr_ids = SKUAttributes.objects.filter(sku__user=user.id, attribute_name=key,
                                                            attribute_value=value). \
                        values_list('sku_id', flat=True)
                    if attr_filter_ids:
                        attr_filter_ids = list(set(attr_filter_ids) & set(attr_ids))
                    else:
                        attr_filter_ids = attr_ids
            if attr_found:
                search_params['id__in'] = attr_filter_ids
        return search_params

    # Helper function to parse date or return error
    def parse_date(self, date_string):
        try:
            return parser.parse(date_string)
        except Exception:
            return None

    def filter_by_date(self, search_params, field_name, date_string):
        parsed_date = self.parse_date(date_string)
        if parsed_date:
            parsed_date = timezone.make_aware(parsed_date, timezone.utc)
            if field_name == 'updation_date' or field_name == 'updation_date_gte':
                parsed_date += timedelta(days=1)
                search_params["updation_date__lte"] = parsed_date
            else:
                search_params[f"{field_name}__gte"] = parsed_date
        else:
            return {'error': [{'message': 'Invalid Date Format'}], 'status': 400}
        return None

    def get(self, *args, **kwargs):
        #get products
        request = self.request
        skus = []
        attr_list, error_status = [], []
        self.set_user_credientials()
        user = self.warehouse
        limit, total_count = 10, 0
        updation_date_gte, creation_date, updation_date = '','',''
        search_params = {'user': user.id}
        order_by = 'creation_date'
        request_data = request.GET
        search_query = Q()
        if request_data:
            updation_date_gte = request_data.get('updation_date_gte','')
            creation_date = request_data.get('from_date', '')
            updation_date = request_data.get('to_date', '')
            if request_data.get('limit'):
                limit = request_data['limit']
            skus = request_data.get('sku_list', [])
            if skus:
                search_params['sku_code__in'] = skus
            date_params = [('creation_date', creation_date), ('updation_date', updation_date), ('updation_date', updation_date_gte)]
            for date_field, date_value in date_params:
                if date_value:
                    error = self.filter_by_date(search_params, date_field, date_value)
                    if error:
                        return error
                else:
                    continue
            if request_data.get('sku_search'):
                search_query = build_search_term_query(['sku_code', 'sku_desc', 'sku_brand', 'sku_category'],
                                                       request_data['sku_search'])
            sku_model = [field.name for field in SKUMaster._meta.get_fields()]
            sku_model.append('creation_date__gt')
            sku_model.append('updation_date__gt')
            if request_data.get('sort_by'):
                order_by = sort_get_skus(sku_model, request_data['sort_by'])

            search_params = self.search_params_preparation(request_data, sku_model, search_params)
            search_params = self.prepare_attributes_ids(user, request_data, search_params)
        sku_records = SKUMaster.objects.filter(search_query, **search_params).order_by(order_by)
        error_skus = set(skus) - set(sku_records.values_list('sku_code', flat=True))
        total_count = sku_records.count()
        for error_sku in error_skus:
            error_status.append({'sku': error_sku, 'message': 'SKU Not found'})
        page_info = scroll_data(request, sku_records, limit=limit, request_type='GET')
        sku_records = page_info['data']
        #sku records preparation
        data = self.get_products_list_preparation(user, sku_records, attr_list)
        page_info['data'] = data
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        page_info['error'] = [{'message': error_status}]
        return page_info

    def validate_seller_master_dict(self, seller_master_dict, misc_dict):
        # check if seller master config is enabled or not
        enabled_sellers = misc_dict.get('enable_seller', 'false')
        if enabled_sellers == 'false':
            return {}
        return seller_master_dict

    def get_products_list_preparation(self, user, sku_records, attr_list):
        data = []
        sku_type_list, sku_invoice_groups, misc_dict = get_required_misc_values(user)
        #tax objects dict preparation
        tax_objs_dict = {}
        tax_objs = TaxMaster.objects.filter(user=user.id)\
                            .values('product_type', 'inter_state', 'cgst_tax', 'sgst_tax', 'igst_tax', 'cess_tax', 'user_id')
        for tax in tax_objs:
            tax_key = (tax['user_id'], tax['product_type'])
            if tax_key not in tax_objs_dict:
                tax_objs_dict[tax_key] = tax
        sku_ids_ = list(sku_records.values_list('id', flat=True))
        sku_attributes = get_custom_sku_attributes_and_values({'sku__user':user.id, 'sku__in' : sku_ids_})
        # fetching seller data from sku data
        seller_master_dict = {}
        seller_ids = [seller_id for seller_id in sku_records.values_list('seller__supplier_id', flat=True) if seller_id is not None]
        seller_master_dict = get_supplier_data(user, {"supplier_id__in":seller_ids}, ['id','supplier_id'])
        seller_master_dict = self.validate_seller_master_dict(seller_master_dict, misc_dict)
        for sku in sku_records:
            tax_unique_key = (user.id, sku.product_type)
            cgst_, sgst_, igst_, cess_, updated = '', '', '', '', ''
            tax_obj_ = tax_objs_dict.get(tax_unique_key, {})
            if tax_obj_:
                tax_ = tax_obj_.get('inter_state', '')
                if not tax_:
                    #inter_tax
                    cgst_ = str(tax_obj_.get('cgst_tax', ''))
                    sgst_ = str(tax_obj_.get('sgst_tax', ''))
                else:
                    #intra_tax
                    igst_ = str(tax_obj_.get('igst_tax', ''))
                    cess_ = str(tax_obj_.get('cess_tax', ''))
            if sku.updation_date:
                updated = sku.updation_date.strftime('%Y-%m-%d %H:%M:%S')
            zone = ''
            if sku.zone:
                zone = sku.zone.zone
            customer_shelf_life = sku.customer_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
            minimum_shelf_life = sku.minimum_shelf_life.total_seconds() / timedelta(days=1).total_seconds()
            sku_type = sku.sku_type if sku.sku_type in sku_type_list else ""
            invoice_group = sku.invoice_group if sku.invoice_group in sku_invoice_groups else ""
            data_dict = OrderedDict((('id', sku.id), ('sku_code', sku.sku_code), ('sku_desc', sku.sku_desc),
                                     ('sku_brand', sku.sku_brand), ('sku_category', sku.sku_category),
                                     ('sku_class', sku.sku_class),
                                     ('sub_category', sku.sub_category),
                                     ('sku_type', sku_type),
                                     ('sku_group', sku.sku_group),
                                     ('sku_size', sku.sku_size),
                                     ('seller_id', seller_master_dict.get(sku.seller_id,'')),
                                     ('style_name', sku.style_name),
                                     ('price', str(sku.price)),
                                     ('mrp', str(sku.mrp)),
                                     ('cost_price', str(sku.cost_price)),
                                     ('product_type', sku.product_type),
                                     ('cgst', cgst_),
                                     ('sgst', sgst_),
                                     ('igst', igst_),
                                     ('cess', cess_),
                                     ('hsn_code', sku.hsn_code),
                                     ('mix_sku', sku.mix_sku),
                                     ('color', sku.color),
                                     ('ean_number', sku.ean_number),
                                     ('zone', zone),
                                     ('threshold_quantity', sku.threshold_quantity),
                                     ('product_shelf_life', sku.shelf_life),
                                     ('customer_shelf_life', customer_shelf_life),
                                     ('minimum_shelf_life', minimum_shelf_life),
                                     ('batch_based', True if sku.batch_based else False),
                                     ('pick_group',  sku.pick_group),
                                     ('measurement_type', sku.measurement_type),
                                     ('image_url', sku.image_url),
                                     ('active', sku.status),
                                     ('scan_picking', sku.scan_picking),
                                     ('pick_and_sort', sku.pick_and_sort),
                                     ('make_or_buy',sku.make_or_buy),
                                     ('receipt_tolerance', sku.receipt_tolerance),
                                     ('created_at', sku.creation_date.strftime('%Y-%m-%d %H:%M:%S')),
                                     ('updated_at', updated),
                                     ('dispensing_enabled', sku.dispensing_enabled),
                                     ('qc_eligible', sku.qc_eligible),
                                     ('is_barcode_required', sku.is_barcode_required),
                                     ('invoice_group', invoice_group),
                                     ('mandate_scan', sku.mandate_scan),
                                     ('serialized', True if sku.enable_serial_based else False),
                                     ('sku_attributes', sku_attributes.get(sku.sku_code, {}))))
                                        
            data.append(data_dict)
        return data

    def post(self, *args, **kwargs):
        request = self.request
        skus, redis_cache_keys = '', set()
        try:
            skus = json.loads(request.body)
        except Exception:
            log.info('Incorrect Request params for ' + request.user.username + ' is ' + str(skus))
            return {'error': [{'message':'Invalid Payload'}], 'status': 400}

        permission = get_permission(request.user, 'add_skumaster')
        if not permission:
            return {'error': [{'message':'No access'}], 'status': 400}
        self.set_user_credientials()
        user = self.warehouse

        log.info('Request params for ' + request.user.username + ' is ' + str(skus))
        try:
            insert_status, failed_status, redis_cache_keys = update_skus(skus, user)
            log.info(insert_status)
            log.info(failed_status)
            if failed_status:
                failed_status = list(failed_status)
                failed_status = failed_status[0]
                return JsonResponse({'error': failed_status},status = 400)
            if not failed_status:
                return JsonResponse({'status': insert_status},status = 200)
            
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Update SKUS data failed for %s and params are %s and error statement is %s' % (
            str(request.user.username), str(request.body), str(e)))
            status = {'status': 500, 'error': [{'message':'Internal Server Error'}]}
            return status
        
        finally:
            clear_redis_cache(redis_cache_keys)

@celery_app.task
def update_skus_bulk(skus_arr=[]):
    """
    Asynchronous Celery task to create & update multiple SKUs in bulk.
    """
    redis_cache_keys = set()
    try:
        for each_sku_data in skus_arr:
            _, _, redis_cache_keys = update_skus(each_sku_data)
    except Exception as e:
        log.error('Error in update_skus_bulk: %s' % str(e))
    
    finally:
        clear_redis_cache(redis_cache_keys)

def update_skus(sku_details, user=''):
    """
    Function to create & update multiple SKUs in bulk.
    """
    redis_cache_keys = set()
    sku_mapping = SKU_MASTER_API_MAPPING
    insert_status = {"skus_created": [], 'skus_updated': []}
    failed_status = OrderedDict()
    create_sku_attrs = defaultdict(set)
    sku_attr_mapping = []
    remove_attr_ids = []
    sku_ean_numbers = {}
    warehouse_id = sku_details.get('warehouse_id')
    if warehouse_id:
        warehouse_obj_dict = get_warehouse_objects([warehouse_id])
        warehouse = warehouse_obj_dict.get(warehouse_id)
        if not user:
            user = warehouse
    try:
        token_user = user
        sister_whs1 = list(get_sister_warehouse(user).values_list('user__username', flat=True))
        if 'warehouse' in sku_details.keys():
            warehouse = sku_details['warehouse']
            sister_whs1.append(token_user.username)
            _, sister_whs2 = get_warehouses_list(user)
            if sister_whs2:
                sister_whs1 += list(sister_whs2.values_list('username', flat=True))
            sister_whs = []
            for sister_wh1 in sister_whs1:
                sister_whs.append(str(sister_wh1).lower())
            if warehouse.lower() in sister_whs:
                user = User.objects.get(username=warehouse)
            else:
                error_message = 'Invalid Warehouse Name'
                log.info(error_message)
                update_error_message(failed_status, 5021, error_message, warehouse, field_key='warehouse')
      
        user_attr_list = get_user_attributes(user, 'sku')
        user_attr_list = list(user_attr_list.values_list('attribute_name', flat=True))
        all_sku_masters = []
        if not sku_details:
            sku_details = {}
        skus = sku_details.get(sku_mapping['skus'], [])
        mysql_file_path = 'static/mysql_files'
        folder_check(mysql_file_path)

        all_sku_codes, tax_masters, gst_tax_masters, inv_group_skus = get_tax_details(user, skus, sku_mapping)
        
        sku_master_objs = SKUMaster.objects.filter(user=user.id, sku_code__in=all_sku_codes)
        sku_masters = {}
        for sku_master in sku_master_objs:
            sku_masters[sku_master.sku_code] = sku_master
            if inv_group_skus.get(sku_master.sku_code) and inv_group_skus[sku_master.sku_code] == sku_master.invoice_group:
                inv_group_skus.pop(sku_master.sku_code)
        
        serialized_values = dict(StockDetail.objects.filter(
            sku__sku_code__in=all_sku_codes, quantity__gt=0, sku__user=user.id, 
            location__zone__segregation='sellable'
        ).values_list('sku__sku_code','sku__enable_serial_based'))
        
        available_hsn_codes = dict(HSNMaster.objects.filter(warehouse = user.id).values_list('hsn_code','product_type'))
        sku_type_list, sku_invoice_groups, misc_dict = get_required_misc_values(user)
        open_orders_sku_list = get_open_orders_sku_list(user, inv_group_skus) if inv_group_skus else []
        zones = dict(ZoneMaster.objects.filter(user = user.id).values_list('zone', 'id'))
        # Fetch Seller Master Data from request
        enable_seller = misc_dict.get('enable_seller', False)
        seller_master_dict = {}
        seller_master_dict = fetch_seller_master_data(enable_seller, user, skus, seller_master_dict)
        sku_codes_list = [ {SKU_CODE: sku_code} for sku_code in all_sku_codes]
        batch_based_dict = get_available_stock_data(sku_codes_list, user)
        sku_update_fields, update_sku_objs = set(), []
        extra_params = {
            "serialized_values" : serialized_values,
            "available_hsn_codes" : available_hsn_codes,
            "sku_type_list" : sku_type_list,
            "sku_invoice_groups": sku_invoice_groups,
            "zones" : zones,
            "batch_based_dict" : batch_based_dict,
            "sku_update_fields" : sku_update_fields,
            "tax_masters" : tax_masters,
            "gst_tax_masters" : gst_tax_masters,
            "open_orders_sku_list": open_orders_sku_list,
            "seller_master_dict": seller_master_dict,
            "enable_seller": enable_seller
        }
        new_sku_objs = []
        found, _ = get_open_sku_stock_details(list(all_sku_codes), user.id)
        for sku_data in skus:
            lock_status = handle_sku_creation_lock(user, sku_data, failed_status, redis_cache_keys)
            if not lock_status:
                return insert_status, failed_status.values(), redis_cache_keys
            sku_master, insert_status, sku_ean_numbers, create_sku_attrs, sku_attr_mapping, remove_attr_ids, update_obj_flag, new_sku_flag = sku_master_insert_update(
                sku_data, user, sku_mapping, insert_status, failed_status, user_attr_list, sku_ean_numbers,
                create_sku_attrs, sku_attr_mapping, remove_attr_ids, sku_masters, extra_params, found
            )
            if new_sku_flag:
                new_sku_objs.append(sku_master)
                continue
            all_sku_masters.append(sku_master)
            if update_obj_flag and sku_update_fields and not failed_status:
                sku_master.volume = float(sku_master.length) * float(sku_master.breadth) * float(sku_master.height)
                update_sku_objs.append(sku_master)
                sku_update_fields.add('volume')
        try:
            if new_sku_objs:
                all_sku_masters.extend(SKUMaster.objects.bulk_create_with_rounding(new_sku_objs, batch_size=500))
        except Exception as e:
            error_msg = f'SKU Creation Failed for Username {user.username} with the exception {str(e)}'
            log.info(error_msg)
            raise Exception(error_msg)

        if update_sku_objs:
            SKUMaster.objects.bulk_update_with_rounding(update_sku_objs, list(sku_update_fields), batch_size=500)
        
        sku_code_id_dict = { sku.sku_code: sku.id for sku in all_sku_masters }
        if sku_ean_numbers:
            update_sku_ean_numbers(user, sku_ean_numbers, sku_code_id_dict)
        
        # Bulk Create SKU Attributes
        if create_sku_attrs:
            create_update_sku_attributes(user, create_sku_attrs, sku_code_id_dict)

        log.info("SKUs Created for warehouse :: %s" % user.username)
        all_users = get_related_users(user.id)
        log.info("SKUs Need TO Be Updated For :: %s" % all_users)
        sync_sku_switch = get_misc_value('sku_sync', user.id)
        log.info("Sync Switch :: %s" % sync_sku_switch)
        log.info("Paramater Chec All Masters:: %s" % all_sku_masters)

        #Sync SKU Master across spoke warehouses
        sync_masters_across_warehouses.apply_async(args=[user.id, user.id, [sku_details], 'sku_master'], link=update_skus_bulk.s())

        return insert_status, failed_status.values(), redis_cache_keys

    except Exception as e:
        import traceback
        log.info(traceback.format_exc())
        log.debug(traceback.format_exc())
        log.debug("Update SKU Failed %s" % str(e))
        if not failed_status:
            update_error_message(failed_status, 5021, "invalid payload", user.username, field_key='warehouse')
        return insert_status, failed_status.values(), redis_cache_keys
    

def handle_sku_creation_lock(user, sku_data, failed_status, redis_cache_keys):
    """
    Handles locking mechanism for SKU creation or updation to prevent concurrent operations.
    """
    sku_code = sku_data.get('sku_code', '')
    cache_key = f"sku_creation_or_updation_{user.username}_{sku_code}"
    cache_status = cache.add(cache_key, 1, timeout=LOCK_EXPIRE)
    if not cache_status:
        error_message = f"SKU Code - {sku_code} creation or updation is already in progress"
        update_error_message(
            failed_status, 5021, error_message, user.username, field_key='warehouse'
        )
        return False
    redis_cache_keys.add(cache_key)
    return True

def clear_redis_cache(redis_cache_keys):
    """
    Clears the Redis cache for SKU creation or updation locks.
    """
    for cache_key in redis_cache_keys:
        cache.delete(cache_key)

def validate_self_lives(sku_master_dict, sku_master, failed_status, sku_code, sku_update_fields):
    if float(sku_master_dict.get("shelf_life", 0))  < float(sku_master_dict.get("customer_shelf_life", 0)):
        error_message = 'Product Shelf life must be greater than  Customer Shelf life'
        update_error_message(failed_status, 5032, error_message, sku_code, field_key='sku_code')
    else:
        sku_master_dict["customer_shelf_life"] = timedelta(sku_master_dict.get("customer_shelf_life", 0))
        if sku_master and getattr(sku_master, 'customer_shelf_life') != sku_master_dict["customer_shelf_life"]:
            setattr(sku_master, 'customer_shelf_life', sku_master_dict["customer_shelf_life"])
            sku_update_fields.add('customer_shelf_life')
    if float(sku_master_dict.get("shelf_life", 0))  <  float(sku_master_dict.get("minimum_shelf_life", 0)):
        error_message = 'Product Shelf life must be greater than  Minimum Shelf life'
        update_error_message(failed_status, 5032, error_message, sku_code, field_key='sku_code')
    else:
        sku_master_dict["minimum_shelf_life"] = timedelta(sku_master_dict.get("minimum_shelf_life", 0))
        if sku_master and getattr(sku_master, 'minimum_shelf_life') != sku_master_dict["minimum_shelf_life"]:
            setattr(sku_master, 'minimum_shelf_life', sku_master_dict["minimum_shelf_life"])
            sku_update_fields.add('minimum_shelf_life')

def validate_numeric_field_type(sku_master_dict, failed_status, sku_code):
    numeric_list = ["shelf_life", "minimum_shelf_life", "customer_shelf_life", "threshold_quantity", "max_norm_quantity"]
    for numeric_value in numeric_list:
        try:
            float(sku_master_dict.get(numeric_value, 0))
        except Exception:
            error_message = 'Invalid Value for %s' % numeric_value
            update_error_message(failed_status, 5032, error_message, sku_code, field_key='sku_code')
            

def validate_hsn_with_available_codes(hsn_code, product_type, available_hsn_codes, sku_code, failed_status):
    hsn_error = ""
    if available_hsn_codes and hsn_code:
        hsn_code = str(hsn_code)

        if not available_hsn_codes.get(hsn_code):
            error_message = error_dict['hsn_code']
            update_error_message(failed_status, 5024, error_message, sku_code,
                                    field_key='sku_code')
            hsn_error = error_message

        if product_type:
            if available_hsn_codes.get(hsn_code):
                available_product_type = available_hsn_codes.get(hsn_code)
                if product_type.lower() != available_product_type.lower():
                    error_message = 'HSN code and Tax name is Not mapped'
                    update_error_message(failed_status, 5024, error_message, sku_code,
                                    field_key='sku_code')
                    hsn_error = error_message
        elif available_hsn_codes.get(hsn_code):
            product_type = available_hsn_codes[hsn_code]
    else:
        hsn_code = 0

    return hsn_code, product_type, hsn_error

def validate_numeric_sku_attributes(key, value , number_fields, failed_status, sku_code):
    if key in number_fields:
        if key == 'min_norm_quantity':
            key = 'threshold_quantity'
        try:
            value = float(value)
        except Exception:
            update_error_message(failed_status, 5024, 'Invalid Value for %s' % key, sku_code,
                                field_key='sku_code')
            value = 0
    return value

def validate_zone(value, sku_code, failed_status, zones):
    value = str(value).upper()
    if value and value not in zones.keys():
        error_message = 'Invalid Put Zone'
        update_error_message(
            failed_status, 5024, error_message, sku_code,field_key='sku_code'
        )
    else:
        value = zones.get(value, '')

    return value

def validate_hsn(value, sku_code, failed_status, hsn_error):
    try:
        value = str(value).strip()
        if value and (not value.isnumeric()):
            error_message = 'HSN code should be numeric'
            update_error_message(failed_status, 5024, error_message, sku_code,
                                field_key='sku_code')
            return ''

        if hsn_error:
            return ''

    except Exception as e:
        log.debug("Making HSN Empyt Cause Of %s" % e)
        value = ''
    return value

def validate_mix_sku(value, failed_status, sku_code):
    if not value:
        return ''
    if str(value).lower() not in MIX_SKU_MAPPING.keys():
        error_message = 'Invalid Mix SKU Attribute'
        update_error_message(failed_status, 5024, error_message, sku_code,
                                field_key='sku_code')
        return ''
    else:
        value = MIX_SKU_MAPPING[value.lower()]

    return value

def validate_sku_type(value, sku_type_list, failed_status, sku_code):
    if not value:
        return ''
    else:
        sku_type = value
        if sku_type in sku_type_list:
            value = sku_type
        else:
            error_message = "Invalid SKU Type"
            update_error_message(failed_status, 5024, error_message, sku_code,
                                field_key='sku_code')
    return value


def validate_invoice_groups(value, sku_invoice_groups, failed_status, sku_code, sku_master, open_orders_sku_list):
    if not value:
        return ''

    if value not in sku_invoice_groups:
        error_message = "Invalid Invoice Group"
    elif sku_master and sku_master.invoice_group != value and sku_code in open_orders_sku_list:
        error_message = "Invoice Group cannot be updated as the SKU is present in open orders"
    else:
        return value

    update_error_message(failed_status, 5024, error_message, sku_code, field_key='sku_code')
    return value

def validate_sku_attributes(value, user_attr_list, failed_status, sku_code):
    sku_options = value
    option_names = map(operator.itemgetter('name'), sku_options)
    option_not_created = list(set(option_names) - set(user_attr_list))
    if option_not_created:
        error_message = 'SKU Options %s are not created' % (','.join(option_not_created))
        update_error_message(failed_status, 5030, error_message, sku_code,
                                field_key='sku_code')
    return sku_options, option_not_created

def validate_image_url(value, sku_code, failed_status):
    if value and 'http' not in value:
        error_message = 'Full Image URL Needed'
        update_error_message(failed_status, 5029, error_message, sku_code,
                                field_key='sku_code')
    return value
        
def validate_serial_flag(value, serialized_value, sku_code, failed_status, sku_serial=None, found=None, stock=None):
    key = ''
    if str(value):
        key = 'enable_serial_based'
        value = 1 if str(value).lower()=="enable" else 0
        if value != serialized_value:
            error_message = validate_sku_serialisation(str(value), sku_serial, found)
            if error_message:
                update_error_message(failed_status, 5029, error_message, sku_code,
                            field_key='sku_code')
            if stock and not error_message:
                error_message = error_dict['serialized']
                update_error_message(failed_status, 5029, error_message, sku_code,
                                        field_key='sku_code')
    return key, value
        
def validate_qc_flag(value, sku_code, failed_status):
    if str(value):
        if str(value).lower() not in ['enable','disable']:
            error_message = 'qc eligible should be either enable or disable'
            update_error_message(failed_status, 5025, error_message, sku_code,field_key='sku_code')
        if str(value).lower() == 'enable':
            value = True
        else:
            value = False
    else:
        value = False

    return value

def validate_enable_disable_flag(field_name, value, sku_code, failed_status):
    """
    Generic validator for fields that accept 'enable' or 'disable' values.

    Args:
        value (str): The value to validate.
        sku_code (str): The SKU code for error reporting.
        failed_status (dict): Dictionary to store validation errors.
        field_name (str): Field name to identify the context (e.g., 'pick_and_sort').

    Returns:
        int | bool: Returns 1/0 for pick_and_sort and mandate_scan,
                    True/False for barcoding_required,
                    or False if invalid.
    """
    if not value:
        return False

    value_str = str(value).lower()
    if value_str not in ['enable', 'disable']:
        field_error_map = {
            'pick_and_sort': "Pick and sort flag should be either 'enable' or 'disable'.",
            'barcoding_required': "Barcoding Required should be either enable or disable.",
            'mandate_scan': "Mandate scan flag should be either 'enable' or 'disable'."
        }
        error_message = field_error_map.get(field_name, f"{field_name} should be either 'enable' or 'disable'.")
        update_error_message(failed_status, 5025, error_message, sku_code, field_key='sku_code')
        return False

    if field_name in ['pick_and_sort', 'mandate_scan']:
        return 1 if value_str == 'enable' else 0
    elif field_name == 'barcoding_required':
        return True if value_str == 'enable' else False
    return False

def validate_json_data(value, sku_code, failed_status):
    if value and not isinstance(value, dict):
        error_message = 'Invalid Json Data'
        update_error_message(failed_status, 5025, error_message, sku_code,field_key='sku_code')
        return {}
    
    return value

def validate_make_or_buy_choice(value, failed_status, sku_code):
    if value:
        value = str(value).capitalize()
        if value not in POChoices.labels:
            error_message = 'Invalid Value for make or buy'
            update_error_message(failed_status, 5025, error_message, sku_code,field_key='sku_code')
            return False
        else:
            value = POChoices[value.upper()]
    
    return value

def validate_measurement_type(value, sku_code, failed_status, measure_type_exists):
    if not measure_type_exists:
        measure_type_exists = True
    else:
        return False
    if value and isinstance(value,(int,float)):
        error_message = 'UOM cannot be a number'
        update_error_message(failed_status, 5024, error_message, sku_code,
                                field_key='sku_code')
        
    return value

def validate_scan_picking(value, sku_code, failed_status):
    if isinstance(value, bool):
        value = 1 if value else 0
    else:
        update_error_message(failed_status, 5024, "Scan picking should be boolean type", sku_code,
                                            field_key='sku_code')
    return value

def validate_dispensing_flag(value, sku_code, failed_status):
    if not isinstance(value, int):
        error_message = 'Dispensing Enabled must be int (0, 1)'
        update_error_message(failed_status, 5029, error_message, sku_code, field_key='sku_code')
    elif value not in (1, 0): #dispensing states are 0 or 1
        error_message = "invalid dipensing value must be either 0 or 1"
        update_error_message(failed_status, 5029, error_message, sku_code, field_key='sku_code')
    if not value:
        value = 0

    return value

def prepare_tax_dict(key, value, taxes_dict, taxes_mapping):
    if value or value == '0':
        try:
            taxes_dict[taxes_mapping[key]] = float(value)
        except Exception:
            taxes_dict[taxes_mapping[key]] = 0
    else:
        taxes_dict[taxes_mapping[key]]=0

    return taxes_dict

def validate_ean_number(value, sku_code, failed_status):
    if value:
        try:
            ean_numbers = str(value.encode('utf-8').replace('\xc2\xa0', '').replace('\\xE2\\x80\\x8B', ''))
        except Exception:
            try:
                ean_numbers = str(value)
            except Exception:
                ean_numbers = ''

        for temp_ean in ean_numbers.split(','):
            if not temp_ean:
                update_error_message(failed_status, 5032, 'Invalid EAN Number Format', sku_code, field_key='sku_code')
                return ''
            if len(str(temp_ean)) > 20:
                error_message = 'EAN Number Length should be less than 20'
                update_error_message(failed_status, 5032, error_message, sku_code, field_key='sku_code')

    return value

def validate_tax_dict(taxes_dict, failed_status, sku_code, user):
    product_type_dict = {}
    cgst_check = True
    product_type = ''
    if taxes_dict.get('cgst_tax', 0) or taxes_dict.get('sgst_tax', 0):
        tax_master_obj = TaxMaster.objects.filter(cgst_tax=taxes_dict.get('cgst_tax', 0),
                                                sgst_tax=taxes_dict.get('sgst_tax', 0), igst_tax=0,
                                                cess_tax=taxes_dict.get('cess_tax', 0), user=user.id)
        if tax_master_obj.exists():
            product_type_dict['product_type__in'] = list(tax_master_obj.values_list("product_type", flat=True))
            product_type = tax_master_obj[0]
        else:
            cgst_check = False
    if taxes_dict.get('igst_tax', 0) and cgst_check:
        tax_master_obj = TaxMaster.objects.filter(igst_tax=taxes_dict.get('igst_tax', 0),
                                                    cess_tax=taxes_dict.get('cess_tax', 0), user=user.id,
                                                    **product_type_dict)
        if not tax_master_obj.exists():
            product_type = '' 
        else:
            product_type = tax_master_obj[0]
    if not (taxes_dict.get('cgst_tax', 0) or taxes_dict.get('sgst_tax', 0) or taxes_dict.get('igst_tax', 0)):
        tax_master_obj = TaxMaster.objects.filter(igst_tax=0,user=user.id)
        if not tax_master_obj.exists():
            product_type = ''
        else:
            product_type = tax_master_obj[0]
    if not product_type and sum(taxes_dict.values()) > 0:
        error_message = 'Tax Master not found'
        update_error_message(failed_status, 5028, error_message, sku_code,
                                field_key='sku_code')
    return product_type
        
def get_tax_type(product_type, failed_status, sku_code, tax_masters):
    tax_obj = tax_masters.get(product_type, '')
    if tax_obj:
        product_type = tax_obj
    else:
        error_message = 'Invalid Tax Type'
        update_error_message(failed_status, 5024, error_message, sku_code,
                                        field_key='sku_code')
    return product_type

def create_tax_with_slab(user, taxes_slab):
    tax_type = 'Tax-'+str(taxes_slab)
    igst_tax = float(taxes_slab)
    cgst_tax = float(taxes_slab)/float(2)
    sgst_tax = float(taxes_slab)/float(2)
    tax_data = {'data':[{'tax_type': 'intra_state', 'min_amt': 0, 'max_amt': 999999, 'sgst_tax': sgst_tax, 'cgst_tax': cgst_tax, 'igst_tax': '', 'cess_tax': '', 'apmc_tax': '','inter_state': 0, 'product_type':tax_type}, 
    {'tax_type': 'inter_state', 'min_amt': 0, 'max_amt': 999999, 'sgst_tax': '', 'cgst_tax': '', 'igst_tax': igst_tax, 'cess_tax': '', 'apmc_tax': '',  'inter_state': 1, 'product_type':tax_type}]}
    status = save_tax_master(tax_data, user)
    return status

def update_sku_options(sku_master, sku_options, option_not_created, failed_status, sku_code, user, create_sku_attrs):
    if not (sku_master and sku_options):
        return create_sku_attrs
    for option in sku_options:
        if not option.get('value', ''):
            continue
        if option.get('name') in option_not_created:
            continue
        try:
            option['name'] = str(option.get('name'))
        except Exception:
            log.info(option['name'])
            log.info("Ascii Code Error Name for %s" % str(sku_master.sku_code))
            error_message = 'Ascii code characters Name found'
            update_error_message(failed_status, 5033, error_message, sku_code,
                                    field_key='sku_code')
        try:
            option['value'] = str(option.get('value'))
            status = validate_drop_down_values(option['name'],option['value'],user)
            if status != "Success":
                update_error_message(failed_status, 5018, status, option['value'],
                                    field_key= option['name'])
            status1 = validate_mandatory_and_type_of_attribute_value(option['name'],str(option['value']), sku_master)
            if status1 != "Success":
                update_error_message(failed_status, 5018, status1, option['value'],
                                    field_key= option['name'])
        except Exception:
            log.info(option['value'])
            log.info("Ascii Code Error Value for %s" % str(sku_master.sku_code))
            error_message = 'Ascii code characters Value found'
            update_error_message(failed_status, 5033, error_message, sku_code,
                                    field_key='sku_code')
        if failed_status:
            continue
        create_sku_attrs[sku_code].add((option['name'], option['value']))
    return create_sku_attrs

def create_ean_objects(user, ean, sku_master, new_ean_objs, exist_ean_list, exist_sku_eans):
    try:
        sku_ean_check= EANNumbers.objects.filter(
            **{'ean_number': ean, 'sku_id': sku_master.id},sku__user = user.id
        )
        if not sku_ean_check:
            new_ean_objs.append(EANNumbers(
                **{'ean_number': ean, 'sku_id': sku_master.id, 'account_id': user.userprofile.id}
                )
            )
            ean_found = False
            if exist_ean_list.get(ean, ''):
                exist_ean_list[ean] = sku_master.sku_code
                ean_found = True
            elif exist_sku_eans.get(ean, ''):
                exist_sku_eans[ean] = sku_master.sku_code
                ean_found = True
            if not ean_found:
                exist_ean_list[ean] = sku_master.sku_code
    except Exception:
        pass

    return new_ean_objs


def update_sku_eans(sku_master, ean_numbers, sku_ean_numbers, update_sku_obj, sku_update_fields):
    ean_numbers = ean_numbers or ''
    sku_ean_numbers[sku_master.sku_code] = set(ean_numbers.split(",") or [])
    if sku_master.ean_number != ean_numbers:
        update_sku_obj = True
        sku_update_fields.add('ean_number')
        sku_master.ean_number = ean_numbers or ''
    
    return update_sku_obj, sku_ean_numbers

def update_sku_ean_numbers(user, sku_ean_numbers, sku_code_id_dict):
    sku_id_eans = {}
    for sku_code, ean in sku_ean_numbers.items():
        sku_id = sku_code_id_dict.get(sku_code)
        if not sku_id:
            continue
        sku_id_eans[sku_id] = ean
    
    ean_data = list(EANNumbers.objects.filter(sku_id__in=sku_id_eans.keys()).values('sku_id', 'ean_number', 'id'))
    eans_to_delete, new_ean_objs = [], []
    for ean_obj in ean_data:
        ean = ean_obj['ean_number']
        sku_id = ean_obj['sku_id']
        ean_id = ean_obj['id']
        if ean not in sku_id_eans.get(sku_id, []):
            eans_to_delete.append(ean_id)
        else:
            sku_id_eans[sku_id].remove(ean)
    
    for sku_id, ean_numbers in sku_id_eans.items():
        for ean in ean_numbers:
            new_ean_objs.append(EANNumbers(**{'ean_number': ean, 'sku_id': sku_id, 'account_id': user.userprofile.id}))
    
    try:
        EANNumbers.objects.bulk_create_with_rounding(new_ean_objs)
        EANNumbers.objects.filter(id__in=eans_to_delete).delete()
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info("Ean Numbers update failed")

def update_tax_attributes(sku_master, product_type, update_sku_obj, taxes_slab, sku_update_fields):
    if sku_master and product_type and not isinstance(product_type, str) and sku_master.product_type != product_type.product_type:
        sku_master.product_type = product_type.product_type
        update_sku_obj = True
        sku_update_fields.add('product_type')
    
    if taxes_slab and sku_master and abs(taxes_slab - 0.0) < 0.0001 and sku_master.product_type != '':
        sku_master.product_type = ''
        update_sku_obj = True
        sku_update_fields.add('product_type')
    
    return update_sku_obj

def get_product_type(taxes_dict, failed_status, sku_code, user, product_type, taxes_slab, sku_master_dict, tax_masters, gst_tax_masters):
    if taxes_dict :
        product_type = validate_tax_dict(taxes_dict, failed_status, sku_code, user)
        
    elif product_type != '':
        product_type = get_tax_type(product_type, failed_status, sku_code, tax_masters)
        
    elif taxes_slab != '' :
        product_type = gst_tax_masters.get(taxes_slab, '')

    if product_type:
        sku_master_dict.update({'product_type': product_type.product_type})

    return product_type

def validate_sku_options(key, value, sku_code, failed_status, hsn_error, sku_type_list, measure_type_exists, zones, sku_invoice_groups, sku_master, open_orders_sku_list, seller_master_dict, enable_seller):
    if key == 'hsn_code':
        value  = validate_hsn(value, sku_code, failed_status, hsn_error)
        
    elif key == 'mix_sku':
        value = validate_mix_sku(value, failed_status, sku_code)
        
    elif key == 'sku_type':
        value = validate_sku_type(value, sku_type_list, failed_status, sku_code)

    elif key == 'invoice_group':
        value = validate_invoice_groups(value, sku_invoice_groups, failed_status, sku_code, sku_master, open_orders_sku_list)

    elif key == "json_data":
        value = validate_json_data(value, sku_code, failed_status)
        
    elif key == 'make_or_buy':
        value = validate_make_or_buy_choice(value, failed_status, sku_code)
        
    elif key == 'measurement_type':
        value = validate_measurement_type(value, sku_code, failed_status, measure_type_exists)

    elif key == 'scan_picking':
        value = validate_scan_picking(value, sku_code, failed_status)
    
    elif key == 'dispensing_enabled':
        value = validate_dispensing_flag(value, sku_code, failed_status)
        
    elif key == 'sku_code':
        value = str(value).strip()

    elif key == 'zone_id':
        value = validate_zone(value, sku_code, failed_status, zones)
        
    elif key == 'seller_id':
        message, value = validate_seller_mater_id(value, seller_master_dict, enable_seller)
        if message:
            update_error_message(failed_status, 5024, message, sku_code, field_key='sku_code')

    elif key == 'image_url':
        value = validate_image_url(value, sku_code, failed_status)

    return value

def update_sku_size_and_type(sku_master, sku_size, size_type, update_sku_obj, sku_update_fields):
    if sku_size and size_type:
        check_update_size_type(sku_master, size_type)
        if sku_master.sku_size != sku_size:
            sku_master.sku_size = sku_size
            sku_update_fields.add('sku_size')
            update_sku_obj = True
        if sku_master.size_type != size_type:
            sku_master.size_type = size_type
            sku_update_fields.add('size_type')
            update_sku_obj = True
    
    return update_sku_obj

def prepare_data_for_creation(key, value, sku_master, sku_master_dict, sku_update_fields):
    if value is None:
        value = ''
    sku_master_dict[key] = value
    if sku_master and key!="customer_shelf_life" and key!="minimum_shelf_life":
        if getattr(sku_master,key) != value:
            setattr(sku_master, key, value)
            sku_update_fields.add(key)
    
    return sku_master_dict

def create_or_update_sku(sku_master, error_message, update_sku_obj, sku_master_dict, 
                         sku_code, insert_status, user, failed_status):
    if sku_master and not error_message:
        update_sku_obj = True
        insert_status['skus_updated'].append(sku_code)
    else:
        sku_master_dict['wms_code'] = sku_master_dict['sku_code']
        if sku_master_dict.get('json_data',''):
            json_data = sku_master_dict.get('json_data',{})
            json_data['created_by'] = 'API'
        else:
            sku_master_dict['json_data']= {'created_by':'API'}

        ##Create SKU or not based on error
        try:
            if not failed_status:
                sku_master_dict['account_id'] = user.userprofile.id
                sku_master = SKUMaster(**sku_master_dict)
                insert_status['skus_created'].append(sku_code)

        except DataError:
            error_message = f'One or more attributes for this SKU {sku_code} exceed the maximum allowed length. Please review and correct the data for all fields in the SKUMaster'
            update_error_message(
                failed_status, 5024, error_message, sku_code, field_key='sku_code'
            )
        except Exception as e:
            error_msg = f'SKU Creation Failed for Username {user.username} with the exception {str(e)}'
            log.info(error_msg)

    return update_sku_obj, sku_master, insert_status

def validate_min_max_norm_quanatity(sku_data, error_message, sku_code, failed_status):
    max_norm_qty, min_norm_qty = sku_data.get('max_norm_quantity',0), sku_data.get('min_norm_quantity',0)
    if not (max_norm_qty and min_norm_qty):
        return error_message
    if float(max_norm_qty) < float(min_norm_qty):
        error_message = SKU_ERROR_DICT["1"]
        update_error_message(
            failed_status, 5024, error_message, sku_code, field_key='sku_code'
        )
    return error_message

def validate_mrp_price(sku_data, error_message, sku_code, failed_status):
    mrp = sku_data.get('mrp',0)
    price = sku_data.get('price',0)
    if not (mrp and price):
        return error_message
    mrp = float(mrp)
    price = float(price)
    if mrp < 0:
        error_message = SKU_ERROR_DICT["42"]
        update_error_message(
            failed_status, 5024, error_message, sku_code, field_key='sku_code'
        )
    elif price < 0:
        error_message = SKU_ERROR_DICT["43"]
        update_error_message(
            failed_status, 5024, error_message, sku_code, field_key='sku_code'
        )
    elif mrp > 0 and (mrp < price):
        error_message = SKU_ERROR_DICT["41"]
        update_error_message(
            failed_status, 5024, error_message, sku_code, field_key='sku_code'
        )
    return error_message

def get_batch_based_key(value):
    if str(value):
        value = 1 if str(value).lower()=="true" else 0
    return value

def update_sku_master_json_data(update_sku_obj, error_message, sku_master, sku_update_fields):
    if update_sku_obj and sku_update_fields and not error_message:
        if sku_master.json_data:
            sku_master.json_data['updated_by']='API'
        else:
            sku_master.json_data={'updated_by':'API'}
        sku_update_fields.add('json_data')
    
    return sku_master

def validate_lbhw_fields(sku_data, failed_status, sku_code, skumaster_ui = False):
    error_messages = ""
    if sku_data.get('length'):
        error_message = validate_numeric_values(sku_data.get('length'), [] ,'Length')
        if error_message: 
            if not skumaster_ui:
                update_error_message(failed_status, 5024, error_message, sku_code,
                                     field_key='sku_code')
            else:
                error_messages += f"{error_message[0]}, "

    if sku_data.get('breadth'):
        error_message = validate_numeric_values(sku_data.get('breadth'), [] ,'Breadth')
        if error_message: 
            if not skumaster_ui:
                update_error_message(failed_status, 5024, error_message, sku_code,
                                     field_key='sku_code')
            else:
                error_messages += f"{error_message[0]}, "

    if sku_data.get('height'):
        error_message = validate_numeric_values(sku_data.get('height'), [] ,'Height')
        if error_message: 
            if not skumaster_ui:
                update_error_message(failed_status, 5024, error_message, sku_code,
                                     field_key='sku_code')
            else:
                error_messages += f"{error_message[0]}, "

    if sku_data.get('weight'):
        error_message = validate_numeric_values(sku_data.get('weight'), [] ,'Weight')
        if error_message: 
            if not skumaster_ui:
                update_error_message(failed_status, 5024, error_message, sku_code,
                                     field_key='sku_code')
            else:
                error_messages += f"{error_message[0]} "

    if skumaster_ui:
        return error_messages

def validate_batch_based(sku_master_dict, sku_master, failed_status, sku_code, batch_based_dict):
    error_message = ''
    batch_based = batch_based_dict.get(str(sku_code).lower())
    if sku_master and batch_based:
        available_batch_flag = 1 if batch_based == 'enable' else 0
        if 'batch_based' in sku_master_dict and str(available_batch_flag) and str(available_batch_flag) != str(sku_master_dict['batch_based']):
            error_message = error_dict['batch_based']
            update_error_message(failed_status, 5032, error_message, sku_code, field_key='sku_code')
    return error_message

def update_sku_tax_type(sku_master, product_type, sku_master_dict, sku_update_fields):
    if sku_master and product_type and getattr(sku_master, 'product_type') != sku_master_dict["product_type"]:
        setattr(sku_master, 'product_type', sku_master_dict["product_type"])
        sku_update_fields.add('product_type')

def validate_seller_id_in_api(enable_seller, sku_data, failed_status, exclude_list, sku_code):  
    # validating seller id in api
    if enable_seller == 'false':
        exclude_list.append('seller_id')
    elif enable_seller == 'true' and "seller_id" not in sku_data:
        error_message = 'Seller ID is mandatory for SKU creation'
        update_error_message(failed_status, 5022, error_message, sku_code, field_key='sku_code')

def sku_master_insert_update(sku_data, user, sku_mapping, insert_status, failed_status, user_attr_list, sku_ean_numbers,
                             create_sku_attrs, sku_attr_mapping, remove_attr_ids, sku_masters, extra_params, found=None):
    available_hsn_codes = extra_params['available_hsn_codes']
    sku_type_list = extra_params['sku_type_list']
    sku_invoice_groups = extra_params.get('sku_invoice_groups', [])
    zones = extra_params['zones']
    batch_based_dict = extra_params['batch_based_dict']
    sku_update_fields = extra_params['sku_update_fields']
    tax_masters = extra_params['tax_masters']
    gst_tax_masters = extra_params['gst_tax_masters']
    open_orders_sku_list = extra_params.get('open_orders_sku_list', [])
    seller_master_dict = extra_params.get('seller_master_dict', {})
    enable_seller = extra_params.get('enable_seller', False)
    sku_master = None
    error_message = ''
    sku_code = str(sku_data.get(sku_mapping['sku_code'], '')).strip()
    if not sku_code:
        error_message = error_dict['sku_code']
        update_error_message(failed_status, 5022, error_message, sku_data.get(sku_mapping['sku_code']), field_key='sku_code')
        return sku_master, insert_status, sku_ean_numbers, create_sku_attrs, sku_attr_mapping, remove_attr_ids, False, False

    sku_master = sku_masters.get(sku_code)
    serialized_value = ''
    if sku_master:
        serialized_value = sku_master.enable_serial_based
    
    sku_master_dict = {'user': user.id, 'creation_date': datetime.datetime.now()}
    exclude_list = ['skus', 'child_skus']
    number_fields = [
        'min_norm_quantity', 'max_norm_quantity', 'price', 'mrp', 'status',
        'product_shelf_life', 'cost_price', 'customer_shelf_life', 'minimum_shelf_life',
        'receipt_tolerance',
    ]
    sku_size = ''
    size_type = ''
    sku_options = []
    ean_numbers = ''
    taxes_mapping = {'cgst': 'cgst_tax', 'sgst': 'sgst_tax', 'igst': 'igst_tax', 'cess': 'cess_tax'}
    taxes_dict = {}
    option_not_created = []
    taxes_slab = sku_data.get('gst_slab', 0) or 0
    product_type = sku_data.get('tax_type','')
    hsn_code = sku_data.get('hsn_code', 0)
    measure_type_exists = False
    hsn_code, product_type, hsn_error  = validate_hsn_with_available_codes(hsn_code, product_type, available_hsn_codes, sku_code, failed_status)
    validate_lbhw_fields(sku_data, failed_status, sku_code)
    error_message = validate_min_max_norm_quanatity(sku_data, error_message, sku_code, failed_status)
    error_message = validate_mrp_price(sku_data, error_message, sku_code, failed_status)
    sku_serial = get_multiple_misc_values(['sku_serialisation'], user.id)
    validate_seller_id_in_api(enable_seller, sku_data, failed_status, exclude_list, sku_code)
    for key, val in sku_mapping.items():
        if key in exclude_list:
            continue
        if val not in sku_data.keys():
            if val == 'measurement_type' and sku_data.get('uom'):
                val = 'uom'
            else:
                continue
        value = sku_data[val]
        value = validate_numeric_sku_attributes(key, value , number_fields, failed_status, sku_code)

        if key == "batch_based":
            value = get_batch_based_key(value)

        elif key in ['pick_and_sort', 'is_barcode_required', 'mandate_scan']:
            value = validate_enable_disable_flag(key, value, sku_code, failed_status)

        elif key == "serialized":
            found_sku={sku_code: found.get(sku_code)} if sku_code in found else {}
            key, value = validate_serial_flag(value, serialized_value, sku_code, failed_status, sku_serial=sku_serial, found=found_sku, stock=batch_based_dict)
           
        elif key == 'qc_eligible':
            value = validate_qc_flag(value, sku_code, failed_status)

        elif key == 'attributes':
            if value and isinstance(value, list):
                sku_options, option_not_created = validate_sku_attributes(value, user_attr_list, failed_status, sku_code)
            continue

        elif key in ["cgst", "sgst", "igst", "cess"]:
            taxes_dict = prepare_tax_dict(key, value, taxes_dict, taxes_mapping)
            continue

        elif key == 'ean_number':
            ean_numbers = validate_ean_number(value, sku_code, failed_status)
        elif key in ['length', 'breadth', 'height'] and not value:
            value = 0

        value = validate_sku_options(key, value, sku_code, failed_status, hsn_error, sku_type_list, measure_type_exists, zones, sku_invoice_groups, sku_master, open_orders_sku_list, seller_master_dict, enable_seller)

        sku_master_dict = prepare_data_for_creation(key, value, sku_master, sku_master_dict, sku_update_fields)
    validate_numeric_field_type(sku_master_dict, failed_status, sku_code)
    validate_self_lives(sku_master_dict, sku_master, failed_status, sku_code, sku_update_fields)
    error_message = validate_batch_based(sku_master_dict, sku_master, failed_status, sku_code, batch_based_dict)
    if sku_code in sum(insert_status.values(), []):
        return sku_master, insert_status, sku_ean_numbers, create_sku_attrs, sku_attr_mapping, remove_attr_ids, False, False
    product_type = get_product_type(taxes_dict, failed_status, sku_code, user, product_type, taxes_slab, sku_master_dict, tax_masters, gst_tax_masters)
    update_sku_tax_type(sku_master, product_type, sku_master_dict, sku_update_fields)
    if '%s:%s' % ('sku_code', str(sku_code)) in failed_status.keys():
        return sku_master, insert_status, sku_ean_numbers, create_sku_attrs, sku_attr_mapping, remove_attr_ids, False, False

    update_sku_obj, new_sku_flag = False, True
    if not failed_status:
        update_sku_obj, sku_master, insert_status = create_or_update_sku(
            sku_master, error_message, update_sku_obj, sku_master_dict, sku_code, insert_status, user, failed_status
        )
        new_sku_flag = not update_sku_obj
        update_sku_obj = update_sku_size_and_type(sku_master, sku_size, size_type, update_sku_obj, sku_update_fields)
        create_sku_attrs = update_sku_options(sku_master, sku_options, option_not_created, failed_status, sku_code, user, create_sku_attrs)
    if not failed_status:
        update_tax_attributes(sku_master, product_type, update_sku_obj, taxes_slab, sku_update_fields)
        update_sku_obj, sku_ean_numbers = update_sku_eans(sku_master, ean_numbers, sku_ean_numbers, update_sku_obj, sku_update_fields)
    sku_master = update_sku_master_json_data(update_sku_obj, error_message, sku_master, sku_update_fields)
    return sku_master, insert_status, sku_ean_numbers, create_sku_attrs, sku_attr_mapping, remove_attr_ids, update_sku_obj, new_sku_flag

def get_tax_details(user, skus, sku_mapping):
    all_sku_codes, product_types, gst_slabs, inv_group_skus = set(), set(), set(), {}
    for sku_data in skus:
        sku_code = str(sku_data.get(sku_mapping['sku_code'], '')).strip()
        all_sku_codes.add(sku_code)
        product_type = sku_data.get('tax_type','')
        taxes_slab = sku_data.get('gst_slab', 0) or 0
        if product_type:
            product_types.add(product_type)
        else:
            gst_slabs.add(float(taxes_slab))
        if sku_data.get('invoice_group'):
            inv_group_skus[str(sku_code)] = sku_data.get('invoice_group')
    
    tax_masters = {}
    if product_types:
        tax_master_objs = TaxMaster.objects.filter(user=user.id, product_type__in=product_types)
        for tax in tax_master_objs:
            tax_masters[tax.product_type] = tax
    
    gst_tax_masters = {}
    if gst_slabs:
        tax_master_objs = TaxMaster.objects.filter(user=user.id, igst_tax__in=gst_slabs)
        for tax in tax_master_objs:
            gst_tax_masters[tax.igst_tax] = tax
    
    gst_slabs_to_create = gst_slabs - gst_tax_masters.keys()
    for gst_slab in gst_slabs_to_create:
        create_tax_with_slab(user, gst_slab)
    
    if gst_slabs_to_create:
        tax_master_objs = TaxMaster.objects.filter(user=user.id, igst_tax__in=gst_slabs)
        for tax in tax_master_objs:
            gst_tax_masters[tax.igst_tax] = tax
    
    return all_sku_codes, tax_masters, gst_tax_masters, inv_group_skus

def serialize_timedelta(obj):
    if isinstance(obj, timedelta):
        return str(obj)
    return obj

def frame_sku_callback_data(warehouse, data):
    field_names = [
        "sku_code", "sku_desc", "sku_group", "sku_type", "sku_category", "sku_brand", "price", "cost_price", "mrp",
        "image_url", "status", "sub_category","pick_group", "shelf_life", "minimum_shelf_life", "enable_serial_based",
        "customer_shelf_life", "block_options", "batch_based", "gl_code", "average_price", "average_price_rt", "length", 
        "breadth", "weight", "height","volume"
    ]
    final_data = []
    sku_codes = []
    for sku in data.get('skus_created',[]):
        sku_codes.append(sku)
    for sku in data.get('skus_updated',[]):
        sku_codes.append(sku)
    sku_obj = list(SKUMaster.objects.filter(sku_code__in=sku_codes, user=warehouse.id).values(*field_names))
    
    timedelta_fields = ['minimum_shelf_life', 'customer_shelf_life']
    for item in sku_obj:
        for field in timedelta_fields:
            if field in item and isinstance(item[field], timedelta):
                item[field] = serialize_timedelta(item[field])
        ordered_item = OrderedDict((field, item.get(field)) for field in field_names)
        final_data.append(ordered_item)
    return final_data
    

@celery_app.task
def async_sku_creation(user_id, warehouse_id, data):
    '''
    This function is used to create SKU's through API in async mode
    '''
    payload = {
        'skus': data
    }
    redis_cache_keys = set()
    user = get_or_none(User, {'id': user_id})
    warehouse = get_or_none(User, {'id': warehouse_id})
    log_message = (("Request Product Creation Username %s, and request params are %s") %(str(user.username), str(payload)))
    log.info(log_message)
    CurrentUserMiddleware.set_current_user(user)
    permission = get_permission(user, 'add_skumaster')
    if not permission:
        return {'message' : 'No access to add skus', 'status': 400}

    log.info('Request params for ' + user.username + ' is ' + str(payload))
    try:
        insert_status, failed_status, redis_cache_keys = update_skus(payload, warehouse)
        log.info(insert_status)
        log.info(failed_status)
        failed_status = list(failed_status)
        if failed_status:
            failed_status = failed_status[0]
        CurrentUserMiddleware.clear_current_user() 
        return {
            'insert_status': insert_status,
            'failed_status': failed_status
        }
        
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Update SKUS data failed for %s and params are %s and error statement is %s' % (
        str(user.username), str(payload), str(e)))
        status = {'failed_status': 'Internal Server Error'}
        return status
    
    finally:
        clear_redis_cache(redis_cache_keys)
    

@celery_app.task
def async_sku_post_creation(results, warehouse_id, callback_id):
    '''async_sku_post_creation'''
    
    skus_created, skus_updated, failed_status = [], [], []
    warehouse = get_or_none(User, {'id': warehouse_id})
    async_api_record = get_or_none(AsyncAPIExecution, {'id': callback_id})
    for data in results:
        if data.get('insert_status'):
            skus_created.extend(data.get('insert_status',{}).get('skus_created', []))
            skus_updated.extend(data.get('insert_status',{}).get('skus_updated', []))     
        if data.get('failed_status'):
            failed_status.extend(data.get('failed_status',[]))
    if not (skus_created or skus_updated):
        async_api_record.status = 3
        async_api_record.result = failed_status
        async_api_record.save()
    else:
        result_data = {'skus_created': skus_created, 'skus_updated': skus_updated}
        if failed_status:
            async_api_record.status = 4
            result_data['errors'] = failed_status
        else:
            async_api_record.status = 2
        async_api_record.result = result_data
        async_api_record.save()
        sku_integration_callback(warehouse, {'skus_created': skus_created, 'skus_updated': skus_updated}, callback_id)
      
    
def sku_integration_callback(warehouse, data, include_id=None):
    '''sku integration callback.'''
    call_ids = []
    integration_apis = UserIntegrationAPIS.objects.filter(
        user_integration__user_id = warehouse.id, trigger='sku_creation', status=1
    )
    callback_data = {}
    callback_data['data'] = frame_sku_callback_data(warehouse, data)
    if include_id:
        callback_data['async_id'] = include_id
    for integration_api in integration_apis:
        if integration_api.data_format == 'callback':
            callback_obj = UserIntegrationCalls.objects.create(
                                user_integrationapis_id = integration_api.id,
                                api_data = callback_data,
                                status = 1,
                                account_id = warehouse.userprofile.id
                            )
            if callback_obj:
                call_ids.append(callback_obj.id)
    filters = {'id__in': call_ids}
    async_run_3p_int_func(filters)

def create_update_sku_attributes(warehouse, attr_dict, sku_masters):
    attr_names, new_sku_attr_objs, update_sku_attr_objs = set(), [], []
    for attrs in attr_dict.values():
        for attr in attrs:
            attr_names.add(attr[0])
    sku_attr_objs = SKUAttributes.objects.filter(sku_id__in=sku_masters.values(), attribute_name__in=attr_names, sku__user=warehouse.id)
    sku_attr_dict = { (sku_attr.sku_id, sku_attr.attribute_name): sku_attr for sku_attr in sku_attr_objs }
    for sku_code, attr_list in attr_dict.items():
        sku_id = sku_masters.get(sku_code)
        if not sku_id:
            continue
        for attr_name, attr_value in attr_list:
            sku_attr_obj = sku_attr_dict.get((sku_id, attr_name))
            if not sku_attr_obj:
                new_sku_attr_objs.append(SKUAttributes(sku_id=sku_id, attribute_name=attr_name, attribute_value=attr_value, account_id=warehouse.userprofile.id))
            elif sku_attr_obj.attribute_value != attr_value:
                sku_attr_obj.attribute_value = attr_value
                sku_attr_obj.updation_date = datetime.datetime.now()
                update_sku_attr_objs.append(sku_attr_obj)
    
    if new_sku_attr_objs:
        SKUAttributes.objects.bulk_create_with_rounding(new_sku_attr_objs)
    if update_sku_attr_objs:
        SKUAttributes.objects.bulk_update(update_sku_attr_objs, ['attribute_value', 'updation_date'])
