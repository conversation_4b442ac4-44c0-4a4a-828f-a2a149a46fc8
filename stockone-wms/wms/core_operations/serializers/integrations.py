from json import dumps, loads

from django.db.models import F

from rest_framework import serializers

from wms_base.models import StaffMaster

from core.models.common import (
    ActionTypes,
    ActionTrigger
)

from core.models.integration import (
    UserIntegrations,
    UserIntegrationAPIS,
    UserIntegrationCalls,
)

class UserIntegrationsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserIntegrations
        fields = '__all__'
        
    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user.current_warehouse
        validated_data['account_id'] = self.context['request'].user.current_warehouse.userprofile.id
        return super(UserIntegrationsSerializer, self).create(validated_data)

    def update(self, instance, validated_data):
        validated_data['user'] = self.context['request'].user.current_warehouse
        validated_data['account_id'] = self.context['request'].user.current_warehouse.userprofile.id
        return super(UserIntegrationsSerializer, self).update(instance,validated_data)


class UserIntegrationsAPISSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserIntegrationAPIS
        fields = '__all__'

    def create(self, validated_data):
        request_filters = self.context['request'].POST.get('filters', "[]")
        filters = loads(request_filters)
        self.prepare_field_types(filters)
        validated_data['filters'] = dumps(self.filters)
        validated_data['account_id'] = self.context['request'].user.current_warehouse.userprofile.id
        return super(UserIntegrationsAPISSerializer, self).create(validated_data)

    def update(self, instance, validated_data):
        request_filters = self.context['request'].POST.get('filters', "[]")
        filters = loads(request_filters)
        self.prepare_field_types(filters)
        validated_data['filters'] = dumps(self.filters)
        validated_data['account_id'] = self.context['request'].user.current_warehouse.userprofile.id
        return super(UserIntegrationsAPISSerializer, self).update(instance,validated_data)

    def prepare_field_types(self, filters):
        self.filters = {}
        filter_mappings = {
            'order_type': 'order__order_type__in',
            'zone': 'location__zone_id__in',
            'movement_type': 'movement_type',
            'sku__dispensing_enabled': 'sku__dispensing_enabled__in',
            'move_inv_exclude_zones': 'exclude_zones',
            'detail_view': 'detail_view',
            'asn_status': 'asn_status',
            'customer_type': 'order__customer_identifier__customer_type__in',
            'order_cancellation_filter': 'order_cancellation_filter',
            'po_type': 'po_type',
        }
        for each_filter in filters:
            filter_key, filter_value = each_filter.get('filter_key'), each_filter.get('filter_value')
            if filter_key in filter_mappings:
                self.filters[filter_mappings[filter_key]] = filter_value.split(',')

class UserIntegrationCallsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserIntegrationCalls
        fields = '__all__'

    def to_internal_value(self,data):
        api_response = data.get('api_response',None)

        # if api_response is str
        try:
            data['api_response'] = loads(api_response)
        except Exception as _:
            data['api_response'] = api_response

        return data

    def update(self, instance, validated_data):
        # if api_response is str
        try:
            existing_response = loads(instance.api_response)
        except Exception as _:
            existing_response = instance.api_response

        new_response = validated_data.get('api_response')

        retain_updates = instance.user_integrationapis.retain_updates

        # if existing response is str
        if isinstance(existing_response,str) and retain_updates:
            existing_response = { "response-1" : existing_response }

        # if previous responses need to be retained
        if new_response and retain_updates:
            response_count = len(existing_response)

            response_key = "response-"+ str(response_count + 1)
            existing_response[response_key] = new_response

        # else override the existing
        elif new_response and not retain_updates:
            existing_response = new_response

        validated_data['api_response'] = dumps(existing_response,indent=4)
        validated_data['account_id'] = self.context['request'].user.current_warehouse.userprofile.id

        return super(UserIntegrationCallsSerializer, self).update(instance,validated_data)

class ActionTriggerSerializer(serializers.ModelSerializer):

    staff_values = serializers.ListField(
        child=serializers.IntegerField(),required=False
    )

    # staff = ActionTriggerToStaffSerializer(source='actiontriggertostaff_set',many=True)

    class Meta:
        model = ActionTrigger
        exclude = ('creation_date','updation_date','staff')

    def to_representation(self, instance):
        data = super(ActionTriggerSerializer, self).to_representation(instance)

        # action = ActionTrigger.objects.select_related('actiontriggertostaff_set')
        staff_details = list(ActionTrigger.objects.filter(id=data['id']).prefetch_related('actiontriggertostaff_set').annotate(
            staff_ids=F('actiontriggertostaff__staffmaster_id'),
            staff_email=F('actiontriggertostaff__staffmaster__email_id')
        ).values('staff_ids','staff_email'))

        action_type = data.get('action_type','')
        action_type = {'action_type' : {'name':ActionTypes[action_type].label , 'value' : action_type}}
        staff_values = { 'staff_values' : staff_details}
        data.update(action_type)
        data.update(staff_values)
        return data

    def create(self,validated_data):
        staff_ids = validated_data.pop('staff_values','')
        staff_ids = [int(ids) for ids in staff_ids]
        warehouse = validated_data['warehouse']
        trigger = ActionTrigger.objects.create(**validated_data)
        action_id = trigger.id
        if staff_ids:
            staff_objs = StaffMaster.objects.filter(id__in=staff_ids,staffwarehousemapping__warehouse=warehouse)
            if not staff_objs.exists():
                raise serializers.ValidationError("Invalid Staff")
            relations = []
            for staff_id in staff_ids:
                relation = ActionTrigger.staff.through(staffmaster_id=staff_id,actiontrigger_id=action_id)
                relations.append(relation)

            if len(relations)>0:
                ActionTrigger.staff.through.objects.bulk_create(relations)

        trigger.save()
        return trigger


    def update(self,instance, validated_data):
        staff_ids = validated_data.pop('staff_values','')
        for key,value in validated_data.items():
            setattr(instance,key,value)

        instance.staff.clear()
        if staff_ids:
            staff_objs = list(StaffMaster.objects.filter(id__in=staff_ids))
            for obj in staff_objs:
                instance.staff.add(obj)

        instance.save()
        return instance
