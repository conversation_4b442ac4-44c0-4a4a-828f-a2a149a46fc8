# Generated by Django 4.2.20 on 2025-06-03 11:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0067_merge_20250602_1006'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='replenishmentclassification',
            name='avail_qty',
        ),
        migrations.AddField(
            model_name='replenishmentclassification',
            name='sku_assumed_avail_qty',
            field=models.FloatField(default=0, help_text='Assumed available quantity based on replenishment logic'),
        ),
        migrations.AlterField(
            model_name='replenishmentclassification',
            name='modified_suggested_qty',
            field=models.FloatField(default=0, help_text='Modified suggested quantity based on user input'),
        ),
        migrations.AlterField(
            model_name='replenishmentclassification',
            name='sku_avail_qty',
            field=models.FloatField(default=0, help_text='Available quantity in the stock after considering the reserved quantity'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='replenishmentclassification',
            name='sku_pen_po_qty',
            field=models.FloatField(default=0, help_text='Pending purchase order quantity including pending approval and GRN quantities'),
        ),
        migrations.AlterField(
            model_name='replenishmentclassification',
            name='sku_pen_putaway_qty',
            field=models.FloatField(default=0, help_text='Pending putaway quantity including JO and CP quantities'),
        ),
    ]
