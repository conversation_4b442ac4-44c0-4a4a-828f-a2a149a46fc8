# Generated by Django 4.2.20 on 2025-06-05 07:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_multitenant.mixins


class Migration(migrations.Migration):

    dependencies = [
        ('wms_base', '0042_scriptusers_scriptmodel'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0066_alter_cyclecount_sku_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CCVLTransaction',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity', models.FloatField(default=0)),
                ('status', models.IntegerField(choices=[(0, 'Completed'), (1, 'Cancelled')], default=0)),
                ('json_data', models.JSONField(blank=True, default=dict, null=True)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('updation_date', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(blank=True, db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)ss_account', to='wms_base.userprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('cycle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.cyclecount')),
                ('source_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.locationmaster')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'CCVL_TRANSACTION',
            },
            bases=(models.Model, django_multitenant.mixins.TenantModelMixin),
        ),
    ]
