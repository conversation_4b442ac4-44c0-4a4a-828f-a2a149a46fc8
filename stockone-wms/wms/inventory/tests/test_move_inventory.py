#python imports
import pytest
from json import loads

#django imports
from django.test import RequestFactory

#common constant imports
from production.tests.constants import USERNAME, WAREHOUSE, JO_REFERENCES

from inventory.tests.constants import(
    GET_SKU_LOCATION_DATA, GET_SKU_BATCHES
)

#common imports
from inventory.tests.common import (
    test_create_user, test_create_sku, test_create_sellable_locations,
    test_create_inventory
)

#Base imports
from wms_base.models import User

#inventory imports
from inventory.views.move.move_inventory import (
    get_sku_location_details, validate_sku_details,
    validate_stock_status, get_source_stock, validate_source_stock,
    validate_move_inventory, aggregate_quantity_on_multiple_stocks,
    move_inventory_call_back_filter, update_stocks, MoveInventorySet,
    get_sku_location_data, get_search_params_of_stock, get_sku_batches,
    prepare_tasks_data
)

#uploads import
from inventory.views.move.uploads import (
    move_inventory_form, validate_and_update_batch_details,
    validate_duplicate_records, split_data_for_tasks_creation,
    validate_available_tasks_with_uploaded_tasks, create_tasks
    )

#production imports
from production.views.create_jo.uploads import validate_job_order

from production.views.create_jo.confirmed_jo import get_jo_material_data

from wms_base.wms_utils import MOVE_INVENTORY_UPLOAD_FIELDS

#Global Constants
invalid_sku_code_const = 'Invalid SKU Code'
moved_success_const = 'Moved Successfully'
validation_dict = [{"sku_code": "M_B_SKU1", "source_loc": "RM-001", "dest_loc": "RM-002", "quantity": 10},
                   {"sku_code": "M_NB_SKU2", "source_loc": "CS-001", "dest_loc": "CS-002", "quantity": 10}]

@pytest.mark.django_db
def test_get_sku_location_details(test_create_user, test_create_sku, test_create_sellable_locations):

    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, loc_master_zone_dict, _= get_sku_location_details(warehouse, validation_dict)

    assert 'M_B_SKU1' in sku_details_dict
    assert 'M_NB_SKU2' in sku_details_dict

    assert 'RM-001' in loc_master_dict
    assert 'RM-002' in loc_master_dict
    assert 'CS-001' in loc_master_dict
    assert 'CS-002' in loc_master_dict
    
    assert 'RM-001' in loc_master_zone_dict
    assert 'RM01' in loc_master_zone_dict.get('RM-001').get('zone')
    assert 'RM-002' in loc_master_zone_dict
    assert 'RM01' in loc_master_zone_dict.get('RM-002').get('zone')

    assert 'CS-001' in loc_master_zone_dict
    assert 'CS01' in loc_master_zone_dict.get('CS-001').get('zone')
    assert 'CS-002' in loc_master_zone_dict
    assert 'CS01' in loc_master_zone_dict.get('CS-002').get('zone')

#Test Validate SKU Details
def test_validate_sku_details_valid_sku():
    record = {'sku_code': 'SKU1'}
    sku_details_dict = {'SKU1': {'sku_id': 1}}
    status = []
    return_dict = {}
    sku_code, status, return_dict = validate_sku_details(record, sku_details_dict, status, return_dict)
    assert sku_code == 'SKU1'
    assert status == []
    assert return_dict == {'sku_id': 1}

def test_validate_sku_details_invalid_sku():
    record = {'sku_code': 'SKU2'}
    sku_details_dict = {'SKU1': {'sku_id': 1}}
    status = []
    return_dict = {}
    sku_code, status, return_dict = validate_sku_details(record, sku_details_dict, status, return_dict)
    assert sku_code == 'SKU2'
    assert status == [invalid_sku_code_const]
    assert return_dict == {}

def test_validate_sku_details_no_sku():
    record = {}
    sku_details_dict = {'SKU1': {'sku_id': 1}}
    status = []
    return_dict = {}
    sku_code, status, return_dict = validate_sku_details(record, sku_details_dict, status, return_dict)
    assert sku_code == ''
    assert status == [invalid_sku_code_const]
    assert return_dict == {}

#Test validate_stock_status
# Assuming reverse_stock_choice_mapping is a dictionary like this:
def test_validate_stock_status_int():
    record = {'source_status': 1}
    return_dict = {}
    return_dict = validate_stock_status(record, return_dict)
    assert return_dict == {'status': '1'}

def test_validate_stock_status_valid_str():
    record = {'source_status': 'Available'}
    return_dict = {}
    return_dict = validate_stock_status(record, return_dict)
    assert return_dict == {'status': '1'}

def test_validate_stock_status_invalid_str():
    record = {'source_status': 'Invalid Status'}
    return_dict = {}
    return_dict = validate_stock_status(record, return_dict)
    assert return_dict == {}

def test_validate_stock_status_no_status():
    record = {}
    return_dict = {}
    return_dict = validate_stock_status(record, return_dict)
    assert return_dict == {}


#Test get_source_stock
def test_get_source_stock_without_stocks(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    return_dict = {
        'sku_id': 1, 'source_loc_id': 1, 
        'batch_no': 'Batch1', 'status': 1
    }
    stocks = get_source_stock(warehouse, return_dict)
    assert stocks == False

def test_get_source_stock_with_stocks(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, _, _ = get_sku_location_details(warehouse, validation_dict)
    return_dict = {
        'sku_id': sku_details_dict.get('M_B_SKU1').get('sku_id'),
        'source_loc_id': loc_master_dict.get('RM-001'),
        'status': 1,
        'batch_no': 'Batch1'
    }
    stocks = get_source_stock(warehouse, return_dict)
    assert stocks != False

#Test validate_source_stock
def test_validate_source_stock_no_stock(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    return_dict = {'quantity': 10, 'sku_id': 1, 'source_loc_id': 1, 'batch_no': 'Batch1', 'status': 1}
    status = []
    allow_reserved = 'false'
    allow_inter_zonal_movement = []
    status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)
    assert status == ['No Source Stock Found']

def test_validate_source_stock_with_stock(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, _, _ = get_sku_location_details(warehouse, validation_dict)
    return_dict = {
        'sku_id': sku_details_dict.get('M_B_SKU1').get('sku_id'),
        'source_loc_id': loc_master_dict.get('RM-001'),
        'status': 1,
        'batch_no': 'Batch1', 'quantity': 100, 'dest_zone': 'Zone2'
    }
    status = []
    allow_reserved = 'false'
    allow_inter_zonal_movement = []
    status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)
    assert 'No Source Stock Found' not in status
    assert 'source_stock' in return_dict

def test_validate_source_stock_insufficient_quantity(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, _, _ = get_sku_location_details(warehouse, validation_dict)
    return_dict = {
        'sku_id': sku_details_dict.get('M_B_SKU1').get('sku_id'),
        'source_loc_id': loc_master_dict.get('RM-001'),
        'status': 1,
        'batch_no': 'Batch1', 'quantity': 10000, 'dest_zone': 'Zone2'
    }
    status = []
    allow_reserved = 'false'
    allow_inter_zonal_movement = []
    status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)
    assert status == ['Entered Quantity is more than Available Stock']

def test_validate_source_stock_inter_zonal_movement_not_allowed(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, _, _ = get_sku_location_details(warehouse, validation_dict)
    return_dict = {
        'sku_id': sku_details_dict.get('M_B_SKU1').get('sku_id'),
        'source_loc_id': loc_master_dict.get('RM-001'),
        'status': '0',
        'batch_no': 'Batch1', 'quantity': 10, 'dest_zone': 'CSO1',
        'source_zone': 'RM01'
    }
    status = []
    allow_reserved = 'false'
    allow_inter_zonal_movement = []
    status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)
    assert 'Inter Zonal Movement is not allowed for SKU' in status


def test_validate_source_stock_inter_zonal_movement_not_allowed(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_details_dict, loc_master_dict, _, _ = get_sku_location_details(warehouse, validation_dict)
    return_dict = {
        'sku_id': sku_details_dict.get('M_B_SKU1').get('sku_id'),
        'source_loc_id': loc_master_dict.get('RM-001'),
        'batch_no': 'Batch1', 'quantity': 10, 'dest_zone': 'CSO1',
        'source_zone': 'RM01'
    }
    status = []
    allow_reserved = 'false'
    allow_inter_zonal_movement = []
    status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)
    assert 'Multiple Stock Status Found, Please Provide Status' in status


def test_validate_move_inventory_with_details(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    validate_list = [{
        'sku_code': 'invalid_sku',
        'source_loc': 'RM-0019',
        'source_zone': 'invalid_zone',
        'source_batch': 'Batch1',
        'source_status': 1,
        'reason': 'Test',
        'dest_loc': '',
        'quantity': -1
       
    }]
    status, return_data_list = validate_move_inventory(warehouse, validate_list)
    assert status == 'Failed'
    assert invalid_sku_code_const in return_data_list[0].get('status')
    assert 'Invalid Source Location' in return_data_list[0].get('status')
    assert 'Invalid Destination Location' in return_data_list[0].get('status')
    assert 'Invalid Quantity' in return_data_list[0].get('status')

def test_validate_move_inventory_without_batch_for_batch_based(test_create_user, test_create_sku):
    warehouse = User.objects.get(username = WAREHOUSE)
    validate_list = [{
        'sku_code': 'M_B_SKU1',
        'source_loc': 'RM-001',
        'source_status': 1,
        'reason': 'Test',
        'dest_loc': 'RM-001',
        'quantity': 1
       
    }]
    status, return_data_list = validate_move_inventory(warehouse, validate_list)
    assert status == 'Failed'
    assert 'Batch Number is Mandatory for Batch Based SKU' in return_data_list[0].get('status')

#Test aggregate_quantity_on_multiple_stocks
def test_aggregate_quantity_on_multiple_stocks_empty():
    source_stock_list = []
    result, _ = aggregate_quantity_on_multiple_stocks(source_stock_list)
    assert result == []

def test_aggregate_quantity_on_multiple_stocks_single():
    source_stock_list = [{'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 10}]
    result, _ = aggregate_quantity_on_multiple_stocks(source_stock_list)
    assert result == [{'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 10}]

def test_aggregate_quantity_on_multiple_stocks_multiple_same_key():
    source_stock_list = [
        {'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 10},
        {'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 5}
    ]
    result, _ = aggregate_quantity_on_multiple_stocks(source_stock_list)
    assert result == [{'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 15}]

def test_aggregate_quantity_on_multiple_stocks_multiple_different_key():
    source_stock_list = [
        {'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 10},
        {'sku_id': 'sku2', 'source_loc_id': 'loc2', 'quantity': 5}
    ]
    result, _ = aggregate_quantity_on_multiple_stocks(source_stock_list)
    assert result == [
        {'sku_id': 'sku1', 'source_loc_id': 'loc1', 'quantity': 10},
        {'sku_id': 'sku2', 'source_loc_id': 'loc2', 'quantity': 5}
    ]

#Test move_inventory_call_back_filter
def test_move_inventory_call_back_filter_empty():
    move_inv_data = {}
    result = move_inventory_call_back_filter(move_inv_data)
    assert result is None

def test_move_inventory_call_back_filter_no_filter():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone1'}]}
    result = move_inventory_call_back_filter(move_inv_data)
    assert result == move_inv_data

def test_move_inventory_call_back_filter_exclude_zones():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone1'}]}
    filter_dict = {'exclude_zones': ['zone1']}
    result = move_inventory_call_back_filter(move_inv_data, filter_dict)
    assert result == move_inv_data

def test_move_inventory_call_back_filter_intra_zone():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone1'}]}
    filter_dict = {'movement_type': ['Intra Zone']}
    result = move_inventory_call_back_filter(move_inv_data, filter_dict)
    assert result == move_inv_data

def test_move_inventory_call_back_filter_inter_zone():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone2'}]}
    filter_dict = {'movement_type': ['Inter Zone']}
    result = move_inventory_call_back_filter(move_inv_data, filter_dict)
    assert result == move_inv_data

def test_move_inventory_call_back_filter_intra_zone_not_allowed():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone1'}]}
    filter_dict = {'movement_type': ['Inter Zone']}
    result = move_inventory_call_back_filter(move_inv_data, filter_dict)
    assert result is None

def test_move_inventory_call_back_filter_inter_zone_not_allowed():
    move_inv_data = {'move_inventory_data': [{'source_zone': 'zone1', 'destination_zone': 'zone2'}]}
    filter_dict = {'movement_type': ['Intra Zone']}
    result = move_inventory_call_back_filter(move_inv_data, filter_dict)
    assert result is None

#Create Move Inventory
def test_update_stocks_for_batch_based_sku_without_source_stocks(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):
    warehouse = User.objects.get(username = WAREHOUSE)
    request = User.objects.get(username = USERNAME)
    request.user = request
    request_data_list = [{
        'dest_loc': "RM-002",
        "dest_zone": "RM01",
        "quantity": 2000,
        "reason": "testing",
        "sku_code": "M_B_SKU1",
        "source": "RM-001",
        "source_loc": "RM-001",
        "source_batch": "Batch1",
        "source_status": 1,
        "source_zone": "RM01",
        "remarks": "Movement"
    }]
    status, return_dict = validate_move_inventory(warehouse, request_data_list)
    #Assert Validation
    assert status == 'Success'
    status_dict = update_stocks(request, warehouse, return_dict)

    #Assert Movement
    assert status_dict.get('message') == moved_success_const
    assert status_dict.get('status') == 200

    #Assert Movement when stock is not available
    status_dict = update_stocks(request, warehouse, return_dict)

    assert status_dict.get('status') == 400
    assert status_dict.get('message') == 'No Source Stock Available'


#Create Move Inventory
def test_update_stocks_for_batch_based_sku(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):
    warehouse = User.objects.get(username = WAREHOUSE)
    request = User.objects.get(username = USERNAME)
    request.user = request
    request_data_list = [{
        'dest_loc': "RM-002",
        "dest_zone": "RM01",
        "quantity": 10,
        "reason": "testing",
        "sku_code": "M_B_SKU1",
        "source": "RM-001",
        "source_loc": "RM-001",
        "source_batch": "Batch1",
        "source_status": 1,
        "source_zone": "RM01",
        "remarks": "Movement"
    }]
    status, return_dict = validate_move_inventory(warehouse, request_data_list)
    #Assert Validation
    assert status == 'Success'
    status_dict = update_stocks(request, warehouse, return_dict)

    #Assert Movement
    assert status_dict.get('message') == moved_success_const
    assert status_dict.get('status') == 200

#Create Move Inventory for Non Batch Based SKU
def test_update_stocks_for_batch_based_sku(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):
    warehouse = User.objects.get(username = WAREHOUSE)
    request = User.objects.get(username = USERNAME)
    request.user = request
    request_data_list = [{
        'dest_loc': "RM-002",
        "dest_zone": "RM01",
        "quantity": 10,
        "reason": "testing",
        "sku_code": "M_NB_SKU2",
        "source": "RM-001",
        "source_loc": "RM-001",
        "source_batch": "",
        "source_status": 1,
        "source_zone": "RM01",
        "remarks": "Movement"
    }]
    status, return_dict = validate_move_inventory(warehouse, request_data_list)
    #Assert Validation
    assert status == 'Success'
    status_dict = update_stocks(request, warehouse, return_dict)

    #Assert Movement
    assert status_dict.get('message') == moved_success_const
    assert status_dict.get('status') == 200

def test_get_sku_location_data_without_stock(
        test_create_user, test_create_sku, test_create_sellable_locations
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_LOCATION_DATA)
    request.warehouse = warehouse
    response = get_sku_location_data(request)
    assert response.status_code == 200
    assert not loads(response.content).get('data')

def test_get_sku_location_data_with_stock_without_reserved_qty(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_LOCATION_DATA)
    request.warehouse = warehouse
    response = get_sku_location_data(request)
    assert response.status_code == 200
    assert loads(response.content).get('data')
    stock_details = {}

    for data in loads(response.content).get('data'):
        stock_details[data.get('sku_code')] = data

    assert stock_details.get('M_B_SKU1')
    assert stock_details.get('M_B_SKU1').get('quantity') == 2000
    assert stock_details.get('M_B_SKU1').get('total_quantity') == 2000
    assert stock_details.get('M_B_SKU1').get('location_name') == 'RM-001'
    assert stock_details.get('M_B_SKU1').get('batch_number') == 'Batch1'
    assert stock_details.get('M_B_SKU1').get('batch_reference') == 'BatchRef001'

    assert stock_details.get('M_NB_SKU2')
    assert stock_details.get('M_NB_SKU2').get('quantity') == 1000
    assert stock_details.get('M_NB_SKU2').get('total_quantity') == 1000
    assert stock_details.get('M_NB_SKU2').get('location_name') == 'RM-001'
    assert not stock_details.get('M_NB_SKU2').get('batch_display_key')


job_order_creation_dict = [{
    'jo_type': 'Non Standard JO',
    'product_code': 'M_NB_SKU2',
    'product_quantity': '10.0',
    'material_code': 'M_B_SKU1',
    'material_quantity': 10.0,
    'batch_reference': 'BatchRef001',
    'carton_id': '',
    'zone': 'RM01',
    'location': 'RM-001',
    'jo_reference': JO_REFERENCES[0],
    'aux_data': {'product_batch': '30174HD1', 'line_reference': '1'},
    'external_id': '1'
}]

def test_get_sku_location_data_with_reserved_qty(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
    ):

    request = User.objects.get(username=USERNAME)
    request.user = request

    warehouse = User.objects.get(username=WAREHOUSE)

    #Job Order Creation and Picklist Generation to validate reserved quantity in GET Inventory 
    #in Move Inventory GET
    validate_job_order(request, warehouse, job_order_creation_dict, update_jo = False)
    get_jo_material_data(request, warehouse, [JO_REFERENCES[0]], [])

    factory = RequestFactory()
    request = factory.get(GET_SKU_LOCATION_DATA)
    request.warehouse = warehouse
    response = get_sku_location_data(request)
    assert response.status_code == 200
    assert loads(response.content).get('data')
    stock_details = {}

    for data in loads(response.content).get('data'):
        stock_details[data.get('sku_code')] = data

    assert stock_details.get('M_B_SKU1')
    assert stock_details.get('M_B_SKU1').get('reserved_qty') == 10
    assert stock_details.get('M_B_SKU1').get('available_quantity') == 2000
    assert stock_details.get('M_B_SKU1').get('quantity') == 1990
    assert stock_details.get('M_B_SKU1').get('location_name') == 'RM-001'
    assert stock_details.get('M_B_SKU1').get('batch_number') == 'Batch1'
    assert stock_details.get('M_B_SKU1').get('batch_reference') == 'BatchRef001'

#Test get_search_params_of_stock

def test_get_search_params_of_stock_empty():
    request_data = {}
    result = get_search_params_of_stock(request_data)
    assert result == ({}, {}, {}, 10)

def test_get_search_params_of_stock_location():
    request_data = {'location': 'loc1'}
    result = get_search_params_of_stock(request_data)
    assert result == ({'location__location__iexact': 'loc1'}, {}, {}, 10)

def test_get_search_params_of_stock_sku_code():
    request_data = {'sku_code': 'sku1'}
    result = get_search_params_of_stock(request_data)
    assert result == ({}, {'sku__sku_code__icontains': 'sku1'}, {'sku__eannumbers__ean_number__iexact': 'sku1'}, 10)

def test_get_search_params_of_stock_carton():
    request_data = {'lpn_number': 'carton1'}
    result = get_search_params_of_stock(request_data)
    assert result == ({'lpn_number': 'carton1'}, {}, {}, 10)

def test_get_search_params_of_stock_limit():
    request_data = {'limit': 5}
    result = get_search_params_of_stock(request_data)
    assert result == ({}, {}, {}, 5)

def test_get_search_params_of_stock_all():
    request_data = {'location': 'loc1', 'sku_code': 'sku1', 'lpn_number': 'carton1', 'limit': 5}
    result = get_search_params_of_stock(request_data)
    assert result == (
        {'location__location__iexact': 'loc1', 'lpn_number': 'carton1'},
        {'sku__sku_code__icontains': 'sku1'},
        {'sku__eannumbers__ean_number__iexact': 'sku1'},
        5
    )

def test_get_sku_batches(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_BATCHES)
    request.warehouse = warehouse
    response = get_sku_batches(request)
    assert response.status_code == 200

def test_get_sku_batches_to_get_locations(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_BATCHES)
    request.GET = {'sku_code': 'M_B_SKU1', 'get_locations': 'true'}
    request.warehouse = warehouse
    response = get_sku_batches(request)
    response_data = loads(response.content)
    assert response.status_code == 200
    assert response_data.get('locations')
    assert 'RM-001' in response_data.get('locations')

def test_get_sku_batches_to_get_stock_details_for_batch_based(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_BATCHES)
    request.GET = {'sku_code': 'M_B_SKU1', 'sku_location': 'RM-001'}
    request.warehouse = warehouse
    response = get_sku_batches(request)
    response_data = loads(response.content)
    assert response.status_code == 200
    assert response_data.get('sku_batches')
    assert response_data.get('sku_batch_details')

def test_get_sku_batches_to_get_stock_details_for_non_batch_based(
        test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory
        ):
    warehouse = User.objects.get(username = WAREHOUSE)
    factory = RequestFactory()
    request = factory.get(GET_SKU_BATCHES)
    request.GET = {'sku_code': 'M_NB_SKU2', 'sku_location': 'RM-001'}
    request.warehouse = warehouse
    response = get_sku_batches(request)
    assert response.status_code == 200


@pytest.mark.django_db
class TestGetMoveInventory:

    @pytest.fixture
    def instance(self, test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory):
        instance = MoveInventorySet()
        instance.warehouse = User.objects.get(username=WAREHOUSE)
        instance.user = User.objects.get(username = USERNAME)
        return instance

    def test_get_move_inventory_search_params(self, instance):
        request_data={
            'limit': 10,
            'from_date': '2021-01-01',
            'to_date': '2021-12-31',
            'batch_reference': 'BatchRef001',
            'batch_number': 'Batch1',
            'source_zone': 'RM01',
            'source_location': 'RM-001',
            'destination_zone': 'RM01',
            'destination_location': 'RM-002',
            'sku_code': 'M_NB_SKU2',
            'sku_list': ['M_NB_SKU2']
        }
        search_params = instance.get_filter_params_for_move_inventory(instance.warehouse, {}, request_data, limit = 10)
        assert search_params

    def test_get_move_inventory(self, instance):
        # Create a mock request
        request_factory = RequestFactory()
        request = request_factory.get('/')
        request.user = instance.user
        request.warehouse = instance.warehouse
        instance.request = request

        request_data_list = [{
            'dest_loc': "RM-002",
            "dest_zone": "RM01",
            "quantity": 10,
            "reason": "testing",
            "sku_code": "M_B_SKU1",
            "source": "RM-001",
            "source_loc": "RM-001",
            "source_batch": "Batch1",
            "source_status": 1,
            "source_zone": "RM01",
            "remarks": "Movement"
        }]

        # Move Inventory Creation
        _, return_dict = validate_move_inventory(instance.warehouse, request_data_list)
        update_stocks(request, instance.warehouse, return_dict)

        # Call the get method
        response = instance.get()
        assert response.get('status') == 200
        assert len(response.get('data')) == 1
        data = response.get('data')[0]
        assert data.get('sku_code') == 'M_B_SKU1'
        assert data.get('batch_number') == 'Batch1'
        assert data.get('remarks') == 'Movement'

def test_move_inventory_form(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    response = move_inventory_form(warehouse, {})
    assert response == MOVE_INVENTORY_UPLOAD_FIELDS

def test_validate_and_update_batch_details(test_create_user, test_create_sku, 
                                           test_create_sellable_locations, test_create_inventory):
    
    warehouse = User.objects.get(username = WAREHOUSE)
    data_list = [{"batch_ref_no": "BatchRef001", "sku_code": "M_B_SKU1"}]
    status, data_list = validate_and_update_batch_details(warehouse, data_list)
    assert status == 'Success'
    assert data_list[0].get('source_batch') == 'Batch1'

    data_list = [{"batch_ref_no": "BatchRef0012", "sku_code": "M_B_SKU1"}]
    status, data_list = validate_and_update_batch_details(warehouse, data_list)
    assert status == 'Failed'
    assert data_list[0].get('Status') == ['Invalid Batch Reference']

def test_validate_duplicate_records():
    data_list = [{
        "sku_code": "SKU1",
        "source_loc": "SOURCE1",
        "dest_loc": "DEST1",
        "batch_ref_no": "BATCH_REF1",
        "source_status" : "Available",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": "Y"
    }]
    status, _ = validate_duplicate_records(data_list)
    assert not status

    data_list = [{
        "sku_code": "SKU1",
        "source_loc": "SOURCE1",
        "dest_loc": "DEST1",
        "batch_ref_no": "BATCH_REF1",
        "source_status" : "Available",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": "Y"
    },
    {
        "sku_code": "SKU1",
        "source_loc": "SOURCE1",
        "dest_loc": "DEST1",
        "batch_ref_no": "BATCH_REF1",
        "source_status" : "Available",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": "Y"
    }]

    status, validated_list = validate_duplicate_records(data_list)
    assert status
    assert validated_list[0].get('status') == 'Duplicate Entry Found For This SKU, Source Location and Batch Reference'

def test_split_data_for_tasks_creation(test_create_user, test_create_sku, 
                                       test_create_sellable_locations, test_create_inventory):
    data_list = [{
        "sku_code": "M_NB_SKU2",
        "source_loc": "RM-001",
        "dest_loc": "RM-002",
        "batch_ref_no": "",
        "source_status" : "",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": ""
    },
    {
        "sku_code": "M_B_SKU1",
        "source_loc": "RM-001",
        "dest_loc": "RM-002",
        "batch_ref_no": "BatchRef001",
        "source_batch" : "Batch1",
        "source_status" : "Available",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": "Y"
    }]
    tasks_data, return_dict = split_data_for_tasks_creation(data_list)
    assert tasks_data[0].get('sku_code') == 'M_B_SKU1'
    assert return_dict[0].get('sku_code') == 'M_NB_SKU2'


def test_validate_available_tasks_with_uploaded_tasks(test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory):
    data_list = [{
        "sku_code": "M_NB_SKU2",
        "source_loc": "RM-001",
        "dest_loc": "RM-002",
        "batch_ref_no": "",
        "source_status" : "",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": ""
    },
    {
        "sku_code": "M_B_SKU1",
        "source_loc": "RM-001",
        "dest_loc": "RM-002",
        "batch_ref_no": "BatchRef001",
        "source_batch" : "Batch1",
        "source_status" : "Available",
        "quantity" : "10",
        "reason": "Testing",
        "task_creation": "Y"
    }]
    warehouse = User.objects.get(username = WAREHOUSE)
    _, return_dict = validate_move_inventory(warehouse, data_list)
    tasks_data, return_dict = split_data_for_tasks_creation(return_dict)
    tasks_data = prepare_tasks_data(tasks_data)
    create_tasks(warehouse, warehouse, tasks_data)
    status, data_list = validate_available_tasks_with_uploaded_tasks(warehouse, data_list)
    assert status
    assert data_list[0].get('status') == 'Entry Already exists in Move Inventory'