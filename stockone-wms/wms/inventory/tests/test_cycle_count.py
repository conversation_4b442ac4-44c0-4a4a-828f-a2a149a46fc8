#package imports
import pytest
import datetime
from json import loads, dumps

#django imports
from django.test import RequestFactory

#common constant imports
from production.tests.constants import USERNAME, WAREHOUSE

#common imports
from inventory.tests.common import (
    test_create_user, test_create_sku, test_create_sellable_locations,
    test_create_inventory
)

#Base imports
from wms_base.models import User

#Inventory Function Imports
from inventory.views.cycle_count.cycle_count import (
    generate_cycle_count, submit_cycle_count)

from inventory.views.cycle_count.datatable import (
    get_cycle_count, get_cycle_confirmed, get_confirmed_cycle,
    )

from inventory.views.adjustment.inventory_adjustment import (get_inventory_adjustment,
    confirm_inventory_adjustment, insert_inventory_adjustment, delete_inventory_adjustment)


#cycle count imports
from inventory.views.cycle_count.uploads import (
    get_location_master_dict,validate_sku_code, validate_location, 
    validate_stock_status,validate_quantity, validate_mrp, validate_weight, 
    validate_price,validate_adjustment_type, validate_batch_based, 
    validate_inv_adj_reason,validate_batch_details,validate_all_fields, validate_cycle_count_form,
    inventory_adjustment_form, create_cycle_count_form, sku_location_validation,
    stock_validation, existing_cycle_count_validation, validate_create_cycle_count_form,
    create_cycle_count, create_cycle_count_upload,
)

from inventory.views.locator.validations import (
    validate_manufactured_date, validate_expiry_date,
    update_dates, get_sku_codes, get_sku_master_dict, validate_dates,
    )
                                                 

from inventory.tests.constants import (
    INSERT_INENTORY_ADJUSTMENT_PAYLOAD_1, 
)

from wms_base.wms_utils import (
    ADJUST_INVENTORY_EXCEL_MAPPING, CYCLE_COUNT_CREATION_HEADERS)

# Assuming sku_code_const is a global constant
sku_code_const = 'SKU Code'
time_zone_constant = 'Asia/Kolkata'
sku_error_message = 'Invalid WMS Code'
location_error = 'Invalid Location'
reason_error = 'Reason is Mandatory'
batch_number = 'Batch Number'
batch_reference = 'Batch Reference'
vendor_batch_number = 'Vendor Batch Number'
status_key = 'Status(OnHold, Available, Reserved, Rejected, Obsolete, Blocked, Consumed)'
physical_quantity_const = "Physical Quantity"
manufactured_date = 'Manufactured Date(YYYY-MM-DD)'
expiry_date = 'Expiry Date(YYYY-MM-DD)'
restest_date = 'Retest Date(YYYY-MM-DD)'
reevaluation_date = 'Reevaluation date(YYYY-MM-DD)'
best_before_date = 'Best Before Date(YYYY-MM-DD)'
batch_ref_number = 'Batch Number/Ref'

# Test get_sku_codes
def test_get_sku_codes():
    # Test get_sku_codes
    assert get_sku_codes([]) == ([], [])

    # Test get_sku_codes with unavailable sku codes
    data_to_integrate = [{'other_key': 'value'}, {'another_key': 'value'}]
    assert get_sku_codes(data_to_integrate) == ([], [])

    # Test get_sku_codes with sku codes
    data_to_integrate = [{'other_key': 'value', 'sku_code': 'sku1'}, {'another_key': 'value', 'sku_code': 'sku2'}]
    result = get_sku_codes(data_to_integrate)
    assert 'sku1' in result[0]
    assert 'sku2' in result[0]

def test_get_location_master_dict(test_create_user):
    #Test get_location_master_dict without Locations
    warehouse = User.objects.get(username = WAREHOUSE)
    result, _, _, _, _ = get_location_master_dict(warehouse)
    assert result == {}


#Test get_location_master_dict with Locations
def test_get_location_master_dict_with_locations(test_create_user, test_create_sellable_locations):
    warehouse = User.objects.get(username = WAREHOUSE)
    result, _, _, _, _= get_location_master_dict(warehouse)
    assert 'RM-001' in result
    assert 'RM-002' in result
    assert 'CS-001' in result
    assert 'CS-002' in result


def test_get_sku_master_dict(test_create_user, test_create_sku):
    #Test get_sku_master_dict without sku codes
    warehouse = User.objects.get(username = WAREHOUSE)
    result, _ = get_sku_master_dict(warehouse, [])
    assert result == {}

    #Test get_sku_master_dict with sku codes
    warehouse = User.objects.get(username = WAREHOUSE)
    result, _ = get_sku_master_dict(warehouse, ["M_B_SKU1", "M_NB_SKU2"])
    assert 'M_B_SKU1' in result
    assert 'M_NB_SKU2' in result
    

def test_validate_sku_code(test_create_user, test_create_sku):
    #test validate_sku_code function with valid sku code
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_master_dict, _ = get_sku_master_dict(warehouse, ["M_B_SKU1", "M_NB_SKU2"])
    data_dict, status, sku_code =  validate_sku_code('M_B_SKU1', {}, sku_master_dict, [])
    assert status == []
    assert sku_code == 'M_B_SKU1'
    assert sku_code in data_dict.get('sku')

    #test validate_sku_code function with empty sku code
    data_dict, status, sku_code =  validate_sku_code('', {}, sku_master_dict, [])
    assert 'SKU Should not be empty' in status
    assert sku_code == ''
    assert data_dict == {}

    #test validate_sku_code function with invalid sku code
    data_dict, status, sku_code =  validate_sku_code('M_B_SKU3', {}, sku_master_dict, [])
    assert sku_error_message in status
    assert sku_code == 'M_B_SKU3'
    assert data_dict == {}


def test_validate_location(test_create_user, test_create_sku, test_create_sellable_locations):
    warehouse = User.objects.get(username = WAREHOUSE)
    location_master_dict, _, _, _, _ = get_location_master_dict(warehouse)

    #test validate validate_location function with valid location
    data_dict, status =  validate_location('RM-001', location_master_dict, {}, [])
    assert status == []
    assert 'RM-001' in data_dict.get('location')

    #test validate validate_location function with empty location
    data_dict, status =  validate_location('', location_master_dict, {}, [])
    assert 'Location should not be empty' in status
    assert data_dict == {}

    #test validate validate_location function with invalid location
    data_dict, status =  validate_location('RM-003', location_master_dict, {}, [])
    assert location_error in status
    assert data_dict == {}

def test_validate_stock_status_valid_stock_status():
    #test validate validate_stock_status function with valid stock status
    data_dict, status = validate_stock_status("Available", {}, [], [])
    assert status == []
    assert 'stock_status' in data_dict

    #test validate validate_stock_status function with invalid stock status
    data_dict, status = validate_stock_status("invalid_stock_status", {}, [], [])
    assert 'Invalid Stock Status' in status
    assert data_dict == {}

    #test validate validate_stock_status function with empty stock status
    data_dict, status = validate_stock_status("", {}, [], [])
    assert status == []
    assert data_dict == {}


def test_validate_quantity():
    #test validate_quantity function with valid quantity
    data_dict, status = validate_quantity("100", {}, [])
    assert status == []
    assert data_dict.get('quantity') == '100.0'

    #test validate_quantity function with invalid quantity
    data_dict, status = validate_quantity("invalid_quantity", {}, [])
    assert 'Invalid Quantity' in status
    assert data_dict == {}

    #test validate_quantity function with empty quantity
    data_dict, status = validate_quantity("", {}, [])
    assert 'Physical Quantity Should not be empty' in status
    assert data_dict == {}

    #test validate_quantity function with negative quantity
    _, status = validate_quantity("-100", {}, [])
    assert 'Invalid Quantity' in status

def test_validate_mrp():
    #test validate_mrp function with valid mrp
    data_dict, status = validate_mrp("100", {}, [])
    assert status == []
    assert data_dict.get('mrp') == '100.0'

    #test validate_mrp function with invalid mrp
    _, status = validate_mrp("invalid_mrp", {}, [])
    assert 'Invalid MRP' in status

    #test validate_mrp function with negative mrp
    _, status = validate_mrp("-100", {}, [])
    assert 'Invalid MRP' in status

    #test validate_mrp function with empty mrp
    data_dict, status = validate_mrp("", {}, [])
    assert status == []
    assert data_dict.get('mrp') == 0

def test_validate_weight_valid_weight():
    #test validate_weight function with valid weight
    data_dict = validate_weight("100", {})
    assert 'weight' in data_dict

    #test validate_weight function with empty weight
    data_dict = validate_weight("", {})
    assert data_dict.get('weight') == ''

def test_validate_price():
    #test validate_price function with valid price
    data_dict, _ = validate_price("100", {}, [])
    assert data_dict.get('unit_price') == '100.0'

    #test validate_price function with invalid price
    _, status = validate_price("invalid_price", {}, [])
    assert 'Invalid Price' in status

    #test validate_price function with negative price
    _, status = validate_price("-100", {}, [])
    assert 'Invalid Price' in status

def test_validate_adjustment_type():
    #test validate_adjustment_type function with valid adjustment type
    data_dict, status = validate_adjustment_type("scrap", {}, [])
    assert status == []
    assert data_dict.get('adjustment_type') == 'scrap'

    #test validate_adjustment_type function with invalid adjustment type
    _, status = validate_adjustment_type("invalid_adjustment_type", {}, [])
    assert 'Invalid Adjustment Type' in status

    #test validate_adjustment_type function with empty adjustment type
    data_dict , status = validate_adjustment_type("", {}, [])
    assert data_dict.get('adjustment_type') == 'unscheduled'
    assert status == []

def test_validate_batch_based_valid_batch_based():
    #test validate_batch_based function with empty data_dict
    _, status = validate_batch_based({}, [])
    assert status == []

    #test validate_batch_based function with valid batch_based
    _, status = validate_batch_based({'batch_based': 'true'}, [])
    assert 'Batch No or Vendor Batch Number or Batch Reference is Mandatory for Batch Based' in status

def test_validate_inv_adj_reason_no_cell_data():
    data_dict = {}
    status = []
    #test validate_inv_adj_reason function with no cell data
    assert validate_inv_adj_reason(None, None, data_dict, status) == ({}, [reason_error])

    data_dict = {}
    status = []
    assert validate_inv_adj_reason('cell_data', None, data_dict, status) == ({'reason': 'cell_data'}, [])

    data_dict = {}
    status = []
    assert validate_inv_adj_reason('cell_data', ['other_reason'], data_dict, status) == ({'reason': 'cell_data'}, ['Enter only configured Inventory Adjustment reasons'])

    data_dict = {}
    status = []
    assert validate_inv_adj_reason('cell_data', ['cell_data'], data_dict, status) == ({'reason': 'cell_data'}, [])

def test_validate_batch_details():
    # Test with empty batch details
    data_dict = validate_batch_details('', '', '', '', {}, {}, '','',[], batch_creation = "true")
    for key, value in data_dict[0].items():
        assert value == ''

    # Test with batch details but without sku details
    data_dict = validate_batch_details('batch1', 'ref1', 'vendor1', 'inspection1', {}, {}, '','',[], batch_creation = "true")

    for key, value in data_dict[0].items():
        if key == 'batch_based':
            assert value == ''

    # Test with batch and sku details
    sku_details_dict = {'sku1': {'batch_based': 'yes'}}
    data_dict = validate_batch_details('batch1', 'ref1', 'vendor1', 'inspection1', {}, sku_details_dict, 'sku1','', [], batch_creation = "true")
    for key, value in data_dict[0].items():
        if key == 'batch_based':
            assert value == 'yes'

def test_validate_manufactured_date():
    status = []

    # Test with None date
    validate_manufactured_date(None, status)
    assert status == []

    # Test with past date
    past_date = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=1)
    validate_manufactured_date(past_date, status)
    assert status == []

    # Test with future date
    future_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1)
    validate_manufactured_date(future_date, status)
    assert status == ['Invalid Manufactured Date manufactured_date format']

def test_validate_expiry_date():
    status = []

    # Test with None dates
    validate_expiry_date(None, None, status)
    assert status == []

    # Test with expiry_date before mfg_date
    mfg_date = datetime.datetime.now(datetime.timezone.utc)
    expiry_date = mfg_date - datetime.timedelta(days=1)
    validate_expiry_date(mfg_date, expiry_date, status)
    assert status == ['Invalid Expiry Date expiry_date format']

    # Test with expiry_date after mfg_date
    status = []
    expiry_date = mfg_date + datetime.timedelta(days=1)
    validate_expiry_date(mfg_date, expiry_date, status)
    assert status == []

def test_update_dates():
    # Test with None dates
    data_dict = update_dates(None, None, None, None, None, {})
    assert data_dict == {}

    # Test with some dates
    mfg_date = datetime.datetime.now(datetime.timezone.utc)
    expiry_date = mfg_date + datetime.timedelta(days=365)
    retest_date = mfg_date + datetime.timedelta(days=180)
    reevaluation_date = mfg_date + datetime.timedelta(days=90)
    best_before_date = mfg_date + datetime.timedelta(days=270)
    data_dict = update_dates(mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date, {})
    assert data_dict == {
        'manufactured_date': mfg_date,
        'expiry_date': expiry_date,
        'retest_date': retest_date,
        'reevaluation_date': reevaluation_date,
        'best_before_date': best_before_date
    }

def test_validate_dates():
    status = []
    data_dict = {}

    # Test with None dates
    data_dict, status = validate_dates(None, None, None, None, None, time_zone_constant, status, data_dict)
    assert data_dict == {
        'manufactured_date': False,
        'expiry_date': False,
        'retest_date': False,
        'reevaluation_date': False,
        'best_before_date': False
    }
    assert status == []

    # Test with some valid dates
    status = []
    data_dict = {}
    mfg_date = '2022-01-01'
    expiry_date = '2023-01-01'
    retest_date = '2022-07-01'
    reevaluation_date = '2022-04-01'
    best_before_date = '2022-10-01'
    data_dict, status = validate_dates(mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date, time_zone_constant, status, data_dict)
    assert data_dict['manufactured_date'] != False
    assert data_dict['expiry_date'] != False
    assert data_dict['retest_date'] != False
    assert data_dict['reevaluation_date'] != False
    assert data_dict['best_before_date'] != False
    assert status == []

    # Test with some invalid dates
    status = []
    data_dict = {}
    mfg_date = '2022-01-01'
    expiry_date = '2021-01-01'  # expiry_date before mfg_date
    retest_date = '2022-07-01'
    reevaluation_date = '2022-04-01'
    best_before_date = '2022-10-01'
    data_dict, status = validate_dates(mfg_date, expiry_date, retest_date, reevaluation_date, best_before_date, time_zone_constant, status, data_dict)
    assert status == ['Invalid Expiry Date expiry_date format']

def test_validate_all_fields():
    data_row = {
        'sku_code': 'SKU123',
        'batch_no': 'BN123',
        'batch_reference': 'BR123',
        'vendor_batch_number': 'VBN123',
        'inspection_lot_number': 'ILN123',
        'location': 'LOC123',
        'reason': 'Reason123',
        'stock_status': 'Available',
        'mrp': 100,
        'quantity': 10,
        'weight': '10kg',
        'Price': 10,
        'adjustment_type': 'Scrap',
        'manufactured_date': '2022-01-01',
        'expiry_date': '2023-01-01',
        'retest_date': '2022-07-01',
        'reevaluation_date': '2022-04-01',
        'best_before_date': '2022-10-01'
    }
    status = []
    data_dict = {}
    sku_details_dict = {'SKU123': {'sku_code': 'SKU123'}}
    location_master_dict = {'LOC123': {'location': 'LOC123'}}
    configured_inv_adjustment_reasons = ['Reason123']
    timezone = time_zone_constant
    restricted_adjustment_status = []

    data_dict, status = validate_all_fields(
        data_row, status, data_dict, sku_details_dict, location_master_dict,
        configured_inv_adjustment_reasons, timezone, restricted_adjustment_status, {}
    )

    assert data_dict != {}
    assert status

    # Test with invalid data
    data_row = {
            'sku_code': 'inv',
            'batch_no': 'inv',
            'batch_reference': 'inv',
            'vendor_batch_number': 'inv',
            'inspection_lot_number': 'inv',
            'location': 'inv',
            'reason': 'inv',
            'stock_status': 'inv',
            'mrp': "inv",
            'quantity': 'inv',
            'weight': 'inv',
            'Price': 'inv',
            'adjustment_type': 'inv',
            'manufactured_date': 'inv',
            'expiry_date': 'inv',
            'retest_date': 'inv',
            'reevaluation_date': 'inv',
            'best_before_date': 'inv'
        }

    data_dict, status = validate_all_fields(
        data_row, status, data_dict, sku_details_dict, location_master_dict,
        configured_inv_adjustment_reasons, timezone, restricted_adjustment_status, {}
    )

    assert data_dict != {}
    assert status != []

def test_validate_cycle_count_form(test_create_user):
    # Assuming Warehouse and UserProfile are models and they have been imported
    warehouse = User.objects.get(username = WAREHOUSE)

    data_to_integrate = [
        {
            'sku_code': 'SKU123',
            'location': 'LOC123',
            'batch_no': 'BN123',
            'vendor_batch_number': 'VBN123',
            'batch_reference': 'BR123',
            'stock_status': 'Available',
            'mrp' : '0'
        },
        {
            'sku_code': 'SKU456',
            'location': 'LOC456',
            'batch_no': 'BN456',
            'vendor_batch_number': 'VBN456',
            'batch_reference': 'BR456',
            'stock_status': 'Available',
            'mrp' : '0'
        }
    ]
    result = validate_cycle_count_form(warehouse, data_to_integrate)

    # Iterate over the result and perform assertions
    for data in result:
        for data_item in data:
            status_list = data_item["Status"]
            assert sku_error_message in status_list
            assert location_error in status_list
            assert reason_error in status_list
    
def test_inventory_adjustment_form(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    header = inventory_adjustment_form(warehouse, extra_params={})
    assert header == ADJUST_INVENTORY_EXCEL_MAPPING
    
@pytest.fixture
def generate_cycle_count_datatable(test_create_user, test_create_sku,
                                   test_create_sellable_locations, test_create_inventory):
    warehouse = User.objects.get(username = WAREHOUSE)
    user = User.objects.get(username = USERNAME)

    # generate Cycle Count Datatable
    factory = RequestFactory()
    request = factory.get('', data={
        'columnFilter': '{"sku_code":"M_NB_SKU2"}',
    })
    temp_data = {'draw': None, 'recordsTotal': 0, 'recordsFiltered': 0, 'aaData': []}
    get_cycle_count(0, 10, temp_data, 0, '', 0, request, warehouse, {})
    return temp_data, warehouse, user

@pytest.fixture
def test_generate_cycle_count_data(generate_cycle_count_datatable):
    temp_data, warehouse, user = generate_cycle_count_datatable
    sku_id = temp_data['aaData'][0]['sku_id']
    sku_code = temp_data['aaData'][0]['sku_code']
    location_id = temp_data['aaData'][0]['location_id']
    location = temp_data['aaData'][0]['location']
    stock_status = temp_data['aaData'][0]['stock_status']
    batch_detail_id = temp_data['aaData'][0]['batch_detail_id']

    # Create a request
    factory = RequestFactory()
    request = factory.post('', data='{}', content_type='')
    data_dict = {"sku_id": sku_id, 
                 "location_id": location_id, 
                 "stock_status": stock_status,
                 "sku_code" : sku_code,
                 "location": location,
                 "batch_detail_id": batch_detail_id,
                 }
    request._body = dumps([data_dict])
    request.warehouse = warehouse
    request.user = user
    response = generate_cycle_count(request)
    return response, warehouse, user

@pytest.fixture
def test_get_cycle_confirmed_datatable(test_generate_cycle_count_data):
    temp_data, warehouse, user = test_generate_cycle_count_data
    factory = RequestFactory()
    request = factory.get('', data={
                        'columnFilter': '{"wms_code":"M_NB_SKU2"}',
             })
    temp_data = {'draw': None, 'recordsTotal': 0, 'recordsFiltered': 0, 'aaData': []}
    get_cycle_confirmed(0, 10, temp_data, 0, '', 0, request, warehouse, {})
    assert len(temp_data['aaData']) == 1
    return temp_data, warehouse, user

@pytest.fixture
def test_confirm_cycle_count_popup(test_get_cycle_confirmed_datatable):
    temp_data, warehouse, user = test_get_cycle_confirmed_datatable
    cycle_id = temp_data['aaData'][0]['cycle_count_id']
    factory = RequestFactory()
    request = factory.get('', data={'data_id': cycle_id})
    request.warehouse = warehouse
    request.user = user
    response = get_confirmed_cycle(request)
    assert response.status_code == 200
    response_data = loads(response.content)
    return response_data, warehouse, user

@pytest.fixture
def test_confirm_cycle_count(test_confirm_cycle_count_popup):
    response_data, warehouse, user = test_confirm_cycle_count_popup
    cycle_id = response_data['data'][0]['id']

    # Create a request to submit cycle count
    factory = RequestFactory()
    request = factory.post('', data='{}', content_type='')
    request.POST = {str(cycle_id):'50'}
    request.warehouse = warehouse
    request.user = user
    submit_response = submit_cycle_count(request)
    assert submit_response.status_code == 200
    assert loads(submit_response.content) == {"message": "Updated Successfully", "status": 200}
    return submit_response, warehouse, user

@pytest.fixture
def test_confirm_inventory_adjustment_data_table(test_confirm_cycle_count):
    _, warehouse, user = test_confirm_cycle_count
    # request for confirm cycle count data table
    factory = RequestFactory()
    request = factory.get('', data={
        'columnFilter': '{"sku_code":"M_NB_SKU2"}',
    })
    temp_data = {'draw': None, 'recordsTotal': 0, 'recordsFiltered': 0, 'aaData': []}
    get_inventory_adjustment(0, 10, temp_data, 0, '', 0, request, warehouse, {})
    return temp_data, warehouse, user

@pytest.fixture
def test_confirm_inventory_adjustments(test_confirm_inventory_adjustment_data_table):
    temp_data, warehouse, user = test_confirm_inventory_adjustment_data_table
    cycle_id = temp_data['aaData'][0]['cycle']
    factory = RequestFactory()
    request = factory.post('', data='{}', content_type='')
    data_dict = {"data":{str(cycle_id):{"reason":"testing"}}}
    request._body = dumps(data_dict)
    request.warehouse = warehouse
    request.user = user
    response = confirm_inventory_adjustment(request)
    return response

@pytest.fixture
def test_reject_inventory_adjustment(test_confirm_inventory_adjustment_data_table):
    temp_data, warehouse, user = test_confirm_inventory_adjustment_data_table
    cycle_id = temp_data['aaData'][0]['cycle']
    factory = RequestFactory()
    request = factory.get('', data={
        str(cycle_id): str(cycle_id),
    })
    request.warehouse = warehouse
    request.user = user
    response = delete_inventory_adjustment(request)
    return response

def test_generate_cycle_count(test_generate_cycle_count_data):
    # case 1: Generate Cycle Count with valid payload
    response, _, _ = test_generate_cycle_count_data
    assert response.status_code == 200

def test_submit_cycle_count_and_confirm_cycle_count_datatable(test_confirm_inventory_adjustment_data_table):
    temp_data,_, _  = test_confirm_inventory_adjustment_data_table
    assert temp_data['recordsTotal'] == 0
    assert temp_data['aaData'][0]['sku_code'] == 'M_NB_SKU2'
    assert temp_data['aaData'][0]['location'] == 'RM-001'
    assert temp_data['aaData'][0]['stock_status'] == 'Available'
    assert temp_data['aaData'][0]['total_quantity'] == 1000
    assert temp_data['aaData'][0]['physical_quantity'] == 50
    assert temp_data['aaData'][0]['discrepancy_qty'] == -950

def test_confirm_inventory_adjustment(test_confirm_inventory_adjustments):
    response = test_confirm_inventory_adjustments
    assert response.status_code == 200
    assert loads(response.content) == {'message': 'Updated Successfully'}

def test_insert_inventory_adjustment(test_create_user, test_create_sku,
                                     test_create_sellable_locations, test_create_inventory):
    
    warehouse = User.objects.get(username = WAREHOUSE)
    user = User.objects.get(username = USERNAME)

    #case 1 test insert_inventory adjustmemnt 
    factory = RequestFactory()
    request = factory.post('', data='{}', content_type='')
    data_dict = INSERT_INENTORY_ADJUSTMENT_PAYLOAD_1
    request._body = dumps(data_dict)
    request.warehouse = warehouse
    request.user = user
    response = insert_inventory_adjustment(request)
    assert response.status_code == 200
    assert loads(response.content).get('message') == ['Added Successfully']
    assert loads(response.content).get('cycle_ids')

def test_delete_inventory_adjustment(test_reject_inventory_adjustment):
    response = test_reject_inventory_adjustment
    assert response.status_code == 200
    assert response.content.decode('utf-8') == 'Deleted Successfully'

def test_create_cycle_count_form(test_create_user):
    warehouse = User.objects.get(username = WAREHOUSE)
    header = create_cycle_count_form(warehouse, extra_params={})
    assert header == CYCLE_COUNT_CREATION_HEADERS

def test_sku_location_validation_function(test_create_user, test_create_sku, test_create_sellable_locations):
    warehouse = User.objects.get(username = WAREHOUSE)
    sku_master_dict, _ = get_sku_master_dict(warehouse, ["M_B_SKU1", "M_NB_SKU2"])
    location_master_dict, _, _ ,_, _= get_location_master_dict(warehouse)
    data_list = [{sku_code_const: "M_B_SKU1", "Location": "RM-001"}]

    #case 1, with correct sku and location
    status, data_list = sku_location_validation(data_list, sku_master_dict, location_master_dict)
    assert not status
    assert data_list == [{sku_code_const : 'M_B_SKU1', 'Location': 'RM-001', 'Status': None}]

    #case 2, with empty sku and location
    data_list = [{sku_code_const : "", "Location": ""}]
    status, data_list = sku_location_validation(data_list, sku_master_dict, location_master_dict)
    assert status
    assert data_list == [{sku_code_const : '', 'Location': '', 'Status': 'Location should not be empty.'}]

    #case 3 with invalid sku and location
    data_list = [{sku_code_const: "unavailable_sku", "Location": "unavailble_location"}]
    status, data_list = sku_location_validation(data_list, sku_master_dict, location_master_dict)
    assert status
    assert data_list ==  [{sku_code_const: 'unavailable_sku', 'Location': 'unavailble_location', 'Status': "SKU code 'unavailable_sku' not found.. Location 'unavailble_location' not found."}]

def test_stock_validation_function(test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory):
    warehouse = User.objects.get(username = WAREHOUSE)
    locations = ["RM-001"]

    #case 1, with invalid stock data.
    data_list = [{sku_code_const: "M_B_SKU1", "Location": "RM-001"}, {sku_code_const: "M_B_SKU1", "Location": "RM-001"}]
    status, data_list, cycle_creation_data = stock_validation(data_list, locations, warehouse)
    assert status
    assert data_list == [{sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', 'Status': 'Stock Not Found for this given SKU, Location Batch Number/Ref and Stock Status Combination.'}, 
                         {sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', 'Status': 'Stock Not Found for this given SKU, Location Batch Number/Ref and Stock Status Combination.'}]
    
    assert cycle_creation_data == []

    #case 2, with valid stock data with batch no in Batch Number/Ref
    data_list = [{sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "Batch1"}]
    status, data_list, cycle_count_data = stock_validation(data_list, locations, warehouse)
    assert not status
    assert data_list == [{sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', batch_ref_number: 'Batch1', 'Status': None}]
    assert cycle_count_data[0].get('batch_detail__batch_no')
    assert cycle_count_data[0].get('batch_detail__batch_reference')
    assert cycle_count_data[0].get('location__location')

    #case 3, with stock data with batch reference in Batch Number/Ref
    data_list = [{sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "BatchRef001"}]
    status, data_list, cycle_count_data = stock_validation(data_list, locations, warehouse)
    assert not status
    assert data_list == [{sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', batch_ref_number : 'BatchRef001', 'Status': None}]
    assert cycle_count_data[0].get('batch_detail__batch_no')
    assert cycle_count_data[0].get('batch_detail__batch_reference')
    assert cycle_count_data[0].get('location__location')

    #case 3, duplicate entry validation
    data_list = [{sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "BatchRef001"},
                 {sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "BatchRef001"}]
    status, data_list, cycle_count_data = stock_validation(data_list, locations, warehouse)
    assert status
    assert data_list == [{sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', batch_ref_number : 'BatchRef001', 'Status': None}, 
                         {sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', batch_ref_number : 'BatchRef001', 'Status': 'Duplicate Entry Found for this given SKU, Location Batch Number/Ref and Stock Status Combination.'}]


def test_existing_cycle_count_validation(test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory, test_generate_cycle_count_data):
    warehouse = User.objects.get(username = WAREHOUSE)

    #case 1: with entry existing in cycle count
    _, warehouse, _ = test_generate_cycle_count_data
    data_list = [{sku_code_const: "M_NB_SKU2", "Location": "RM-001", batch_ref_number : "", "Stock Status": ''}]
    data_list = existing_cycle_count_validation(data_list, locations, warehouse)
    assert data_list == []

def test_validate_create_cycle_count_form(test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory):
    warehouse = User.objects.get(username = WAREHOUSE)
    data_list =  [{sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "BatchRef001"},
                 {sku_code_const: "M_NB_SKU2", "Location": "RM-001", batch_ref_number : ""}]
    
    #case 1: validate create cycle count form
    status, data_list, cycle_creation_data = validate_create_cycle_count_form(warehouse, data_list)
    assert status == 'Success'
    assert data_list == [{sku_code_const: 'M_B_SKU1', 'Location': 'RM-001', batch_ref_number : 'BatchRef001', 'Status': None}, 
                         {sku_code_const: 'M_NB_SKU2', 'Location': 'RM-001', batch_ref_number : '', 'Status': None}]
    
    #case 2: create_cycle_count for validated cycle count form
    response = create_cycle_count( '' , warehouse, cycle_creation_data)
    assert response == 'Success'

def test_create_cycle_count_upload(test_create_user, test_create_sku, test_create_sellable_locations, test_create_inventory):
    warehouse = User.objects.get(username = WAREHOUSE)
    user = User.objects.get(username = USERNAME)
    factory = RequestFactory()
    request = factory.post('', data='{}', content_type='')
    request.user = user
    request.username = user.username
    data_list =  [{sku_code_const: "M_B_SKU1", "Location": "RM-001", batch_ref_number : "BatchRef001"},
                 {sku_code_const: "M_NB_SKU2", "Location": "RM-001", batch_ref_number : ""}]
    response = create_cycle_count_upload(request, warehouse, data_list, extra_params={})
    assert response.status_code == 200
    assert response.content.decode() == 'Success'
