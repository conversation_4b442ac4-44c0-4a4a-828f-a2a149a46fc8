#package imports
import datetime
import json
import uuid
import pytz

import copy
import pandas as pd

from json import loads
from collections import defaultdict, OrderedDict
from json import dumps
from dateutil import parser
from decimal import Decimal
from wms.celery import app as celery_app
#django imports
from django.db.models import Q, Sum, F, Count, Max, Value, When, Case, IntegerField
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.contrib.postgres.aggregates import ArrayAgg
from django.utils import timezone

#inventory imports
from inventory.models import (
    StockDetail, LocationMaster,
    BatchDetail, MoveInventory, SerialNumber, CycleCount
)
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#inbound immports
from inbound.models import POLocation

#quality control imports
from quality_control.models import QualityControl

#auth utils
from auth.utils import get_admin

#core imports
from core.models import (
    SKUMaster, UserIntegrationAPIS,
    UserIntegrationCalls, UserPrefixes
)

#wms base imports
from wms_base.wms_utils import (
    init_logger, reverse_stock_choice_mapping
)
from wms_base.models import User

#outbound imports
from outbound.models import (
    Picklist, SellerOrderSummary, StockAllocation, 
    OrderTypeZoneMapping, StagingInfo
)

#production imports
from production.models import JobOrder

#lms imports
from lms.models import TaskMaster, EmployeeMaster

#core common functions
from core_operations.views.common.main import (
    get_misc_value,get_local_date_known_timezone, get_incremental,
    get_user_time_zone, scroll_data, get_multiple_misc_values, get_misc_options_list,
    get_warehouse, WMSListView, truncate_float,  get_decimal_value, create_default_zones,
    generate_log_message, frame_datatable_header_filter, get_user_prefix_incremental
)
from core_operations.views.services.packing_service import PackingService

from inventory.models.locator import INVENTORY_CHOICES
from inventory.views.locator.stock_detail import format_date
from inventory.views.masters.location_master import (
    get_next_suggested_staging_location, get_staging_route_location_details
)
# approval imports
from core_operations.views.services.approval_service import ApprovalService
from inbound.views.common.common import preload_stock_details_for_locations, sku_batch_mixing_validation

from inbound.views.quality_check.quality_check import update_inspection_quantity


status_choices = dict(INVENTORY_CHOICES)


transaction_number_const = 'Transaction Number'
pick_location_const = 'Pick Location'
sku_code_const, sku_desc_const = 'SKU Code', 'SKU Description'
drop_location_cost = 'Drop Location'
picked_quantity_const = 'Picked Quantity'
dropped_quantity_const = 'Dropped Quantity'
batch_no_const = 'Batch No'
picked_by_const = 'Picked By'
picked_at_const = 'Picked At'
carton_id_const = 'Carton ID'

MOVE_INVENTORY_HEADERS = {
    transaction_number_const: transaction_number_const,
    pick_location_const: pick_location_const,
    sku_code_const: sku_code_const,
    sku_desc_const: sku_desc_const,
    drop_location_cost: drop_location_cost,
    picked_quantity_const: picked_quantity_const,
    dropped_quantity_const: dropped_quantity_const,
    batch_no_const: batch_no_const,
    picked_by_const: picked_by_const,
    picked_at_const: picked_at_const,
    carton_id_const: carton_id_const
}

from core_operations.views.integration.run_3pintegration import async_run_3p_int_func

log = init_logger('logs/cycle_count.log')

@get_warehouse
def insert_move_inventory(request, warehouse:User):
    '''
    Validates and moves inventory from source to destination location.
    
    Args:
        request: HTTP request object containing move inventory data.
        warehouse: User object representing the warehouse.
        
    Processes the request data to extract move inventory details.
    Validates source and destination locations, SKUs, and quantities.
    Updates source stock records and creates destination stock records.
    Handles LPN management if enabled for the warehouse.
    Returns JSON response with success or failure message.
    '''
    move_inv_id = ''
    request_data_list, tasks_flag, pick_flag = get_request_data(request)
    lpn_based_move_inv = get_misc_value('lpn_based_move_inventory', warehouse.id)

    #Validate Move Inventory
    status, return_dict = validate_move_inventory(
        warehouse, request_data_list, pick_flag, lpn_based_move_inv, task_flag=tasks_flag
    )
    #Return Error if validation fails
    if status == 'Failed':
        return JsonResponse({'message': return_dict}, status= 400)

    #Update Task Status for task based movement
    if tasks_flag:
        move_inv_id, message  = update_task_status(request, request_data_list, return_dict, pick_flag, warehouse, lpn_based_move_inv)
        if message == "Updated":
            return JsonResponse({'message': "Success"}, status= 200)
    
    #Update Stocks
    status_dict = update_stocks(request, warehouse, return_dict, move_inv_id = move_inv_id)
    if lpn_based_move_inv:
        lpn_numbers = { record['lpn_number'] for record in return_dict if record.get('lpn_number')}
        release_lpns(request, warehouse, lpn_numbers, lpn_based_mov_inv=True)
    return JsonResponse({'message' : status_dict.get('message')},status = status_dict.get('status'))



def release_lpns(request, warehouse, lpn_numbers, lpn_based_mov_inv = False, extra_params = None):
    '''
    Updates LPN status after inventory movement operations.
    
    Args:
        request: HTTP request object with headers and metadata.
        warehouse: User object for the warehouse.
        lpn_numbers: Set of LPN identifiers to update.
        lpn_based_mov_inv: Boolean flag for LPN-based movement.
        extra_params: Optional parameters for API calls.
        
    Filters out LPNs still in use and updates status of available LPNs.
    '''
    if not lpn_numbers:
        return
    free_lpns = lpn_numbers
    if lpn_based_mov_inv:
        open_lpns = set(MoveInventory.objects.filter(json_data__lpn__in=lpn_numbers, status__in=[1,2], sku__user=warehouse.id).values_list('json_data__lpn', flat=True))
        free_lpns = lpn_numbers - open_lpns
        if not free_lpns:
            return

    if extra_params:
        request_dict = {
            "request_headers": {
                "Warehouse": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_WAREHOUSE', ''),
                "Authorization": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_AUTHORIZATION', '')
            },
            "request_meta": extra_params.get('json_data', {}).get('request_meta', {}),
            "request_scheme": extra_params.get('json_data', {}).get('request_meta', {}).get('request_scheme', ''),
        }
        request_user = request
    else:
        request_dict = {
            "request_headers": {
                "Warehouse": request.headers.get("Warehouse"),
                "Authorization": request.headers.get('Authorization', ''),
            },
            "request_meta": request.META,
            "request_scheme": request.scheme,
        }
        request_user = request.user

    packing_service_instance = PackingService(request_dict, request_user, warehouse)
    lpn_numbers_str = ','.join(free_lpns)
    params = {
        "warehouse": warehouse.username,
        "lpn_number": lpn_numbers_str,
        "status": True,
        "usable": True,
        "dropped": False,
        "blocked": False
    }
    lpn_details, packing_service_errors = packing_service_instance.update_lpn(params)
    log.info(generate_log_message("LPNStatusUpdateDetails",empty_cartons=free_lpns,params=params,lpn_details=lpn_details))
    if packing_service_errors:
        log.info(generate_log_message("LPNStatusUpdateFailure",empty_cartons=free_lpns,params=params,error=packing_service_errors))

def get_request_data(request):
    '''
    Extracts move inventory data from the request.
    
    Args:
        request: HTTP request object containing the data.
        
    Parses JSON data and extracts task flags and inventory data.
    Returns tuple of request data list, tasks flag, and pick flag.
    '''
    request_data = loads(request.body)
    request_data_dict = request_data[0] if request_data else {}
    tasks_flag =  request_data_dict.get('istasks', False)
    pick_flag = request_data_dict.get('is_pick', False)
    request_data_list = request_data_dict.get('move_inventory_data', []) if 'move_inventory_data' in request_data_dict else request_data
    return request_data_list, tasks_flag, pick_flag

def update_task_status(request, request_data_list, return_dict, pick_flag, warehouse, lpn_based_move_inv = 'false'):
    '''
    Updates the status of move inventory tasks.
    
    Args:
        request: HTTP request object with user information.
        request_data_list: List of move inventory data from request.
        return_dict: Dictionary with validated move inventory details.
        pick_flag: Boolean indicating if operation is a pick.
        warehouse: User object representing the warehouse.
        lpn_based_move_inv: String flag for LPN-based movement.
        
    Processes task updates and returns task ID and status message.
    '''
    move_inv_data = request_data_list[0]
    move_inv_id = move_inv_data.get('move_inventory_id')
    message = update_tasks(request, return_dict, move_inv_id, warehouse, pick_flag, lpn_based_move_inv)
    return move_inv_id, message

def update_stocks(request, warehouse, return_list, move_inv_id = '', extra_params = None):
    '''
    Updates source stocks and creates destination stocks for inventory movement.
    
    Args:
        request: HTTP request object with user information.
        warehouse: User object representing the warehouse.
        return_list: List of validated move inventory records.
        move_inv_id: Optional ID of existing move inventory task.
        extra_params: Optional parameters for additional processing.
        
    Processes stock updates within a database transaction.
    Reduces quantities from source locations and adds to destinations.
    Creates move inventory records and triggers callbacks.
    Returns status dictionary with result message and HTTP status code.
    '''
    try:
        decimal_limit = get_decimal_value(warehouse.id)
        with transaction.atomic('default'):
            #Updating Stock
            source_stock_list = []
            for each_record in return_list:
                stocks, move_quantity, stock_serial_dict = each_record.get('source_stock'), each_record['quantity'], each_record.get('stock_serial_dict', {})
                stocks = stocks.select_for_update()

                for stock in stocks:
                    record_copy = copy.deepcopy(each_record)
                    #Receipt Type and Receipt Number based on Internal WIPZONE
                    if (record_copy.get('source_storage_type') and record_copy.get('dest_storage_type') == 'wip_area') or \
                        (record_copy.get('source_segregation') and record_copy.get('destination_segregation') in ['inbound_staging', 'outbound_staging']):
                        record_copy['receipt_type'], record_copy['receipt_number'] = stock.receipt_type, stock.receipt_number
                        record_copy['grn_number'] = stock.grn_number
                        record_copy['transact_number'] = stock.transact_number
                        json_data = stock.json_data or {}
                        if record_copy.get('json_data'):
                            record_copy['json_data'].update(json_data)

                    record_copy['batch_detail_id'] = stock.batch_detail_id
                    record_copy['sku_code'] = stock.sku.sku_code
                    record_copy['batch_no'] = stock.batch_detail.batch_no if stock.batch_detail else ''
                    record_copy['status'] =  stock.status
                    record_copy['unit_price'] = stock.unit_price
                    if stock.lpn_number:
                        record_copy['lpn_number'] = stock.lpn_number
                        if record_copy.get('json_data'):
                            record_copy['json_data']['lpn_number'] = stock.lpn_number
                    if isinstance(stock_serial_dict, dict) and stock_serial_dict.get(stock.id):
                        record_copy['serial_numbers'] = stock_serial_dict.get(stock.id)
                    elif isinstance(stock_serial_dict, dict) and stock_serial_dict:
                        continue

                    #Stock Reduction
                    if stock.quantity > move_quantity:
                        stock.quantity -= move_quantity
                        record_copy['quantity'] = move_quantity
                        move_quantity = 0

                    elif stock.quantity <= move_quantity:
                        move_quantity -= stock.quantity
                        record_copy['quantity'] = stock.quantity
                        stock.quantity = 0

                    stock.save()
                    source_stock_list.append(record_copy)
                    move_quantity = truncate_float(move_quantity, decimal_limit)
                    if move_quantity == 0: break

                if move_quantity > 0:
                    transaction.set_rollback(True)
                    return {"message": "No Source Stock Available", "status": 400}

            #Aggregate With Unique Constraints
            aggr_destination_list, qc_updation_list = aggregate_quantity_on_multiple_stocks(source_stock_list, move_inv_id=move_inv_id)
            #Creates Destination Stocks
            sn_item_dict, release_lpn_list = create_destination_stocks(warehouse, aggr_destination_list)
            if move_inv_id:
                MoveInventory.objects.filter(id = int(move_inv_id)).update(status = 0)
                move_ids = [int(move_inv_id)]
                update_sn_item_dict(sn_item_dict, move_inv_id, move_inv_id)
                if sn_item_dict:
                    create_serial_number_mapping_for_move_inventory(warehouse, move_inv_id, list(sn_item_dict.values()))
                trigger_move_inventory_call_back(warehouse, move_ids)
            else:
                #Creates Move Inventory and Callback
                create_move_inventory(request, warehouse, aggr_destination_list,  sn_item_dict=sn_item_dict)
            if release_lpn_list:
                release_lpns(request, warehouse, set(release_lpn_list), lpn_based_mov_inv=False, extra_params=extra_params)

            #Update QC Quantity
            if qc_updation_list:
                update_inspection_quantity(warehouse, qc_updation_list)

        return {"message": "Moved Successfully", "status": 200}
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(("Move inventory failed for User %s, params %s and error statement is %s") % (
            str(warehouse.username), str(return_list), str(e)
        ))
        return {"message": "Update Stocks Failed", "status": 400}

def aggregate_quantity_on_multiple_stocks(source_stock_list, move_inv_id = None):
    '''
    Aggregates quantities from multiple source stocks with the same attributes.
    
    Args:
        source_stock_list: List of source stock records to be aggregated.
        
    Groups stocks by unique key attributes like SKU, location, batch, and LPN.
    Assigns unique reference keys to each aggregated group.
    Combines quantities and serial numbers from matching stock records.
    Returns list of aggregated stock records for destination creation.
    '''
    agg_dest_stocks = {}
    for stock in source_stock_list:
        uniq_key = (
            stock.get('sku_id'), stock.get('source_loc_id'),
            stock.get('batch_detail_id', None), stock.get('dest_loc_id', None),
            stock.get('receipt_number', None), stock.get('receipt_type', None),
            stock.get('lpn_number', None)
        )
        if uniq_key not in agg_dest_stocks:
            unique_key = move_inv_id
            if not unique_key:
                unique_key = get_unique_reference()
            json_data = stock.get('json_data', {}) or {}
            json_data['unique_key'] = unique_key
            stock['remarks'] = unique_key
            agg_dest_stocks[uniq_key] = stock
        else:
            agg_dest_stocks[uniq_key]['quantity'] += stock['quantity']
            agg_dest_stocks[uniq_key]['serial_numbers'] = (agg_dest_stocks[uniq_key].get('serial_numbers', []) or []) + (stock.get('serial_numbers', []) or [])

    qc_updation_list = []
    for stock in agg_dest_stocks.values():
        source_storage_type = stock.get('source_storage_type', '')
        dest_storage_type = stock.get('dest_storage_type', '')

        quantity_increment = True if dest_storage_type == 'INSP' else False
        if 'INSP' in [source_storage_type, dest_storage_type] and not (source_storage_type == 'INSP' and dest_storage_type == 'INSP'):
            receipt_number = stock.get('receipt_number')
            quantity = stock.get('quantity') if quantity_increment else -stock.get('quantity')
            qc_updation_list.append({
                'receipt_number': receipt_number,
                'quantity': quantity,
                'lpn_number': stock.get('lpn_number'),
                'serial_numbers': stock.get('serial_numbers', []),
            })

    return (list(agg_dest_stocks.values()), qc_updation_list)

def prepare_serial_numbers(record, sn_item_dict, stock=None, open_status = False):
    '''
    Prepares serial number data for inventory movement transactions.
    
    Args:
        record: Dictionary containing stock record details.
        sn_item_dict: Dictionary to store serial number mappings.
        stock: Optional StockDetail object for existing stock records.
        
    Creates a structured mapping of serial numbers to their new locations.
    Associates serial numbers with stock records and transaction details.
    Supports tracking of serial numbers through the movement process.
    '''
    unique_key = record.get('remarks', '')
    sn_item_dict[unique_key] = {
        'transact_type': 'move_inventory',
        'lpn_number': record.get('lpn_number', ''),
        'serial_numbers': record.get('serial_numbers', []),
        'sku_code': record.get('sku_code', ''),
        'sku_id': record.get('sku_id', ''),
        'location_id': record['dest_loc_id'],
        'location': record.get('dest_loc', ''),
        'zone': record.get('dest_zone', ''),
        'batch_number': record.get('batch_no', ''),
        'batch_detail_id': record.get('batch_detail_id', ''),
        'status': 0
    }
    if open_status:
        sn_item_dict[unique_key]['status'] = 1
        sn_item_dict[unique_key]['transact_type'] = 'task_based_move_inventory'
        del sn_item_dict[unique_key]['location_id']
    if stock:
        sn_item_dict[unique_key]['stock_id'] = stock.id

def update_stock_id_in_serial_dict(sn_item_dict, created_stocks):
    '''
    Updates serial number dictionary with stock IDs from created stocks.
    
    Args:
        sn_item_dict: Dictionary mapping unique keys to serial number data.
        created_stocks: List of newly created stock objects.
        
    Links serial numbers to their corresponding stock records.
    Ensures proper association between stocks and serial numbers.
    '''
    for stock in created_stocks:
        unique_key = stock.remarks
        if unique_key in sn_item_dict:
            sn_item_dict[unique_key]['stock_id'] = stock.id

def create_destination_stocks(warehouse, destination_details, staging_stock = False):
    '''
    Creates or updates destination stocks for inventory movement.
    
    Args:
        warehouse: User object representing the warehouse.
        destination_details: List of destination stock details.
        staging_stock: Boolean flag for staging area stocks.
        
    Processes each destination record to find existing stocks or create new ones.
    Handles LPN management and serial number tracking.
    Updates quantities and calculates weighted average costs.
    Returns serial number mapping dictionary and list of LPNs to release.
    '''
    release_lpn_list = []
    destination_list, sn_item_dict = [], {}
    cycle_objs = []
    stock_objs = StockDetail.objects.filter(sku__user=warehouse.id)
    enable_empty_location_cycle_count = get_misc_value('enable_empty_location_cycle_count',warehouse.id)
    if not staging_stock and enable_empty_location_cycle_count == "true":
        cycle_objs = CycleCount.objects.filter(run_type="empty_location", location__zone__user=warehouse.id, status=1)
    
    for record in destination_details:

        #Dispensing, Staging Lanes Movement
        given_receipt_number = record.get('receipt_number')
        filter_dict = {
            'sku_id': record['sku_id'],
            'receipt_type': record.get('receipt_type', 'move_inventory'),
            'receipt_number': given_receipt_number,
            'status': record['status'],
            'location_id': record['dest_loc_id']

        }
        if record.get('batch_detail_id'):
            filter_dict['batch_detail_id'] = record.get('batch_detail_id')

        if not staging_stock and record.get('lpn_number'):
            if record.get('is_dest_lpn_managed') or (record.get('destination_segregation') in ['inbound_staging', 'outbound_staging']):
                filter_dict['lpn_number'] = record.get('lpn_number')
            else:
                release_lpn_list.append(record.get('lpn_number'))
                filter_dict['lpn_number'] = None
                record['lpn_number'] = None

        if given_receipt_number:
            dest_stocks = stock_objs.filter(
                **filter_dict
            )
            if dest_stocks.exists():
                quantity = float(record['quantity'])
                dest_stocks = dest_stocks[0]
                dest_stocks.quantity += quantity
                dest_stocks.original_quantity += quantity
                prepare_serial_numbers(record, sn_item_dict, stock=dest_stocks)
                dest_stocks.save()
            else:
                prepare_serial_numbers(record, sn_item_dict)
                destination_list.append(record)

        else:
            filter_dict = {
                'sku_id': record['sku_id'],
                'status': record['status'],
                'location_id': record['dest_loc_id']
            }
            if record.get('batch_detail_id'):
                filter_dict['batch_detail_id'] = record.get('batch_detail_id')

            if record.get('lpn_number'):
                filter_dict['lpn_number'] = record.get('lpn_number')

            dest_stocks = stock_objs.filter(
                **filter_dict
            )
            if dest_stocks.exists():
                dest_stocks = dest_stocks[0]
                quantity = float(record['quantity'])
                dest_quantity = dest_stocks.quantity
                total_qty = quantity + dest_quantity

                #Unit Price calculation
                stock_value = dest_quantity * dest_stocks.unit_price + quantity * record.get('unit_price', 0)
                wac = stock_value / total_qty if total_qty else 0
                dest_stocks.unit_price = wac

                if not dest_stocks.quantity:
                    dest_stocks.receipt_date = datetime.datetime.now()

                dest_stocks.quantity += quantity
                dest_stocks.original_quantity += quantity
                prepare_serial_numbers(record, sn_item_dict, stock=dest_stocks)
                dest_stocks.save()

                # Update cycle counts for empty locations if applicable
                if cycle_objs:
                    dest_loc_id = record.get('dest_loc_id')
                    update_empty_location_cycle_counts(cycle_objs, dest_loc_id)
            else:
                prepare_serial_numbers(record, sn_item_dict)
                destination_list.append(record)

    #Bulk creation of Destination Stocks
    created_stocks = bulk_create_destination_stock(warehouse, destination_list, cycle_objs)
    if created_stocks:
        update_stock_id_in_serial_dict(sn_item_dict, created_stocks)
    return sn_item_dict, release_lpn_list

def update_empty_location_cycle_counts(cycle_objs, dest_loc_id):
    """
    Updates cycle count records for empty locations after inventory movement.
    """
    empty_location_qs = cycle_objs.filter(location=dest_loc_id)
    if empty_location_qs.exists():
        empty_location_qs.update(status=3, remarks="System Updated", updation_date=datetime.datetime.now())

def bulk_create_destination_stock(warehouse, destination_list, cycle_objs):
    '''
    Creates multiple destination stock records in bulk.
    
    Args:
        warehouse: User object representing the warehouse.
        destination_list: List of destination stock details.
        
    Generates receipt numbers for new stock records.
    Prepares stock dictionaries with appropriate attributes.
    Creates stock records efficiently using bulk operations.
    Returns list of created stock objects for further processing.
    '''
    created_stocks = []

    receipt_number = get_incremental(warehouse, 'move_inventory', increment=len(destination_list))
    destination_stocks = []
    for record in destination_list:
        quantity = float(record['quantity'])
        stock_dict = {
            'original_quantity': quantity,
            'receipt_date': datetime.datetime.now(),
            'quantity': quantity,
            'location_id': record['dest_loc_id'], 'sku_id': record['sku_id'],
            'receipt_type': record.get('receipt_type', 'move_inventory'),
            'account_id': warehouse.userprofile.id,
            'remarks': record.get('remarks') or '',
            'status': record['status']
        }
        if record.get('json_data'):
            stock_dict.update({'json_data': record.get('json_data')})
        if record.get('receipt_number'):
            stock_dict.update({'receipt_number': record.get('receipt_number')})
        else:
            stock_dict.update({'receipt_number': receipt_number + 1})
            receipt_number += 1

        if record.get('batch_detail_id'):
            stock_dict['batch_detail_id'] = record.get('batch_detail_id')

        if record.get('grn_number'):
            stock_dict['grn_number'] = record.get('grn_number')
        
        if record.get('transact_number') and record.get('receipt_type') == 'so_picking':
            stock_dict['transact_number'] = record.get('transact_number')

        if record.get('unit_price'):
            stock_dict['unit_price'] = record.get('unit_price')

        if record.get('lpn_number'):
            stock_dict['lpn_number'] = record.get('lpn_number')

        destination_stocks.append(StockDetail(**stock_dict))

        # Update cycle counts for empty locations if applicable
        if cycle_objs:
            dest_loc_id = record.get('dest_loc_id')
            update_empty_location_cycle_counts(cycle_objs, dest_loc_id)

    if destination_stocks:
        created_stocks = StockDetail.objects.bulk_create_with_rounding(destination_stocks)

    return created_stocks

def create_serial_number_mapping_for_move_inventory(warehouse, reference_number, sn_item_list, reference_type = 'move_inventory'):
    '''
    Creates serial number transaction records for move inventory operations.
    
    Args:
        warehouse: User object representing the warehouse.
        reference_number: Unique reference number for the transaction.
        sn_item_list: List of serial number items to be mapped.
        
    Builds a transaction dictionary with reference information.
    Uses SerialNumberTransactionMixin to create or update records.
    Ensures serial numbers are properly tracked through inventory movements.
    '''
    sn_transact_dict = {
            'reference_number': reference_number,
            'reference_type': reference_type,
            'items': sn_item_list
        }
    SerialNumberTransactionMixin(None, warehouse, sn_transact_dict).create_update_sn_transaction()
    
    
    
    
class MoveInventoryApprovalMixin():
    def init_approval_header_data(self, transaction_number):
        """
        Initialize the approval header data for a move inventory transaction.
        
        Args:
            transaction_number (str): The unique identifier for the move inventory.
            
        Returns:
            dict: The initialized approval header data.
        """
        self.approval_header_data = {
            "transaction_type": "move_inventory",
            "transaction_number": transaction_number,
            "json_data": {},
            "lines": []
        }
        return self.approval_header_data
    
    def get_approval_service_instance(self, request_data, warehouse):
        """
        Initializes and returns an ApprovalService instance for the given request data and warehouse.
        Args:
            request_data (dict): The request data containing headers, user, and meta information.
            warehouse (User): The warehouse user object.
        Returns:
            ApprovalService: An initialized ApprovalService instance.
        """
        request_dict = {
            "request_headers": {
                "Warehouse": request_data.get("warehouse"),
                "Authorization": request_data.get('headers', {}).get('Authorization')
            },
            "request_meta": request_data.get('request_meta'),
        }
        return ApprovalService(request_dict, request_data.get("user"), warehouse)
    
    def create_approval_entry(self, approval_data, approval_header_data, warehouse, data, approval_flow=True):
        """
        Create an approval entry for a move inventory transaction.
        
        Args:
            approval_data (list): List of approval data for each move inventory item.
            approval_header_data (dict): Header data for the approval.
            warehouse (User): The warehouse user object.
            data (dict): Additional data for the approval entry.
            approval_flow (bool): Whether to use the approval flow.
            
        Returns:
            str: The status of the approval entry creation.
        """
        status = ""
        self.final_approval_data = {}
        if approval_data and approval_header_data and approval_flow:
            approval_header_data['lines'] = approval_data
            self.final_approval_data = {"data": [approval_header_data]}
            request_data = data.get('request_data')
            approval_service_instance = self.get_approval_service_instance(request_data, warehouse)
            final_data, _ = approval_service_instance.create_approval(self.final_approval_data)
            if final_data:
                status = final_data[0].get('status')
        return status
    
    
    def frame_approval_data(self, warehouse_id, move_ids, approval_stage="source"):
        """
        Prepares approval data for given move inventory IDs.
        
        Args:
            warehouse_id (int): The ID of the warehouse.
            move_ids (list): List of move inventory IDs to retrieve data for.
            approval_stage (str): The stage of approval (default: "source").
            
        Returns:
            tuple: A tuple containing (approval_data, transaction_number)
                  - approval_data (list): List of dictionaries with approval data
                  - transaction_number (str): Reference number for the transaction
        """
        move_objs = MoveInventory.objects.filter(
            id__in=move_ids, 
            sku__user=warehouse_id
        ).select_related(
            'sku', 
            'source_location__zone', 
            'dest_location__zone'
        )
        
        if not move_objs:
            return [], ""
        
        # Extract transaction number only once
        transaction_number = move_objs[0].reference
        
        approval_data = [
            {
                'transaction_id': move.id,
                'item_code': move.sku.sku_code,
                'item_description': move.sku.sku_desc,
                'json_data': {"move_inventory_reference": transaction_number},
                'source_zone': move.source_location.zone.zone,
                'source_location': move.source_location.location,
                'destination_zone': move.dest_location.zone.zone,
                'destination_location': move.dest_location.location,
                'quantity': move.quantity,
                'approval_stage': approval_stage
            }
            for move in move_objs
        ]
        
        return approval_data, transaction_number
    
    
    def get_approval_transactions(self, warehouse, request):
        """
        Retrieves pending approval transactions for move inventory operations.
        
        Args:
            warehouse (User): The warehouse user object for which to retrieve approvals.
            request (HttpRequest): The HTTP request object containing user information.
            
        Returns:
            list: A list of approval transactions matching the specified criteria
        """
       
        request_data = {"warehouse": warehouse,
                                        'user': request.user,
                                        "headers": request.headers,
                                        "request_meta": request.META,
                                        "request_scheme": request.scheme,
                        }
        approval_service_instance = self.get_approval_service_instance(request_data, warehouse)
        params={
            "approver": request.user.email,
            "status":"pending",
            "transaction_type": "move_inventory",
        }   
        return approval_service_instance.get_approval_transactions(params)
    
    def fetch_pending_approval_transactions(self, warehouse, request):
        """
        Fetches pending approval transactions for move inventory.
        
        Args:
            warehouse (User): The warehouse user object.
            request (HttpRequest): The HTTP request object.
            
        Returns:
            list: List of pending approval transactions.
        """
        pending_transactions_numbers= {}
        approval_transactions = self.get_approval_transactions(warehouse, request)
        for transaction in approval_transactions:
            if transaction.get('status') == 'pending':
                transaction_number = transaction.get('transaction_number','')
                approval_id = transaction.get('approval_id','')
                pending_transactions_numbers[transaction_number] = approval_id
        return pending_transactions_numbers           



def update_serial_numbers(warehouse, data, serial_numbers, move_inv_id, reference_type='move_inventory', location_update = False):
    """
    Update serial numbers status to 1 for move inventory transactions.
    
    Args:
        serial_numbers (list): List of serial numbers to update
        move_inv_id (int): The ID of the move inventory transaction
        warehouse (User): The warehouse object
        reference_type (str): The reference type, default is 'move_inventory'
        
    Returns:
        dict: Result of the update operation
    """
    if not serial_numbers:
        return {"message": "No serial numbers to update", "status": 200}
    
    sku_code = data.get('sku_code')
    location_id = data.get('dest_loc_id')
    # Create request data for SerialNumberTransactionMixin
    item_dict = {
        'serial_numbers': serial_numbers,  # This should be a list of serial numbers
        'status': 0,
        'transact_id': move_inv_id,
        'transact_type': reference_type,
        'sku_code': sku_code
    }
    if location_update:
        item_dict.update({'location_id': location_id})

    request_data = [{
        'reference_number': str(move_inv_id),
        'reference_type': reference_type,
        'items': [item_dict]
    }]
    
    # Update serial numbers using SerialNumberTransactionMixin
    sntd_mixin = SerialNumberTransactionMixin(None, warehouse, request_data)
    result = sntd_mixin.create_update_sn_transaction()
    
    return result

def create_move_inventory_approval(warehouse, move_ids, extra_params):
    """
    Creates approval requests for move inventory operations.
    
    Args:
        warehouse: User object representing the warehouse.
        move_ids: List of move inventory IDs to be approved.
        extra_params: Additional parameters for creating approval requests.
        
    Returns:
        The approval object created for the move inventory.
    """
        
    
    request_dict = {}
    # Extracting request headers and meta information for approval
    if extra_params:
        request_dict = {
            "request_headers": {
                "Warehouse": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_WAREHOUSE', ''),
                "Authorization": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_AUTHORIZATION', '')
            },
            "request_meta": extra_params.get('json_data', {}).get('request_meta', {}),
            "request_scheme": extra_params.get('json_data', {}).get('request_meta', {}).get('request_scheme', ''),
            "user": extra_params.get('user', '')
        }
    approval_request_data = {}
    # framing request data for approval
    approval_request_data['request_data'] = {"warehouse": warehouse,
                                        "user": request_dict.get('user', ''),
                                        "headers": request_dict.get('request_headers', {}),
                                        "request_meta": request_dict.get('request_meta', {}),
                                        "request_scheme": request_dict.get('request_scheme', ''),
                                        }
    
    
    

    log.info("Approval Request Data: %s", approval_request_data)

    approval_mixin = MoveInventoryApprovalMixin()
    
    # Frame the approval data and get transaction number
    approval_data, transaction_number = approval_mixin.frame_approval_data(
        warehouse.id, 
        move_ids
    )
    
    # Initialize header data with the transaction number
    approval_header_data = approval_mixin.init_approval_header_data(transaction_number)
    
    # Create the approval entry
    approval_obj = approval_mixin.create_approval_entry(
        approval_data, 
        approval_header_data, 
        warehouse, 
        approval_request_data, 
        True
    )
    
    return approval_obj



@celery_app.task
def async_create_approval_entry(approval_data, approval_header_data, warehouse_id, approval_request_data):
    from wms_base.models import User
    from .move_inventory import MoveInventoryApprovalMixin  # adjust import if needed
    warehouse = User.objects.get(id=warehouse_id)
    user = User.objects.get(id=approval_request_data['request_data']['user'])
    approval_request_data['request_data']['warehouse'] = warehouse
    approval_request_data['request_data']['user'] = user
    approval_mixin = MoveInventoryApprovalMixin()
    approval_mixin.create_approval_entry(
        approval_data,
        approval_header_data,
        warehouse,
        approval_request_data,
        True
    )
    

@get_warehouse
def update_move_inventory_status(request, warehouse: User):
    """
    Updates the status of move inventory transactions based on approval decisions.
    
    Implements a two-stage approval workflow (source → destination).
    When source approval is granted, automatically initiates destination approval.

    Args:
        request (HttpRequest): Django HTTP request with JSON body.
        warehouse (User): User object injected by the @get_warehouse decorator.
        
    Returns:
        JsonResponse: Success or error message with appropriate HTTP status code.
    """
    try:
        status_mapping = {
            'approved': 1,  # Approved status
            'rejected': 5   # Rejected status
        }
        data = json.loads(request.body)
        new_status_key = data.get('status')
        transaction_number = data.get('transaction_number')
        approval_stage = data.get('json_data', {}).get('approval_stage', '')
        
        log.info("MoveInventoryStatusUpdateRequest approval_stage=%s new_status_key=%s transaction_number=%s", approval_stage, new_status_key, transaction_number)
        
        if not new_status_key:
            return JsonResponse({'error': 'Status not found'}, status=400)
        
        # Fetching the move inventory records based on the provided reference number and warehouse
        move_queryset = MoveInventory.objects.filter(
            reference=transaction_number,
            sku__user=warehouse.id
        )
        
        # Get the move IDs for later use
        move_ids = list(move_queryset.values_list('id', flat=True))
            
        # Handle based on approval stage and status
        if approval_stage == 'source' and new_status_key == 'approved':
           
            # Create approval request for destination stage
            # Prepare request data
            approval_request_data = {}
            approval_request_data['request_data'] = {
                "warehouse": warehouse.id,
                "user": request.user.id,
                "headers": dict(request.headers),
                "request_meta": request.META
            }
            
            # Use the MoveInventoryApprovalMixin to frame the data
            approval_mixin = MoveInventoryApprovalMixin()
            
            # Frame approval data for destination stage
            approval_data, transaction_number = approval_mixin.frame_approval_data(
                warehouse.id, 
                move_ids, 
                approval_stage="destination"  # Key change: set stage to destination
            )
            
            # Initialize header data
            approval_header_data = approval_mixin.init_approval_header_data(transaction_number)
            
            try:
                # Create the approval entry
                async_create_approval_entry.delay(
                    approval_data,
                    approval_header_data,
                    warehouse.id,
                    approval_request_data
                )
            except Exception as e:
                import traceback
                log.error("Error in async_create_approval_entry: %s traceback=%s", str(e), traceback.format_exc())
                return JsonResponse({
                    'error': 'Failed to trigger destination approval.',
                    'details': str(e),
                    'traceback': traceback.format_exc()
                }, status=500)
                        
            return JsonResponse({
                'message': 'Source Approval Granted. Destination Approval Requested.'
            }, status=200)
        else:
            # Handle approval or rejection for either stage
            updated_status = status_mapping.get(new_status_key)
            if updated_status is None:
                return JsonResponse({'error': 'Invalid status value'}, status=400)
                
            move_queryset.update(status=updated_status)
            log.info("MoveInventory status updated to %s for move_ids=%s at approval_stage=%s", updated_status, move_ids, approval_stage)
            
            return JsonResponse({
                'message': f'Move Inventory {new_status_key} at {approval_stage} stage.'
            }, status=200)
        
    except Exception as e:
        import traceback
        log.error("MoveInventoryStatusUpdateFailure move_ids=%s approval_stage=%s error=%s traceback=%s", \
            str(data.get('ids', [])), data.get('approval_stage', 'unknown'), str(e), traceback.format_exc())
        return JsonResponse({'error': 'Internal server error'}, status=500)
        

def create_move_inventory(request, warehouse, return_list,  task_status = False, ba_to_sa = False, sn_item_dict=None, extra_params=None):
    '''
    Creates move inventory records for stock movements.
    
    Args:
        request: HTTP request object or username string.
        warehouse: User object for the warehouse.
        return_list: List of validated move inventory records.
        task_status: Optional status flag for task-based movements.
        ba_to_sa: Flag for bin-to-staging area movements.
        sn_item_dict: Optional dictionary for serial number tracking.
        
    Creates records, updates serial numbers, and triggers callbacks.
    '''
    
    
    move_inventory_approval = 'false'
    if extra_params:
        move_inventory_approval = extra_params.get('move_inventory_approval', 'false')
        extra_params['user'] = request
    
    if ba_to_sa:
        request_user = request
        integration_user = 1
    else:
        try:
            request_user = request.user.username
            integration_user = request.user.userprofile.integrations_user
        except AttributeError:
            request_user = request.username
            integration_user = request.userprofile.integrations_user
    move_inventory_objects = prepare_data_for_move_inventory_creation(return_list, request_user, warehouse, task_status)
    move_ids = []
    #Bulk Move Inventory Creation

    if move_inventory_objects:
        move_objs = MoveInventory.objects.bulk_create_with_rounding(move_inventory_objects)
        move_ids = []
        for obj in move_objs:
            move_ids.append(obj.id)
            if sn_item_dict:
                update_sn_item_dict(sn_item_dict, obj.id, obj.json_data.get('remarks'))
        if sn_item_dict:
            reference_type = 'task_based_move_inventory' if task_status else 'move_inventory'
            create_serial_number_mapping_for_move_inventory(warehouse, obj.id, list(sn_item_dict.values()), reference_type=reference_type)
        # Approval Creation
        if move_inventory_approval == 'true':
            for move_obj in move_objs:
                create_move_inventory_approval(warehouse, [move_obj.id], extra_params)
        if task_status:
            return move_objs

    if not integration_user:
        trigger_move_inventory_call_back(warehouse, move_ids)


def trigger_move_inventory_call_back(warehouse, move_ids):
    '''
    Triggers integration callbacks after move inventory operations.
    
    Args:
        warehouse: User object representing the warehouse.
        move_ids: List of move inventory record IDs.
        
    Updates transaction IDs for move inventory records.
    Collects data for inventory and move inventory callbacks.
    Sends webhook notifications to integrated systems.
    Ensures external systems are updated with inventory changes.
    '''
    from core_operations.views.integration.integration import webhook_integration_3p

    update_transaction_ids(warehouse, move_ids)
    move_inv_data, sku_codes, sku_zones_dict = get_move_inventory_call_back_data(warehouse, move_ids)

    #Inventory Callback on Move Inventory
    filters = {
        "sku_codes": sku_codes,
        "zones_data": sku_zones_dict
    }
    webhook_integration_3p(warehouse.id, "move_inventory", filters)

    #Move Inventory Callback
    move_inventory_call_back_3p_integration(move_inv_data, warehouse)




def update_transaction_ids(warehouse, move_ids):
    '''
    Updates transaction IDs for move inventory records.
    
    Args:
        warehouse: User object representing the warehouse.
        move_ids: List of move inventory record IDs.
        
    Finds the highest existing transaction ID and increments from there.
    Updates each move inventory record with a unique transaction ID.
    Uses bulk update for efficiency when processing multiple records.
    '''
    last_transaction_id = (
        MoveInventory.objects
        .filter(sku__user=warehouse.id)
        .aggregate(max_id=Max('move_inventory_id'))['max_id'] or 0
    )
    move_inventory_objects = MoveInventory.objects.filter(sku__user = warehouse.id , id__in = move_ids).only("id", "move_inventory_id")
    transaction_id_list = []
    for move_inventory in move_inventory_objects:
        last_transaction_id += 1
        move_inventory.move_inventory_id = last_transaction_id
        transaction_id_list.append(last_transaction_id)
    MoveInventory.objects.bulk_update(move_inventory_objects, ['move_inventory_id'])

def update_sn_item_dict(sn_item_dict, reference, unique_key):
    '''
    Updates serial number item dictionary with transaction reference.
    
    Args:
        sn_item_dict: Dictionary containing serial number mappings.
        reference: Transaction reference ID to be added.
        unique_key: Key to identify the specific item in the dictionary.
        
    Adds transaction ID to the serial number record for tracking.
    '''
    if unique_key in sn_item_dict:
        sn_item_dict[unique_key]['transact_id'] = reference

def prepare_data_for_move_inventory_creation(return_dict, request_user, warehouse, task_status):
    '''
    Prepares data for creating move inventory records.
    
    Args:
        return_dict: List of validated move inventory records.
        request_user: Username of the requesting user.
        warehouse: User object representing the warehouse.
        task_status: Optional status flag for task-based movements.
        
    Formats each record with required fields for database insertion.
    Generates unique references for each movement transaction.
    Includes metadata like source, price, and serial numbers.
    Returns list of prepared MoveInventory objects ready for creation.
    '''
    move_inventory_objects = []
    for record in return_dict:
        json_data = record.get('json_data', {
            'done_by': record.get('done_by', 'WEB'),
            'request_user': request_user,
            'price': record.get('unit_price', 0),
            'serial_numbers': record.get('serial_numbers', []),
            'lpn_number': record.get('lpn_number', '')
        })

        if record.get('remarks'):
            json_data.update({'remarks': record.get('remarks')})

        #Incremental Unique Reference
        reference = record.get('reference', None)
        if not reference:
            reference = get_move_inventory_incremental_reference(warehouse)

        move_inventory_dict = {
            'sku_id': record['sku_id'],
            'source_location_id': record['source_loc_id'],
            'reason': record['reason'],
            'dest_location_id': record['dest_loc_id'],
            'quantity': record['quantity'],
            'json_data': json_data,
            'account_id': warehouse.userprofile.id,
            'transact_type': record.get('transact_type', '') or '',
            'reference': reference
        }
        if task_status:
            move_inventory_dict['status'] = task_status

        if record.get('batch_detail_id'):
            move_inventory_dict['batch_detail_id'] = record['batch_detail_id']
        move_inventory_objects.append(MoveInventory(**move_inventory_dict))

    return move_inventory_objects


def validate_move_inventory(warehouse, validate_dict, pick_flag=False, lpn_based_move_inv='false', task_flag=False):
    '''
    Validates move inventory data before processing.
    
    Args:
        warehouse: User object representing the warehouse.
        validate_dict: List of move inventory records to validate.
        pick_flag: Boolean indicating if operation is a pick.
        lpn_based_move_inv: String flag for LPN-based movement.
        
    Validates SKUs, locations, quantities, and stock availability.
    Checks configuration settings for movement restrictions.
    Ensures batch requirements are met for batch-based SKUs.
    Returns validation status and processed data list.
    '''
    #Misc Details
    allow_inter_zonal_movement = []
    misc_types = ['restrict_movement_in_wip', 'allow_inter_zone_movement', 'task_based_move_inventory','restrict_sku_batch_mixing']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    restrict_movement_in_wip = misc_dict.get('restrict_movement_in_wip', 'false')
    task_based_move_inventory = misc_dict.get('task_based_move_inventory', 'false')
    move_location_sku_batch_map = {}
    restrict_sku_batch_mixing = misc_dict.get('restrict_sku_batch_mixing', 'false')
    if misc_dict.get('allow_inter_zone_movement') not in ['', None, 'false', False]:
        allow_inter_zonal_movement = misc_dict.get('allow_inter_zone_movement').split(',')
    if restrict_movement_in_wip not in ['', None, 'false']:
        restrict_movement_in_wip = list(map(str.lower, misc_dict.get('restrict_movement_in_wip').split(',')))
    sku_details_dict, loc_master_dict, zone_dict, zone_restrictions =  get_sku_location_details(warehouse, validate_dict)
    stock_map = {}
    if restrict_sku_batch_mixing == 'true':
        stock_map = preload_stock_details_for_locations(warehouse.id, set(loc_master_dict.keys()))
    
    return_data_list, error_data_list, error_status = [], [], False
    for record in validate_dict:
        status = []

        return_dict = {
            'done_by': record.get('source', 'UPLOADS'), 'sku_code': record.get('sku_code'),
            'remarks': record.get('remarks', ''), 'task_creation': record.get('task_creation'),
            'reference' : record.get('transaction_reference','')
        }

        #SKU Validation
        sku_code, status, return_dict = validate_sku_details(record, sku_details_dict, status, return_dict)

        status, return_dict = validate_location_zone_details(
            record, loc_master_dict, status, return_dict, zone_dict
        )
        return_dict['reason'] = record.get('reason') if record.get('reason') else status.append('Reason is Mandatory')

        #Lpn check
        task_based = record.get('move_inventory_id', None)
        if lpn_based_move_inv == 'true' and task_based:
            if pick_flag and not record.get('lpn'):
                status.append('LPN details are Mandatory for LPN Based Pick')
            elif not pick_flag and not record.get('lpn_number'):
                status.append('Package reference is Mandatory for LPN Based Drop')
            return_dict['lpn'] = record.get('lpn')
        return_dict['lpn_number'] = record.get('lpn_number', '')
        
        dest_location = record.get('dest_loc', '')
        if dest_location and restrict_sku_batch_mixing == 'true':
            if dest_location not in move_location_sku_batch_map:
                move_location_sku_batch_map[dest_location] = {
                    'skus': set(),
                    'batches': set()
                }
            # validating if the SKU and batch are compatible with the destination location
            is_location_compatible, error_message = sku_batch_mixing_validation(
                stock_map,
                dest_location,
                record['sku_code'],
                record.get('source_batch', ''),
                move_location_sku_batch_map,
                zone_restrictions
                )
            if not is_location_compatible:
                status.append(error_message)
            else:
                move_location_sku_batch_map[dest_location]['skus'].add(record['sku_code']) 
                if record.get('batch_number', ''):
                    move_location_sku_batch_map[dest_location]['batches'].add(record.get('batch_number', ''))

        #Batch Based SKU Check
        status = validate_batch_based_check(sku_details_dict, sku_code, status, return_dict, record)

        #Validate Move Quantity
        status = validate_move_quantity(record, return_dict, status)

        #Status Validation
        return_dict = validate_stock_status(record, return_dict)

        #Validate Staging Lane Movement
        status = validate_staging_lanes_movement(return_dict, status)

        # If Task Based Movement is mandatory and source/destination is not staging then return error
        if (
            not task_flag
            and task_based_move_inventory
            and task_based_move_inventory == 'mandatory'
            and record.get('task_creation', '') != 'Y'
            and (
                return_dict.get('source_segregation') or return_dict.get('destination_segregation')
            ) not in ['inbound_staging', 'outbound_staging']
        ):
            status.append('Task Based Movement is not allowed')

        #Source Stock Validation
        if not status:
            status, return_dict = validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement)

        if not status and restrict_movement_in_wip:
            status = validate_wip_movement(warehouse, return_dict, restrict_movement_in_wip)
        if status:
            error_status, record['status'] = True, status

        elif restrict_movement_in_wip:
            status = validate_wip_movement(warehouse, return_dict, restrict_movement_in_wip)
            if status:
                error_status, record['status'] = True, status

        error_data_list.append(record), return_data_list.append(return_dict)

    if error_status:
        return 'Failed', error_data_list
    return 'Success', return_data_list

def validate_staging_lanes_movement(return_dict, status):
    '''
    Validates movement between staging lanes.
    
    Args:
        return_dict: Dictionary containing movement details including segregation and storage types.
        status: List to collect validation error messages.
        
    Checks if the movement between source and destination staging areas is allowed.
    Prevents invalid movements like moving from inbound staging before putaway completion.
    Returns updated status list with any error messages added.
    '''
    source_segregation, destination_segregation = return_dict.get('source_segregation'), return_dict.get('destination_segregation')
    source_storage_type, destination_storage_type = return_dict.get('source_storage_type'), return_dict.get('dest_storage_type')
    error_message = check_stock_movement(source_segregation, destination_segregation, source_storage_type, destination_storage_type)
    if error_message:
        status.append(error_message)
    return status

def validate_move_quantity(record, return_dict, status):
    '''
    Validates the quantity to be moved.
    
    Args:
        record: Original record from request containing quantity information.
        return_dict: Dictionary to store validated data.
        status: List to collect validation error messages.
        
    Ensures quantity is positive and valid.
    Checks for consistency between quantity and serial numbers if applicable.
    Returns updated status list with any error messages added.
    '''
    # Quantity Validation
    quantity = float(record.get('quantity', 0)) if record.get('quantity', 0) else 0
    if quantity and quantity > 0:
        return_dict['quantity'] = quantity
    else:
        status.append('Invalid Quantity')
    if return_dict.get('serial_numbers') and return_dict.get('serial_numbers') != [''] and quantity != len(set(return_dict.get('serial_numbers', []))):
        status.append('Quantity and Serial Numbers Mismatch')
    return status

def validate_batch_based_check(sku_details_dict, sku_code, status, return_dict, record):
    '''
    Validates batch information for batch-based SKUs.
    
    Args:
        sku_details_dict: Dictionary containing SKU details including batch requirements.
        sku_code: Code of the SKU being validated.
        status: List to collect validation error messages.
        return_dict: Dictionary to store validated data.
        record: Original record from request containing batch information.
        
    Ensures batch number is provided for batch-based SKUs.
    Stores batch information in the return dictionary if valid.
    Returns updated status list with any error messages added.
    '''
    batch_based = sku_details_dict.get(sku_code, {}).get('batch_based', 0)
    if batch_based and not record.get('source_batch'):
        status.append('Batch Number is Mandatory for Batch Based SKU')
    else:
        return_dict['batch_no'] = record.get('source_batch')
        return_dict['batch_detail_id'] = record.get('batch_detail_id')

    return status

def validate_stock_status(record, return_dict):
    '''
    Validates and normalizes stock status values.
    
    Args:
        record: Original record containing source status information.
        return_dict: Dictionary to store validated data.
        
    Handles both integer and string status values.
    Converts string status names to their numeric equivalents.
    Returns updated return dictionary with normalized status value.
    '''
    stock_status = record.get('source_status')
    if isinstance(stock_status, int):
        return_dict['status'] = str(stock_status)
    elif isinstance(stock_status, str) and reverse_stock_choice_mapping.get(stock_status) not in [None, '']:
        return_dict['status'] = str(reverse_stock_choice_mapping.get(stock_status))
    return return_dict

def validate_wip_movement(warehouse, return_dict, restrict_movement_in_wip):
    '''
    Validates movement restrictions for work-in-progress locations.
    
    Args:
        warehouse: User object representing the warehouse.
        return_dict: Dictionary containing movement details.
        restrict_movement_in_wip: List of WIP restriction types.
        
    Checks if movement from WIP areas is allowed based on configuration.
    Validates against open job orders and sales orders.
    Returns status list with error messages if movement is restricted.
    '''
    src_storage_type, status = return_dict.get('source_storage_type'), []
    source_zone, dest_zone = return_dict.get('source_zone'), return_dict.get('dest_zone')
    if (src_storage_type in ['wip_area', 'before_invoice']) and (source_zone != dest_zone):
        src_receipt_nos = get_source_receipt_numbers(return_dict)
        open_job_order = None
        open_sale_order = None
        if 'job_order' in restrict_movement_in_wip:
            open_job_order = check_open_job_order(warehouse, src_receipt_nos, return_dict.get('sku_id'))
        if 'sale_order' in restrict_movement_in_wip:
            open_sale_order = check_open_sale_order(warehouse, src_receipt_nos, return_dict.get('sku_id'))

        if open_job_order:
            status.append("Move Inventory is not allowed in this config")
            return status
        if open_sale_order:
            status.append("Move Inventory is not allowed in WipLocations stock picked for orders when restrict movement in WIP is selected")
            return status
    return status

def get_source_receipt_numbers(return_dict):
    '''
    Retrieves receipt numbers from source stocks.
    
    Args:
        return_dict: Dictionary containing source stock information.
        
    Extracts receipt numbers from stock records.
    Filters for specific receipt types related to job orders and sales orders.
    Returns list of receipt numbers for further validation.
    '''
    source_stocks = return_dict.get('source_stock')
    source_receipt_numbers = list(
        source_stocks.filter(receipt_type__in = ['jo_dispense', 'rm_picking', 'so_picking', 'so_dispense']).values_list(
            'receipt_number', flat = True
        ))
    return source_receipt_numbers

def check_open_job_order(warehouse, src_receipt_nos, sku_id):
    '''
    Checks for open job orders related to source stocks.
    
    Args:
        warehouse: User object representing the warehouse.
        src_receipt_nos: List of receipt numbers to check.
        sku_id: ID of the SKU being moved.
        
    Queries job orders that are still in progress.
    Compares product quantity with received quantity.
    Returns boolean indicating if open job orders exist.
    '''
    open_job_order = False
    filter_dict = {
        'product_code__user': warehouse.id, 'jomaterial__id__in': src_receipt_nos,
        'jomaterial__material_code_id': sku_id,
    }
    exclude_dict = {'status__in': ['short-closed', 'cancelled_picklist', 'grn-completed', 'partial-grn']}
    values_list = ['product_quantity', 'received_quantity']
    job_order = JobOrder.objects.exclude(**exclude_dict).filter(**filter_dict).distinct().values(*values_list)
    if job_order.exists():
        for jo in job_order:
            if jo.get('product_quantity') > jo.get('received_quantity'):
                open_job_order = True

    return open_job_order

def check_open_sale_order(warehouse, src_receipt_nos, sku_id):
    '''
    Checks for open sales orders related to source stocks.
    
    Args:
        warehouse: User object representing the warehouse.
        src_receipt_nos: List of receipt numbers to check.
        sku_id: ID of the SKU being moved.
        
    Queries sales orders that are in processed status.
    Checks for positive quantities indicating incomplete orders.
    Returns boolean indicating if open sales orders exist.
    '''
    open_sale_order = False
    filter_dict = {
        'order__user': warehouse.id, 'id__in': src_receipt_nos, 'quantity__gt': 0,
        'picklist__sku_id': sku_id, "order_status_flag__in": ['processed_orders']
    }
    values_list = ['quantity']
    sale_order = SellerOrderSummary.objects.filter(**filter_dict).distinct().values(*values_list)
    if sale_order.exists():
        return True

    return open_sale_order

def validate_source_stock(warehouse, return_dict, status, allow_inter_zonal_movement):
    '''
    Validates Source Stock and Returns Source Stock Objects
    '''
    source_reserved_stock = 0
    source_stocks = get_source_stock(warehouse, return_dict)
    if not source_stocks:
        status.append('No Source Stock Found')
        return status, return_dict
    else:
        source_reserved_stock = get_source_reserved_stock(warehouse, return_dict)

    #Validating Source Status
    if source_stocks and len(list(source_stocks.values_list('status').distinct())) > 1:
        status.append('Multiple Stock Status Found, Please Provide Status')

    stock_quantity = source_stocks.aggregate(Sum('quantity'))['quantity__sum'] or 0

    return_dict['stock_serial_dict'] = {}
    #Quantity Validation
    if stock_quantity - source_reserved_stock < return_dict['quantity']:
        status.append('Entered Quantity is more than Available Stock')

    #Don't allow partial movement if the destination zone is carton managed
    if return_dict.get('is_dest_lpn_managed') and return_dict.get('quantity') < stock_quantity-source_reserved_stock:
        status.append('Partial Movement is not allowed for Carton Managed Destination Zone')

    if return_dict.get('serial_numbers') and return_dict.get('serial_numbers') != ['']:
        serial_stock_dict = dict(SerialNumber.objects.filter(stock__in=source_stocks, status=1).values_list('serial_number', 'stock_id'))
        if not set(return_dict.get('serial_numbers')).issubset(list(serial_stock_dict.keys())):
            status.append('Invalid Serial Numbers')
        else:
            stock_serial_dict = {}
            for serial in return_dict.get('serial_numbers'):
                stock_id = serial_stock_dict.get(serial)
                stock_serial_dict.setdefault(stock_id, []).append(serial)
            return_dict['stock_serial_dict'] = stock_serial_dict
    return_dict['source_stock'] = source_stocks

    available_status = list(source_stocks.values_list('status', flat=True).distinct())

    #dont_restict_inter_zonal_movement based on config
    if return_dict['dest_zone'] not in allow_inter_zonal_movement:

        #Block Inter Move Inventory for all status other than available
        if any(status != 1 for status in available_status) and return_dict['dest_zone'] != return_dict['source_zone']:
            status.append('Inter Zonal Movement is not allowed for SKU  %s with Status %s' % (return_dict.get('sku_code'), status_choices.get(source_stocks[0].status)))

    return status, return_dict

def get_source_stock(warehouse, return_dict):
    '''
    Returns Available Stock
    '''
    stock_filter_dict = {
        'quantity__gt': 0, 'sku__user': warehouse.id,
        'sku_id': return_dict['sku_id'],
        'location_id': return_dict['source_loc_id']
    }
    if return_dict.get('batch_no'):
        stock_filter_dict['batch_detail__batch_no'] = return_dict['batch_no']
    if return_dict.get('status'):
        stock_filter_dict['status'] = return_dict['status']
    if return_dict.get('lpn_number'):
        stock_filter_dict['lpn_number'] = return_dict['lpn_number']

    stocks = StockDetail.objects.filter(**stock_filter_dict)
    if not stocks.exists():
        return False
    return stocks

def get_source_reserved_stock(warehouse, return_dict):
    '''
    Returns Reserved Stock
    '''
    reserved_filter_dict = {
        'stock__sku_id': return_dict['sku_id'],
        'user': warehouse.id, 'status': "open",
        'stock__location_id': return_dict['source_loc_id']
    }
    if return_dict.get('batch_no'):
        reserved_filter_dict['stock__batch_detail__batch_no'] = return_dict['batch_no']
    if return_dict.get('lpn_number'):
        reserved_filter_dict['stock__lpn_number'] = return_dict['lpn_number']

    picklist_reserved = Picklist.objects.exclude(stock=None).filter(**reserved_filter_dict).aggregate(
        Sum('reserved_quantity'))['reserved_quantity__sum'] or 0

    # Get allocation reservations
    allocation_filter_dict = {
        'stock__sku_id': return_dict['sku_id'],
        'warehouse_id': warehouse.id,
        'status': 1,
        'stock__location_id': return_dict['source_loc_id']
    }
    if return_dict.get('batch_no'):
        allocation_filter_dict['stock__batch_detail__batch_no'] = return_dict['batch_no']

    allocation_reserved = StockAllocation.objects.filter(**allocation_filter_dict).aggregate(
        Sum('quantity'))['quantity__sum'] or 0

    return picklist_reserved+allocation_reserved

def validate_sku_details(record, sku_details_dict, status, return_dict):

    #Returns Required Details Dict
    sku_code = record.get('sku_code', '')
    serial_based = sku_details_dict.get(sku_code, {}).get('enable_serial_based', 0)
    if not sku_code or not sku_details_dict.get(sku_code):
        status.append('Invalid SKU Code')
    else:
        return_dict['sku_id'] = sku_details_dict.get(sku_code).get('sku_id')

    if serial_based and not record.get('serial_numbers'):
        status.append('Serial Numbers are Mandatory for Serial Based SKU')
    else:
        return_dict['serial_numbers'] = record.get('serial_numbers')

    return sku_code, status, return_dict

def validate_location_zone_details(record, loc_master_dict, status, return_dict, zone_dict):
    """
    Validates the source and destination locations and their zones.

    Args:
        record: The record containing source and destination locations.
        loc_master_dict: Dictionary of location masters.
        status: List to store validation status messages.
        return_dict: Dictionary to store validated data.
        zone_dict: Dictionary of zone details for locations.

    Returns:
        A tuple containing the updated status and return dictionary.
    """
    #Location Validation
    source_loc, dest_loc = record.get('source_loc'), record.get('dest_loc')
    if not (source_loc and loc_master_dict.get(source_loc)):
        status.append('Invalid Source Location')
    else:
        location_status = zone_dict.get(source_loc).get('location_status', '')
        if location_status == 0:
            status.append('Inactivate Locations not allowed to move Inventory')
        return_dict['source_loc_id'] = loc_master_dict.get(source_loc)
        return_dict['source_zone'] = zone_dict.get(source_loc).get('zone')
        return_dict['source_storage_type'] = zone_dict.get(source_loc).get('storage_type')
        return_dict['source_segregation'] = zone_dict.get(source_loc).get('segregation')
        return_dict['is_source_lpn_managed'] = zone_dict.get(source_loc).get('carton_managed')

        if return_dict.get('is_source_lpn_managed') and not record.get('lpn_number'):
            status.append('LPN Number is Mandatory for LPN Managed Source Location')

    if not dest_loc or not loc_master_dict.get(dest_loc):
        status.append('Invalid Destination Location')
    else:
        location_status = zone_dict.get(dest_loc).get('location_status', '')
        if location_status == 0:
            status.append('Inactivate Locations not allowed to move Inventory')
        return_dict['dest_loc'] = dest_loc
        return_dict['dest_loc_id'] = loc_master_dict.get(dest_loc)
        return_dict['dest_zone'] = zone_dict.get(dest_loc).get('zone')
        return_dict['dest_storage_type'] = zone_dict.get(dest_loc).get('storage_type')
        return_dict['destination_segregation'] = zone_dict.get(dest_loc).get('segregation')
        return_dict['is_dest_lpn_managed'] = zone_dict.get(dest_loc).get('carton_managed')
        # Check if destination zone is carton managed and source stock is missing LPN number
        if (return_dict.get('is_dest_lpn_managed') and not return_dict.get('is_source_lpn_managed')) and ((return_dict.get('source_segregation') or return_dict.get('destination_segregation')) not in ['inbound_staging', 'outbound_staging']):
            status.append('Source Should be LPN Managed When Destination is LPN Managed')

    return status, return_dict

def get_sku_location_details(warehouse, validate_dict):
    '''
    Retrieves SKU and location details for validation.
    
    Args:
        warehouse: User object representing the warehouse.
        validate_dict: List of records to validate.
        
    Extracts unique SKUs and locations from input records.
    Queries database for SKU details including batch and serial requirements.
    Retrieves location information including zone and storage type.
    Returns dictionaries mapping codes to their detailed information.
    '''
    zone_restrictions = {
        'restrict_one_location_to_one_sku': set(),
        'unique_batch': set()
    }
    #SKU Master Validation
    input_skus, input_locations = set(), set()
    for record in validate_dict:
        input_skus.add(record.get('sku_code'))
        input_locations.add(record.get('source_loc'))
        input_locations.add(record.get('dest_loc'))

    sku_objs = SKUMaster.objects.filter(user = warehouse.id, sku_code__in = input_skus).values('sku_code', 'id', 'batch_based', 'enable_serial_based')
    sku_details_dict = {sku.get('sku_code'): {'sku_id': sku.get('id'), 'batch_based': sku.get('batch_based'), 'enable_serial_based': sku.get('enable_serial_based')}
                        for sku in sku_objs}
    #Location Master Validation
    loc_master_dict, loc_master_zone_dict = {}, {}
    loc_obj = LocationMaster.objects.exclude(zone__storage_type__in = ['replenishment_staging_area', 'nte_staging_area']).filter(
        zone__user = warehouse.id, location__in = input_locations
    ).values('location', 'id', 'zone__zone', 'zone__storage_type', 'zone__segregation', 'zone__carton_managed', 'status','zone__restrict_one_location_to_one_sku','zone__unique_batch')
    
    for loc in loc_obj:
        location = loc.get('location')
        if loc.get('zone__restrict_one_location_to_one_sku', ''):
                zone_restrictions['restrict_one_location_to_one_sku'].add(location)

        if loc.get('zone__unique_batch', ''):
            zone_restrictions['unique_batch'].add(location)
            
        loc_master_dict[loc.get('location')] = loc.get('id')
        loc_master_zone_dict[loc.get('location')] = {
            'zone': loc.get('zone__zone'),
            'storage_type': loc.get('zone__storage_type'),
            'segregation': loc.get('zone__segregation'),
            'carton_managed': loc.get('zone__carton_managed'),
            'location_status': loc.get('status')
        }

    return sku_details_dict, loc_master_dict, loc_master_zone_dict, zone_restrictions

def move_inventory_call_back_filter(move_inv_data,filter={}):
    '''
    Filters move inventory data based on movement type and zones.
    
    Args:
        move_inv_data: Dictionary containing move inventory records.
        filter: Dictionary with filtering criteria.
        
    Applies filters for inter-zone and intra-zone movements.
    Excludes movements involving specified zones.
    Returns filtered data or None if no records match criteria.
    '''
    filter_data, exclude_zones = filter.get('movement_type',[]), set(filter.get('exclude_zones', []))
    send_default = False if filter_data else True
    allow_inter_zone = 'Inter Zone' in filter_data
    allow_intra_zone = 'Intra Zone' in filter_data

    return_data = {'move_inventory_data': []}
    for row in move_inv_data.get('move_inventory_data',[]):
        is_intra_zone, is_inter_zone, is_not_exclude = False, False, True
        source_zone, dest_zone = row.get('source_zone'), row.get('destination_zone')
        if dest_zone == source_zone:
            is_intra_zone = True
        else:
            is_inter_zone = True

        #Validating Source and Destination zones
        is_not_exclude = source_zone not in exclude_zones and dest_zone not in exclude_zones #valid

        #Validating Intra Zone
        is_intra_zone = (allow_intra_zone and is_intra_zone)

        #Validating Inter Zone
        is_inter_zone = (allow_inter_zone and is_inter_zone)

        if (is_not_exclude and (is_intra_zone or is_inter_zone)) or (send_default):
            return_data['move_inventory_data'].append(row)

    if return_data['move_inventory_data']:
        return return_data
    return None

def move_inventory_call_back_3p_integration(move_inv_data, warehouse):
    '''
    Integrates move inventory data with third-party systems.
    
    Args:
        move_inv_data: Dictionary containing move inventory records.
        warehouse: User object representing the warehouse.
        
    Retrieves integration APIs configured for move inventory trigger.
    Applies filters to data based on integration configuration.
    Creates integration call records for filtered data.
    Triggers asynchronous execution of integration calls.
    '''
    int_apis = UserIntegrationAPIS.objects.filter(
        user_integration__user_id=warehouse.id, trigger='move_inventory', status=1
    )
    new_created_ids, integration_call_objs = [], []
    for int_api in int_apis:
        filters_data = int_api.filters.replace("'", "\"")
        if filters_data:
            filters_dict = json.loads(filters_data)
        else:
            filters_dict = {}

        if int_api.data_format == 'callback':
            if int_api.filters:
                move_inv_data = move_inventory_call_back_filter(move_inv_data, filters_dict)
            if move_inv_data:
                data_dict = move_inv_data
            else:
                continue
            data_dict = {
                'user_integrationapis_id': int_api.id,
                'api_data': data_dict,
                'status': 1, 'account_id': warehouse.userprofile.id
            }
            integration_call_objs.append(UserIntegrationCalls(**data_dict))
    if integration_call_objs:
        objs = UserIntegrationCalls.objects.bulk_create_with_rounding(integration_call_objs)
        if objs:
            new_created_ids = [obj.id for obj in objs]

        filters ={"id__in":new_created_ids}
        async_run_3p_int_func(filters)

def get_existing_serials(warehouse, reference_type, move_ids, open_status = False):
    '''
    Retrieves existing serial numbers for move inventory transactions.
    
    Args:
        warehouse: User object representing the warehouse.
        reference_type: Type of reference for serial transactions.
        move_ids: List of move inventory IDs to retrieve serials for.
        
    Creates a filter for serial number transactions.
    Uses SerialNumberTransactionMixin to retrieve serial details.
    Returns a DataFrame containing serial numbers and transaction IDs.
    Returns empty DataFrame if no serials found or move_ids is empty.
    '''
    values_list = ['serial_number', 'transact_id']
    if not move_ids:
        return pd.DataFrame(columns=values_list)
    serial_filter = {'filters':{'reference_type': reference_type, 'transact_id__in': move_ids, 'status': 0}}
    if open_status:
        serial_filter['filters']['status'] = 1

    serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    serial_df = pd.DataFrame(existing_serials.get('data', []))
    if serial_df.empty:
        serial_df = pd.DataFrame(columns=values_list)
    return serial_df

def fetch_serial_numbers_for_sps_id(move_id, serial_df):
    '''
    Extracts serial numbers for a specific move inventory ID.
    
    Args:
        move_id: Move inventory ID to fetch serial numbers for.
        serial_df: DataFrame containing serial number data.
    '''
    filtered_serials = serial_df[(serial_df['transact_id'] == move_id)]
    if filtered_serials.empty:
        return []
    serial_numbers = filtered_serials['serial_numbers'].tolist()[0]
    return serial_numbers

def get_move_inventory_call_back_data(warehouse, move_ids):
    '''
    Retrieves formatted move inventory data for callbacks.
    
    Args:
        warehouse: User object representing the warehouse.
        move_ids: List of move inventory IDs to retrieve data for.
        
    Queries move inventory records with related data.
    Formats data with timezone-adjusted dates and additional metadata.
    Collects SKU codes and zone mappings for inventory updates.
    Returns structured data for integration callbacks.
    '''
    data_list, sku_codes = [], []
    sku_zones_dict = defaultdict(list)
    move_inventory_data = {}
    timezone = get_user_time_zone(warehouse)
    dept_name = warehouse.username
    store_name = get_admin(warehouse).first_name
    move_inv_obj = MoveInventory.objects.filter(id__in = move_ids, sku__user = warehouse.id)

    serial_df = get_existing_serials(warehouse, 'move_inventory', move_ids)

    move_inv_data = list(move_inv_obj.values(
        'sku__sku_code','sku__sku_desc','sku__sku_size','sku__sku_category',
        'sku__sub_category','sku__sku_brand','source_location__location',
        'dest_location__location','quantity','creation_date','reason','json_data',
        'batch_detail__batch_no','batch_detail__manufactured_date','batch_detail__expiry_date',
        'source_location__zone_id', 'dest_location__zone_id',
        'source_location__zone__zone','dest_location__zone__zone', 'id',
        'sku__measurement_type', 'batch_detail__batch_reference', 'move_inventory_id',
        'reference'
        )
    )
    for data in move_inv_data:
        created_by,created_from, picked_by, lpn_number = '','', '', ''
        batch_no,batch_reference, manufactured_date, expiry_date = '', '', '', ''
        sku_code = data.get('sku__sku_code','')
        sku_desc = data.get('sku__sku_desc','')
        sku_size = data.get('sku__sku_size','')
        sku_category = data.get('sku__sku_category','')
        sku_sub_category = data.get('sku__sub_category','')
        sku_brand = data.get('sku__sku_brand','')
        source_location = data.get('source_location__location','')
        destination_location = data.get('dest_location__location','')
        source_zone = data.get('source_location__zone__zone','')
        source_zone_id = data.get('source_location__zone_id','')
        destination_zone_id = data.get('dest_location__zone_id','')
        destination_zone = data.get('dest_location__zone__zone','')
        serial_numbers = fetch_serial_numbers_for_sps_id(data.get('id'), serial_df)
        quantity = data.get('quantity',0)
        reason = data.get('reason')
        batch_no = data.get('batch_detail__batch_no','')
        batch_reference = data.get('batch_detail__batch_reference', '')
        grouping_reference = data.get('reference', '')
        if data.get('batch_detail__manufactured_date',''):
            manufactured_date = data.get('batch_detail__manufactured_date','')
            manufactured_date = get_local_date_known_timezone(timezone, manufactured_date)
        if data.get('batch_detail__expiry_date',''):
            expiry_date = data.get('batch_detail__expiry_date','')
            expiry_date =  get_local_date_known_timezone(timezone, expiry_date)
        creation_date = data.get('creation_date','')
        json_data = data.get('json_data','')
        if json_data:
            created_by = json_data.get('request_user','')
            created_from = json_data.get('done_by','')
            picked_by = json_data.get('picked_user','')
            lpn_number = json_data.get('lpn_number','')

        sku_codes.append(sku_code)
        sku_zones_dict[source_zone_id].append(sku_code)
        sku_zones_dict[destination_zone_id].append(sku_code)

        data_dict = {
                    'id': data.get('id'),
                    'zone':store_name,
                    'warehouse': dept_name,
                    'sku_code':sku_code,
                    'sku_description': sku_desc,
                    'sku_size': sku_size,
                    'sku_category': sku_category,
                    'sku_sub_category': sku_sub_category,
                    'sku_brand':sku_brand,
                    'source_location': source_location,
                    'uom': data.get('sku__measurement_type', ''),
                    'destination_location' : destination_location,
                    'source_zone' :source_zone,
                    'destination_zone' : destination_zone,
                    'serial_numbers': serial_numbers,
                    'lpn_number': lpn_number,
                    'batch_no':batch_no,
                    'batch_reference': batch_reference,
                    'manufactured_date':manufactured_date,
                    'expiry_date' : expiry_date,
                    'quantity' : quantity,
                    'reason' :reason,
                    'created_at': get_local_date_known_timezone(timezone, creation_date),
                    'created_by':created_by,
                    'Created_from' : created_from,
                    'transaction_id': data.get('move_inventory_id'),
                    'picked_by': picked_by,
                    'grouping_reference': grouping_reference
        }
        data_list.append(data_dict)
    move_inventory_data['move_inventory_data'] = data_list
    return move_inventory_data, sku_codes, sku_zones_dict

def get_available_stock_df(stock_detail, load_lpns):
    '''
    Converts stock detail queryset to a DataFrame with aggregated data.
    
    Args:
        stock_detail: QuerySet of StockDetail objects.
        load_lpns: Boolean flag to include LPN-related fields.
        
    Defines fields to extract from stock records.
    Aggregates quantities and collects stock IDs for each unique combination.
    Handles null values and data type conversions.
    Returns a DataFrame with stock information ready for further processing.
    '''
    values_list = ['location_id', 'lpn_number']

    values_dict = {
        'sku_detail_id': F('sku_id'),
        'sku_code': F('sku__sku_code'),
        'sku_desc': F('sku__sku_desc'),
        'sku_size':  F('sku__sku_size'),
        'serial_based': F('sku__enable_serial_based'),
        'batch_based': F('sku__batch_based'),
        'stock_status': F('status'),
        'sku_uom': F('sku__measurement_type'),
        'sku_image': F('sku__image_url'),
        'mfg_date': F('batch_detail__manufactured_date'),
        'expiry_date': F('batch_detail__expiry_date'),
        'mrp': F('batch_detail__mrp'),
        'batch_number': F('batch_detail__batch_no'),
        'batch_reference': F('batch_detail__batch_reference'),
        'batch_id': F('batch_detail_id'),
        'location_name': F('location__location'),
        'zone': F('location__zone__zone'),
        'storage_type': F('location__zone__storage_type'),
        'check_digit': F('location__check_digit')
    }
    if load_lpns:
        values_list.extend(['receipt_number', 'receipt_type'])

    stock_objs = stock_detail.values(
        *values_list, **values_dict
    ).annotate(available_quantity = Sum('quantity'), stock_ids = ArrayAgg('id'))

    stock_df = pd.DataFrame(stock_objs)

    #Replacing Nan with None
    stock_df = stock_df.astype(object)
    stock_df = stock_df.where(pd.notna(stock_df), None)

    return stock_df


def get_reserved_stock_df(stock_df, warehouse_id):
    '''
    Calculate the reserved quantity for available stock.
    
    Args:
        stock_df: DataFrame containing stock information.
        warehouse_id: ID of the warehouse user.
        
    Retrieves picklist reservations for the stock items.
    Merges reservation data with stock data.
    Handles data type conversions for proper joining.
    Returns stock DataFrame with reserved quantities added.
    '''
    if stock_df.empty:
        return stock_df

    sku_ids = stock_df['sku_detail_id'].unique()
    location_ids = stock_df['location_id'].unique()

    reserved_filter = {
        'sku_id__in': sku_ids,
        'location_id__in': location_ids,
        'status__in': ['open', 'Allocated'],
        'user': warehouse_id
    }

    picklist_obj = Picklist.objects.filter(**reserved_filter).values(
            'location_id',
            batch_id = F('stock__batch_detail_id'), stock_status = F('stock__status'),
            sku_detail_id = F('stock__sku_id')
        ).annotate(reserved_qty=Sum('reserved_quantity'))

    reserved_df = pd.DataFrame(picklist_obj)
        #Replacing Nan with None
    reserved_df = reserved_df.astype(object)
    reserved_df = reserved_df.where(pd.notna(reserved_df), None)

    if not reserved_df.empty:

        columns_to_convert = ['sku_detail_id', 'location_id', 'stock_status', 'batch_id']

        for col in columns_to_convert:
            stock_df[col] = stock_df[col].astype(str)
            reserved_df[col] = reserved_df[col].astype(str)
            stock_df[col] = stock_df[col].str.rstrip('.0')
            reserved_df[col] = reserved_df[col].str.rstrip('.0')

        stock_df = pd.merge(
            stock_df, reserved_df, on=['sku_detail_id', 'location_id', 'batch_id', 'stock_status'], how='left'
        )
        # Apply fillna(0) only to specific columns
        columns_to_fill = ['reserved_qty']  # Replace with your actual columns
        stock_df[columns_to_fill] = stock_df[columns_to_fill].fillna(0)
    else:
        stock_df['reserved_qty'] = 0

    return stock_df

def get_search_params_of_stock(request_data):
    '''
    Extracts search parameters from request data for stock filtering.
    
    Args:
        request_data: Dictionary containing request parameters.
        
    Creates filter dictionaries for different search criteria.
    Handles location, batch, SKU code, and LPN filters.
    Sets pagination parameters for result limiting.
    Returns tuple of search parameters and pagination settings.
    '''
    search_params,search_params1,search_params2 = {},{},{}

    if request_data.get('location'):
        search_params['location__%s__%s' % ('location','iexact')] = request_data['location']
    if request_data.get('batch_no'):
        search_params1['batch_detail__%s__%s' % ('batch_no', 'iexact')] = request_data['batch_no']
    if request_data.get('source_location'):
        search_params['location__%s__%s' % ('location','iexact')] = request_data['source_location']
    if request_data.get('sku_code'):
        search_params1['sku__%s__%s' % ("sku_code", 'icontains')] = request_data['sku_code']
        search_params2['sku__%s__%s__%s' % ('eannumbers','ean_number','iexact')] = request_data['sku_code']
    if request_data.get('lpn_number'):
        search_params['lpn_number'] = request_data['lpn_number']

    limit = int(request_data['limit']) if request_data.get('limit') else 10
    offset = int(request_data['offset']) if request_data.get('offset') else 0

    return search_params, search_params1, search_params2, limit, offset

def get_stock_objects(warehouse_id, search_params, search_params1, search_params2):
    '''
    Get Stocks based on search parameters.
    
    Args:
        warehouse_id: ID of the warehouse user.
        search_params: Dictionary of primary search parameters.
        search_params1: Dictionary of SKU-related search parameters.
        search_params2: Dictionary of alternative SKU search parameters.
        
    Queries StockDetail objects with positive quantities.
    Excludes stocks in specific staging areas.
    Applies search filters using Q objects for complex queries.
    Returns filtered QuerySet of stock objects.
    '''
    stock_detail = StockDetail.objects.exclude(status = 2).exclude(
        location__zone__storage_type__in = ['replenishment_staging_area', 'nte_staging_area', 'cc_variance']
    ).filter(sku__user=warehouse_id, quantity__gt=0)
    if search_params:
        stock_detail = stock_detail.filter(Q(**search_params) & (Q(**search_params1) | Q(**search_params2)))
    return stock_detail

def get_available_stocks(warehouse_id, stock_detail, load_lpns = False):
    '''
    Processes stock details to calculate available quantities.
    
    Args:
        warehouse_id: ID of the warehouse user.
        stock_detail: QuerySet of StockDetail objects.
        load_lpns: Boolean flag to include LPN-related fields.
        
    Retrieves decimal precision setting for the warehouse.
    Converts stock details to DataFrame format.
    Calculates available quantities by subtracting reserved quantities.
    Returns processed stock data and decimal precision setting.
    '''
    decimal_limit = get_decimal_value(warehouse_id)

    stock_df = get_available_stock_df(stock_detail, load_lpns)

    stock_df = get_reserved_stock_df(stock_df, warehouse_id)

    if not stock_df.empty:
        stock_df['available_quantity'] = stock_df['available_quantity'].fillna(0)
        if 'reserved_qty' not in stock_df.columns:
            stock_df['reserved_qty'] = 0

        stock_df['reserved_qty'] = stock_df['reserved_qty'].fillna(0)
        for index, stock_record in stock_df.iterrows():
            stock_df.loc[index, 'quantity'] = float(
                Decimal(str(stock_record['available_quantity'])) - Decimal(str(stock_record['reserved_qty']))
            )

    if not stock_df.empty:
        stock_df = stock_df.to_dict(orient='records')

    return stock_df, decimal_limit

def get_staging_suggested_location_details(warehouse, stock_details, limit, offset):
    '''
    Determines suggested locations for staging area inventory.
    
    Args:
        warehouse: User object representing the warehouse.
        stock_details: QuerySet of stock details to process.
        limit: Maximum number of records to return.
        offset: Number of records to skip.
        
    Identifies the current staging area type.
    Retrieves receipt numbers and associated putaway information.
    Determines suggested next locations based on staging routes.
    Returns suggested locations for each LPN and transaction type mapping.
    '''

    return_list, lpn_wise_details, lpn_transac_type_dict = [], {}, {}

    location = stock_details.values_list('location__location', flat=True).first()

    # Get the storage type from locationmaster (assuming only one distinct storage type)
    stage = LocationMaster.objects.filter(zone__user = warehouse.id, location = location).values_list('zone__storage_type', flat=True).first()

    if stage and stage in ['REC', 'REPACK', 'INSP']:
        # Fetch receipt numbers associated with 'grn_packing' type
        receipt_numbers = list(stock_details.filter(receipt_type='grn_packing').exclude(lpn_number__isnull=True).values_list('receipt_number', flat=True))

        # Fetch PO Location details for the corresponding receipt numbers
        po_loc_details = POLocation.objects.filter(
            sku__user=warehouse.id, id__in=receipt_numbers, status=1, original_quantity__gt=0
        ).values('location__zone__zone', 'json_data__lpn_number', 'sku__is_barcode_required', 'putaway_type')

        if stage == 'REC':

            # Fetch QC details for the corresponding receipt numbers
            qc_control_objs = list(QualityControl.objects.filter(warehouse_id = warehouse.id, id__in = receipt_numbers).values('json_data__lpn_number', 'json_data__grn_type'))
            transaction_type = qc_control_objs[0].get('json_data__grn_type') if qc_control_objs else 'PO'
            next_suggesed_location = get_staging_route_location_details(warehouse, [], [transaction_type], get_insp = True)

            for qc_obj in qc_control_objs:
                lpn_number = qc_obj.get('json_data__lpn_number')
                if lpn_number not in lpn_wise_details:
                    lpn_wise_details[lpn_number] = next_suggesed_location
                    return_list.append({'lpn_number': lpn_number, 'suggested_location': next_suggesed_location})

        # Extract unique zones for the route details
        putzones, transaction_types = [], set()

        putaway_type_dict = {'po_grn': 'PO', 'sales_return': 'SR'}
        for po_loc in po_loc_details:
            putzones.append(po_loc['location__zone__zone'])
            transaction_types.add(putaway_type_dict.get(po_loc.get('putaway_type'), ''))

        # Fetch the staging route data based on the current stage
        staging_route_data = get_staging_route_location_details(
            warehouse, list(putzones), transaction_types, current_stage=stage
        )

        lpn_wise_suggested_locations = {}
        for po_loc in po_loc_details:
            transaction_type = putaway_type_dict.get(po_loc.get('putaway_type'), '')
            lpn_number = po_loc.get('json_data__lpn_number')
            lpn_transac_type_dict[lpn_number] = transaction_type
            if not lpn_number:
                continue

            putzone = po_loc['location__zone__zone']
            is_barcode_required = 'yes' if po_loc['sku__is_barcode_required'] else 'no'

            # Get the next suggested location based on the current stage, putzone, and barcode status
            suggested_location_details = get_next_suggested_staging_location(
                staging_route_data, (putzone, is_barcode_required, stage, transaction_type)
            )

            suggested_location = suggested_location_details.get('location', '')
            if lpn_number not in lpn_wise_suggested_locations:
                lpn_wise_suggested_locations[lpn_number] = {
                    'lpn_number': lpn_number,
                    'suggested_location': suggested_location,
                    'transaction_type': transaction_type
                }
                lpn_wise_details[lpn_number] = suggested_location

        return_list.extend(list(lpn_wise_suggested_locations.values()))

    elif stage and stage in ['sorting', 'packing']:
        stock_values = list(stock_details.filter(receipt_type='so_picking').exclude(lpn_number__isnull=True).values('receipt_number', 'lpn_number', 'grn_number', 'location__zone__storage_type'))
        receipt_numbers, receipt_number_lpn_map, lpn_number_storage_type_map = [], {}, {}
        storage_type = ''
        for stock in stock_values:
            receipt_number = stock.get('receipt_number')
            lpn_number = stock.get('lpn_number')
            receipt_numbers.append(receipt_number)
            if lpn_number:
                receipt_number_lpn_map.setdefault(receipt_number, []).append(lpn_number)
            storage_type = stock.get('location__zone__storage_type', '')
            lpn_number_storage_type_map[lpn_number] = storage_type
            
        sos_objects = SellerOrderSummary.objects.filter(picklist__user=warehouse, id__in=receipt_numbers, order_status_flag='processed_orders').values_list('order__order_reference', 'order__order_type', 'picklist__pick_type', 'picklist__json_data')
        order_types = set()
        pick_types = set()
        order_details = {}
        for sos in sos_objects:
            order_reference, order_type, pick_type, json_data = sos
            order_types.add(order_type)
            pick_types.add(pick_type)
            order_details[order_reference] = {
                'order_type': order_type,
                'pick_type': pick_type,
                'json_data__cluster_name': json_data.get('cluster_name', '')
            }
        misc_option_keys = ['outbound_staging_lanes']
        misc_options_dict = get_misc_options_list(misc_option_keys, warehouse, False)
        
        # assuming only one lpn in one location
        if len(lpn_number_storage_type_map.keys()) == 1:
            if storage_type == 'sorting':
                priority_list = ['PACKING', 'PRE_INVOICE']
            elif storage_type == 'packing':
                priority_list = ['PRE_INVOICE']
            orders_staging_location = get_next_location_suggestion_for_outbound(warehouse, order_types, pick_types, order_details, misc_options_dict, priority_list)
            lpn_wise_suggested_locations = {}
            for stock in stock_values:
                lpn_number = stock.get('lpn_number')
                if lpn_number not in lpn_wise_suggested_locations:
                    suggested_location = orders_staging_location.get(stock.get('grn_number'), '')
                    if not suggested_location:
                        continue
                    lpn_wise_suggested_locations[lpn_number] = {
                        'lpn_number': lpn_number,
                        'suggested_location': suggested_location,
                        'order_type': order_details.get(stock.get('grn_number'), {}).get('order_type', '')
                    }
                    lpn_wise_details[lpn_number] = suggested_location
            return_list = list(lpn_wise_suggested_locations.values())

    # Handle case for stages other than 'REC' or 'REPACK' (assign empty location)
    elif stage:
        lpn_numbers = stock_details.exclude(lpn_number__isnull=True).values_list('lpn_number', flat=True).distinct()
        for lpn_number in lpn_numbers:
            return_list.append({'lpn_number': lpn_number, 'suggested_location': ''})
            lpn_wise_details[lpn_number] = ''

    return_list = return_list[offset : offset + limit]

    return return_list, lpn_wise_details, lpn_transac_type_dict

def get_next_location_suggestion_for_outbound(warehouse, order_types, pick_types, order_details, misc_options_dict, priority_list):
    """
    Determines the next staging location for outbound orders based on order types and pick types.
    Args:
        warehouse: User object representing the warehouse.
        order_types: Set of order types to filter staging locations.
        pick_types: Set of pick types to filter staging locations.
        order_details: Dictionary containing order details for each order reference.
        misc_options_dict: Dictionary containing miscellaneous options for outbound staging lanes.
        priority_list: List of priority staging locations.
    Returns:
        A dictionary mapping order references to their suggested staging locations.
    To add:
        currently this will only work for sorting to packing/pre_invoice movement. need to handle for multiple stages
    
    """
    orders_staging_location = {}
    no_zone_orders = []
    filters = {
        'user': warehouse,
        'order_type__in': list(order_types),
        'pick_type__in': list(pick_types) + [''],
        'status': 1,
        'receipt_type__in': priority_list,
        'zone__isnull': False,
    }

    order_type_zone_mapping = list(OrderTypeZoneMapping.objects
        .filter(**filters)
        .values('order_type', 'pick_type', 'receipt_type', 'zone__zone', 'json_data')
        .annotate(pick_type_priority=Case(
            When(pick_type__in=list(pick_types), then=Value(1)),
            default=Value(2),
            output_field=IntegerField()
        ))
        .order_by('order_type', 'pick_type_priority', 'priority')
    )

    # Prepare order type zone mapping dict
    order_type_dict = {}
    for order_type_zone in order_type_zone_mapping:
        order_type = order_type_zone['order_type']
        pick_type = order_type_zone['pick_type']
        receipt_type = order_type_zone['receipt_type']
        if order_type not in order_type_dict:
            order_type_dict[order_type] = {}
        if pick_type not in order_type_dict[order_type]:
            order_type_dict[order_type][pick_type] = {}
        if receipt_type not in order_type_dict[order_type][pick_type]:
            order_type_dict[order_type][pick_type][receipt_type] = {'zones': [], 'json_data': []}
        order_type_dict[order_type][pick_type][receipt_type]['zones'].append(order_type_zone['zone__zone'])
        order_type_dict[order_type][pick_type][receipt_type]['json_data'].append(order_type_zone['json_data'])

    for order_reference, order_data in order_details.items():
        order_type = order_data['order_type']
        pick_type = order_data['pick_type']
        cluster_name = order_data['json_data__cluster_name']
        if order_type not in order_type_dict:
            no_zone_orders.append(order_reference)
            continue
        if pick_type not in order_type_dict[order_type]:
            if '' not in order_type_dict[order_type]:
                no_zone_orders.append(order_reference)
                continue
            pick_type = ''
        for priority in priority_list:
            if priority not in order_type_dict[order_type][pick_type]:
                continue
            if not order_type_dict[order_type][pick_type][priority]['zones']:
                continue
            # Filter zones based on cluster name

            staging_zones = order_type_dict[order_type][pick_type][priority]['zones'].copy()
            for index, json_data in enumerate(order_type_dict[order_type][pick_type][priority]['json_data']):
                if pick_type == 'cluster_picking' and cluster_name and json_data.get('cluster_name', '') and cluster_name != json_data.get('cluster_name', ''):
                    staging_zones.remove(order_type_dict[order_type][pick_type][priority]['zones'][index])
            if not staging_zones:
                continue
            # Fetch locations based on filtered zones
            locations = LocationMaster.objects.filter(zone__user=warehouse.id, zone__zone__in=staging_zones, status=1).order_by('fill_sequence')
            if not locations:
                locations = LocationMaster.objects.filter(zone__user=warehouse.id, zone__storage_type=priority.lower(), status=1).order_by('fill_sequence')
            if not locations:
                locations = create_default_zones(warehouse, priority + '_ZONE', priority + '_DFLT', 99999, 'outbound_staging', priority.lower())
            orders_staging_location[order_reference] = locations[0].location
            break
        if order_reference not in orders_staging_location:
            no_zone_orders.append(order_reference)

    # Fetch Locations for orders without any staging zone
    if no_zone_orders:
        storage_type = 'pre_invoice'
        if "PACKING_STAGING" in misc_options_dict.get('outbound_staging_lanes', []) or "all" in misc_options_dict.get('outbound_staging_lanes', []):
            storage_type = 'packing'
        locations = LocationMaster.objects.filter(zone__user=warehouse.id, zone__storage_type=storage_type, status=1).order_by('fill_sequence')
        if not locations:
            locations = create_default_zones(warehouse, storage_type.upper() + '_ZONE', storage_type.upper() + '_DFLT', 99999, 'outbound_staging', storage_type)
        for order_reference in no_zone_orders:
            orders_staging_location[order_reference] = locations[0].location

    return orders_staging_location


@get_warehouse
def get_sku_location_data(request, warehouse:User):
    '''
    API endpoint to retrieve SKU location data with suggested locations.
    
    Args:
        request: HTTP request object containing query parameters.
        warehouse: User object representing the warehouse.
        
    Processes request parameters to filter stock data.
    Retrieves stock information with suggested locations for staging areas.
    Formats response with stock details, serial numbers, and batch information.
    Returns JSON response with paginated stock data.
    '''
    try:
        request_data = request.GET
    except TypeError:
        return JsonResponse({'message': 'Please send proper data'}, safe=False)
    timezone = get_user_time_zone(warehouse)

    warehouse_id = warehouse.id

    send_lpns = request_data.get('send_lpns', False)

    load_lpns = request_data.get('load_lpns', False)

    #Search Params
    search_params, search_params1, search_params2, limit, offset = get_search_params_of_stock(request_data)

    #Available Stocks
    stock_detail = get_stock_objects(warehouse_id, search_params, search_params1, search_params2)

    #GET LPN Details and Next Suggested Location
    lpn_level_suggested_locations, lpn_wise_details, lpn_transac_type_dict = get_staging_suggested_location_details(
        warehouse, stock_detail, limit, offset
    )
    lpn_number_order_type_map = {}
    for record in lpn_level_suggested_locations:
        lpn_number = record.get('lpn_number', '')
        if lpn_number:
            lpn_number_order_type_map[lpn_number] = record.get('order_type', '')
    if send_lpns and send_lpns == 'true':
        return JsonResponse({'data': lpn_level_suggested_locations})

    total_count = stock_detail.count()

    page_info = scroll_data(request, stock_detail, limit=limit, request_type='GET')
    if not load_lpns:
        stock_detail = page_info['data']

    stock_df, decimal_limit = get_available_stocks(warehouse_id, stock_detail, load_lpns = load_lpns)

    all_stock_ids = []
    receipt_numbers = []
    for stock in stock_df:
        if stock.get('serial_based'):
            all_stock_ids.extend(stock['stock_ids'])
        receipt_number = stock.get('receipt_number', '')
        if receipt_number:
            receipt_numbers.append(receipt_number)

    stock_serial_dict = {}
    if all_stock_ids:
        serial_stock_dict = dict(SerialNumber.objects.filter(stock__in=all_stock_ids, status=1).values_list('serial_number', 'stock_id'))
        for serial_number, stock_id in serial_stock_dict.items():
            stock_serial_dict.setdefault(stock_id, []).append(serial_number)

    return_data = []
    po_map_dict, putaway_type_dict ={}, {}
    if request_data.get('combined_lpn_putaway') == 'true':
        putaway_type = POLocation.objects.filter(sku__user=warehouse.id, id__in=receipt_numbers, status=1).values_list('id','putaway_type')
        if putaway_type:
            putaway_type_dict = {item[0]: item[1] for item in putaway_type}
        po_map_dict = {'po_grn': 'po_putaway', 'sales_return': 'sales_return_putaway', 'job_order': 'jo_putaway', 'cancelled_picklist': 'cp_putaway'}
        
    for data in stock_df:
        stock_ids = data.get('stock_ids', [])
        serial_numbers = []
        for stock_id in stock_ids:
            serial_numbers.extend(stock_serial_dict.get(stock_id, []))
        data['serial_numbers'] = serial_numbers
        data['total_quantity'] = truncate_float(data.get('quantity'), decimal_limit)
        if data['total_quantity'] <= 0:
            continue
        data['expiry_date'] = format_date(timezone, data.get('expiry_date', None))
        data['mfg_date'] = format_date(timezone, data.get('mfg_date', None))
        batch_number, batch_reference = data.get('batch_number'), data.get('batch_reference')
        data['stock_status'] = status_choices.get(data.get('stock_status'))
        data['batch_display_key'] = batch_reference or batch_number
        data['batch_detail__batch_no'] = batch_number
        data['batch_detail__batch_reference'] = batch_reference
        data['batch_detail__expiry_date'] = data.get('expiry_date')
        data['batch_detail__manufactured_date'] = data.get('mfg_date')
        data['location__location'] = data.get('location_name')
        data['location__zone__zone'] = data.get('zone')
        data['check_digit'] = data.get('check_digit')
        data['sku__image_url'] = data.get('sku_image')
        data['sku__sku_code'] = data.get('sku_code')
        data['sku__measurement_type'] = data.get('sku_uom')
        data['sku__sku_desc'] = data.get('sku_desc')
        data['sku__sku_size'] = data.get('sku_size')
        data['suggested_location'] = lpn_wise_details.get(data.get('lpn_number', ''), '')
        data['order_type'] = lpn_number_order_type_map.get(data.get('lpn_number', ''), '')
        data['transaction_type'] = lpn_transac_type_dict.get(data.get('lpn_number', ''), '')
        data['putaway_type'] = po_map_dict.get(putaway_type_dict.get(data.get('receipt_number', ''), None), '') if putaway_type_dict else ''
        return_data.append(data)

    page_info['data'] = return_data
    page_info['message'] = "Success"
    page_info['status'] = 200
    page_info['page_info']['total_count'] = total_count
    return JsonResponse(page_info)

@get_warehouse
def get_sku_batches(request, warehouse:User):
    """
    Retrieves batch information for a specific SKU.
    
    Args:
        request: HTTP request object containing query parameters.
        warehouse: User object representing the warehouse.
        
    Returns:
        HttpResponse: JSON response containing batch details, weights, prices,
        carton information, and availability status for the requested SKU.
    """
    sku_batches = defaultdict(list)
    sku_weights = defaultdict(list)
    sku_prices = defaultdict(list)
    sku_cartons,location_list = [],[]
    sku_batch_details = {}
    sku_weight_details = {}
    timezone = get_user_time_zone(warehouse)
    sku_code = request.GET.get('sku_code')
    get_only_locations = request.GET.get('get_locations','')
    sku_location = request.GET.get('sku_location','')
    serial_flag, batch_based = 0, 0
    sku_id = SKUMaster.objects.filter(user=warehouse.id, sku_code=sku_code).only('id', 'enable_serial_based')
    if sku_id:
        serial_flag = sku_id[0].enable_serial_based
        batch_based = sku_id[0].batch_based
        if get_only_locations:
            sku_id = sku_id[0].id
            location_list = list(set(StockDetail.objects.filter(
                sku__user=warehouse.id,sku=sku_id, quantity__gt=0
            ).values_list('location__location',flat=True)))
            return HttpResponse(dumps({"locations": location_list}))
        else:
            sku_id = sku_id[0].id
            stock_detail_obj = StockDetail.objects.filter(sku=sku_id, quantity__gt=0)
            sku_cartons = list(stock_detail_obj.filter(carton__bin_number__isnull=False).\
                               values_list('carton__bin_number', flat=True).distinct())

            batch_values = ['batch_no', 'mrp', 'buy_price', 'manufactured_date', 'expiry_date',
                    'tax_percent', 'transact_type', 'transact_id', 'weight', 'stockdetail__status',
                    'batch_reference']
            batch_filters = {
                'stockdetail__sku': sku_id,
                'stockdetail__quantity__gt': 0,
                'stockdetail__sku__user': warehouse.id,
                'stockdetail__status__in': [0,1,3,4,5,6]
            }
            if sku_location:
                batch_filters.update({'stockdetail__location__location': sku_location})
            batch_obj = BatchDetail.objects.filter(**batch_filters)

            if batch_obj:
                batch_obj = batch_obj.values(*batch_values).annotate(stock_quantity = Sum('stockdetail__quantity'))

            for batch in batch_obj:
                sku_batches[batch['batch_no']].append(batch['mrp'])
                sku_batches[batch['batch_no']] = list(set(sku_batches[batch['batch_no']]))
                weight = batch['weight']
                buy_price = batch['buy_price']
                sku_weights[batch['batch_no']].append(weight)
                sku_weights[batch['batch_no']] = list(set(sku_weights[batch['batch_no']]))
                sku_prices[batch['batch_no']].append(buy_price)
                sku_prices[batch['batch_no']] = list(set(sku_prices[batch['batch_no']]))

                batch['manufactured_date'] = get_local_date_known_timezone(
                    timezone, batch['manufactured_date']
                ) if batch['manufactured_date'] else ""
                batch['expiry_date'] = get_local_date_known_timezone(
                    timezone, batch['expiry_date']
                ) if batch['expiry_date'] else ""
                batch['batch_display_key'] = batch.get('batch_reference') or batch.get('batch_no')
                sku_batch_details.setdefault("%s_%s" % (
                    batch['batch_no'], str(int(batch['mrp']))
                ), []).append(batch)
                sku_weight_details.setdefault("%s_%s" % (
                    batch['batch_no'], batch['weight']
                ), []).append(batch)

    #Sending Available Status for Non Batch Based SKUS
    available_status = {}
    if not sku_batch_details and sku_location:
        available_status = set(stock_detail_obj.filter(
            location__location = sku_location
        ).values_list('status', flat=True))

    return HttpResponse(dumps({
        "sku_batches": sku_batches, "sku_batch_details": sku_batch_details,
        'sku_weights': sku_weights, 'sku_weight_details': sku_weight_details,
        "sku_prices":sku_prices, 'supplier_details' : [],
        'sku_cartons': sku_cartons,'serial_flag': serial_flag,
        'batch_based': batch_based,
        'available_status': list(available_status)
        })
    )

class MoveInventorySet(WMSListView):
    """
    View class for handling Move Inventory operations.
    
    This class provides endpoints for retrieving move inventory records with various
    filtering options. It extends the WMSListView base class to leverage common
    warehouse management system functionality.
    """
    def get_queryset(self, args, kwargs, warehouse=None):
        return None

    def frame_date_filters(self, request_data, search_params):
        """
        Frame date filters based on the request data.
        Args:
            request_data (dict): The request data containing the date filter values.
        Returns:
            dict: The date filters dictionary.
        """
        try:
            # Parsing the date filters to convert them to the required format
            date_keys = {'from_date': 'creation_date__gte', 'to_date': 'creation_date__lte', 'updation_date': 'updation_date__gte', 'to_updation_date': 'updation_date__lte'}
            for key, filter_key in date_keys.items():
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    search_params[filter_key] = localized_date.astimezone(pytz.UTC)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('GET Move Inventory API Date Filters Failed for {} and params are {} and error statement is {}'.format(
                self.request.user.username, request_data, e))
        return search_params

    def get_filter_params_for_move_inventory(self, warehouse, request_data, limit):
        """
        Processes request data to build search parameters for move inventory queries.
        
        Args:
            warehouse: User object representing the warehouse.
            request_data: Dictionary containing filter parameters from the request.
            limit: Default pagination limit for query results.
            
        Returns:
            tuple: (search_params, limit) where search_params is a dictionary of 
            filter conditions and limit is the pagination limit.
        """
        search_params = {'sku__user__in': [warehouse.id]}

        if request_data.get('limit'):
            limit = request_data['limit']

        if request_data.get('batch_reference'):
            search_params['batch_detail__batch_reference'] = request_data['batch_reference']

        if request_data.get('batch_number'):
            search_params['batch_detail__batch_no'] = request_data['batch_number']

        if request_data.get('source_zone'):
            search_params['source_location__zone__zone'] = request_data['source_zone']

        if request_data.get('source_location'):
            search_params['source_location__location'] = request_data['source_location']

        if request_data.get('destination_zone'):
            search_params['dest_location__zone__zone'] = request_data['destination_zone']

        if request_data.get('destination_location'):
            search_params['dest_location__location'] = request_data['destination_location']

        if request_data.get('sku_code'):
            search_params['sku__sku_code'] = request_data['sku_code']

        skus = request_data.get('sku_list', [])
        if skus:
            search_params['sku__sku_code__in'] = skus

        search_params = self.frame_date_filters(request_data, search_params)

        return search_params, limit


    def get(self, *args, **kwargs):
        """
        Handles GET requests for move inventory data.
        
        Retrieves move inventory records based on filter parameters in the request.
        Applies pagination, formatting, and timezone adjustments to the results.
        
        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
            
        Returns:
            dict: Response containing paginated move inventory data.
        """
        self.set_user_credientials()
        warehouse = self.warehouse
        request = self.request
        self.timezone = get_user_time_zone(self.warehouse)
        data_list = []
        limit, total_count = 10, 0
        search_params  = {}
        request_data = request.GET
        if request_data:
            search_params, limit = self.get_filter_params_for_move_inventory(
                warehouse, request_data, limit
            )
        move_inventory_objs = MoveInventory.objects.filter(**search_params).order_by('-creation_date')
        total_count = move_inventory_objs.count()
        page_info = scroll_data(request, move_inventory_objs, limit=limit, request_type='GET')
        move_inventory_objs = page_info['data']
        timezone = get_user_time_zone(warehouse)

        if move_inventory_objs:
            move_inventory_objs = list(move_inventory_objs.values(
                'sku__sku_code', 'sku__sku_desc', 'sku__sku_size', 'sku__sku_category', 'sku__sub_category',
                'sku__sku_brand', 'source_location__location', 'source_location__zone__zone',
                'dest_location__location', 'dest_location__zone__zone', 'batch_detail__batch_no',
                'batch_detail__batch_reference', 'quantity', 'creation_date', 'reason', 'id',
                'json_data'
                )
            )
            for move_obj in move_inventory_objs:
                creation_date = get_local_date_known_timezone(timezone, move_obj.get('creation_date'))
                moved_serial_number = []
                updated_user_name, mv_inventory_by, remarks = '', '', ''

                if move_obj.get('json_data'):
                    if move_obj.get('json_data').get('request_user'):
                        updated_user_name = move_obj.get('json_data').get('request_user')
                    if move_obj.get('json_data').get('done_by'):
                        mv_inventory_by =  move_obj.get('json_data').get('done_by')
                    if move_obj.get('json_data').get('remarks'):
                        remarks = move_obj.get('json_data').get('remarks')

                #This code will use later for serial based move inventory
                # moved_serial_number = get_move_inventory_serial_number(warehouse, move_obj.get('id'))

                data_dict = OrderedDict((
                    ('id', move_obj.get('id')),
                    ('warehouse', warehouse.username),
                    ('sku_code', move_obj.get('sku__sku_code')),
                    ('sku_description', move_obj.get('sku__sku_desc')),
                    ('sku_size', move_obj.get('sku__sku_size')),
                    ('sku_category', move_obj.get('sku__sku_category')),
                    ('sku_sub_category', move_obj.get('sku__sub_category')),
                    ('sku_brand', move_obj.get('sku__sku_brand')),
                    ('source_zone', move_obj.get('source_location__zone__zone')),
                    ('source_location', move_obj.get('source_location__location')),
                    ('destination_zone', move_obj.get('dest_location__zone__zone')),
                    ('destination_location', move_obj.get('dest_location__location')),
                    ('batch_number', move_obj.get('batch_detail__batch_no')),
                    ('batch_reference', move_obj.get('batch_detail__batch_reference')),
                    ('quantity', move_obj.get('quantity')),
                    ('created_at', creation_date),
                    ('created_from', mv_inventory_by),
                    ('reason', move_obj.get('reason')),
                    ('remarks', remarks),
                    ('user', updated_user_name)
                ))

                if moved_serial_number:
                    data_dict.update({'Serial_numbers' : moved_serial_number})
                data_list.append(data_dict)

        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        page_info['error'] = [{'message': ''}]
        return page_info

    def post(self, *args, **kwargs):
        # This method is empty because it is a placeholder for handling POST requests.
        # The implementation can be added based on the specific requirements of the application.
        pass
    

def get_move_inventory(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse, status):
    """
    Retrieves move inventory data with pagination and filtering.
    
    Args:
        start_index: Starting index for pagination.
        stop_index: Ending index for pagination.
        temp_data: Dictionary to store the result data.
        search_term: Search term for filtering records.
        order_term: Column to order results by.
        col_num: Column number for ordering.
        request: HTTP request object containing filter parameters.
        warehouse: User object representing the warehouse.
        status: Status filter for move inventory records.
        
    Returns:
        Updates the temp_data dictionary
    """
    search_term, sort_by_column, modified_column_filters, batch_filter = get_filters_for_move_inventory(request)
    extra_params = request.GET.get('extraParams', {})
    if extra_params:
        try:
            extra_params = json.loads(extra_params)
        except json.JSONDecodeError:
            extra_params = {}
    view = ''
    if extra_params:
        view = extra_params.get('view', '')
    misc_types =["move_inventory_approval"]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    move_inventory_approval = misc_dict.get("move_inventory_approval", 'false')
    pending_approval_data = {}
    if move_inventory_approval.lower() == 'true' and view == 'approval-pending':
        approval_mixin = MoveInventoryApprovalMixin()
        pending_approval_data = approval_mixin.fetch_pending_approval_transactions(warehouse, request)
    move_inventory_data = get_move_inventory_data(warehouse, search_term, sort_by_column, modified_column_filters, batch_filter, pending_approval_data, view)

    temp_data['recordsTotal'] = len(move_inventory_data)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    final_list = []
    move_inventory_data = move_inventory_data[start_index:stop_index]
    picked_details = get_picked_user(move_inventory_data, warehouse)
    final_list = prepare_get_data_for_move_inventory(move_inventory_data, warehouse, pick_data=picked_details,approval_data=pending_approval_data)
    temp_data['aaData'] = final_list

def get_filters_for_move_inventory(request):
    """
    Processes request data to extract filter parameters for move inventory queries.
    
    Args:
        request: HTTP request object containing filter parameters.
        
    Returns:
        tuple: (search_term, sort_by_column, modified_column_filters, batch_filter) 
    """
    filtered_dict = {
        'reference_number':'id', 'sku_code': 'sku__sku_code',
        'sku_desc': 'sku__sku_desc', 'batch_display_key': 'batch_detail__batch_no',
        'source_loc': 'source_location__location', 'carton_id': 'json_data__carton_id',
        'dest_loc': 'dest_location__location', 'picked_quantity': 'json_data__picked_quantity',
        'dropped_quantity': 'json_data__dropped_quantity',
        'move_quantity': 'quantity', 'status': 'status'
    }
    status_dict = {
        'Open' : 1, 'Picked': 2, 'Dropped' : 3, 'Approval Pending' : 4
    }
    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'creation_date', '1'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = filtered_dict.get(request.GET.get('sort_by_column'))

    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_header_filter(loads(column_headers))

    modified_column_filters = {}
    batch_key = {}
    for key, value in column_filters.items():
        if key == "batch_display_key":
            batch_key.update({
                "batch_detail__batch_no__icontains" : value,
                "batch_detail__batch_reference__icontains" : value
            })
        elif key != 'status':
            modified_column_filters.update({filtered_dict.get(key)+"__icontains" : value})
        elif status_dict.get(value,''):
            modified_column_filters.update({filtered_dict.get(key): status_dict[value]})

    batch_filter = Q()
    if batch_key:
        batch_filter = Q(**batch_key, _connector=Q.OR)

    sort_type = request.GET.get('sort_type')
    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    return search_term, sort_by_column, modified_column_filters, batch_filter

def get_move_inventory_data(warehouse, search_term, sort_by_column, modified_column_filters, batch_filter, pending_transaction_numbers = None, view= ''):
    """
    Retrieves move inventory data based on search criteria and filters.
    Applies search terms, column filters, and sorting to the query.
    Returns filtered queryset of move inventory records.
    """
    #Main Query
    # Build the status filter condition

    if pending_transaction_numbers or view == 'approval-pending':
        # If we have pending transactions or approval-pending view, include status 4 records
        status_condition = Q(
            status=4, 
            reference__in=pending_transaction_numbers.keys()
        )
    else:
        # Otherwise, only include status 1, 2, 3
        status_condition = Q(status__in=[1, 2, 3])  
      
    # Main Query with combined status condition
    move_inventory_data = MoveInventory.objects.filter(
        status_condition,
        sku__user=warehouse.id, 
        quantity__gt=0,
    )
    values_list = move_inventory_values_list()
    if search_term:
        move_inventory_data = move_inventory_data.filter(
            Q(sku__sku_code__icontains=search_term) | Q(sku__sku_desc__icontains=search_term) |
            Q(source_location__location__icontains=search_term) | Q(dest_location__location__icontains=search_term) |
            Q(batch_detail__batch_no__icontains=search_term) | Q(batch_detail__batch_reference__icontains=search_term) |
            Q(json_data__picked_quantity__icontains=search_term) | Q(json_data__dropped_quantity__icontains=search_term) |
            Q(quantity__icontains=search_term)
        ).filter(batch_filter) \
        .values(*values_list).order_by(sort_by_column)

    elif sort_by_column:
        move_inventory_data = move_inventory_data.filter(**modified_column_filters).filter(batch_filter).values(*values_list).order_by(sort_by_column)

    else:
        move_inventory_data = move_inventory_data.filter(batch_filter).values(*values_list).order_by('-id')

    return move_inventory_data

def move_inventory_values_list():
    """
    Returns a list of field names to retrieve from MoveInventory objects.
    Includes fields from related models via foreign key relationships.
    Used to standardize the data structure across move inventory queries.
    """
    return ['sku__sku_code', 'sku__sku_desc', 'sku__enable_serial_based', 'source_location__location', 'dest_location__location', 'quantity','reference',
            'reason', 'id', 'status', 'creation_date', 'json_data', 'batch_detail__batch_no', 'batch_detail__batch_reference',
            'updation_date','source_location__zone__zone','dest_location__zone__zone', 'id', 'source_location_id','dest_location_id',
            'batch_detail_id', 'batch_detail__manufactured_date', 'batch_detail__expiry_date', 'sku__scan_picking', 'sku_id', 'source_location__check_digit', 'dest_location__check_digit']

def get_picked_user(move_inventory_data, warehouse):
    """
    Retrieves user information for picked move inventory tasks.
    Maps task reference IDs to employee usernames and update timestamps.
    Returns dictionary mapping move inventory IDs to task details.
    """
    task_data_dict = {}
    move_inv_ids = [str(data.get('id')) for data in move_inventory_data]
    tasks_data = TaskMaster.objects.filter(task_ref_id__in = move_inv_ids, warehouse = warehouse.id).values('task_ref_id', 'employee__user__username','updation_date')
    task_data_dict = {task['task_ref_id']: task for task in tasks_data}
    return task_data_dict

def prepare_get_data_for_move_inventory(move_inventory_data, warehouse, pick_data = {}, approval_data = None):
    """
    Formats move inventory data for API responses.
    Converts database values to user-friendly formats with proper timezone handling.
    Adds picking details and formats dates for display.
    """
    if approval_data is None:
        approval_data = {}
    final_list = []
    time_zone = get_user_time_zone(warehouse)

    status_dict = {
        1: 'Open', 2: 'Picked', 3: 'Dropped', 4: 'Approval Pending'
    }
    reference_ids = []
    for data in move_inventory_data:
        reference_ids.append(str(data.get('id')))
    serial_df = get_existing_serials(warehouse, 'task_based_move_inventory', reference_ids, open_status=True)

    for data in move_inventory_data:
        updation_date = ''
        if data.get('status') != 2:
            updation_date = get_local_date_known_timezone(time_zone, data.get('updation_date'))
        batch_no = data.get('batch_detail__batch_no')
        batch_reference = data.get('batch_detail__batch_reference')
        status = status_dict.get(data.get('status'))
        mov_inv_id = data.get('id')
        serial_numbers = fetch_serial_numbers_for_sps_id(mov_inv_id, serial_df)

        final_dict = {
            'reference_number': data.get('id'),
            'sku_code': data.get('sku__sku_code'),
            'sku_desc': data.get('sku__sku_desc'),
            'serial_based': data.get('sku__enable_serial_based'),
            'batch_display_key': batch_reference or batch_no,
            'source_batch': batch_no,
            'source_loc': data.get('source_location__location'),
            'source_zone': data.get('source_location__zone__zone'),
            'dest_loc': data.get('dest_location__location'),
            'destination_zone': data.get('dest_location__zone__zone'),
            'move_quantity': data.get('quantity'),
            'quantity': data.get('quantity'),
            'original_move_quantity': data.get('quantity'),
            'reason': data.get('reason'),
            'status': status,
            'creation_date': get_local_date_known_timezone(time_zone, data.get('creation_date')),
            'updation_date': updation_date,
            'picked_quantity': data.get('json_data').get('picked_quantity',''),
            'drop_quantity': data.get('json_data').get('dropped_quantity',''),
            'move_inventory_id' : data.get('id'),
            'is_scanable_sku': data.get('sku__scan_picking'),
            'manufactured_date': get_local_date_known_timezone(time_zone, data.get('batch_detail__manufactured_date'), send_date=True).strftime('%Y-%m-%d') if data.get('batch_detail__manufactured_date') else '',
            'expiry_date': get_local_date_known_timezone(time_zone, data.get('batch_detail__expiry_date'), send_date=True).strftime('%Y-%m-%d') if data.get('batch_detail__expiry_date') else '',
            'picked_at': "",
            'picked_by': "",
            #'lpn_number' : data.get('lpn',{}).get('package_reference',""), Check this later
            'lpn_number': data.get('json_data').get('lpn_number',""),
            'transaction_reference' : data.get('reference'),
            'batch_detail_id' : data.get('batch_detail_id'),
            'source_check_digit': data.get('source_location__check_digit'),
            'dest_check_digit': data.get('dest_location__check_digit'),
            'serial_numbers': serial_numbers,
            'approval_id': approval_data.get(data.get('reference'),'')
        }
        if pick_data:
            task_data = pick_data.get(str(data.get('id')))
            if task_data and status == 'Picked':
                final_dict['picked_quantity'] = data.get('quantity')
                final_dict['picked_at'] = get_local_date_known_timezone(time_zone, task_data['updation_date'])
                final_dict['picked_by'] = task_data['employee__user__username']
        final_list.append(final_dict)
    return final_list

@get_warehouse
def get_move_inventory_reasons(request, warehouse: User):
    log.info("get_move_inventory_reasons %s" % request)
    move_inventory_reasons = get_misc_value('move_inventory_reasons', warehouse.id).split(',')
    reasons_available = 1

    if not move_inventory_reasons or move_inventory_reasons[0] == '':
        reasons_available = 0

    return JsonResponse({
        'move_inventory_reasons': move_inventory_reasons,
        'reasons_available': reasons_available,
    })

def create_tasks(request, warehouse, tasks_data, task_status=1, employee_id=None, extra_params=None):
    sn_item_dict = {}
    move_inventory_approval= extra_params.get('move_inventory_approval', 'false') if extra_params else 'false'
    if move_inventory_approval == 'true':
        task_status = 4
    for data in tasks_data:
        unique_reference = get_unique_reference()
        data.update(
            {'remarks': unique_reference}
        )
        if data.get('serial_numbers') and data.get('serial_numbers') != ['']:
            prepare_serial_numbers(data, sn_item_dict, stock=None, open_status = True)

    move_inv_objs = create_move_inventory(
        request, warehouse, tasks_data, task_status=task_status,
        sn_item_dict=sn_item_dict, extra_params=extra_params
    )
    if move_inv_objs:
        create_tasks_for_move_inventory(move_inv_objs, warehouse, employee_id)

def update_tasks(request, return_dict, move_inv_id, warehouse, pick_flag, lpn_based_move_inv='false'):
    return_data_dict = return_dict[0]
    message = "Success"
    employee_id = EmployeeMaster.objects.filter(user = request.user.id).values_list('id', flat=True)
    move_inventory_objs = MoveInventory.objects.filter(id= move_inv_id, sku__user=warehouse.id)
    actual_move_quantity = move_inventory_objs.aggregate(Sum('quantity'))['quantity__sum'] or 0
    json_data = move_inventory_objs.values_list('json_data', flat=True)

    #Prepare Serial Numbers incase of Partial Movement
    actual_serial_numbers = json_data[0].get('serial_numbers', []) if json_data else []
    completed_serial_numbers = return_data_dict.get('serial_numbers', [])
    remaining_serial_numbers = []
    if actual_serial_numbers and completed_serial_numbers:
        actual_serial_numbers = set(actual_serial_numbers)
        completed_serial_numbers = set(completed_serial_numbers)
        remaining_serial_numbers = list(actual_serial_numbers - completed_serial_numbers)

    if employee_id and pick_flag:
        task_master_obj = TaskMaster.objects.filter(task_ref_id = str(move_inv_id), task_ref_type = 'move_inventory_task')
        task_master_obj.update(employee_id = employee_id[0], updation_date = datetime.datetime.now())
        if json_data:
            json_data = json_data[0]
            json_data.update({
                'picked_user': request.user.username,
                'short_pick_reason': return_data_dict.get('short_pick_reason',''),
            })
        quantity = return_data_dict.get('quantity')
        if lpn_based_move_inv == 'true':
            lpn_details = return_data_dict.pop('lpn',[])
            json_data['lpn'] = lpn_details[0]
            quantity = lpn_details[0]['packed_quantity']
            tasks_data = []
            for idx in range(1, len(lpn_details)):
                partial_pick_record = return_data_dict.copy()
                picked_quantity = lpn_details[idx]['packed_quantity']
                partial_pick_record['quantity'] = picked_quantity
                partial_pick_record['json_data'] = copy.deepcopy(json_data)
                partial_pick_record['json_data']['lpn'] = lpn_details[idx]
                tasks_data.append(partial_pick_record)

            if tasks_data:
                create_tasks(request, warehouse, tasks_data, task_status=2, employee_id=employee_id[0])
        move_inventory_objs.update(status=2, quantity = quantity, json_data = json_data)
        #Update Serial Number status to 0 with reference as move inventory and move_inv_id
        update_serial_numbers(
            warehouse, return_data_dict, remaining_serial_numbers,
            move_inv_id, reference_type='task_based_move_inventory'
        )
        message =  "Updated"
    if return_data_dict.get('quantity') < actual_move_quantity:
        short_pick_record_creation = return_data_dict.copy()
        short_quantity = actual_move_quantity - return_data_dict.get('quantity')
        short_pick_record_creation.update({
            'quantity': short_quantity,
            'serial_numbers': remaining_serial_numbers
        })
        tasks_data = prepare_tasks_data([short_pick_record_creation], lpn_based_move_inv)
        create_tasks(request, warehouse, tasks_data)
        if message != "Updated":
            move_inventory_objs.update(quantity = return_data_dict.get('quantity'))
    serial_numbers = return_data_dict.get('serial_numbers', [])

    if not pick_flag and serial_numbers:
        #Update Serial Number status to 0 with reference as move inventory and move_inv_id
        update_serial_numbers(
            warehouse, return_data_dict, serial_numbers,
            move_inv_id, reference_type='task_based_move_inventory', location_update=True
        )
    return message

def create_tasks_for_move_inventory(move_inv_objs, warehouse, employee_id=None):
    """
    Creates task records for move inventory operations.
    
    Args:
        move_inv_objs: List of MoveInventory objects to create tasks for.
        warehouse: User object representing the warehouse.
        employee_id: Optional ID of employee assigned to the tasks.
        
    Creates TaskMaster records linking to move inventory operations for tracking.
    """
    tasks_data_list = []
    for move_inv in move_inv_objs:
        tasks_data_list.append(TaskMaster(warehouse_id = warehouse.id, task_ref_type = 'move_inventory_task', task_ref_id = move_inv.id
                                            ,reference_number= move_inv.reference, employee_id=employee_id))
    TaskMaster.objects.bulk_create(tasks_data_list)

@get_warehouse
def aggregated_move_inventory_count(request, warehouse: User):
    """
    Aggregates counts of pending move inventory tasks by type.
    
    Args:
        request: HTTP request object with optional zone filter.
        warehouse: User object representing the warehouse.
        
    Returns:
        JsonResponse with counts of pending pick and drop tasks.
    """
    final_response = {}
    group_type_str = request.GET.get('zone', "")
    group_type = group_type_str.split(",") if group_type_str else []
    zone_filter = {'source_location__zone__zone__in': group_type} if group_type else {}
    # Fetch all tasks
    tasks = TaskMaster.objects.filter(warehouse_id=warehouse.id, task_ref_type='move_inventory_task', status=False)
    employee_id = EmployeeMaster.objects.filter(user = request.user.id).values_list('id', flat=True)
    # Extract pending pick and drop task IDs
    pending_drop_ids = list(tasks.filter(employee_id=employee_id[0]).values_list('task_ref_id', flat=True))
    pending_drop_ids = set(map(int, pending_drop_ids))  # Convert to set for faster membership check
    pending_pick_ids = list(tasks.exclude(task_ref_id__in=pending_drop_ids).filter(employee__isnull = True).values_list('task_ref_id', flat=True))
    pending_pick_ids = list(map(int, pending_pick_ids))

    # Filter MoveInventory tasks based on warehouse, status, and zone
    move_inventory_tasks = MoveInventory.objects.filter(status__in=[1, 2], sku__user=warehouse.id, quantity__gt = 0,**zone_filter)

    # Aggregate data for pending pick and drop tasks
    pick_data = move_inventory_tasks.filter(id__in=pending_pick_ids).aggregate(item=Sum('quantity'), SKU=Count('sku_id', distinct=True), count=Count('id', distinct=True))
    drop_data = move_inventory_tasks.filter(id__in=pending_drop_ids).aggregate(item=Sum('quantity'), SKU=Count('sku_id', distinct=True), count=Count('id', distinct=True))

    # Construct the final response
    final_response = {
        "move_inventory": {
            "open": {
                "Pick": pick_data['count'] if pick_data['count'] else 0,
                "Drop": drop_data['count'] if drop_data['count'] else 0
            },
            "pick": {
                "open": {
                    "item": pick_data['item'] if pick_data['item'] else 0,
                    "SKU": pick_data['SKU'] if pick_data['SKU'] else 0
                }
            },
            "drop": {
                "open": {
                    "item": drop_data['item'] if drop_data['item'] else 0,
                    "SKU": drop_data['SKU'] if drop_data['SKU'] else 0
                }
            }
        }
    }
    return JsonResponse({'message': final_response}, status=200)


@get_warehouse
def get_move_inventory_task_data(request, warehouse: User):
    """
    Retrieves move inventory task data for mobile applications.
    
    Args:
        request: HTTP request with type (pick/drop), zone, and LPN filters.
        warehouse: User object representing the warehouse.
        
    Returns:
        JsonResponse with formatted task data for the requested type.
    """
    task_count = get_misc_value('move_inventory_task_count', warehouse.id)
    group_type_str = request.GET.get('zone', "")
    move_inventory_type = request.GET.get('type', "")
    lpn = request.GET.get('lpn',"")
    group_type = group_type_str.split(",") if group_type_str else []
    filters = {}
    if move_inventory_type == "pick":
        # Fetch all tasks
        move_inv_ids = list(TaskMaster.objects.filter(
            warehouse_id=warehouse.id, task_ref_type='move_inventory_task', employee= None, status=False
            ).values_list('task_ref_id', flat=True))

        move_inv_ids = list(map(int, move_inv_ids))

        filters = {'source_location__zone__zone__in': group_type} if group_type else {}
        order_by = "source_location__pick_sequence"

    elif move_inventory_type == "drop":
        employee_id = EmployeeMaster.objects.filter(user = request.user.id).values_list('id', flat=True)
        move_inv_ids = list(TaskMaster.objects.filter(
            warehouse_id=warehouse.id, task_ref_type='move_inventory_task', employee_id = employee_id[0], status=False
            ).values_list('task_ref_id', flat=True))
        move_inv_ids = list(map(int, move_inv_ids))
        order_by = "-id"
        filters = {'json_data__lpn_number': lpn} if lpn else {}
    values_list = move_inventory_values_list()
    move_inventory_data = MoveInventory.objects.filter(
        id__in = move_inv_ids, status__in = [1,2], sku__user=warehouse.id, quantity__gt = 0, **filters
        ).values(*values_list).order_by(order_by)

    if move_inventory_type == "pick" and isinstance(task_count, int):
        move_inventory_data = move_inventory_data[:task_count]
    if move_inventory_type == 'drop':
        for move_inv in move_inventory_data:
            json_data = move_inv['json_data'] or {}
            move_inv['lpn'] = json_data.get('lpn', {})
    data_list = prepare_get_data_for_move_inventory(move_inventory_data, warehouse)
    return JsonResponse({'data': data_list}, status = 200)

@get_warehouse
def delete_move_inventory_tasks(request, warehouse: User):
    """
    Deletes selected move inventory tasks.
    
    Args:
        request: HTTP request with move_inv_ids in the request body.
        warehouse: User object representing the warehouse.
        
    Returns:
        JsonResponse with success or error message.
    """
    request_data = loads(request.body)
    move_inventory_ids = request_data.get('move_inv_ids', [])
    if not move_inventory_ids:
        return JsonResponse({'message': 'Please Select Tasks for Deletion'}, status =  400)

    # Fetch all relevant MoveInventory objects in one queryn
    MoveInventory.objects.filter(id__in=move_inventory_ids, sku__user=warehouse.id
                                 ).update(status = 0, json_data = {'deleted': True})

    return JsonResponse({'message': 'Move Inventory tasks deleted successfully'}, status = 200)


def prepare_tasks_data(tasks_data, lpn_based_move_inv='false'):
    """
    Prepares task data for creation with proper references.
    
    Args:
        tasks_data: List of task records to prepare.
        lpn_based_move_inv: Flag for LPN-based movement.
        
    Returns:
        List of task records with unique references and batch details.
    """
    tasks_list = []
    if lpn_based_move_inv == 'true':
        reference = get_unique_reference()
    for record in tasks_data:
        if lpn_based_move_inv != 'true':
            reference = get_unique_reference()
        if record.get('source_stock'):
            source_stocks = dict(record.get('source_stock').values_list('id','batch_detail_id'))
            if source_stocks and list(source_stocks.values()):
                record['batch_detail_id'] = list(source_stocks.values())[0]
        record['reference'] = reference
        tasks_list.append(record)
    return tasks_list

def get_unique_reference():
    """
    Generates a unique reference ID for move inventory operations.
    
    Returns:
        str: Hexadecimal string representation of a UUID4.
    """
    return uuid.uuid4().hex

def get_segregation_type(loc_dict, source_location, dest_location):
    """
    Retrieves segregation and storage type information for locations.
    
    Args:
        loc_dict: Dictionary mapping location names to their properties.
        source_location: Source location name.
        dest_location: Destination location name.
        
    Returns:
        tuple: Contains source and destination segregation types, storage types,
               and carton management flags.
    """
    # Returns the storage_type for a given location or an empty string if not found
    return (
        loc_dict.get(source_location, {}).get('segregation', ''),
        loc_dict.get(source_location, {}).get('storage_type', ''),
        loc_dict.get(source_location, {}).get('carton_managed', ''),
        loc_dict.get(dest_location, {}).get('segregation', ''),
        loc_dict.get(dest_location, {}).get('storage_type', ''),
        loc_dict.get(dest_location, {}).get('carton_managed', '')
    )

def check_stock_movement(source_segregation, destination_segregation, source_storage_type, dest_storage_type):
    """
    Validates stock movement between different segregation and storage types.
    
    Args:
        source_segregation: Segregation type of source location.
        destination_segregation: Segregation type of destination location.
        source_storage_type: Storage type of source location.
        dest_storage_type: Storage type of destination location.
        
    Returns:
        str: Error message if movement is invalid, empty string if valid.
    """
    conditions = {
        'inbound_staging': {
            'message': 'Unable to move stock from the Staging Area due to pending putaway process. Please complete the putaway first',
            'required_destination': 'inbound_staging'
        },
        'outbound_staging': {
            'message': 'Unable to move stock from the Staging Area due to pending Invoice process. Please complete the invoice first',
            'required_destination': 'outbound_staging'
        },
        'nte_staging_area': {
            'message': 'Unable to move stock from the Staging Area due to pending NTE process. Please complete the NTE putaway',
            'required_destination': 'nte_staging_area'
        },
        'replenishment_staging_area': {
            'message': 'Unable to move stock from the Staging Area due to pending Replenishment process. Please complete the Replenishment first',
            'required_destination': 'replenishment_staging_area'
        }
    }

    # Check if source or destination has pending processes
    if source_segregation in conditions:
        condition = conditions[source_segregation]
        if destination_segregation != condition['required_destination']:
            return condition['message']

    if destination_segregation in conditions:
        condition = conditions[destination_segregation]
        if source_segregation != condition['required_destination']:
            return condition['message']

    if source_storage_type in conditions:
        condition = conditions[source_storage_type]
        if dest_storage_type != condition['required_destination']:
            return condition['message']

    if dest_storage_type in conditions:
        condition = conditions[dest_storage_type]
        if dest_storage_type != condition['required_destination']:
            return condition['message']
    return ''

def update_lpn_status_for_staging_stock(request, warehouse, lpn_numbers, source_storage_type, dest_storage_type):
    """
    Updates LPN status when moving stock between staging areas.
    
    Args:
        request: HTTP request object.
        warehouse: User object representing the warehouse.
        lpn_numbers: List of LPN numbers to update.
        source_storage_type: Storage type of source location.
        dest_storage_type: Storage type of destination location.
        
    Updates LPN blocked status based on movement.
    """
    # Determine LPN status based on source and destination storage types
    is_blocked = False
    rec_storage_type = 'REC'
    if source_storage_type == rec_storage_type and dest_storage_type != rec_storage_type:
        is_blocked = True

    elif source_storage_type != rec_storage_type and dest_storage_type == rec_storage_type:
        is_blocked = False
    else:
        return  # No status change required

    request_dict = {
        "request_headers": request.headers,
        "request_meta": request.META,
    }

    for lpn_number in lpn_numbers:
        params = {
            "warehouse": warehouse.username,
            "lpn_number": lpn_number,
            "status": True,
            "blocked": is_blocked,
            "dropped": False
        }

        packing_service_instance = PackingService(request_dict, request.user, warehouse)
        _, packing_service_errors = packing_service_instance.update_lpn(params)

        if packing_service_errors:
            log.info("MoveInventory Update LPN for lpn_number - %s failed with %s" % (str(lpn_number), str(packing_service_errors)))

@get_warehouse
def location_movement(request, warehouse:User):
    """
    Handles location-to-location movement of inventory.
    
    Args:
        request: HTTP request with source/destination locations and LPN numbers.
        warehouse: User object representing the warehouse.
        
    Returns:
        JsonResponse with success or error message.
        
    Validates movement between locations, checks for restrictions based on
    storage types and segregation, and processes the movement by creating
    move inventory records. Also handles LPN status updates for staging areas.
    """
    request_data = loads(request.body)
    source_location = request_data.get('source_location')
    destination_location = request_data.get('destination_location')
    reason = request_data.get('reason')
    remarks = request_data.get('remarks')
    lpn_numbers = request_data.get('lpn_numbers', [])
    transaction_type = request_data.get('transaction_type', '')

    task_based_move_inventory = get_misc_value('task_based_move_inventory', warehouse.id)

    loc_master_obj = list(LocationMaster.objects.exclude(
        zone__storage_type__in = ['replenishment_staging_area', 'nte_staging_area']
    ).filter(
        location__in = [source_location, destination_location], zone__user = warehouse.id
        ).values(
                'location', 'id', 'zone__segregation', 'zone__storage_type', 'zone__carton_managed'
            )
        )

    loc_master_dict = {}
    for loc in loc_master_obj:
        loc_master_dict[
            loc.get('location')
        ] = {'id': loc.get('id'), 'segregation': loc.get('zone__segregation'), 'storage_type': loc.get('zone__storage_type'), 'carton_managed': loc.get('zone__carton_managed')}

    #Source and Destination Mandatory Check
    if not (source_location and destination_location):
        return JsonResponse({'message': 'Please provide source and destination locations'}, status=400)

    if source_location not in loc_master_dict or destination_location not in loc_master_dict:
        return JsonResponse({'message': 'Invalid Source / Destination Location'}, status = 400)

    #Restict WIP Movement
    # Get the storage types for both locations
    source_segregation, source_storage_type, is_source_lpn_managed, destination_segregation, dest_storage_type, is_dest_lpn_managed = get_segregation_type(
        loc_master_dict, source_location, destination_location
    )

    # Check if 'wip_area' is in either storage type
    if 'wip_area' in source_storage_type:
        return JsonResponse({'message': 'WIP Movement is Restricted'}, status = 400)

    # Check if destination zone is carton managed and source stock is missing LPN number
    if (is_dest_lpn_managed and not is_source_lpn_managed) and ((source_segregation or destination_segregation) not in ['inbound_staging', 'outbound_staging']):
        return JsonResponse({'message': 'Source Should be LPN Managed When Destination is LPN Managed'}, status = 400)
    
    #If Task Based Movement is mandatory and source/destination is not staging then return error
    if (task_based_move_inventory and task_based_move_inventory == 'mandatory') and (source_segregation or destination_segregation) not in ['inbound_staging', 'outbound_staging']:
        return JsonResponse({'message': 'Task Based Movement is not allowed'}, status = 400)

    #Validate Staging Lanes Stock Movement
    error_message = check_stock_movement(source_segregation, destination_segregation, source_storage_type, dest_storage_type)
    if error_message:
        return JsonResponse({'message': error_message}, status = 400)

    warehouse_id = warehouse.id

    #Search Params
    search_params, search_params1, search_params2, _, _ = get_search_params_of_stock(request_data)

    if lpn_numbers:
        search_params['lpn_number__in'] = lpn_numbers

    #Available Stocks
    stock_detail = get_stock_objects(warehouse_id, search_params, search_params1, search_params2)

    if not stock_detail:
        return JsonResponse({'message': 'No Stock Found'}, status=400)

    #Get Available Stocks(Available - Reserved)
    stock_df, decimal_limit = get_available_stocks(warehouse_id, stock_detail)

    #Check any Reserved quantity in stock_df dict
    check_reserved_quantity = any(stock.get('reserved_qty', 0) > 0 for stock in stock_df)
    if check_reserved_quantity:
        return JsonResponse({'message': 'Stock is Reserved in Given LPN/Locations'}, status=400)

    all_stock_ids = []
    for stock in stock_df:
        if stock.get('serial_based'):
            all_stock_ids.extend(stock['stock_ids'])

    stock_serial_dict = {}
    if all_stock_ids:
        serial_stock_dict = dict(SerialNumber.objects.filter(stock__in=all_stock_ids, status=1).values_list('serial_number', 'stock_id'))
        for serial_number, stock_id in serial_stock_dict.items():
            stock_serial_dict.setdefault(stock_id, []).append(serial_number)

    move_inventory_list = []
    grn_numbers = []
    for stock in stock_df:
        stock_ids = stock.get('stock_ids')
        stock_objs = StockDetail.objects.filter(id__in = stock_ids)
        for stock_obj in stock_objs:
            grn_numbers.append(stock_obj.grn_number)

        lpn_number = stock.get('lpn_number')
        storage_type = loc_master_dict.get(source_location).get('storage_type')
        move_inventory_list.append({
            'is_dest_lpn_managed': is_dest_lpn_managed,
            'is_source_lpn_managed': is_source_lpn_managed,
            'source_segregation': source_segregation,
            'destination_segregation': destination_segregation,
            'reason': reason,
            'remarks': remarks,
            'sku_id': stock.get('sku_detail_id'),
            'source_loc_id': stock.get('location_id'),
            'dest_loc_id': loc_master_dict.get(destination_location).get('id'),
            'batch_no': stock.get('batch_number'),
            'quantity': round(stock.get('quantity'), int(decimal_limit)),
            'status': stock.get('stock_status'),
            'dest_zone': stock.get('zone'),
            'source_zone': stock.get('zone'),
            'source_stock': stock_objs,
            'lpn_number': lpn_number,
            'stock_serial_dict': stock_serial_dict,
            'source_storage_type': source_storage_type,
            'dest_storage_type' : dest_storage_type,
            'json_data': {
                'done_by': 'APP',
                'request_user': request.user.username,
                'carton_id': lpn_number,
                'dest_carton': lpn_number
            },
        })

    if move_inventory_list:
        response = update_stocks(request, warehouse, move_inventory_list)
        if response.get('status') == 200:
            # update staginginfo and create new
            if source_segregation == 'outbound_staging' and destination_segregation == 'outbound_staging':
                update_staging_info(warehouse, grn_numbers, storage_type, destination_location, loc_master_dict)

        if response.get('status') == 400:
            return JsonResponse({'message': response}, status = 400)
        elif transaction_type in ['PO', 'SR']:
            update_lpn_status_for_staging_stock(request, warehouse, lpn_numbers, source_storage_type, dest_storage_type)

    return JsonResponse({'message': 'Moved Successfully'}, status = 200)


def update_staging_info(warehouse, order_references, storage_type, destination_location, loc_master_dict):
    """
    Updates StagingInfo records for the old entries and creates new staging info records.
    
    Args:
        warehouse: User object representing the warehouse.
        order_references: List of order references to update.
        storage_type: Storage type of the destination location.
        destination_location: Name of the destination location.
        loc_master_dict: Dictionary mapping location names to their properties.
    """
    remove_fields = ['id', '_state', 'creation_date', 'updation_date']
    pre_inv_stage_records = []
    sorting_stage_records = StagingInfo.objects.filter(user=warehouse, segregation='outbound_staging', location__zone__storage_type=storage_type, order_reference__in=order_references, status=0)
    for sorting_stage_record in sorting_stage_records:
        # Pre invoice staging record creation
        pre_inv_stage_dict = sorting_stage_record.__dict__.copy()
        for field in remove_fields:
            pre_inv_stage_dict.pop(field, None)
        pre_inv_stage_dict['location_id'] = loc_master_dict.get(destination_location).get('id')
        pre_inv_stage_records.append(StagingInfo(**pre_inv_stage_dict))
        # Sorting staging record updation
        sorting_stage_record.status = 1
        sorting_stage_record.updation_date = timezone.now()
    if pre_inv_stage_records:
        StagingInfo.objects.bulk_create(pre_inv_stage_records)
    if sorting_stage_records:
        StagingInfo.objects.bulk_update(sorting_stage_records, ['status', 'updation_date'])


def get_move_inventory_incremental_reference(warehouse):
    """
    Generates an incremental reference number for move inventory operations.
    
    Args:
        warehouse: User object representing the warehouse.
        
    Returns:
        str: Formatted reference number with prefix 'MOV' and incremental suffix.
        
    Creates a new prefix record if one doesn't exist for the warehouse.
    """
    reference = "move_inventory"
    _, _, mv_inv_reference, _, inc_status = get_user_prefix_incremental(warehouse, reference, '',  job_order_ref = True)
    if inc_status:
        prefix_dict = {
            'user_id': warehouse.id, 'product_category': '','sku_category': '', 'type_name': reference,
            'prefix': 'MOV', 'account_id': warehouse.userprofile.id
        }
        UserPrefixes.objects.create(**prefix_dict)
        _, _, mv_inv_reference, _, inc_status = get_user_prefix_incremental(warehouse, reference, '', job_order_ref = True)
    return mv_inv_reference
