#django imports
from django.http import HttpResponse
from django.db.models import Max, Min
from collections import defaultdict
from itertools import chain
import re
import ast

#core operation functions
from core_operations.views.common.main import (
    convert_sku, get_multiple_misc_values
)
from core_operations.views.services.packing_service import LpnManager

#wms utils
from wms_base.wms_utils import (
    rename_dict_keys, init_logger, reverse_stock_choice_mapping,
    ADJUST_INVENTORY_EXCEL_MAPPING , CYCLE_COUNT_CREATION_HEADERS,
    BATCH_CREATION_EXCEL_MAPPING, CONFIRM_CYCLE_COUNT_HEADERS,
    CONFIRM_CYCLE_COUNT_MAPPING
)

#serial number functions
from inventory.views.serial_numbers.serial_number import SerialNumberMixin

#inventory models
from inventory.models import (
    LocationMaster, StockDetail,
    CycleCount, BatchDetail
)

#inbound models
from inbound.views.rtv.common import get_return_reasons

#core models
from core.models import (
    PurchaseApprovalConfig
)

#cycle count 
from .cycle_count import (
    generate_and_submit_cycle_count, submit_cycle_count, group_cycle_objs_based_on_sku_and_location
)

#import pandas fro validation
import pandas as pd
import numpy as np

from oauth2_provider.models import Application

from inventory.views.locator.validations import (
    get_sku_codes, get_sku_master_dict, validate_dates
)
from inbound.views.common.common import preload_stock_details_for_locations,sku_batch_mixing_validation

from core_operations.views.integration.integration import webhook_integration_3p

from django.test.client import RequestFactory

#Global Constants
sku_code_const = 'SKU Code'
sku_code_status = 'SKU Code Status'
location_status = 'Location Status'
batch_ref_key = 'Batch Number/Ref'

log = init_logger('logs/cycle_count_upload.log')

def inventory_adjustment_form(warehouse, extra_params={}):
    log.info("Request Inventory Adjustment Form user {}, extra params {}".format(warehouse.username, extra_params))
    return ADJUST_INVENTORY_EXCEL_MAPPING

def batch_creation_form(warehouse, extra_params={}):
    log.info("Request Batch Creation Form user {}, extra params {}".format(warehouse.username, extra_params))
    return BATCH_CREATION_EXCEL_MAPPING

def inventory_adjustment_upload(request, warehouse, data_list, extra_params={}):
    """
    Function to handle inventory adjustment upload requests.
    It validates the request data, generates a cycle count, and submits it.
    Args:
        request: The HTTP request object containing the cycle count data.
        warehouse: The warehouse object associated with the request.
        data_list: List of dictionaries containing inventory adjustment data.
        extra_params: Additional parameters for processing the request.
    Returns:
        HttpResponse: A response indicating the success or failure of the inventory adjustment upload.
    """
    log.info("Request Inventory Adjustment Upload user {}, extra params {}".format(warehouse.username, extra_params))

    data_list = rename_dict_keys(ADJUST_INVENTORY_EXCEL_MAPPING, data_list)
    status, data_list = validate_cycle_count_form(request, warehouse, data_list, extra_params=extra_params)
    if status != 'Success':
        status = rename_dict_keys(ADJUST_INVENTORY_EXCEL_MAPPING, status, reverse=True)
        return status
    try:
        log_message = (("Request Inventory Updation Username %s, data %s ") % (
            str(request.username), str(data_list)
        ))
        log.info(log_message)
        response = generate_and_submit_cycle_count(request, warehouse, data_list, extra_params = extra_params)
        if isinstance(response, list):
            return response

    except Exception as e:
        log_message = (("Inventory Updatation Failed for Username %s, data %s and error message %s") % (
            str(request.username), str(data_list), str(e)
        ))
        log.info(log_message)
    return HttpResponse('Success')

def get_required_misc_values(warehouse):
    """
    Get Required Miscellaneous Values for a given warehouse.
    """
    misc_keys = [
        'enable_inventory_approval', 'restricted_adjustment_status',
        'allow_negative_inventory', 'negative_inventory_threshold',
        'allow_negative_inventory_in_zones', 'restrict_sku_batch_mixing'
    ]

    # Fetch required data
    inv_adjustment_reasons = get_return_reasons(warehouse, 'inv_adj_reasons') or {}
    multiple_misc_values = get_multiple_misc_values(misc_keys, warehouse.id)

    # Parse values with defaults
    inv_adjustment_approval = multiple_misc_values.get('enable_inventory_approval', 'false')
    restricted_adjustment_status = multiple_misc_values.get('restricted_adjustment_status', 'false')
    restricted_adjustment_status = restricted_adjustment_status.split(',') if restricted_adjustment_status != 'false' else []

    configured_inv_adjustment_reasons = list(inv_adjustment_reasons.values())

    allow_negative_inventory = multiple_misc_values.get('allow_negative_inventory', 'false')
    negative_inventory_threshold = int(multiple_misc_values.get('negative_inventory_threshold', 0)) if allow_negative_inventory == 'true' else 0
    allow_negative_inventory_in_zones = multiple_misc_values.get('allow_negative_inventory_in_zones', 'false')

    allow_negative_inventory_in_zones = (
        allow_negative_inventory_in_zones.split(',') if allow_negative_inventory_in_zones != 'false' else []
    )

    return {
        'inv_adjustment_approval': inv_adjustment_approval,
        'configured_inv_adjustment_reasons': configured_inv_adjustment_reasons,
        'restricted_adjustment_status': restricted_adjustment_status,
        'allow_negative_inventory': allow_negative_inventory,
        'negative_inventory_threshold': negative_inventory_threshold,
        'allow_negative_inventory_in_zones': allow_negative_inventory_in_zones,
        'restrict_sku_batch_mixing': multiple_misc_values.get('restrict_sku_batch_mixing', 'false')
    }

def get_location_master_dict(warehouse, locations = None):
    """
    Get Location Master Dictionary
    Args:
        warehouse: The warehouse object.
        locations: List of location names to filter the dictionary.
    Returns:
        location_master_dict: Dictionary containing location details.
        carton_managed_locations: List of locations with carton management enabled.
        location_zone_info: Dictionary mapping locations to their zones.
        location_wise_zone_mapping: Dictionary mapping locations to their zone IDs.
        zone_restrictions: Dictionary containing zone restrictions.
    """
    if locations is None:
        locations = []
    filter_dict = {}
    zone_restrictions = {
        'restrict_one_location_to_one_sku': set(),
        'unique_batch': set()
    }
    if locations:
        filter_dict = {'location__in': locations}
    location_master_data = list(
        LocationMaster.objects.select_related('zone').exclude(
            zone__segregation__in=['inbound_staging','outbound_staging']
        ).exclude(
            zone__storage_type__in=['wip_area', 'replenishment_staging_area', 'nte_staging_area']
        ).filter(
            zone__user=warehouse.id,
            status = 1, **filter_dict
        ).values( 
            'location','id','zone__carton_managed', 'zone_id', 'zone__restrict_one_location_to_one_sku', 'zone__unique_batch', 'zone__zone'
        ))
    location_master_dict, location_zone_info, location_wise_zone_mapping, carton_managed_locations = {},{}, {}, []
    for location in location_master_data:
        location_name = location.get('location')
        location_id = location.get('id')
        carton_managed = location.get('zone__carton_managed')
        if location.get('zone__restrict_one_location_to_one_sku', ''):
            zone_restrictions['restrict_one_location_to_one_sku'].add(location_name)

        if location.get('zone__unique_batch', ''):
            zone_restrictions['unique_batch'].add(location_name)
            
        if carton_managed:
            carton_managed_locations.append(location_name)
        location_master_dict[location_name] = location_id
        location_zone_info[location_name] = location['zone__zone']
        location_wise_zone_mapping[location_name] = location.get('zone_id')

    return location_master_dict, carton_managed_locations, location_zone_info, location_wise_zone_mapping, zone_restrictions


def get_existing_serials(warehouse, serial_skus, locations):
    """
    Get Existing Serial Numbers
    Args:
        warehouse: The warehouse object.
        serial_skus: List of SKU codes for which serial numbers are needed.
        locations: List of location names to filter serial numbers.
    Returns:
        serial_df: DataFrame containing existing serial numbers.
    """
    values_list = ['sku__sku_code', 'location__location', 'batch_detail__batch_no', 'serial_number', 'lpn_number']
    serial_filter = {'filters':{'sku__sku_code__in': serial_skus, 'location__location__in': locations, 'status': 1}, 'values': values_list}
    serial_mixin = SerialNumberMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_serial_numbers()
    serial_df = pd.DataFrame(existing_serials.get('data', []))
    if serial_df.empty:
        serial_df = pd.DataFrame(columns=values_list)
    return serial_df

def check_serial_dict_with_available_serials(serial_dict, serial_df):
    """
    Check the serial_dict against existing serials in the database.
    serial_dict: dict
        Key: (sku_code, location, batch_no, lpn_number)
        Value: List of serial numbers
        serial_df: DataFrame
        DataFrame containing existing serials with columns:
        - 'sku__sku_code'
        - 'location__location'
        - 'batch_detail__batch_no'
        - 'serial_number'
        - 'lpn_number'
    Returns:
        diff_serials: dict
            Key: (sku_code, location, batch_no, lpn_number)
            Value: List of serial numbers that are in the database but not in the input
        new_serials: dict
            Key: (sku_code, location, batch_no, lpn_number)
            Value: List of serial numbers that are in the input but not in the database
        conflict_serials_dict: dict
            Key: (sku_code, location, batch_no, lpn_number)
            Value: List of dictionaries with 'serial_number', 'batch_no', and 'lpn_number' for conflicts
    """
    diff_serials, new_serials = {}, {}
    conflict_serials_dict = defaultdict(list)

    # Preprocess conflict map:
    # For each (sku, location, serial_number) → list of (batch, lpn)
    conflict_map = (
        serial_df
        .groupby(['sku__sku_code', 'location__location', 'serial_number'])
        .apply(lambda df: df[['batch_detail__batch_no', 'lpn_number']].to_dict('records'))
        .to_dict()
    )

    for key, serials in serial_dict.items():
        sku_code, location, batch_no, lpn_number = key
        if sku_code in serial_df['sku__sku_code'].unique():
            sku_serials = serial_df[serial_df['sku__sku_code'] == sku_code]
            location_serials = sku_serials[sku_serials['location__location'] == location]
            if lpn_number:
                location_serials = location_serials[location_serials['lpn_number'] == lpn_number]
            if batch_no:
                batch_serials = location_serials[location_serials['batch_detail__batch_no'] == batch_no]['serial_number'].tolist()
                new_serials[key] = list(set(serials) - set(batch_serials))
                diff_serials[key] = list(set(batch_serials) - set(serials))
            else:
                location_serial_nos = location_serials['serial_number'].tolist()
                new_serials[key] = list(set(serials) - set(location_serial_nos))
                diff_serials[key] = list(set(location_serial_nos) - set(serials))
        else:
            diff_serials[key] = []
            new_serials[key] = serials

        # Check for conflicts in serial numbers
        for serial in serials:
            potential_conflicts = conflict_map.get((sku_code, location, serial), [])
            for row in potential_conflicts:
                if (batch_no and row['batch_detail__batch_no'] != batch_no) or (lpn_number and row['lpn_number'] != lpn_number):
                    conflict_serials_dict[key].append({
                        'serial_number': serial,
                        'batch_no': row['batch_detail__batch_no'],
                        'lpn_number': row['lpn_number']
                    })

    return diff_serials, new_serials, dict(conflict_serials_dict)

def get_existing_batches(warehouse, sku_codes):
    """
    Get Existing Batches for given SKUs in the specified warehouse.
    Args:
        warehouse: The warehouse object.
        sku_codes: List of SKU codes to filter batches.
    Returns:
        existing_batches: Dictionary containing existing batches for each SKU.
    """
    existing_batches = {}
    if not sku_codes:
        return existing_batches
    batch_filters = {'warehouse': warehouse.id, 'sku__sku_code__in': sku_codes}
    batch_objs = BatchDetail.objects.filter(**batch_filters).values('sku__sku_code', 'batch_reference', 'batch_no')
    for batch in batch_objs:
        sku_code = batch.get('sku__sku_code')
        batch_no = batch.get('batch_no')
        batch_reference = batch.get('batch_reference')
        if sku_code not in existing_batches:
            existing_batches[sku_code] = {'batch_no': [], 'batch_reference': []}
        if batch_no:
            existing_batches[sku_code]['batch_no'].append(batch_no)
        if batch_reference:
            existing_batches[sku_code]['batch_reference'].append(batch_reference)
    return existing_batches

def get_existing_lpn_stock(warehouse, lpn_numbers):
    """
    Get Existing LPN Stock for given LPN numbers in the specified warehouse.
    Args:
        warehouse: The warehouse object.
        lpn_numbers: List of LPN numbers to filter stock.
    Returns:
        existing_lpn_stock: Dictionary containing existing LPN stock with LPN number as key and location as value.
    """
    existing_lpn_stock = {}
    if not lpn_numbers:
        return existing_lpn_stock
    stock_filters = {'sku__user': warehouse.id, 'lpn_number__in': lpn_numbers, 'quantity__gt': 0}
    existing_lpn_stock = dict(StockDetail.objects.filter(**stock_filters).values_list('lpn_number', 'location__location'))
    return existing_lpn_stock

def get_inv_approval_data(warehouse, inv_adjustment_approval):
    """
    Get Inventory Approval Data
    Args:
        warehouse: The warehouse object.
        inv_adjustment_approval: Boolean indicating if inventory adjustment approval is enabled.
    Returns:
        minimum_approval_value: Minimum approval value for inventory adjustments.
        maximum_approval_value: Maximum approval value for inventory adjustments.
    """
    minimum_approval_value, maximum_approval_value = 0, 0
    if inv_adjustment_approval == 'true':
        po_approval_min_max = PurchaseApprovalConfig.objects.filter(
            user_id=warehouse.id,approval_type='INV',purchase_type='INV'
        ).values('min_Amt','max_Amt').aggregate(Min('min_Amt'), Max('max_Amt'))
        
        minimum_approval_value, maximum_approval_value = (
            po_approval_min_max.get('min_Amt__min'),
            po_approval_min_max.get('max_Amt__max')
        )

    return minimum_approval_value, maximum_approval_value

def validate_sku_code(cell_data, data_dict, sku_details_dict, status, batch_creation = False):
    """
    Validate SKU Code
    Args:
        cell_data: The SKU code from the data row.
        data_dict: Dictionary to store validated data.
        sku_details_dict: Dictionary containing SKU details.
        status: List to store validation messages.
        batch_creation: Boolean indicating if batch creation is allowed.
    Returns:
        data_dict: Updated dictionary with SKU details.
        status: List of validation messages.
        sku_code: The validated SKU code.
    """
    sku_code = ''
    if cell_data:
        sku_code = convert_sku(cell_data)
        if sku_code in sku_details_dict:
            sku_id = sku_details_dict.get(sku_code).get('sku_id')
            data_dict['sku'] = (sku_id, sku_code)
            data_dict['sku_id'] = sku_id
            data_dict['sku_code'] = sku_code

            #Batch Creation Validation for Batch Based SKUS
            if batch_creation and sku_details_dict.get(sku_code).get('batch_based') != 1:
                status.append('Batch can not be created for batch disabled SKUs')
        else:
            status.append('Invalid WMS Code')
    else:
        status.append('SKU Should not be empty')
    
    return data_dict, status, sku_code

def validate_batch_details(
        batch_no, batch_reference, vendor_batch_number,
        inspection_lot_number, data_dict, sku_details_dict,
        sku_code, existing_batches, status, batch_creation = False
    ):
    """
    Validate Batch Details
    Args:
        batch_no: The batch number from the data row.
        batch_reference: The batch reference from the data row.
        vendor_batch_number: The vendor batch number from the data row.
        inspection_lot_number: The inspection lot number from the data row.
        data_dict: Dictionary to store validated data.
        sku_details_dict: Dictionary containing SKU details.
        sku_code: The SKU code being processed.
        existing_batches: Dictionary of existing batches for validation.
        status: List to store validation messages.
        batch_creation: Boolean indicating if batch creation is allowed.
    Returns:
        data_dict: Updated dictionary with batch details.
        status: List of validation messages.
    """
    batch_keys = {'batch_no': batch_no, 'batch_reference': batch_reference, 'vendor_batch_number': vendor_batch_number}
    if batch_creation:
        batch_keys.update({
            'inspection_lot_number': inspection_lot_number
        })

    for key, attr in batch_keys.items():
        data_dict[key] = ''
        if attr:
            attr = convert_sku(attr)      
            data_dict[key] = attr

        if key == 'batch_no' and sku_code:
            batch_based = sku_details_dict.get(sku_code,{}).get('batch_based','')
            data_dict['batch_based'] = batch_based

        if attr and key in ['batch_no', 'batch_reference'] and sku_code:
            status = validate_batch_details_for_batch_creation_or_updation(
                sku_code, key, attr, existing_batches, status, batch_creation
            )

    return data_dict, status

def validate_batch_details_for_batch_creation_or_updation(sku_code, key, attr, existing_batches, status, batch_creation):
    """
    Validate batch details for batch creation or updation.
    Args:
        sku_code: The SKU code being processed.
        key: The key for the batch detail (batch_no or batch_reference).
        attr: The value of the batch detail to validate.
        existing_batches: Dictionary of existing batches for validation.
        status: List to store validation messages.
        batch_creation: Boolean indicating if batch creation is allowed.
    Returns:
        status: List of validation messages.
    """
    batch_exist_error_msg = 'Cannot create batch, Batch already exists. Please adjust the existing batches if needed using adjustment form'
    batch_not_found_error_msg = 'New batch cannot be created through Inventory Adjustment upload'

    if batch_creation:
        if sku_code in existing_batches:
            if key == 'batch_no' and attr in existing_batches[sku_code].get('batch_no', []):
                status.append(batch_exist_error_msg)
            elif key == 'batch_reference':
                if attr in existing_batches[sku_code].get('batch_reference', []) and batch_exist_error_msg not in status:
                    status.append(batch_exist_error_msg)
    else:
        if sku_code not in existing_batches:
            status.append(batch_not_found_error_msg)
        else:
            if key == 'batch_no' and attr not in existing_batches[sku_code].get('batch_no', []):
                status.append(batch_not_found_error_msg)
            elif key == 'batch_reference' and attr not in existing_batches[sku_code].get('batch_reference', []) and batch_not_found_error_msg not in status:
                status.append(batch_not_found_error_msg)
    return status

def validate_location(cell_data, location_master_dict, location_zone_info, data_dict, status):
    """
    Validate Location
    Args:
        cell_data: The location name from the data row.
        location_master_dict: Dictionary containing valid locations.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
    Returns:
        data_dict: Updated dictionary with location details.
        status: List of validation messages.
    """
    if cell_data:
        location = convert_sku(cell_data)
        if location in location_master_dict:
            data_dict['location'] = (location_master_dict.get(location,''),location)
            data_dict['location_name'] = location
            data_dict['location_id'] = location_master_dict.get(location,'')
            data_dict['zone'] = location_zone_info.get(location, '')
        else:
            status.append('Invalid Location')
    else:
        status.append('Location should not be empty')
    
    return data_dict, status

def validate_lpn_number(data_row, existing_lpn_stock, data_dict, status, carton_managed_locations, invalid_lpn_numbers):
    """
    Validate LPN Number
    Args:
        data_row: Dictionary containing the data row being processed.
        existing_lpn_stock: Dictionary of existing LPN stock.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages
        carton_managed_locations: List of locations that are carton managed.
    Returns:
        data_dict: Updated dictionary with LPN number details.
        status: List of validation messages.
    """
    lpn_number = data_row.get('lpn_number', '') or ''
    data_dict['lpn_number'] = lpn_number
    location_name = data_row.get('location')
    if lpn_number:
        lpn_number = convert_sku(lpn_number)
        data_dict['lpn_number'] = lpn_number
        if lpn_number in invalid_lpn_numbers:
            status.append(invalid_lpn_numbers.get(lpn_number))
        elif lpn_number in existing_lpn_stock:
            location = existing_lpn_stock.get(lpn_number)
            if location != location_name:
                status.append('LPN Number already exists in %s' % location)
        elif location_name not in carton_managed_locations:
            status.append('LPN Number is only allowed in carton managed locations')
    elif location_name in carton_managed_locations:
        status.append('LPN Number is mandatory for carton managed locations')

    return data_dict, status


def validate_stock_status(cell_data, data_dict, status, restricted_adjustment_status):
    """
    Validate Stock Status
    Args:
        cell_data: The stock status from the data row.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
        restricted_adjustment_status: List of stock statuses where adjustments are restricted.
    Returns:
        data_dict: Updated dictionary with stock status details.
        status: List of validation messages.
    """
    if cell_data:
        stock_status = convert_sku(cell_data)
        reversed_stock_status = str(reverse_stock_choice_mapping.get(stock_status))
        if stock_status and stock_status not in reverse_stock_choice_mapping:
            status.append('Invalid Stock Status')
        elif reversed_stock_status == '2':
            status.append('Inventory Adjustment cannot be done for Consumed Stock')
        else:
            data_dict['stock_status'] = reversed_stock_status

        if stock_status in restricted_adjustment_status:
            status.append('Inventory Adjustment is not allowed for this stock status')

    return data_dict, status

def validate_inv_adj_reason(cell_data, configured_inv_adjustment_reasons, data_dict, status, adhoc=False):
    """
    Validate Inventory Adjustment Reason
    Args:
        cell_data: The reason for inventory adjustment from the data row.
        configured_inv_adjustment_reasons: List of configured inventory adjustment reasons.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
        adhoc: Boolean indicating if this is an adhoc adjustment.
    Returns:
        data_dict: Updated dictionary with reason details.
        status: List of validation messages.
    """
    if cell_data:
        if configured_inv_adjustment_reasons:
            if cell_data not in configured_inv_adjustment_reasons and not adhoc:
                status.append(
                    'Enter only configured Inventory Adjustment reasons'
                )
        data_dict['reason'] = cell_data
    else:
        status.append('Reason is Mandatory')
    
    return data_dict, status

def validate_quantity(cell_data, data_dict, status, negative_adjustment_configs, batch_creation):
    """
    Validates the entered quantity based on warehouse and zone-level negative inventory configurations.

    - If `batch_creation` is `True`, negative quantities are **never allowed**.
    - If `batch_creation` is `False`, negative quantities are validated based on warehouse & zone settings.
    """
    try:
        if str(cell_data).strip():  # Ensure cell_data is not empty or just whitespace
            quantity = float(cell_data)
            data_dict['quantity'] = str(quantity)

            # Disallow negative quantity if it's a batch creation
            if batch_creation and quantity < 0:
                status.append('Negative quantity is not allowed during batch creation')

            # Validate negative inventory based on warehouse & zone configs
            elif quantity < 0:
                allow_negative_inventory = negative_adjustment_configs.get('allow_negative_inventory', 'false') == 'true'
                negative_inventory_threshold = int(negative_adjustment_configs.get('negative_inventory_threshold', 0))
                allowed_zones = negative_adjustment_configs.get('allow_negative_inventory_in_zones', [])

                # Global setting: Negative inventory not allowed
                if not allow_negative_inventory:
                    status.append('Negative Inventory is not allowed')

                # Warehouse-level threshold check
                elif abs(quantity) > negative_inventory_threshold:
                    status.append(f'Negative Inventory Threshold Exceeded: Allowed up to {-negative_inventory_threshold}')

                # Zone-level restriction check
                elif data_dict.get('zone') not in allowed_zones:
                    status.append('Negative Inventory is not allowed in this zone')

        else:
            status.append('Physical Quantity should not be empty')

    except ValueError:
        status.append('Invalid Quantity')

    return data_dict, status

def safe_parse_list(ss):
    """Safely parse a string or list into a list of strings."""
    if isinstance(ss, list):
        return ss
    if isinstance(ss, str):
        try:
            if not ss.strip():
                return []
            parsed = ast.literal_eval(ss)
            return parsed if isinstance(parsed, list) else [str(ss)]
        except (ValueError, SyntaxError):
            return [str(ss)]
    return [str(ss)]

def format_serial_conflict_error(conflicts, location, batch_no):
    """
    Formats the serial conflict error message for serial numbers already present in a location with different LPNs.
    """
    conflict_lpn, conflict_serials, conflict_batches = set(), set(), set()
    
    # Collect unique serial numbers, LPNs, and batches from conflicts
    for conflict in conflicts:
        conflict_serials.add(conflict.get('serial_number') or '')
        conflict_lpn.add(conflict.get('lpn_number') or '')
        conflict_batches.add(conflict.get('batch_no') or '')

    # Prepare strings
    serials_str = ', '.join(conflict_serials)
    lpn_str = ', '.join(conflict_lpn)
    batch_str = ', '.join(conflict_batches) or batch_no

    return (
        f"Serial Number {serials_str} already exists in location {location} "
        f"with different batch {batch_str} or different LPN {lpn_str}"
    )

def validate_serial_number(cell_data, data_dict, sku_details_dict, status, sku_code, location, batch_no, lpn_number, diff_serials, new_serials, remove_serials, data_row, conflict_serials):
    """
    Validate Serial Number
    Args:
        cell_data: The serial number from the data row.
        data_dict: Dictionary to store validated data.
        sku_details_dict: Dictionary containing SKU details.
        status: List to store validation messages.
        sku_code: The SKU code being processed.
        location: The location name from the data row.
        batch_no: The batch number from the data row.
        lpn_number: The LPN number from the data row.
        diff_serials: Dictionary of serial numbers that differ from existing ones.
        new_serials: Dictionary of new serial numbers that are not in existing ones.
        remove_serials: Dictionary to store serial numbers to be removed.
        data_row: The entire data row being processed.
        conflict_serials : Dictionary of conflicted serial numbers that matches with existing ones.
    Returns:
        data_dict: Updated dictionary with serial number details.
        status: List of validation messages.
        remove_serials: Dictionary of serial numbers to be removed.
    """
    data_dict['serial_number'] = ''
    key = (sku_code, location, batch_no, lpn_number)
    serial_based = sku_details_dict.get(sku_code, {}).get('enable_serial_based')
    quantity = float(data_dict.get('quantity', 0))
    if cell_data and serial_based:
        serial_number = convert_sku(cell_data)
        serial_number = safe_parse_list(serial_number)
        request_add_serials = data_row.get('add_serials', [])
        request_remove_serials = data_row.get('remove_serials', [])

        # Validate unique serial numbers across lpns and batches
        if conflict_serials.get(key):
            conflicts = conflict_serials.get(key, [])
            if conflicts:
                status.append(format_serial_conflict_error(conflicts, location, batch_no))
        if diff_serials.get(key):
            remove_serials.setdefault(key, []).extend(diff_serials.get(key, []))
        if new_serials.get(key) and (remove_serials.get(key) or diff_serials.get(key)):
            status.append('Addition and Removal of serial numbers is not allowed together')
        if request_add_serials and request_remove_serials:
            status.append("Addition and removal of serial numbers is not allowed together")
        elif int(quantity) and int(quantity) != len(serial_number):
            status.append('Quantity and Serial Number does not match')
        if quantity == 0:
            remove_serials.setdefault(key, []).extend(serial_number)
        data_dict['serial_number'] = serial_number
    elif serial_based and not quantity and diff_serials.get(key):
        data_dict['serial_number'] = ','.join(diff_serials.get(key, []))
        remove_serials.setdefault(key, []).extend(diff_serials.get(key, []))
    elif serial_based:
        status.append('Serial Number is Mandatory')   
    elif cell_data:
        status.append('Serial Number is not required for non serial based SKU')

    return data_dict, status, remove_serials

def validate_mrp(cell_data, data_dict, status):
    """
    Validate MRP (Maximum Retail Price)
    Args:
        cell_data: The MRP value from the data row.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
    Returns:
        data_dict: Updated dictionary with MRP details.
        status: List of validation messages.
    """
    data_dict['mrp'] = 0
    if str(cell_data):
        try:
            mrp = float(cell_data)
            data_dict['mrp'] = str(mrp)
            if mrp < 0:
                status.append('Invalid MRP')
        except ValueError:
            status.append('Invalid MRP')
    return data_dict, status

def validate_weight(cell_data, data_dict):
    """
    Validate Weight
    Args:
        cell_data: The weight value from the data row.
        data_dict: Dictionary to store validated data.
    Returns:
        data_dict: Updated dictionary with weight details.
    """
    data_dict['weight'] = ''
    if cell_data:
        weight = convert_sku(cell_data)
        data_dict['weight'] = weight

    return data_dict

def validate_price(cell_data, data_dict, status):
    """
    Validate Unit Price
    Args:
        cell_data: The unit price from the data row.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
    Returns:
        data_dict: Updated dictionary with unit price details.
        status: List of validation messages.
    """
    # Initialize unit_price to 0 if cell_data is empty or None  
    data_dict['unit_price'] = 0
    if cell_data:
        try:
            unit_price = float(cell_data)
            data_dict['unit_price'] = str(unit_price)
            if unit_price < 0:
                status.append('Invalid Price')
        except ValueError:
            status.append('Invalid Price')
    
    return data_dict, status

def validate_adjustment_type(cell_data, data_dict, status):
    """
    Validate Adjustment Type
    Args:
        cell_data: The adjustment type from the data row.
        data_dict: Dictionary to store validated data.
        status: List to store validation messages.
    Returns:
        data_dict: Updated dictionary with adjustment type details.
        status: List of validation messages.
    """
    data_dict['adjustment_type'] = 'unscheduled'
    if cell_data:
        if cell_data.lower() in ['scrap']:
            data_dict['adjustment_type'] = 'scrap'
        else:
            status.append('Invalid Adjustment Type')
    
    return data_dict, status

def validate_all_fields(
        data_row, status, data_dict, sku_details_dict, location_master_dict,
        configured_inv_adjustment_reasons, timezone, restricted_adjustment_status, 
        existing_batches, location_zone_info, adhoc=False, batch_creation = False,
        params_dict = None
    ):
    """
    Validate all fields in the data row for inventory adjustment.
    Args:
        data_row: Dictionary containing the data row being processed.
        status: List to store validation messages.
        data_dict: Dictionary to store validated data.
        sku_details_dict: Dictionary containing SKU details.
        location_master_dict: Dictionary containing valid locations.
        configured_inv_adjustment_reasons: List of configured inventory adjustment reasons.
        timezone: Timezone object for date validation.
        restricted_adjustment_status: List of stock statuses where adjustments are restricted.
        existing_batches: Dictionary of existing batches for validation.
        adhoc: Boolean indicating if this is an adhoc adjustment.
        batch_creation: Boolean indicating if batch creation is allowed.
        params_dict: Optional dictionary containing additional parameters for validation.
    Returns:
        data_dict: Updated dictionary with validated data.
        status: List of validation messages.
        remove_serials: Dictionary of serial numbers to be removed.
    """
    # Initialize data_dict if not provided
    if params_dict is None:
        params_dict = {}

    diff_serials = params_dict.get('diff_serials', {})
    new_serials = params_dict.get('new_serials', {})
    remove_serials = params_dict.get('remove_serials', {})
    conflict_serials = params_dict.get('conflict_serials', {})

    existing_lpn_stock = params_dict.get('existing_lpn_stock', {})
    carton_managed_locations = params_dict.get('carton_managed_locations', [])
    invalid_lpn_numbers = params_dict.get('invalid_lpn_numbers', {})

    #Configuration Details
    negative_adjustment_configs = params_dict.get('negative_inventory_configs', {})

    sku_code = data_row.get('sku_code', '')
    batch_no = data_row.get('batch_no', '')
    lpn_number = data_row.get('lpn_number', '')
    batch_reference = data_row.get('batch_reference', '')
    vendor_batch_number = data_row.get('vendor_batch_number', '')
    inspection_lot_number = data_row.get('inspection_lot_number', '')
    location = data_row.get('location', '')
    reason = data_row.get('reason', '')
    stock_status = data_row.get('stock_status', '')
    mrp = data_row.get('mrp', 0)
    quantity = data_row.get('quantity', 0)
    weight = data_row.get('weight', '')
    price = data_row.get('unit_price', 0)
    adjustment_type = data_row.get('adjustment_type')
    serial_number = data_row.get('serial_number', '')
    manufactured_date, expiry_date, retest_date, reevaluation_date, best_before_date = (
        data_row.get('manufactured_date'),
        data_row.get('expiry_date'),
        data_row.get('retest_date'),
        data_row.get('reevaluation_date'),
        data_row.get('best_before_date')
    )

    #Validate SKU Code
    data_dict, status, sku_code = validate_sku_code(sku_code, data_dict, sku_details_dict, status, batch_creation=batch_creation)

    #Validate Batch Details
    data_dict, status = validate_batch_details(
        batch_no, batch_reference, vendor_batch_number, inspection_lot_number,
        data_dict, sku_details_dict, sku_code, existing_batches, status, batch_creation = batch_creation
    )

    if batch_creation:
        #Validate Dates
        data_dict, status = validate_dates(
            manufactured_date, expiry_date, retest_date, reevaluation_date, best_before_date, 
            timezone, status, data_dict
        )

    #Validate Location
    data_dict, status = validate_location(location, location_master_dict, location_zone_info, data_dict, status)

    #Validate LPN Number
    data_dict, status = validate_lpn_number(data_row, existing_lpn_stock, data_dict, status, carton_managed_locations, invalid_lpn_numbers)

    #Validate Stock Status
    data_dict, status = validate_stock_status(stock_status, data_dict, status, restricted_adjustment_status)

    #Validate Reason
    data_dict, status = validate_inv_adj_reason(reason, configured_inv_adjustment_reasons, data_dict, status, adhoc=adhoc)

    #Quantity Validation
    data_dict, status = validate_quantity(quantity, data_dict, status, negative_adjustment_configs, batch_creation)

    #Serial Number Validation
    data_dict, status, remove_serials = validate_serial_number(
        serial_number, data_dict, sku_details_dict, status, sku_code,
        location, batch_no, lpn_number, diff_serials, new_serials, remove_serials, data_row, conflict_serials
    )

    #MRP Validation
    data_dict, status = validate_mrp(mrp, data_dict, status)

    #Weight Validation
    data_dict = validate_weight(weight, data_dict)

    #Price Validation
    data_dict, status = validate_price(price, data_dict, status)

    #Adjustment Type Validation
    data_dict, status = validate_adjustment_type(adjustment_type, data_dict, status)

    return data_dict, status, remove_serials

def validate_batch_based(data_dict, status):
    """
    Validate if batch based fields are correctly set.
    Args:
        data_dict: Dictionary containing the data row being processed.
        status: List to store validation messages.
    Returns:
        data_dict: Updated dictionary with batch based fields.
        status: List of validation messages.
    """
    if data_dict.get('batch_based'):
        if not (data_dict.get('batch_no') or data_dict.get('batch_reference') or data_dict.get('vendor_batch_number')):
            status.append('Batch No or Vendor Batch Number or Batch Reference is Mandatory for Batch Based')
    if not data_dict.get('batch_based'):
        data_dict['vendor_batch_number'], data_dict['batch_no'], data_dict['batch_reference'] = False, False, False

    return data_dict, status

def validate_duplicate_records(warehouse, data_dict, validation_check_list, status, available_cycle_count_data):
    """
    Validate duplicate records in the cycle count data.
    Args:
        warehouse: The warehouse object.
        data_dict: Dictionary containing the data row being processed.
        validation_check_list: List to store validation keys for duplicate checks.
        status: List to store validation messages.
        available_cycle_count_data: DataFrame containing existing cycle count records.
    Returns:
        validation_check_list: Updated list with validation keys.
        status: List of validation messages.
    """
    validation_dict, validation_key = {}, ()
    validation_list = [
        ('sku', 'sku__sku_code'),('location','location__location'),
        ('batch_no','batch_detail__batch_no'),
        ('vendor_batch_number', 'batch_detail__vendor_batch_no'),
        ('batch_reference', 'batch_detail__batch_reference'),
        ('stock_status', 'stock_status'),
        ('unit_price', 'price'),
        ('lpn_number', 'lpn_number'),
    ]
    for field, filter in validation_list:
        if data_dict.get(field):
            if field in ['sku','location']:
                validation_dict.update({filter : data_dict.get(field)[1]})
                validation_key = validation_key + (str(data_dict.get(field)[1]),)
            else:
                validation_dict.update({filter : data_dict.get(field)})
                validation_key = validation_key + (str(data_dict.get(field)),)

    data_frame_filter = {}
    cycle_validation = False
    # Build the filter dictionary with column names and conditions
    for field_name, field_value in validation_dict.items():
        data_frame_filter[field_name] = f"== '{field_value}'"

    cycle_validation_df = pd.DataFrame()
    # Generate validation conditions as strings
    validation_conditions = [
        f"({column} {condition})" for column, condition in data_frame_filter.items()
    ]

    if validation_conditions:
        query = " & ".join(validation_conditions)
        try:
            if not available_cycle_count_data.empty:
                cycle_validation_df = available_cycle_count_data.query(query)
        except Exception as e:
            log.info(f"Error in validating duplicate records: {str(e)}")

    if not cycle_validation_df.empty:
        cycle_validation = True

    validation_key = validation_key + (str(data_dict.get('serial_number')),)
    if validation_key not in validation_check_list:
        validation_check_list.append(validation_key)
    else:
        status.append('Duplicate entry for %s' % str(validation_key))

    if cycle_validation:
        status.append(
            'Entry already exists in confirm cycle count/inventory adjustment, please update before uploading'
        )
    
    return validation_check_list, status

def get_available_cycle_count_data(warehouse, sku_codes, locations, adhoc):
    """
    Fetch available cycle count data for validation.
    Args:
        warehouse: The warehouse object.
        sku_codes: List of SKU codes to filter cycle counts.
        locations: List of location names to filter cycle counts.
        adhoc: Boolean indicating if this is an adhoc cycle count.
    Returns:
        cycle_validation_data: DataFrame containing available cycle count data.
    """
    cycle_validation_dict = {'sku__sku_code__in': sku_codes ,
                             'location__location__in':locations,
                             'sku__user':warehouse.id}
    if adhoc:
        cycle_validation_dict['status'] = 2
    else:
        cycle_validation_dict['status__in'] = [1,2]
    
    cycle_validation_data = pd.DataFrame(CycleCount.objects.select_related('sku', 'location').filter(
        **cycle_validation_dict).values('sku_id', 'location_id', 'batch_detail_id', 'stock_status',
                                        'sku__sku_code', 'location__location', 'batch_detail__batch_no',
                                        'batch_detail__vendor_batch_no',
                                        'batch_detail__batch_reference', 'price'))

    return cycle_validation_data.astype(str)

def fetch_updated_serials(row, new_serials, remove_serials):
    """
    Fetch updated serial numbers based on new_serials or remove_serials.
    Args:
        row: DataFrame row containing SKU, location, batch number, and LPN number.
        new_serials: Dictionary of new serial numbers.
        remove_serials: Dictionary of serial numbers to be removed.
    Returns:
        List of updated serial numbers.
    """
    batch_no = row['batch_no'] or ''
    lpn_number = row.get('lpn_number') or ''
    key = (row['sku_code'], row['location_name'], batch_no, lpn_number)
    if new_serials.get(key):
        return list(new_serials[key])  # Use new_serials if available
    if remove_serials.get(key):
        return list(set(remove_serials[key]))  # Use remove_serials if available
    return []

def format_serial_data_list(data_list, new_serials, remove_serials):
    """
    Format the data list to group serial numbers and quantities,
    and replace serial numbers based on new_serials or remove_serials.
    """
    updated_data = []

    for row in data_list:
        # Make a shallow copy to avoid modifying original input
        updated_row = row.copy()

        if 'quantity' in updated_row:
            try:
                updated_row['quantity'] = float(str(updated_row['quantity']))
            except (ValueError, TypeError):
                updated_row['quantity'] = 0.0

        updated_row['serial_number'] = safe_parse_list(updated_row['serial_number'])
        if 'serial_number' in updated_row:
            serial_number = updated_row.pop('serial_number')
            updated_row['serial_numbers'] = serial_number
            updated_row['serial_number'] = ','.join(serial_number)

        # Use the fetch_updated_serials to get updated serials if any
        updated_serials = fetch_updated_serials(row, new_serials, remove_serials)

        # If updated serials found, replace them
        if updated_serials:
            updated_row['serial_numbers'] = updated_serials

        updated_data.append(updated_row)

    return updated_data

def get_required_configs(misc_dict, batch_creation):
    configs = {}
    negative_adjustment_configs = {'allow_negative_inventory': misc_dict.get('allow_negative_inventory'), 'negative_inventory_threshold': misc_dict.get('negative_inventory_threshold'), 'allow_negative_inventory_in_zones': misc_dict.get('allow_negative_inventory_in_zones')} if not batch_creation else {}
    if negative_adjustment_configs:
        configs['negative_adjustment_configs'] = negative_adjustment_configs

    return configs


def frame_lpn_error_messages(errors):
    """Frame error messages for ASN upload"""
    lpn_error_dict = defaultdict(list)

    for error in errors:
        error = error.strip()
        # Case 1: Error ends with "LPN: <lpn_numbers>"
        if 'LPN:' in error:
            message_part, lpn_part = error.rsplit('LPN:', 1)
            message = message_part.strip()
            lpn_numbers = [lpn.strip() for lpn in lpn_part.split(',') if lpn]

            for lpn in lpn_numbers:
                if message not in lpn_error_dict[lpn]:
                    lpn_error_dict[lpn].append(message)

        # Case 2: Handle "Invalid LPN <lpn_number>"
        elif 'invalid lpn' in error.lower():
            parts = error.split('-', 1)
            if len(parts) > 1:
                message = parts[0].strip()
                lpn_str = parts[1].strip()
                # Split the second part (LPNs) by spaces and strip any leading/trailing spaces
                lpns = [lpn.strip() for lpn in re.split(r'[ ,]+', lpn_str)]

                # If the message is not already in the dictionary for these LPNs, add it
                for lpn in lpns:
                    if message not in lpn_error_dict.get(lpn, []):
                        lpn_error_dict.setdefault(lpn, []).append('Invalid LPN Number')
    
    return lpn_error_dict

def validate_lpn_data(lpn_numbers, warehouse, request_user, extra_params):
    """
    Processes a list of data to extract LPN information,
    validates the LPNs, and creates them using the LpnManager.
    """
    authorization = Application.objects.filter(user_id = request_user.id).values('accesstoken__token').order_by('-accesstoken__id').first()
    authorization_token = authorization.get('accesstoken__token', '')
    extra_params = {'headers': {'Authorization': authorization_token}, 'request_meta': {'request_meta': extra_params.get('json_data', {}).get('request_meta', '')}, 'request_schedule': extra_params.get('json_data', {}).get('request_schedule', '')}
    json_data = {
        'lpn_in_response': True
    }
    extra_params.update({'json_data': json_data})

    # Initialize LpnManager
    lpn_manager = LpnManager(warehouse, extra_params, request_user)
    transaction_number = ''
    # Validate and create LPNs
    lpn_errors = []
    if lpn_numbers:
        lpn_numbers = list(set(lpn_numbers))
        lpn_errors = lpn_manager.create_and_validate_lpns(lpn_numbers, transaction_number, allow_multiple_transactions=False)
    
    # Frame error messages
    invalid_lpn_numbers = frame_lpn_error_messages(lpn_errors)

    return invalid_lpn_numbers
    
def validate_cycle_count_form(request_user, warehouse, data_to_integrate, adhoc = False, batch_creation = False, extra_params=None):
    """
    Validate Cycle Count Form
    Args:
        request_user: The user making the request.
        warehouse: The warehouse object.
        data_to_integrate: List of data rows to validate.
        adhoc: Boolean indicating if this is an adhoc cycle count (default: False).
        batch_creation: Boolean indicating if batch creation is allowed (default: False).
        extra_params: Additional parameters for validation (default: None).
    Returns:
        Tuple containing:
            - 'Success' if validation passes, otherwise a list of error data rows.
            - List of validated data rows if successful, otherwise an empty list.
    """
    data_list, error_data_list, validation_check_list = [], [], []
    existing_batches, remove_serials, location_sku_batch_map = {}, {}, {}
    error_status = False
    user_timezone = warehouse.userprofile.timezone
    timezone = user_timezone if user_timezone else 'Asia/Calcutta'

    #Get Required Misc Values
    misc_dict = get_required_misc_values(warehouse)
    restrict_sku_batch_mixing = misc_dict.get('restrict_sku_batch_mixing', 'false')
    inv_adjustment_approval = misc_dict.get('inv_adjustment_approval', 'false')
    configured_inv_adjustment_reasons = misc_dict.get('configured_inv_adjustment_reasons', [])
    restricted_adjustment_status = misc_dict.get('restricted_adjustment_status', [])

    #GET SKU Details from input
    sku_codes, locations, lpn_numbers, serial_dict, data_to_integrate = get_sku_codes(data_to_integrate, fetch_serials=True) 

    #Location Master Dict
    location_master_dict, carton_managed_locations, location_zone_info, _, zone_restrictions = get_location_master_dict(warehouse, locations = locations)

    if restrict_sku_batch_mixing == 'true':
        stock_map=preload_stock_details_for_locations(warehouse.id, locations)

    #GET SKU Detail
    sku_details_dict, batch_based_skus, serial_skus = get_sku_master_dict(warehouse, sku_codes, fetch_serials=True)
    
    #Get Exsting Batches for sku codes
    existing_batches = get_existing_batches(warehouse, batch_based_skus)

    #Get Existing LPN Numbers Stock
    existing_lpn_stock = get_existing_lpn_stock(warehouse, lpn_numbers)

    #Check for availability of LPN Numbers
    invalid_lpn_numbers = validate_lpn_data(lpn_numbers, warehouse, request_user, extra_params)

    #Get Existing Serial Number Data
    serial_df = get_existing_serials(warehouse, serial_skus, locations)

    #Check All serial Numbers in stock are available in the input
    diff_serials, new_serials, conflict_serials = check_serial_dict_with_available_serials(serial_dict, serial_df)

    #GET Inventory Approval Details
    minimum_approval_value, maximum_approval_value = get_inv_approval_data(warehouse, inv_adjustment_approval)

    #get available cycle count data
    available_cycle_count_data = get_available_cycle_count_data(warehouse, sku_codes, locations, adhoc)

    for data_row in data_to_integrate:
        data_dict = {'batch_based': False}
        status = []

        if inv_adjustment_approval == 'true':
            if minimum_approval_value == None or maximum_approval_value == None:
                status.append('Please define approval matrix in approval master')

        #Frame the config data
        config_dict = get_required_configs(misc_dict, batch_creation)

        if not status:
            params_dict = {
                'diff_serials': diff_serials,
                'new_serials': new_serials,
                'conflict_serials' : conflict_serials,
                'remove_serials': remove_serials,
                'existing_lpn_stock': existing_lpn_stock,
                'carton_managed_locations': carton_managed_locations,
                'invalid_lpn_numbers': invalid_lpn_numbers,
                'negative_inventory_configs': config_dict.get('negative_adjustment_configs', {})
            }
            # Validate all fields in the data row
            data_dict, status, remove_serials = validate_all_fields(
                data_row, status, data_dict, sku_details_dict, location_master_dict,
                configured_inv_adjustment_reasons, timezone, restricted_adjustment_status,
                existing_batches, location_zone_info, adhoc=adhoc, batch_creation=batch_creation,
                params_dict=params_dict
            )
            #Batch Based Validations
            data_dict, status = validate_batch_based(data_dict, status)

            #validating the records in DB and in same sheet
            validation_check_list, status = validate_duplicate_records(warehouse, data_dict,validation_check_list, status, available_cycle_count_data)
            valid_location = data_row.get('location', '')
            if restrict_sku_batch_mixing == 'true' and valid_location:
                if valid_location not in location_sku_batch_map:
                    location_sku_batch_map[valid_location] = {
                        'skus': set(),
                        'batches': set()
                    }
                # validating if the SKU and batch are compatible with the destination location
                is_location_compatible, error_message = sku_batch_mixing_validation(
                    stock_map,
                    valid_location,
                    data_row.get('sku_code', ''),
                    data_row.get('batch_no', ''),
                    location_sku_batch_map,
                    zone_restrictions
                    )
                if not is_location_compatible:
                    status.append(error_message)
                else:
                    location_sku_batch_map[valid_location]['skus'].add(data_row.get('sku_code', ''))
                    if data_row.get('batch_no', ''):
                        location_sku_batch_map[valid_location]['batches'].add(data_row.get('batch_no', ''))
            

        if status:
            error_status = True
            data_row['Status'] = status
        
        data_row.pop('add_serials', None)
        data_row.pop('remove_serials', None)
        error_data_list.append(data_row)
        data_list.append(data_dict)

    if not error_status:
        data_list = format_serial_data_list(data_list, new_serials, remove_serials)
        return 'Success', data_list
    return error_data_list, []

def create_cycle_count_form(warehouse, extra_params={}):
    '''
    Create Cycle Count Form
    '''
    log.info("Request Create Cycle Count Form user {}, extra params {}".format(warehouse.username, extra_params))
    return CYCLE_COUNT_CREATION_HEADERS

def create_cycle_count_upload(request, warehouse, data_list, extra_params={}):
    """
    Create a cycle count upload.

    Args:
        request: The HTTP request object.
        warehouse: The warehouse object.
        data_list: The list of data for cycle count upload.
        extra_params: Additional parameters (default: {}).

    Returns:
        HttpResponse: The HTTP response indicating the success of the operation.
    """

    log.info("Request Inventory Adjustment Upload user {}, extra params {}".format(warehouse.username, extra_params))

    status, data_list, cycle_creation_list, sku_codes, sku_zones_dict = validate_create_cycle_count_form(warehouse, data_list)
    if status != 'Success':
        return status
    try:
        log_message = (("Request Cycle Count Upload Generation Username %s, data %s ") % (
            str(request.username), str(data_list)
        ))
        log.info(log_message)
        response = create_cycle_count(request ,warehouse, cycle_creation_list, sku_codes, sku_zones_dict)
        if isinstance(response, list):
            return response
             
    except Exception as e:
        log_message = (("Cycle Count Upload Generation Failed for Username %s, data %s and error message %s") % (
            str(request.username), str(data_list), str(e)
        ))
        log.info(log_message)
    return HttpResponse('Success')

# Function to validate SKU Code
def validate_sku_code_df(sku_code, sku_details_dict):
    """
    Validates the SKU code against the given SKU details dictionary.

    Args:
        sku_code (str): The SKU code to be validated.
        sku_details_dict (dict): A dictionary containing SKU details.

    Returns:
        str or None: If the SKU code is not found in the dictionary, returns an error message.
                    Otherwise, returns None indicating the SKU code is valid.
    """
    if sku_code and sku_code not in sku_details_dict:
        return f"SKU code '{sku_code}' not found."
    else:
        return None  # Valid

# Function to validate Location
def validate_location_df(location, location_master_dict):
    """
    Validates if a given location exists in the location_master_dict.

    Args:
        location (str): The location to be validated.
        location_master_dict (dict): A dictionary containing the master list of locations.

    Returns:
        str or None: Returns an error message if the location is not found in the dictionary,
                     otherwise returns None to indicate that the location is valid.
    """
    if not location:
        return "Location should not be empty."
    
    if location not in location_master_dict:
        return f"Location '{location}' not found."
    else:
        return None  # Valid
    
# Function to concatenate error messages
def concatenate_errors(row):
    """
    Concatenates the error messages for SKU Code Status and Location Status.

    Args:
        row (dict): A dictionary containing the row data.

    Returns:
        str: The concatenated error message.

    """
    sku_code_status_ = row[sku_code_status]
    location_status_ = row[location_status]
    
    if sku_code_status_ is None and location_status_ is None:
        return None
    else:
        return f"{sku_code_status_}. {location_status_}" if sku_code_status_ is not None and location_status_ is not None else (sku_code_status_ or location_status_)

def sku_location_validation(data_list, sku_details_dict, location_master_dict):
    """
    Validate SKU codes and locations in the given data list using the provided dictionaries.

    Args:
        data_list (list): A list of data containing SKU codes and locations.
        sku_details_dict (dict): A dictionary containing SKU details.
        location_master_dict (dict): A dictionary containing location details.

    Returns:
        tuple: A tuple containing a boolean value indicating if there are any errors and a dictionary
        representing the validated data.

    Example:
        data_list = [
            {'SKU Code': 'ABC123', 'Location': 'A1'},
            {'SKU Code': 'DEF456', 'Location': 'B2'},
            {'SKU Code': 'GHI789', 'Location': 'C3'}
        ]
        sku_details_dict = {'ABC123': {'name': 'Product 1'}, 'DEF456': {'name': 'Product 2'}}
        location_master_dict = {'A1': {'name': 'Location 1'}, 'B2': {'name': 'Location 2'}}
        error, validated_data = sku_location_validation(data_list, sku_details_dict, location_master_dict)
    """

    error = False
    df = pd.DataFrame(data_list)
    # Add status columns
    df[sku_code_status] = df[sku_code_const].apply(validate_sku_code_df, args=(sku_details_dict,))
    df[location_status] = df['Location'].apply(validate_location_df, args=(location_master_dict,))
    
    # Concatenate error messages
    df['Status'] = df.apply(concatenate_errors, axis=1)

    # Remove status columns
    columns_to_remove = [sku_code_status, location_status]
    df.drop(columns=columns_to_remove, inplace=True)

    # Check if there are any errors
    if df['Status'].notnull().any():
        error = True

    return error, df.to_dict(orient='records')

def prepare_filtered_stock_data(data, stock_data):
    """
    Prepare a dictionary to filter the DataFrame based on the given data.

    Args:
        data (list): A list of dictionaries containing the data to be filtered.

    Returns:
        dict: A dictionary containing the column names and conditions to filter the DataFrame.

    """
    query_string = ''
    stock_status = data.get('Stock Status')
    stock_status = str(reverse_stock_choice_mapping.get(stock_status)) if stock_status else 1
    query_string = f"status == {stock_status}"
    sku_code = data.get(sku_code_const)
    if sku_code:
        query_string += f" & sku__sku_code == '{sku_code}'"
    location = data.get('Location')
    if location:
        query_string += f" & location__location == '{location}'"
    batch_number_ref = data.get(batch_ref_key) if data.get(batch_ref_key) else None
    if batch_number_ref:
        batch_query = f"(batch_detail__batch_no == '{batch_number_ref}' | batch_detail__batch_reference == '{batch_number_ref}')"
        query_string += f" & {batch_query}"

    filtered_df = stock_data.query(query_string)
    return filtered_df
    

def stock_validation(data_list, locations, warehouse):
    """
    Validate stock data based on given SKU codes, location, batch number/reference, and stock status combination.

    Args:
        data_list (list): A list of dictionaries containing stock data.
        sku_codes (list): A list of SKU codes to filter the stock data.
        warehouse (object): The warehouse object to filter the stock data.

    Returns:
        tuple: A tuple containing the following values:
            - error (bool): Indicates if there was an error during stock validation.
            - data_list (list): The original list of dictionaries containing stock data.
            - cycle_creation_data (list): A list of stock records that passed the validation.

    """
    error = False
    cycle_creation_data = []
    duplicate_validation_list = set()
    stock_data = pd.DataFrame(StockDetail.objects.filter(location__location__in = locations, sku__user = warehouse.id, quantity__gt = 0).exclude(
        location__zone__segregation__in=['inbound_staging', 'outbound_staging']
    ).exclude(
        location__zone__storage_type__in =  ['wip_area', 'replenishment_staging_area', 'nte_staging_area']
    ).values(
        'sku__sku_code', 'location__location', 'batch_detail__batch_no', 'batch_detail__batch_reference', 'status',
        'sku_id', 'batch_detail_id', 'location_id').distinct())
    stock_data = stock_data.replace({np.nan: None})
    
    for data in data_list:
        data['Status'] = []
        stock_status = data.get('Stock Status','')
        if stock_status:
            reversed_stock_status = str(reverse_stock_choice_mapping.get(stock_status))
            if stock_status and stock_status not in reverse_stock_choice_mapping:
                data['Status'].append('Invalid Stock Status')
            elif reversed_stock_status == '2':
                data['Status'].append('Cycle Count cannot be created for Consumed Stock')
        filtered_df = prepare_filtered_stock_data(data, stock_data)
        if filtered_df.empty:
            data['Status'].append("Stock Not Found for this given SKU, Location Batch Number/Ref and Stock Status Combination.")
        else:
            for record in filtered_df.to_dict(orient='records'):
                record_tuple = (record['sku__sku_code'], record['location__location'], record['batch_detail__batch_no'], record['batch_detail__batch_reference'], record['status'])
                if record_tuple not in duplicate_validation_list:
                    duplicate_validation_list.add(record_tuple)
                    cycle_creation_data.append(record)
                else:
                    data['Status'].append("Duplicate Entry Found for this given SKU, Location Batch Number/Ref and Stock Status Combination.")

    df = pd.DataFrame(data_list)
    if df['Status'].apply(lambda x: len(x) > 0).any():
        error = True

    return error, data_list, cycle_creation_data


def existing_cycle_count_validation(cycle_creation_data, locations, warehouse, location_wise_zone_mapping={}):
    """
    Validates the existing cycle count data based on the provided data list.

    Args:
        data_list (list): A list of dictionaries containing the data to be validated.
        sku_codes (list): A list of SKU codes to filter the existing cycle count data.
        warehouse (object): The warehouse object to filter the existing cycle count data.

    Returns:
        tuple: A tuple containing the error flag and the updated data list.
            - error (bool): True if there are validation errors, False otherwise.
            - data_list (list): The updated data list with error messages added.

    """
    new_cycle_count_data = []
    # Fetch existing cycle count data from the database
    existing_cycle_count_data = CycleCount.objects.filter(
        location__location__in=locations,
        sku__user=warehouse.id,
        status=1
    ).values(
        'sku__sku_code',
        'location__location',
        'batch_detail__batch_no',
        'batch_detail__batch_reference',
        'stock_status',
        'employee'
    ).distinct()

    # Dictionary to store existing cycle count data
    existing_batch_records = {}
    for record in existing_cycle_count_data:
        existing_batch_records[(record['sku__sku_code'], record['location__location'], record['batch_detail__batch_no'], record['stock_status'])] = record['employee']
    
    sku_zones_dict = defaultdict(list)
    cycle_count_sku_codes = []
    # Iterate over data_list and update with error messages if necessary
    for item in cycle_creation_data:
        sku_code = item.get('sku__sku_code')
        location = item.get('location__location')
        batch_number_ref = item.get('batch_detail__batch_no')
        stock_status = item.get('status')
        cycle_count_sku_codes.append(sku_code)
        zone_id = location_wise_zone_mapping.get(location)
        sku_zones_dict[zone_id].append(sku_code)

        # Check if the record exists in the existing cycle count data
        if (sku_code, location, batch_number_ref, int(stock_status)) not in existing_batch_records:
            new_cycle_count_data.append(item)
    
    return new_cycle_count_data, cycle_count_sku_codes, sku_zones_dict

def get_sku_codes_list(data_to_integrate):
    """
    Extracts SKU codes and locations from the data to be integrated.
    Args:
        data_to_integrate (list): A list of dictionaries containing the data to be integrated.
    Returns:
        tuple: A tuple containing two lists:
            - sku_codes: A list of SKU codes extracted from the data.
            - locations: A list of locations extracted from the data.
    """
    sku_codes, locations = [], []
    for data in data_to_integrate:
        if data.get(sku_code_const):
            sku_codes.append(data.get(sku_code_const))
        if data.get('Location'):
            locations.append(data.get('Location'))
        
    return sku_codes, locations


def validate_create_cycle_count_form(warehouse, data_list):
    """
    Validates the create cycle count form.

    Args:
        warehouse (str): The name of the warehouse.
        data_list (list): A list of data to be validated.

    Returns:
        tuple: A tuple containing the validation status ('Success' if successful),
               the updated data list, and the cycle creation data.
    """
    cycle_count_sku_codes, sku_zones_dict = [], defaultdict(list)
    cycle_creation_data = []
    sku_codes, locations = get_sku_codes_list(data_list)

    # Location Master Dict
    location_master_dict, _, _,location_wise_zone_mapping, _= get_location_master_dict(warehouse, locations=locations)

    # GET SKU Details
    sku_details_dict, _ = get_sku_master_dict(warehouse, sku_codes)

    status, data_list = sku_location_validation(data_list, sku_details_dict, location_master_dict)
    if status:
        return data_list, data_list, cycle_creation_data, cycle_count_sku_codes, sku_zones_dict

    status, data_list, cycle_creation_data = stock_validation(data_list, locations, warehouse)
    if status:
        return data_list, data_list, cycle_creation_data, cycle_count_sku_codes, sku_zones_dict

    cycle_creation_data, cycle_count_sku_codes, sku_zones_dict = existing_cycle_count_validation(cycle_creation_data, locations, warehouse, location_wise_zone_mapping)

    return 'Success', data_list, cycle_creation_data, cycle_count_sku_codes, sku_zones_dict

def create_cycle_count(request , warehouse, cycle_creation_data, sku_codes, sku_zones_dict):
    """
    Create cycle count records in the database.

    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (Warehouse): The warehouse object.
        cycle_creation_data (list): A list of dictionaries containing cycle count data.
        sku_codes (list): A list of SKU codes.
        sku_zones_dict (dict): A dictionary containing SKU codes mapped to zones.

    Returns:
        str: The result of the operation. Returns 'Success' if the cycle count records were created successfully,
        or 'Failed to Upload' if an error occurred.

    Raises:
        Exception: If an error occurs during the creation of cycle count records.

    """
    try:
        bulk_cycle_objs = []
        for data in cycle_creation_data:
            data_dict = {}
            data_dict['account_id'] = warehouse.userprofile.id
            data_dict['sku_id'] = data.get('sku_id')
            data_dict['location_id'] = data.get('location_id')
            data_dict['seen_quantity'] = 0
            data_dict['run_type'] = 'unscheduled'
            data_dict['batch_detail_id'] = data.get('batch_detail_id')
            data_dict['stock_status'] = data.get('status')
            data_dict['status'] = 1
            data_dict['json_data'] = {
                'generated_user': request.username, 'generation_source': 'User Generated', 
                'source_of_generation': 'Uploads', 'is_cycle_count': "true"
            }
            bulk_cycle_objs.append(CycleCount(**data_dict))

        #Grouping Cycle ID based on sku and location
        bulk_cycle_objs = group_cycle_objs_based_on_sku_and_location(warehouse, bulk_cycle_objs)

        #Bulk Cycle Count Creation
        if bulk_cycle_objs:
            CycleCount.objects.bulk_create_with_rounding(bulk_cycle_objs)

        #Inventory Callback
        filters = {
            'sku_codes': sku_codes,
            "zones_data": sku_zones_dict
        }
        webhook_integration_3p(warehouse.id, "cycle_count", filters)

        return 'Success'
    
    except Exception as e:
        log_message = (("Cycle Count Generation Failed for Username %s, data %s and error message %s") % (
            str(request.username), str(cycle_creation_data), str(e)
        ))
        log.info(log_message)
        return 'Failed to Upload'
    
def batch_creation_upload(request, warehouse, data_list, extra_params=None):
    """
    Handles the batch creation upload request.
    Args:
        request: The HTTP request object.
        warehouse: The warehouse object.
        data_list: The list of data for batch creation upload.
        extra_params: Additional parameters (default: None).
    Returns:
        HttpResponse: The HTTP response indicating the success of the operation.
    """
    log.info("Request Batch Creation Upload user {}, extra params {}".format(warehouse.username, extra_params))

    data_list = rename_dict_keys(BATCH_CREATION_EXCEL_MAPPING, data_list)
    status, data_list = validate_cycle_count_form(request, warehouse, data_list, batch_creation = True, extra_params=extra_params)
    if status != 'Success':
        status = rename_dict_keys(BATCH_CREATION_EXCEL_MAPPING, status, reverse=True)
        return status
    try:
        log_message = (("Request Inventory Creation Upload Username %s, data %s ") % (
            str(request.username), str(data_list)
        ))
        log.info(log_message)
        extra_params.update({'batch_creation': "true"}) if extra_params else {'batch_creation': "true"}
        response = generate_and_submit_cycle_count(request, warehouse, data_list, extra_params = extra_params)
        if isinstance(response, list):
            return response

    except Exception as e:
        log_message = (("Batch Creation Upload Failed for Username %s, data %s and error message %s") % (
            str(request.username), str(data_list), str(e)
        ))
        log.info(log_message)
    return HttpResponse('Success')

def confirm_cycle_count_form(warehouse, extra_params={}):
    '''
    Create Cycle Count Form
    '''
    log.info("Request Create Cycle Count Form user {}, extra params {}".format(warehouse.username, extra_params))
    return CONFIRM_CYCLE_COUNT_HEADERS

def confirm_cycle_count_upload(request, warehouse, data_list, extra_params={}):
    """
    Handles the confirm cycle count upload request.
    Args:
        request: The HTTP request object.
        warehouse: The warehouse object.
        data_list: The list of data for cycle count confirmation upload.
        extra_params: Additional parameters (default: {}).
    Returns:
        str: The result of the operation. Returns 'Success' if the cycle count confirmation upload was successful,
        or a list of error data rows if there were validation errors.
    """
    log.info("Request for confirm cycle count Upload user {}, extra params {}".format(warehouse.username, extra_params))
    data_list = rename_dict_keys(CONFIRM_CYCLE_COUNT_MAPPING, data_list)
    sku_codes,_ = get_sku_codes(data_list)
    sku_details_dict, _ = get_sku_master_dict(warehouse, sku_codes)
    status, data_list = validate_sku_code_availability(data_list, sku_details_dict)
    if status:
        return data_list
    status, data_list = validate_cycle_sku_batch_duplication(data_list)
    if status:
        return data_list
    status, data_list = validate_cycle_id_existence(data_list)
    if status:
        return data_list
    status, data_list, confirm_cycle_count_data = validate_cycle_count_existence(data_list, warehouse)
    if status:
        return data_list
    try:
        log_message = (("Request Confirm Cycle Count Upload Generation Username %s, data %s ") % (
            str(request.username), str(confirm_cycle_count_data)
        ))
        log.info(log_message)
        if confirm_cycle_count_data:
            inv_request = RequestFactory()
            inv_request.method= 'POST'
            inv_request.POST = confirm_cycle_count_data
            inv_request.user = request
            inv_request.META = {'HTTP_REFERER': extra_params.get('base_url')}
            inv_request.warehouse = warehouse
            submit_cycle_count(inv_request)
    except Exception as e:
        log_message = (("Cycle Count Upload Generation Failed for Username %s, data %s and error message %s") % (
            str(request.username), str(data_list), str(e)
        ))
        log.info(log_message)
    return "Success"

def validate_sku_code_availability(data_list, sku_details_dict):
    """
    Validate SKU codes in the data list against the provided SKU details dictionary.
    Args:
        data_list (list): A list of dictionaries containing SKU codes to validate.
        sku_details_dict (dict): A dictionary containing SKU details.
    Returns:
        tuple: A tuple containing:
            - error (bool): True if there are validation errors, False otherwise.
            - data_list (list): The updated data list with error messages added.
    """
    error = False
    for data in data_list:
        sku_code = data.get('sku_code')
        if not sku_code:
            data['Status'] = 'SKU Code is mandatory'
            error = True
        if sku_code not in sku_details_dict:
            data['Status'] = 'Invalid SKU Code'
            error = True
    return error, data_list

def validate_cycle_sku_batch_duplication(data_list):
    """
    Validate for duplicate entries in the cycle count data based on SKU code, batch number/reference, and cycle count ID.
    Args:
        data_list (list): A list of dictionaries containing cycle count data.
    Returns:
        tuple: A tuple containing:
            - error (bool): True if there are duplicate entries, False otherwise.
            - data_list (list): The updated data list with error messages added.
    """
    # Check for duplicate entries based on SKU code, batch number/reference, and cycle count ID
    error = False
    validation_check_list = []
    for data in data_list:
        cycle = data.get('cycle')
        sku_code = data.get('sku_code')
        batch_no = data.get('batch_no_or_reference')
        if (sku_code, batch_no, cycle) not in validation_check_list:
            validation_check_list.append((sku_code, batch_no, cycle))
        else:
            data['Status'] = 'Duplicate Entry found for this Cycle Count Id, SKU Code and Batch Number/Ref Combination'
            error = True
    return error, data_list

def validate_cycle_id_existence(data_list):
    """
    Validate that each entry in the data list has a cycle count ID.
    Args:
        data_list (list): A list of dictionaries containing cycle count data.
    Returns:
        tuple: A tuple containing:
            - error (bool): True if any entry is missing a cycle count ID, False otherwise.
            - data_list (list): The updated data list with error messages added.
    """
    # Check if cycle count ID is present in each entry
    error = False
    for data in data_list:
        cycle = data.get('cycle')
        if not cycle:
            data['Status'] = 'Cycle Count Id is mandatory'
            error = True
    return error, data_list

def validate_cycle_count_existence(data_list, warehouse):
    """
    Validate the existence of cycle counts based on cycle count ID, SKU code, and batch number/reference.
    Args:
        data_list (list): A list of dictionaries containing cycle count data.
        warehouse (Warehouse): The warehouse object to filter the cycle counts.
    Returns:
        tuple: A tuple containing:
            - error (bool): True if any entry is invalid, False otherwise.
            - data_list (list): The updated data list with error messages added.
            - cycle_id_seen_quantity_dict (dict): A dictionary mapping cycle count IDs to seen quantities and reasons.
    """
    # feching cycle count confugured reasons for validation
    configured_reasons = get_return_reasons(warehouse, 'inv_adj_reasons')
    configured_reasons = list(configured_reasons.values())
    cycle_ids = [data.get('cycle') for data in data_list]
    available_cycle_counts = CycleCount.objects.filter(cycle__in=cycle_ids, sku__user = warehouse.id, status = 1).values(
        'id', 'cycle', 'sku__sku_code', 'batch_detail__batch_no', 'batch_detail__batch_reference','stock_status')
    cycle_count_batch_no_dict = {}
    cycle_count_batch_ref_dict = {}
    reasons_dict = {}
    batch_ref_wise_stock_status_dict, batch_no_wise_stock_status_dict = {}, {}
    cycle_id_seen_quantity_dict = {"source": "Uploads"}
    error = False
    for cycle_count in available_cycle_counts:
        cycle_count_batch_no_dict[(cycle_count['cycle'], cycle_count['sku__sku_code'], cycle_count['batch_detail__batch_no'])] = cycle_count['id']
        cycle_count_batch_ref_dict[(cycle_count['cycle'], cycle_count['sku__sku_code'], cycle_count['batch_detail__batch_reference'])] = cycle_count['id']
        batch_ref_wise_stock_status_dict[(cycle_count['cycle'], cycle_count['sku__sku_code'], cycle_count['batch_detail__batch_reference'])] = cycle_count.get('stock_status',None)
        batch_no_wise_stock_status_dict[(cycle_count['cycle'], cycle_count['sku__sku_code'], cycle_count['batch_detail__batch_no'])] = cycle_count.get('stock_status',None)

    for data in data_list:
        cycle = int(data.get('cycle'))
        sku_code = data.get('sku_code')
        batch_no = data.get('batch_no_or_reference') if data.get('batch_no_or_reference') else None
        if (cycle, sku_code, batch_no) not in cycle_count_batch_no_dict and (cycle, sku_code, batch_no) not in cycle_count_batch_ref_dict:
            data['Status'] = 'Cycle Count not found for this Cycle Count Id, SKU Code and Batch Number/Ref Combination'
            error = True
        else:
            if not data.get('seen_quantity'):
                data['Status'] = 'Seen Quantity is mandatory'
                error = True
            if data.get('seen_quantity'):
                try:
                    seen_quantity = float(data.get('seen_quantity'))
                    if seen_quantity < 0:
                        data['Status'] = 'Invalid Seen Quantity'
                        error = True
                except ValueError:
                    data['Status'] = 'Invalid Seen Quantity'
                    error = True
            reason = data.get('reason', '')
            # validating reasons in the request
            if not reason:
                data['Status'] = 'Reason is Mandatory'
                error = True
            elif reason not in configured_reasons:
                data['Status'] = 'Invalid Reason'
                error = True
            if (cycle, sku_code, batch_no) in batch_no_wise_stock_status_dict:
                stock_status = batch_no_wise_stock_status_dict[(cycle, sku_code, batch_no)]
            elif (cycle, sku_code, batch_no) in batch_ref_wise_stock_status_dict:
                stock_status = batch_ref_wise_stock_status_dict[(cycle, sku_code, batch_no)]
            else:
                stock_status = None
            if stock_status:
                if stock_status not in reverse_stock_choice_mapping.values():
                    data['Status'] = 'Invalid Stock Status'
                    error = True
                elif stock_status == 2:
                    data['Status'] = 'Cycle Count cannot be created for Consumed Stock'
                    error = True
            id = cycle_count_batch_no_dict.get((cycle, sku_code, batch_no)) or cycle_count_batch_ref_dict.get((cycle, sku_code, batch_no))
            if not error:
                cycle_id_seen_quantity_dict[id] = str(data.get('seen_quantity'))
                reasons_dict[str(id)] = data.get('reason')
    cycle_id_seen_quantity_dict['reasons'] = reasons_dict

    return error, data_list, cycle_id_seen_quantity_dict