import pandas as pd
from json import loads

from django.http import JsonResponse
from django.db.models import F

from inventory.models import SerialNumber
from core.models import SKUMaster, UserPrefixes

from core_operations.views.common.main import WMSListView, get_incremental


from core_operations.views.common.main import get_company_id, get_company_admin_user

SERIAL_NUMBER_FIELDS = ['stock_id', 'location_id', 'batch_detail_id', 'lpn_number', 'status', 'json_data']

class SerialNumberMixin:
    def __init__(self, user,  warehouse, request_data):
        self.user = user
        self.warehouse = warehouse
        self.request_data = request_data
        self.company = get_company_id(self.warehouse, obj=True)
        self.final_dict = {
            'errors': [],
            'serial_numbers': [],
            'message': 'Serial numbers created/updated successfully!',
            'status': 200
        }
        self.default_values = ["id", "creation_date", "updation_date", "warehouse_id", "serial_number",
                               "sku_id", "stock_id", "location_id", "batch_detail_id", "lpn_number", "status", "json_data"
                            ]

    def get_company_existing_serial_numbers(self, serial_numbers_or_items, exclude_inactive=True, items_format=False):
        """
        Get serial numbers that exist in other warehouses of the same company.

        Args:
            serial_numbers_or_items (list): Either a list of serial numbers or a list of items with serial_numbers field
            exclude_inactive (bool): Whether to exclude inactive serial numbers (status=0)
            items_format (bool): Whether the input is in items format (list of dicts with serial_numbers field)

        Returns:
            dict: A dictionary mapping (serial_number, sku_code) to warehouse_name
        """
        company_existing = {}

        if not serial_numbers_or_items or not (company_id := (self.company.id if self.company else None)):
            return company_existing

        # Extract serial numbers from items if needed
        if items_format:
            # Get all SKU IDs from items
            sku_code_map = self.get_sku_codes_from_request_data(serial_numbers_or_items)

            # Create a mapping of serial numbers to their SKU codes
            all_serial_numbers = []
            serial_sku_map = {}

            for item in serial_numbers_or_items:
                sku_id = item.get('sku_id')
                sku_code = sku_code_map[sku_id]
                for serial_number in item.get('serial_numbers', []):
                    all_serial_numbers.append(serial_number)
                    serial_sku_map[(serial_number, sku_code)] = item

            serial_numbers = all_serial_numbers
        else:
            serial_numbers = serial_numbers_or_items
            serial_sku_map = None

        # Build the query
        query = SerialNumber.objects.filter(
            company_id=company_id,
            serial_number__in=serial_numbers
        ).exclude(warehouse_id=self.warehouse.id)

        # Optionally exclude inactive serial numbers
        if exclude_inactive:
            query = query.exclude(status=0)

        # Execute the query and build the mapping
        for serial_number, sku_code, warehouse_name in query.values_list(
            'serial_number', 'sku__sku_code', 'warehouse__username'
        ):
            company_existing[(serial_number, sku_code)] = warehouse_name

        # If we have items, mark them with duplicate serial numbers
        if items_format and serial_sku_map:
            for (serial_number, sku_code), item in serial_sku_map.items():
                if (serial_number, sku_code) in company_existing:
                    item['Status'].append(
                        f'Serial number {serial_number} for SKU {sku_code} already exists in warehouse {company_existing[(serial_number, sku_code)]}'
                    )

        return company_existing

    def get_sku_codes_from_request_data(self, items):
        """
        Get SKU codes from the request data.

        Args:
            items (list): A list of items containing the SKU IDs.

        Returns:
            dict: A dictionary mapping SKU IDs to their corresponding SKU codes.
        """
        sku_ids = {item.get('sku_id') for item in items if item.get('sku_id')}
        if not sku_ids:
            return {}
        return {sku['id']: sku['sku_code'] for sku in
                SKUMaster.objects.filter(id__in=sku_ids).values('id', 'sku_code')}

    def create_serial_numbers(self):
        """
        Creates serial numbers based on the provided filters and returns a dictionary containing the results.

        Returns:
            dict: A dictionary containing the following keys:
                - 'errors': A list of errors encountered during the creation of serial numbers.
                - 'serial_numbers': A list of serial numbers that were successfully created.
                - 'message': A message indicating the status of the operation.
                - 'status': The HTTP status code indicating the success or failure of the operation.
        """
        self.final_dict['message'] = 'Serial numbers created successfully!'
        self.errors = []

        self.validate_and_prepare_filters()
        self.get_existing_serial_numbers()

        self.new_serial_number_objs, self.serial_numbers = [], set()
        self.validate_and_prepare_serial_numbers()

        if self.errors:
            self.final_dict.update({
                'errors': self.errors,
                'message': 'Failed to create serial numbers',
                'status': 400
            })
            return self.final_dict

        if self.new_serial_number_objs:
            SerialNumber.objects.bulk_create(self.new_serial_number_objs)

        self.final_dict['serial_numbers'] = list(self.serial_numbers)
        return self.final_dict

    def update_serial_numbers(self):
        """
        Updates the serial numbers based on the provided filters and details.

        Returns:
            dict: A dictionary containing the updated serial numbers information.
                  If the update is successful, the dictionary will have the following keys:
                  - 'message': A success message.
                  - 'errors': An empty list.
                  If the update fails, the dictionary will have the following keys:
                  - 'errors': A list of errors encountered during the update process.
                  - 'message': A failure message.
                  - 'status': An HTTP status code indicating the failure.
        """
        self.final_dict['message'] = 'Serial numbers updated successfully!'
        self.errors = []

        self.validate_and_prepare_filters()
        self.get_serial_number_objs()

        if self.serial_numbers_df.empty:
            self.final_dict.update({
                'errors': ['Serial numbers not found'],
                'message': 'Failed to update serial numbers',
                'status': 400
            })
            return self.final_dict

        self.validate_and_update_serial_number_details()
        if self.errors:
            self.final_dict.update({
                'errors': self.errors,
                'message': 'Failed to update serial numbers',
                'status': 400
            })
            return self.final_dict

        SerialNumber.objects.bulk_update(self.serial_numbers_df['object'].tolist(), SERIAL_NUMBER_FIELDS)
        self.final_dict.update({
            'serial_numbers': list(self.updated_serial_numbers),
            'message': 'Serial numbers updated successfully!',
            'status': 200
        })
        return self.final_dict

    def get_serial_numbers(self):
        """
        Retrieves serial numbers based on the provided filters and exclude filters.

        Returns:
            dict: A dictionary containing the retrieved serial numbers, status, and message.
        """
        filters = self.request_data.get('filters', {})
        exclude_filters = self.request_data.get('exclude_filters', {})
        values = self.request_data.get('values', self.default_values)

        values_dict = {
            "sku_code": F("sku__sku_code"),
            "batch_number": F("batch_detail__batch_no"),
            "location_name": F("location__location"),
            "sku_description": F("sku__sku_desc"),
        }

        self.final_dict['data'] = list(SerialNumber.objects.filter(warehouse_id=self.warehouse.id, **filters).exclude(**exclude_filters).values(*values, **values_dict))
        if not self.final_dict['data']:
            self.final_dict['message'] = 'No serial numbers found'
            self.final_dict['status'] = 400
        else:
            self.final_dict['message'] = 'Serial numbers retrieved successfully!'
            self.final_dict['status'] = 200
        return self.final_dict

    def validate_and_prepare_filters(self):
        '''
        {
            'items': [
                {
                    'sku_id': 1,
                    'location_id': 123,
                    'batch_detail_id': 124,
                    'lpn_number': '123456',
                    'stock_id': 4,
                    'status': 1,
                    'json_data': {},
                    'serial_numbers': ['123456', '123457', '123458'],
                }
            ],
        }
        '''
        items = self.request_data.get('items', [])
        if not items:
            self.errors.append('items is required')

        if not isinstance(items, list):
            self.errors.append('items should be a list')

        self.request_serial_numbers = set()
        for item in items:
            serial_numbers = item.get('serial_numbers', [])
            if not isinstance(serial_numbers, list):
                self.errors.append('serial_numbers should be a list')
                break

            self.request_serial_numbers.update(serial_numbers)

        self.request_serial_numbers = list(self.request_serial_numbers)

    def validate_create_data(self):
        """
        Validates the create data for serial numbers.

        Returns:
            None
        """
        self.errors = []
        items = self.request_data
        if not items:
            self.errors.append('items is required')

        if not isinstance(items, list):
            self.errors.append('items should be a list')

        if self.errors:
            return self.errors

        self.request_serial_numbers = set()

        for item in items:
            item['Status'] = []
            if not item.get('sku_id'):
                item['Status'].append('sku_id is required')
            if not isinstance(item.get('serial_numbers', []), list):
                item['Status'].append('serial_numbers should be a list')
            else:
                self.request_serial_numbers.update(item.get('serial_numbers', []))

        self.get_serial_number_objs()
        items = self.validate_serial_status(items)

        return items

    def validate_serial_status(self, items):
        """
        Validates the serial status for the given items.

        Args:
            items (list): A list of items containing the serial numbers.

        Returns:
            list: A list of items with the updated serial status.
        """
        unique_serial_numbers = set()
        is_serial_df = True
        if self.serial_numbers_df.empty:
            is_serial_df = False

        # Check for duplicates across warehouses within the same company
        # This will automatically extract serial numbers, get SKU codes, and mark items with duplicate serial numbers
        self.get_company_existing_serial_numbers(items, items_format=True)

        # Continue with the rest of the validation
        for item in items:
            serial_numbers = item.get('serial_numbers', [])
            sku_id = item.get('sku_id')
            for serial_number in serial_numbers:
                if is_serial_df:
                    srn_objs = self.serial_numbers_df[
                        (self.serial_numbers_df['serial_number'] == serial_number) & (self.serial_numbers_df['sku_id'] == sku_id)
                    ]
                    for index, srn_record in srn_objs.iterrows():
                        if srn_record['status'] == 1 and srn_record['location_id'] != item.get('location_id'):
                            item['Status'].append(f'Serial number {serial_number} is already active in Other Location')
                            break
                        if srn_record['status'] == 2:
                            item['Status'].append(f'Serial number {serial_number} is already InTransit')
                            break

                unique_serial_number = (serial_number, sku_id)
                if unique_serial_number in unique_serial_numbers:
                    item['Status'].append(f'Duplicate serial number {serial_number}')
                    break
                unique_serial_numbers.add(unique_serial_number)

        return items



    def get_existing_serial_numbers(self):
        """
        Retrieve existing serial numbers for the given warehouse and create new serial numbers.
        Also check for serial numbers that exist in other warehouses of the same company.

        Returns:
            A list of tuples containing the existing serial numbers and their corresponding SKU IDs.
        """
        # Get serial numbers in the current warehouse
        self.existing_serial_numbers = list(SerialNumber.objects.filter(
            warehouse_id=self.warehouse.id,
            serial_number__in=self.request_serial_numbers
        ).values_list('serial_number', 'sku_id'))

        # Also check for serial numbers in other warehouses of the same company using our common function
        self.company_existing = self.get_company_existing_serial_numbers(self.request_serial_numbers)

    def validate_and_prepare_serial_numbers(self):
        """
        Validates and prepares the serial numbers for processing.

        This method checks for existing and duplicate serial numbers, prepares the serial number objects,
        and adds the created serial numbers to the set of created serial numbers.
        It also checks for serial numbers that exist in other warehouses of the same company.

        Returns:
            None
        """
        items = self.request_data.get('items', [])
        self.account_id = self.warehouse.userprofile.id

        existing_serial_numbers, duplicate_serial_numbers, unique_serial_numbers = set(), set(), []

        # Use the company-level existing serial numbers map
        company_existing = self.company_existing

        # Get all SKU IDs from items
        sku_code_map = self.get_sku_codes_from_request_data(items)

        # Process each item
        for item in items:
            sku_id = item.get('sku_id')
            sku_code = sku_code_map.get(sku_id, '')

            for serial_number in item.get('serial_numbers', []):
                # Check for company-level duplicates using direct key lookup
                if (serial_number, sku_code) in company_existing:
                    warehouse_name = company_existing[(serial_number, sku_code)]
                    self.errors.append(f'Serial number {serial_number} for SKU {sku_code} already exists in warehouse {warehouse_name}')
                    continue

                # Check for warehouse-level duplicates
                unique_serial_number = (serial_number, sku_id)
                if unique_serial_number in self.existing_serial_numbers:
                    existing_serial_numbers.add(serial_number)
                    continue

                # Check for duplicates in current batch
                if unique_serial_number in unique_serial_numbers:
                    duplicate_serial_numbers.add(serial_number)
                    continue

                # All checks passed, prepare the serial number
                unique_serial_numbers.append(unique_serial_number)
                self.prepare_serial_number_objects(item, serial_number)
                self.serial_numbers.add(serial_number)

        if existing_serial_numbers:
            self.errors.append(f'Serial numbers {", ".join(existing_serial_numbers)} already exist')
        if duplicate_serial_numbers:
            self.errors.append(f'Duplicate serial numbers {", ".join(duplicate_serial_numbers)}')

    def prepare_serial_number_objects(self, item, serial_number):
        """
        Prepare serial number objects for insertion into the database.

        Args:
            item (dict): A dictionary containing the details of the item.
            serial_number (str): The serial number of the item.

        Returns:
            None
        """
        self.new_serial_number_objs.append(SerialNumber(
            company_id=self.company.id,
            account_id = self.account_id,
            warehouse_id=self.warehouse.id,
            serial_number=serial_number,
            sku_id=item.get('sku_id'),
            stock_id=item.get('stock_id'),
            location_id=item.get('location_id'),
            batch_detail_id=item.get('batch_detail_id'),
            lpn_number=item.get('lpn_number'),
            status=item.get('status'),
            json_data=item.get('json_data', {})
        ))

    def get_serial_number_objs(self):
        """
        Retrieves the serial number objects based on the warehouse ID and requested serial numbers.

        Returns:
            A DataFrame containing the serial number objects.
        """
        self.serial_number_objs = SerialNumber.objects.filter(warehouse_id=self.warehouse.id, serial_number__in=self.request_serial_numbers)
        serial_number_data = list(self.serial_number_objs.values())
        self.serial_numbers_df = pd.DataFrame(serial_number_data)
        for serial_obj in list(self.serial_number_objs):
            self.serial_numbers_df.loc[self.serial_numbers_df['id'] == serial_obj.id, 'object'] = serial_obj

    def validate_and_update_serial_number_details(self):
        """
        Validates and updates the details of serial numbers.

        This method takes the request data, which should contain a list of items.
        Each item should have a list of serial numbers and a SKU ID.
        It iterates through each item and serial number, checks if the serial number exists in the serial_numbers_df DataFrame,
        and updates the corresponding stock details if found.

        If any serial numbers are not found, they are added to the errors list.

        Returns:
            None
        """
        items = self.request_data.get('items', [])
        serial_number_not_found, self.updated_serial_numbers = set(), set()

        for item in items:
            serial_numbers = item.get('serial_numbers', [])
            sku_id = item.get('sku_id')
            for serial_number in serial_numbers:
                srn_objs = self.serial_numbers_df[
                    (self.serial_numbers_df['serial_number'] == serial_number) & (self.serial_numbers_df['sku_id'] == sku_id)
                ]
                if srn_objs.empty:
                    serial_number_not_found.add(serial_number)
                    continue

                self.updated_serial_numbers.add(serial_number)
                for index, srn_record in srn_objs.iterrows():
                    for key in SERIAL_NUMBER_FIELDS:
                        if key in item:
                            if key == 'json_data':
                                srn_record['object'].json_data.update(item.get(key))
                            else:
                                setattr(srn_record['object'], key, item.get(key))
                    self.serial_numbers_df.loc[self.serial_numbers_df['id'] == srn_record['object'].id, 'object'] = srn_record['object']

        if serial_number_not_found:
            self.errors.append(f'Serial numbers {", ".join(serial_number_not_found)} not found')
            
    def generate_serial_numbers(self):
        """
        Generates unique serial numbers using company's counter and user's prefix.
        
        Returns:
            JsonResponse: Contains serial numbers list or error message with status code.
        """
        try:
            # Get count parameter from request data with default value of 1
            count = int(self.request_data.get('count', 1))
        
            # Get user's serial number prefix
            serial_number_prefix = UserPrefixes.objects.filter(
                user=self.warehouse.id, 
                type_name='serial_number_prefix'
            ).values_list("prefix", flat=True).first()
            
            # Check if prefix exists
            if not serial_number_prefix:
                return JsonResponse({
                    'error': 'Serial number prefix not configured for this user',
                    'status': 400
                }, status=400)
            company = get_company_admin_user(self.warehouse)
            # Get next available increment from company-level counter
            current_increment = get_incremental(
                warehouse=company,
                type_name='serial_number',
                default_val=1,
                increment=count
            )
            # Generate serial numbers
            serial_numbers = [
                f"{serial_number_prefix}{current_increment + i:012d}" 
                for i in range(count)
            ]
            # Return success response
            return JsonResponse({
                'status': 200,
                'message': 'Serial numbers generated successfully',
                'serial_numbers': serial_numbers,
                'count': count,
                'prefix': serial_number_prefix
            })
            
        except Exception as e:
            # Handle any unexpected errors
            return JsonResponse({
                'error': f'Failed to generate serial numbers: {str(e)}',
                'status': 500
            }, status=500)
        

class SerialNumberSet(WMSListView):
    def post(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = loads(self.request.body)

        serial_number_mixin_objs = SerialNumberMixin(self.user, self.warehouse, self.request_data)
        self.final_response = serial_number_mixin_objs.create_serial_numbers()
        return JsonResponse(self.final_response, status=self.final_response.get('status', 200))

    def put(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = loads(self.request.body)

        serial_number_mixin_objs = SerialNumberMixin(self.user, self.warehouse, self.request_data)
        self.final_response = serial_number_mixin_objs.update_serial_numbers()
        return JsonResponse(self.final_response, status=self.final_response.get('status', 200))

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = {'filters': self.request.GET.dict()}

        serial_number_mixin_objs = SerialNumberMixin(self.user, self.warehouse, self.request_data)
        self.final_response = serial_number_mixin_objs.get_serial_numbers()
        return JsonResponse(self.final_response, status=self.final_response.get('status', 200))
    
def generate_serial(request):
    """
    Endpoint to generate serial numbers
    """
    user = request.user
    warehouse = request.warehouse
    
    request_data = loads(request.body)
    
    # Create a SerialNumberMixin instance and call generate_serial_numbers
    serial_number_mixin = SerialNumberMixin(user, warehouse, request_data)
    return serial_number_mixin.generate_serial_numbers()