#package imports
import math
import pytz
from dateutil import parser
import traceback
import pandas as pd

from json import loads
from collections import OrderedDict, defaultdict

#django imports
from django.db.models import Q, Max
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.core.cache import cache

#inventory imports
from inventory.models import (
    InventoryAdjustment, LocationMaster, CycleCount,
    StockDetail, SerialNumberTransactionMapping,
    SerialNumberMapping, BatchDetail, INVENTORY_CHOICES
)
from inventory.views.cycle_count.cycle_count import generate_and_submit_cycle_count

#serial number functions
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

status_choices = dict(INVENTORY_CHOICES)

#core imports
from core.models import (
    SKUMaster, PurchaseApprovalConfig, MasterEmailMapping,
    UserIntegrationAPIS, UserIntegrationCalls, MiscDetailOptions
)

#inbound models
from inbound.models import(
    SupplierMaster, PurchaseApprovals
)

#wms base imports
from wms_base.wms_utils import (
    init_logger, CYCLE_COUNT_FIELDS
)
from wms_base.models import User

#core common functions
from core_operations.views.common.main import (
    get_misc_value, get_decimal_value, truncate_float,
    get_user_time_zone, get_uom_with_sku_code,
    get_local_date_known_timezone, newGenerateHashCodeForMail, scroll_data,
    WMSListView, get_user_attributes, frame_datatable_header_filter,
    get_warehouse, get_company_id, get_uom_decimals, get_incremental,
    get_multiple_misc_values
)
from core_operations.views.integration.run_3pintegration import async_run_3p_int_func
from core_operations.views.integration.integration import webhook_integration_3p

import json
import datetime
#Global Constants
update_msg_const = 'Updated Successfully'

log = init_logger('logs/inventory_adjustment.log')

def fetch_serial_numbers_for_cycle_id(cycle_id, serial_df):
    filtered_serials = serial_df[(serial_df['transact_id'] == cycle_id)]
    if filtered_serials.empty:
        return []
    serial_numbers = filtered_serials['serial_numbers'].tolist()[0]
    return serial_numbers

def get_inventory_adjustment(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, status):
    '''
    Get Inventory Adjustment Datatable
    '''
    
    #Filters from Request
    search_term, sort_by_column, modified_column_filters, batch_filter = get_filters_for_get_inventory_adjustment(request)

    values_list = get_required_inventory_adjustment_values()
    last_approved = {}
    #Misc Details
    inventory_approval_config = get_misc_value('enable_inventory_approval', warehouse.id)
    price_decimal = get_decimal_value(warehouse.id, price=1)
    decimal_limit = get_decimal_value(warehouse.id)
    company_id = get_company_id(warehouse)
    #GET Inventory Adjustment Objects
    if inventory_approval_config == 'true':
        (users_with_approval_permission, approval_dict,
        last_approved, approval_pending, max_pa_range,
        inventory_adjustment )= process_inventory_adjustment_approval(warehouse, values_list, search_term, sort_by_column, modified_column_filters, batch_filter)

    else:
        inventory_adjustment = get_inventory_data_object(
            warehouse, values_list, search_term, sort_by_column, modified_column_filters, batch_filter
        )

    if request.GET.get('count', '') == 'true':
        temp_data['count'] = inventory_adjustment.count()
        return

    data_id, confirm_button_permission, skip_loop = 1, True, False
    final_list = []
    sku_uoms = get_sku_uoms(inventory_adjustment, start_index, stop_index)
    uom_decimals = get_uom_decimals(company_id, uom_codes = sku_uoms)
    available_restricted_stock_status = get_misc_value('restricted_adjustment_status', warehouse.id).split(',')
    inventory_adjustment = inventory_adjustment[start_index:stop_index]
    cycle_ids = [inv_item.get('cycle_id') for inv_item in inventory_adjustment]
    serial_df = get_existing_serials(warehouse, cycle_ids)

    for inv_item in inventory_adjustment:
        last_approved = last_approved if last_approved else {}
        available_levels = 0
        to_be_approved_by = ''
        current_level = ''
        min_value, max_value = 0, 0
        confirm_button_permission =  False
        cycle_id, cycle_json_data = inv_item.get('cycle_id'), inv_item.get('cycle__json_data', {}) or {}
        serial_numbers = fetch_serial_numbers_for_cycle_id(cycle_id, serial_df)
        adjusted_qty = get_adjusted_quantity(cycle_json_data, inv_item)
        carton_id = cycle_json_data.get('carton_id', '')
        price = cycle_json_data.get('price', 0)
        adjusted_value = inv_item.get('cycle__adjusted_value',0)
        if inventory_approval_config == 'true':
            available_levels, current_level = [], []
            request_user = request.user.email
            current_value = math.ceil(adjusted_value)

            if abs(current_value) > max_pa_range:
                if request_user in users_with_approval_permission:
                    confirm_button_permission = True

                skip_loop = False
                current_level = '-'
                to_be_approved_by = "Adjustment value is not defined in Approval master"
                available_levels = []

            elif request_user in users_with_approval_permission:
                (current_level,to_be_approved_by,available_levels,
                 confirm_button_permission,min_value, max_value, skip_loop )= get_approval_details_if_request_user_has_permission(
                      approval_dict, current_value, available_levels, approval_pending, cycle_id, request_user, current_level,to_be_approved_by,
                      confirm_button_permission
                 )

            else:
                current_level, to_be_approved_by, available_levels, confirm_button_permission = get_approval_details(
                    approval_pending, adjusted_value, approval_dict, available_levels, cycle_id
                )
                if current_level in ['',None]:
                    continue

        if skip_loop:
            continue

        inventory_adjustment_id = inv_item.get('id')
        supplier_name = inv_item.get('cycle__supplier__name', None)
        checkbox = "<input type='hidden' name='%s' value='%s'>" % (cycle_id, cycle_id)

        serial_flag, adjustment_type, reason, batch_no, display_batch_key = get_required_flags(inv_item)

        #Round off Based on UOM / Deciaml Limit Config
        uom_decimal_limit = uom_decimals.get(inv_item.get('cycle__sku__measurement_type'))
        round_off = uom_decimal_limit or decimal_limit
        discrepancy_qty = truncate_float(adjusted_qty, round_off)
        physical_quantity = truncate_float(inv_item.get('cycle__seen_quantity',0), round_off)
        total_quantity = truncate_float(inv_item.get('cycle__quantity',0), round_off)
        final_dict = {
            '': checkbox,
            'batch_display_key': display_batch_key,
            'zone': inv_item.get('cycle__location__zone__zone',''),
            'sub_zone': inv_item.get('cycle__location__sub_zone__zone',''),
            'location': inv_item.get('cycle__location__location',''),
            'check_digit': inv_item.get('cycle__location__check_digit',''),
            'sku_code': inv_item.get('cycle__sku__sku_code',''),
            'sku_size': inv_item.get('cycle__sku__sku_size',''),
            'batch_no': batch_no,
            'serial_numbers': serial_numbers,
            'batch_wac': inv_item.get('cycle__batch_detail__json_data__batch_wac',''),
            'price' : truncate_float(float(price), price_decimal) if price else '',
            'carton_id': carton_id,
            'sku_desc': inv_item.get('cycle__sku__sku_desc',''),
            'total_quantity': total_quantity,
            'physical_quantity': physical_quantity,
            'discrepancy_qty': discrepancy_qty,
            'discrepancy_value': truncate_float(adjusted_value, price_decimal),
            'serial_flag': serial_flag,
            'supplier_name':supplier_name,
            'Inventory_adjustment_id': inventory_adjustment_id,
            'reason': reason,
            'adjustment_type': adjustment_type,
            'DT_RowClass': 'results', 'id': data_id,
            'cycle': cycle_id,
            'mrp': inv_item.get('cycle__batch_detail__mrp', 0),
            'cost_price': inv_item.get('cycle__sku__cost_price', 0),
            'serial_number': '',
            'attributes': inv_item.get('json_data__custom_fields', {}),
            'stock_status': status_choices.get(inv_item.get('cycle__stock_status'))
        }
        update_final_dict(final_dict, status_choices.get(inv_item.get('cycle__stock_status')), available_restricted_stock_status)

        final_dict = update_data_for_approval_flow(final_dict,inventory_approval_config, available_levels, 
                                                   cycle_id, to_be_approved_by, last_approved, current_level,
                                                   confirm_button_permission,min_value, max_value, inv_item,
                                                   reason)

        final_list.append(final_dict)
        data_id += 1

    temp_data['aaData'] = final_list

@get_warehouse
def get_mobile_inventory_adjustment(request, warehouse:User):
    '''
    API endpoint for Inventory Adjustment Mobile
    '''
    start = int(request.GET.get('start', 0))
    length = int(request.GET.get('length', 10))
    global_search = request.GET.get('global_search', '')
    order_index = request.GET.get('order_index', 0) 
    status = request.GET.get('filter', '')
    
    filter_fields = ['sku_code', 'batch_display_key', 'zone', 'location', 'sku_desc']
    column_filters = {field: request.GET[field].strip() for field in filter_fields if request.GET.get(field, '')}

    if column_filters:
        request.GET = request.GET.copy()
        request.GET['columnFilter'] = json.dumps(column_filters)
    
    try:
        temp_data = {'aaData': [], 'count': 0}
        get_inventory_adjustment(
            start_index=start,
            stop_index=start + length,
            temp_data=temp_data,
            global_search_term=global_search,
            order_term=order_index,
            col_num=0,
            request=request,
            warehouse=warehouse,
            status=status
        )
        temp_data['count'] = temp_data['count'] or len(temp_data['aaData'])

        return JsonResponse(temp_data)
    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'traceback': traceback.format_exc().split('\n')
        }, status=500)

def update_final_dict(final_dict, cycle_status, available_restricted_stock_status):
    final_dict.update({'restricted_stock_status': 1 if cycle_status in available_restricted_stock_status else 0})

def update_data_for_approval_flow(final_dict, inventory_approval_config, available_levels,
                                  cycle_id, to_be_approved_by, last_approved, current_level,
                                  confirm_button_permission, min_value, max_value, inv_item,
                                  reason):
    if inventory_approval_config == 'true':
        final_level = 'level'+str(len(available_levels)-1) if available_levels else '-'
        final_dict.update({
            'cycle': cycle_id,
            'To Be Approved By': to_be_approved_by,
            'Last Updated By': last_approved.get(cycle_id,''),
            'available_levels': available_levels,'current_level':current_level,
            'button_permission': confirm_button_permission,
            'Pending Level': "%s of %s"%(current_level,final_level),
            'min_value': min_value,
            'max_value': max_value,
            'remarks': inv_item.get('json_data__remarks') if inv_item.get('json_data__remarks') else {}
        })
        final_dict = update_disable_reason(current_level, reason, final_dict)
    return final_dict

def get_sku_uoms(inventory_adjustment, start_index, stop_index):
    sku_uoms = [inv_item.get('cycle__sku__measurement_type') for inv_item in inventory_adjustment[start_index:stop_index]]
    return sku_uoms

def get_adjusted_quantity(cycle_json_data, inv_item):
    adjusted_qty = cycle_json_data.get('adjusted_qty', 0)
    if not adjusted_qty:
        adjusted_qty = int(inv_item.get('cycle__seen_quantity')) - int(inv_item.get('cycle__quantity'))
    return adjusted_qty

def update_disable_reason(current_level, reason, final_dict):
    if current_level != 'level0' or (current_level == 'level0' and reason):
        final_dict.update({'disable_reason': True})
    return final_dict

def get_required_flags(inv_item):
    serialized = inv_item.get('cycle__sku__enable_serial_based') if inv_item.get('cycle__sku__enable_serial_based') else 0
    serial_flag = True if serialized == 1 else False
    adjustment_type = inv_item.get('cycle__run_type', '')
    if adjustment_type:
        adjustment_type = adjustment_type.capitalize()

    reason = inv_item.get('reason', '')
    if inv_item.get('cycle__json_data', {}).get('upload_reason', ''):
        reason = inv_item['cycle__json_data']['upload_reason']
        _, reason = (
            reason.split(":", 1) + [reason]
        )[:2] if ":" in reason else ("", reason)

    batch_no, batch_reference = inv_item.get('cycle__batch_detail__batch_no'), inv_item.get('cycle__batch_detail__batch_reference')
    display_batch_key = batch_reference or batch_no

    return serial_flag, adjustment_type, reason, batch_no, display_batch_key


def get_filters_for_get_inventory_adjustment(request):

    '''
    Process Request Data and return required Filters
    '''

    filtered_dict = {
        'location':'cycle__location__location', 'zone': 'cycle__location__zone__zone',
        'sub_zone': 'cycle__location__sub_zone__zone', 'sku_code': 'cycle__sku__sku_code',
        'sku_size': 'cycle__sku__sku_size', 'batch_display_key': 'cycle__batch_no',
        'carton_id': 'cycle__json_data', 'sku_desc': 'cycle__sku__sku_desc',
        'reason': 'reason', 'cycle': 'cycle_id', 'total_quantity': 'cycle__quantity',
        'physical_quantity': 'cycle__seen_quantity', 'discrepancy_qty': 'cycle__quantity',
        'discrepancy_value': 'cycle__quantity','supplier_name': 'cycle__supplier__name',
        'adjustment_type': 'cycle__run_type',
    }

    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'creation_date', '1'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = filtered_dict.get(request.GET.get('sort_by_column'))

    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_header_filter(loads(column_headers))

    modified_column_filters = {}
    batch_key = {}
    for key, value in column_filters.items():
        if key == "batch_display_key":
            batch_key.update({"cycle__batch_detail__batch_no__icontains" : value})
            batch_key.update({"cycle__batch_detail__batch_reference__icontains" : value})
        else:
            modified_column_filters.update({filtered_dict.get(key)+"__icontains" : value})

    batch_filter = Q()
    if batch_key:
        batch_filter = Q(**batch_key, _connector=Q.OR)

    sort_type = request.GET.get('sort_type')
    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    return search_term, sort_by_column, modified_column_filters, batch_filter


def get_required_inventory_adjustment_values():
    return [
        'id','cycle__location__location', 'cycle__sku__sku_code',
        'cycle__sku__sku_desc', 'cycle__sku__sku_size',
        'cycle__sku__cost_price', 'cycle__batch_detail__mrp',
        'cycle__quantity', 'cycle__seen_quantity',
        'cycle__json_data','cycle__run_type','cycle_id',
        'cycle__sku__cost_price', 'cycle__quantity',
        'cycle__seen_quantity','cycle__sku__enable_serial_based',
        'cycle__sku__batch_based', 'cycle__sku__skuwac__average_price_rt',
        'cycle__adjusted_value','json_data__remarks','reason', 'json_data__custom_fields',
        'cycle__batch_detail__batch_no', 'cycle__batch_detail__batch_reference',
        'cycle__stock_status', 'cycle__sku__measurement_type',
        'cycle__location__sub_zone__zone', 'cycle__location__zone__zone',
        'cycle__batch_detail__json_data__batch_wac', 'cycle__location__check_digit'
    ]

def process_inventory_adjustment_approval(warehouse, values_list, search_term, sort_by_column, modified_column_filters, batch_filter):
    (users_with_approval_permission, approval_dict,
     approval_pending, last_approved) = get_inventory_adjustment_approval_data(warehouse)
    cycle_count_ids = list(approval_pending.keys())
    modified_column_filters.update({'cycle_id__in': cycle_count_ids})

    max_pa_range = PurchaseApprovalConfig.objects.filter(
        user_id=warehouse.id,
        approval_type='INV',
        purchase_type='INV'
    ).aggregate(Max('max_Amt'))['max_Amt__max']

    inventory_adjustment = get_inventory_data_object(
        warehouse, values_list, search_term, sort_by_column, modified_column_filters, batch_filter
    )
    return (
        users_with_approval_permission, approval_dict, last_approved, 
        approval_pending, max_pa_range, inventory_adjustment
    )


def get_inventory_adjustment_approval_data(warehouse):
    '''Returns users in approval matrix,approval dict and pending approvals'''
    approval_pending_dict, last_approved = {}, {}
    pa_filter = {
        'user_id': warehouse.id,
        'approval_type': 'INV',
        'purchase_type': 'INV'
    }
    (approval_dict, name,
        users_with_approval_permission
    ) = prepare_inventory_approval_dict(warehouse, pa_filter)
    approval_pending = PurchaseApprovals.objects.filter(pr_user_id=warehouse.id,approval_type='INV').\
                values('purchase_number','validated_by', 'level','status')
    for each_pending in approval_pending.iterator():
        if each_pending.get('status') != 'approved':
            approval_pending_dict[each_pending.get('purchase_number')] = {
                'level': each_pending.get('level'),
                'validated_by': each_pending.get('validated_by')
            }
        else:
            last_approved[each_pending.get('purchase_number')] = each_pending.get('validated_by')

    return users_with_approval_permission, approval_dict, approval_pending_dict, last_approved

def prepare_inventory_approval_dict(warehouse, pa_filter):
    '''Returns a dict with min,max as key and levels and users as value'''
    approval_dict = {}
    approval_objs = PurchaseApprovalConfig.objects.filter(**pa_filter).\
        values('min_Amt','max_Amt','id','level','name')
    
    config_name = approval_objs[0]['name'] if approval_objs.exists() else ''
    master_ids_list = [val.get('id') for val in approval_objs.iterator()]

    master_email_obj = MasterEmailMapping.objects.filter(
        user_id=warehouse.id,master_type='inventory_approvals_conf',master_id__in=master_ids_list
    ).values('master_id','email_id')

    users_with_approval_permission = set([val.get('email_id') for val in master_email_obj])

    for each_approval_obj in approval_objs.iterator():
        key = (each_approval_obj.get('min_Amt'),each_approval_obj.get('max_Amt'))
        approval_dict.setdefault(key,[])
        emails = []
        for each_email_obj in master_email_obj.iterator():
            if str(each_approval_obj.get('id')) == each_email_obj.get('master_id'):
                emails.append(each_email_obj.get('email_id'))
        approval_dict[key].append({
            'level': each_approval_obj.get('level'),
            'users': emails
        })
    return approval_dict, config_name, users_with_approval_permission

def get_inventory_data_object(warehouse, values_list, search_term, sort_by_column, modified_column_filters, batch_filter):
    if search_term:
        inventory_adjustment = InventoryAdjustment.objects.filter(
                            cycle__status='2', cycle__sku__user=warehouse.id, **modified_column_filters
                            ).filter(Q(cycle__cycle__icontains=search_term) |
                                     Q(cycle__sku__wms_code__icontains=search_term) | 
                                     Q(cycle__sku__sku_desc__icontains=search_term) |
                                     Q(cycle__location__zone__zone__icontains=search_term) |
                                     Q(cycle__location__location__icontains=search_term) |
                                     Q(creation_date__regex=search_term)
                             ).filter(batch_filter) \
                               .values(*values_list) \
                               .order_by(sort_by_column)
    elif sort_by_column:
        inventory_adjustment = InventoryAdjustment.objects.filter(
            cycle__status='2', cycle__sku__user=warehouse.id, **modified_column_filters
            ).filter(batch_filter).values(*values_list).order_by(sort_by_column)
    else:
        inventory_adjustment = InventoryAdjustment.objects.filter(
            cycle__status='2', cycle__sku__user=warehouse.id, **modified_column_filters
        ).filter(batch_filter).values(*values_list).order_by('-id')
    return inventory_adjustment


def get_approval_details(approval_pending, adjusted_value, approval_dict, available_levels, cycle_id):
    current_level = approval_pending.get(cycle_id,{}).get('level')
    to_be_approved_by = approval_pending.get(cycle_id,{}).get('validated_by')
    current_value = math.ceil(adjusted_value)
    for key,val in approval_dict.items():
        if abs(current_value) >= key[0] and abs(current_value) <= key[1]:
            for each_level in val:
                available_levels.append(each_level.get('level'))
    confirm_button_permission = False
    return current_level, to_be_approved_by, available_levels, confirm_button_permission

def get_approval_details_if_request_user_has_permission(
        approval_dict, current_value, available_levels, approval_pending, cycle_id, request_user, current_level, to_be_approved_by,
        confirm_button_permission
    ):
    skip_loop = True
    min_value, max_value = 0, 0
    for key,val in approval_dict.items():
        if abs(current_value) >= key[0] and abs(current_value) <= key[1]:

            for each_level in val:
                available_levels.append(each_level.get('level'))
                if approval_pending.get(cycle_id):
                    users = approval_pending[cycle_id]['validated_by'].split(',')
                    if approval_pending[cycle_id]['level'] == each_level.get('level') and request_user in users:
                        skip_loop = False
                        confirm_button_permission = True
                        current_level = approval_pending[cycle_id]['level']
                        to_be_approved_by = approval_pending[cycle_id]['validated_by']
                        min_value = key[0]
                        max_value = key[1]

    return current_level, to_be_approved_by, available_levels, confirm_button_permission, min_value, max_value, skip_loop

@get_warehouse
def insert_inventory_adjustment(request, warehouse:User):
    restricted_stock_statuses = get_misc_value('restricted_adjustment_status', warehouse.id).split(',')
    request_data = loads(request.body)
    if not request_data or not isinstance(request_data, dict):
        return JsonResponse({'message': ["Invalid Request"]}, status=400)
    
    supplier_inventory = get_misc_value('stock_supplier_id', warehouse.id)

    stock_status = request_data.get('source_status')
    stock_status = str(stock_status) if str(stock_status) and stock_status not in [None, '', "None"] else None
    if stock_status and dict(INVENTORY_CHOICES).get(int(stock_status)) in restricted_stock_statuses:
        return JsonResponse({'message': ["Inventory Adjustment is not allowed for this stock status"]}, status=400)


    sku_code = request_data.get('sku_code')
    quantity = request_data.get('quantity', 0)
    reason = request_data.get('reason')
    location = request_data.get('location')

    misc_reasons = dict(
         MiscDetailOptions.objects.filter(
            misc_detail__misc_type = 'inv_adj_reasons', misc_detail__user = warehouse.id,
            misc_key = reason
        ).values_list('misc_key', 'misc_value')
    )

    reason = misc_reasons.get(reason, reason)

    unit_price = request_data.get('price', 0)
    if unit_price == 'none':
        unit_price = request_data.get('price_input', 0)

    batch_no = request_data.get('batch_number', '')
    carton_id = request_data.get('carton_id', '')
    mrp = request_data.get('mrp', '')
    weight = request_data.get('weight', '')
    supplier_id = request_data.get('supplier_id')
    cycle_type = request_data.get('adjustment_type', 'unscheduled')

    if supplier_inventory and  supplier_id:
        if ':' in supplier_id:
            supplier_id, _ = supplier_id.split(':')
    else:
        supplier_id = False

    if not batch_no:
        batch_no = False

    error_status = []

    #Quantity Check
    error_status = quantity_check(quantity, error_status, cycle_type, stock_status)

    if error_status:
        return JsonResponse({'message': error_status}, status=400)

    #get inventory adjustment custom fields
    custom_fields_dict = get_custom_fields_dict(warehouse, request_data)

    #Supplier Details
    supplier_dict = get_supplier_details(warehouse, supplier_id)

    #SKU Details
    sku_details_dict = get_sku_attributes_data(warehouse, sku_code)

    #Location Details
    location_dict, zone = get_location_details(warehouse, location)

    error_status, batch_obj = validate_sku_location_related_data(
        warehouse, sku_details_dict, sku_code, error_status, batch_no, zone
    )

    if error_status:
        return JsonResponse({'message': error_status}, status=400)

    inv_adj_data = {
        'batch_no': batch_no, 'sku_code': sku_code,'stock_status': stock_status, 'weight': weight,
        'carton_id': carton_id, 'mrp': mrp, 'unit_price': unit_price, 
        'supplier_dict': supplier_dict, 'sku_details_dict': sku_details_dict,
        'supplier_id': supplier_id, 'location': location, 'reason': reason, 'location_dict': location_dict,
        'quantity': quantity,  'custom_fields_dict': custom_fields_dict, 'cycle_type': cycle_type,
        'batch_obj': batch_obj
    }
    inv_dict, inv_adj_filters = prepare_inventory_adjustment_data(inv_adj_data)

    #Inventory Adjustment Check
    inv_obj = InventoryAdjustment.objects.filter(
        cycle__sku__user = warehouse.id, cycle__status = 2, **inv_adj_filters
    )
    if inv_obj.exists():
        return JsonResponse({'message': ["Record Already Exists in Inventory Adjustment"]}, status=400)

    try:
        log_message = (("Request Inventory Adjustment Username %s, data %s ") % (
            str(request.user.username), str(inv_dict)
        ))
        log.info(log_message)
        response = generate_and_submit_cycle_count(
            request, warehouse, [inv_dict], cycle_type = 'Inventory Adjustment'
        )
        cycle_ids = []
        if isinstance(response, dict):
            cycle_ids = response.get('cycle_ids', [])

        return JsonResponse({'message': ["Added Successfully"], 'cycle_ids': cycle_ids}, status=200)

    except Exception as e:
        log_message = (("Inventory Adjustment Failed for Username %s, data %s and error message %s") % (
            str(request.user.username), str(inv_dict), str(e)
        ))
        log.info(log_message)
        return JsonResponse({'message': ["Failed"]}, status=400)

def quantity_check(quantity, error_status, cycle_type, stock_status):
    if stock_status == '2':
        error_status.append('Inventory Adjustment cannot be done for Consumed Stock')
        return error_status
    try:
        quantity = float(quantity)
        if quantity < 0:
            error_status.append('Invalid Quantity')
    except ValueError:
        error_status.append('Invalid Quantity')

    #Restrict Scrap to Blocked Stock
    if cycle_type.lower() == 'scrap' and float(stock_status) != 6:
        error_status.append("Scrap adjustment can only be performed for blocked stock")

    return error_status

def get_custom_fields_dict(warehouse, request_data):

    #get inventory adjustment custom fields
    inventory_adjustment_custom_fields = list(get_user_attributes(
        warehouse, 'inventory_adjustment'
    ).values_list('attribute_name', flat=True).distinct())

    custom_fields_dict = {}
    for attribute_name in inventory_adjustment_custom_fields:
        field_name = 'attr_' + attribute_name
        custom_field_value = request_data.get(field_name, '')
        if custom_field_value:
            custom_fields_dict[attribute_name] = custom_field_value

    return custom_fields_dict

def get_supplier_details(warehouse, supplier_id):
    supplier_dict = {}
    if supplier_id:
        supplier_dict = dict(SupplierMaster.objects.filter(
            user=warehouse.id, supplier_id = supplier_id
        ).values_list('supplier_id','id'))
    return supplier_dict

def get_sku_attributes_data(warehouse, sku_code):
    sku_master_dict = SKUMaster.objects.filter(
        user = warehouse.id, sku_code = sku_code
    ).values('id','sku_code','batch_based', 'enable_serial_based').distinct()
    sku_details_dict = {}
    for sku in sku_master_dict:
        sku_details_dict[sku.get('sku_code')] = {
            'sku_id': sku.get('id'), 'batch_based': sku.get('batch_based'),
            'serial_sku': sku.get('enable_serial_based')
        }
    return sku_details_dict

def validate_sku_location_related_data(warehouse, sku_details_dict, sku_code, error_status, batch_no, zone):
    batch_obj = None
    if sku_details_dict:
        #Serialized SKU
        serial_sku = sku_details_dict.get(sku_code,{}).get('serial_sku',0)
        if serial_sku:
            error_status.append(
                "Cycle count adjustments for serial based SKU can \
                not be done through UI , Please use uploads."
            )

        #Batch Based Validation
        batch_based = sku_details_dict.get(sku_code,{}).get('batch_based',0)
        if batch_based and not batch_no:
            error_status.append("Please select batch for inventory adjustment")

        batch_obj = BatchDetail.objects.filter(warehouse = warehouse.id, batch_no = batch_no, sku__sku_code = sku_code)
        if batch_based and batch_no and not batch_obj:
            error_status.append("Invalid Batch Details")

        #Restricting Adjustments for WIP Zones
        if zone and zone == 'WIPZONE':
            error_status.append("Cycle Count Adjustments Cannot be done in WIPZone")
    else:
        error_status.append("Invalid SKU Details")

    return error_status, batch_obj

def get_location_details(warehouse, location):
    location_dict, zone = {}, ''
    location_objs = list(LocationMaster.objects.filter(
        zone__user = warehouse.id, location = location
    ).values('location', 'id', 'zone__zone'))

    for loc in location_objs:
        location_dict[loc.get('location')] = loc.get('id')
        zone = loc.get('zone__zone')
    return location_dict, zone

def prepare_inventory_adjustment_data(inv_adj_data):
    '''
    Prepare Inventory Adjustment Data
    '''
    sku_code, location, sku_details_dict, location_dict, batch_no = (
        inv_adj_data.get('sku_code'), inv_adj_data.get('location'),
        inv_adj_data.get('sku_details_dict'),
        inv_adj_data.get('location_dict'),
        inv_adj_data.get('batch_no')
    )

    stock_status, weight, carton_id, mrp, unit_price, supplier_dict, supplier_id, batch_obj = (
        inv_adj_data.get('stock_status'), inv_adj_data.get('weight'),
        inv_adj_data.get('carton_id'), inv_adj_data.get('mrp'),
        inv_adj_data.get('unit_price'), inv_adj_data.get('supplier_dict'),
        inv_adj_data.get('supplier_id'), inv_adj_data.get('batch_obj')
    )

    sku_id = sku_details_dict.get(sku_code).get('sku_id')
    inv_dict = {
        'sku': [sku_id, sku_code],
        'sku_code': sku_code,
        'location': [location_dict.get(location), location],
        'reason': inv_adj_data.get('reason'),
        'quantity': str(float(inv_adj_data.get('quantity'))),
        'batch_no': batch_no, 'manufactured_date': False,
        'expiry_date': False, 'mrp': False,
        'unit_price': False, 'weight': False,
        'custom_fields': inv_adj_data.get('custom_fields_dict'),
        'adjustment_type': inv_adj_data.get('cycle_type'),
        'sku_id': sku_id
    }

    inv_adj_filters = {'cycle__sku__sku_code': sku_code, 'cycle__location__location': location}

    batch_based = sku_details_dict.get(sku_code,{}).get('batch_based',0)

    inv_dict.update({'batch_based': batch_based, 'batch_detail_id': ''})
    if batch_obj:
        inv_dict['batch_detail_id'] = batch_obj[0].id

    if batch_no:
        inv_dict.update({'batch_no': batch_no})
        inv_adj_filters.update({'cycle__batch_detail__batch_no': inv_dict.get('batch_no')})

    if stock_status:
        inv_dict.update({'stock_status': stock_status})
        inv_adj_filters.update({'cycle__stock_status': stock_status})
    if weight:
        inv_dict.update({'weight': weight})
        inv_adj_filters.update({'cycle__json_data__weight': inv_dict.get('weight')})
    if carton_id:
        inv_dict.update({'carton_id': carton_id})
        inv_adj_filters.update({'cycle__json_data__carton_id': carton_id})
    if mrp:
        inv_dict.update({'mrp': mrp})
        inv_adj_filters.update({'cycle__json_data__mrp': inv_dict.get('mrp')})
    if unit_price:
        inv_dict.update({'unit_price': unit_price})
        inv_adj_filters.update({'cycle__json_data__unit_price': inv_dict.get('unit_price')})
    if supplier_dict:
        inv_dict.update({'supplier': [supplier_dict.get(supplier_id), supplier_id]})
        inv_adj_filters.update({'cycle__supplier__supplier_id': supplier_id})

    return inv_dict, inv_adj_filters

def process_request_data_for_delete(request_data, inventory_approval_config, request_user, time_zone):
    cycle_ids, cycle_id_wise_rejected_user = [], {}
    current_date = datetime.datetime.now()
    current_date = get_local_date_known_timezone(time_zone, current_date)
    for key, value in request_data.GET.items():
        cycle_ids.append(key)
        if inventory_approval_config == 'true' and value:
            cycle_id_wise_rejected_user[key] =  {value: {"approved_by": "", 
                                                                 "approved_at": "",
                                                                 "rejected_by": request_user.username, 
                                                                 "rejected_at": current_date}}
    return cycle_ids, cycle_id_wise_rejected_user

def update_rejected_user_data(cycle_id_wise_rejected_user, cycle_ids, warehouse):
    inventory_adjustments = InventoryAdjustment.objects.filter(
        cycle__sku__user=warehouse.id,
        cycle_id__in=cycle_ids
    )
    inventory_adjustment_map = {str(ia.cycle_id): ia for ia in inventory_adjustments}
    objects_to_update = []
    for cycle_id, rejected_user_data in cycle_id_wise_rejected_user.items():
        inventory_adjustment = inventory_adjustment_map.get(cycle_id)
        if not inventory_adjustment.json_data:
            inventory_adjustment.json_data = {
                'approved_users_data': rejected_user_data }
        else:
            json_data = inventory_adjustment.json_data
            if 'approved_users_data' in json_data:
                    json_data['approved_users_data'].update(rejected_user_data)
            else:
                json_data['approved_users_data'] = rejected_user_data
                inventory_adjustment.json_data = json_data
        objects_to_update.append(inventory_adjustment)

    # Perform a bulk update
    InventoryAdjustment.objects.bulk_update(
        objects_to_update,
        fields=['json_data']
    )

def update_serial_status(warehouse, cycle_ids):
    serial_filter = {'filters':{'reference_type': 'cycle_count', 'transact_id__in': cycle_ids}}
    serial_mixin = SerialNumberTransactionMixin(None, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    if not existing_serials.get('data'):
        return
    for each_data in existing_serials.get('data', []):
        each_data['status'] = 3
        if each_data['transact_type'] == 'add':
            each_data['serial_status'] = 0
        else:
            each_data['serial_status'] = 1
    
    sn_transact_dict = {
        'reference_type': 'cycle_count',
        'items': existing_serials.get('data')
    }
    SerialNumberTransactionMixin(None, warehouse, sn_transact_dict).create_update_sn_transaction()

@get_warehouse
def delete_inventory_adjustment(request, warehouse:User):
    try:
        cycle_ids, sku_codes = [], []
        sku_zones_dict = defaultdict(list)    
        inventory_approval_config = get_misc_value('enable_inventory_approval',warehouse.id)
        time_zone = get_user_time_zone(warehouse)
        cycle_ids, cycle_id_wise_rejected_user = process_request_data_for_delete(request, inventory_approval_config, request.user, time_zone)
        if inventory_approval_config == 'true':
            update_rejected_user_data(cycle_id_wise_rejected_user, cycle_ids, warehouse)
        if cycle_ids:
            cycle_objs = CycleCount.objects.filter(id__in = cycle_ids, sku__user = warehouse.id).select_related('sku', 'location','location__zone')
            for cycle_obj in cycle_objs:
                sku_codes.append(cycle_obj.sku.sku_code)
                zone_id = cycle_obj.location.zone.id
                sku_zones_dict[zone_id].append(cycle_obj.sku.sku_code)
                
            cycle_objs.update(status = 'inventory_delete')
            update_serial_status(warehouse, cycle_ids)
        if inventory_approval_config == 'true':
            PurchaseApprovals.objects.filter(pr_user_id=warehouse.id,approval_type='INV',purchase_number__in=cycle_ids).delete()
        
        #Inventory Callback
        filters = {
            'sku_codes': sku_codes,
            "zones_data": sku_zones_dict
        }
        webhook_integration_3p(warehouse.id, "cycle_count", filters)

        return HttpResponse('Deleted Successfully')
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(("Webhook failed for %s in delete inventory adjustment and params are %s and error statement is %s") % (str(warehouse.username), str(cycle_ids), str(e)))

def get_restricted_cycle_ids(warehouse,cycle_ids):
    restricted_stock_statuses = get_misc_value('restricted_adjustment_status', warehouse.id).split(',')
    available_stock_statuses = dict(INVENTORY_CHOICES)
    restricted_stock_statuses_id = [key for key, value in available_stock_statuses.items() if value in restricted_stock_statuses]
    restricted_ids = CycleCount.objects.filter(id__in = cycle_ids, sku__user = warehouse.id, stock_status__in = restricted_stock_statuses_id).values_list('id', flat=True)
    restricted_ids = [str(id) for id in restricted_ids]
    return restricted_ids

def check_and_update_cache(cycle_ids, add=False, delete=False):
    error_message = ''
    for cycle_id in cycle_ids:
        if add:
            cache_status = cache.add(str(cycle_id), 'True', timeout=60*10)
            if not cache_status:
                error_message = "Adjustment is in progress, Please wait"
                return error_message
        elif delete:
            cache.delete(str(cycle_id))
    return error_message

def get_existing_serials(warehouse, cycle_ids):
    values_list = ['sku_code', 'location', 'batch_number', 'serial_number', 'transact_id', 'transact_type']
    serial_filter = {'filters':{'reference_type': 'cycle_count', 'transact_id__in': cycle_ids}}
    serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    serial_df = pd.DataFrame(existing_serials.get('data', []))
    if serial_df.empty:
        serial_df = pd.DataFrame(columns=values_list)
    return serial_df

@get_warehouse
def confirm_inventory_adjustment(request, warehouse:User):
    '''Confirm Inventory Adjustment'''
    inventory_update  = []
    quantity_dict = {}

    #Get Misc Values
    misc_types = ['enable_inventory_approval', 'allow_negative_inventory', 'allow_negative_inventory_in_zones', 'enable_empty_location_cycle_count']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    inventory_approval_config, allow_negative_inventory, allow_negative_inventory_in_zones, enable_empty_location_cycle_count = (
        misc_dict.get('enable_inventory_approval', 'false'),
        misc_dict.get('allow_negative_inventory', 'false'),
        misc_dict.get('allow_negative_inventory_in_zones', 'false'),
        misc_dict.get('enable_empty_location_cycle_count', 'false')
    )
    allow_negative_inventory_in_zones = (
        allow_negative_inventory_in_zones.split(',') if allow_negative_inventory_in_zones != 'false' else []
    )

    request_data = loads(request.body)
    confirm_adj_permission = True
    internal_adjustment = False
    time_zone = get_user_time_zone(warehouse)
    skip_callback = request_data.get('skip_callback', False) or False
    #Process Input Data
    cycle_ids, cycle_ids_to_adjust, remove_cycle, reason_dict, remarks_data, confirm_adj_permission, internal_adjustment, cycle_id_wise_approvel_user = process_request_data(
        request_data, inventory_approval_config, confirm_adj_permission, internal_adjustment, request.user, time_zone
    )

    error_message = check_and_update_cache(cycle_ids, add=True)
    if error_message:
        return JsonResponse({'message': 'Adjustment is in Progress, Please wait'}, status = 400)

    if inventory_approval_config == 'true' and not internal_adjustment:
        url = request.META.get('HTTP_REFERER', '')
        if not confirm_adj_permission:
            error_message = check_and_update_cache(cycle_ids, delete=True)
            return JsonResponse(
               {'message': 'This user does not have permission to adjust inventory'},
                status = 400
            )
        if remove_cycle:
            cycle_ids = list(set(cycle_ids)-set(remove_cycle))

        cycle_to_approve = list(set(cycle_ids)-set(cycle_ids_to_adjust))
        cycle_ids = cycle_ids_to_adjust
        error = approve_inventory_adjustment(cycle_to_approve, cycle_ids, request_data,\
                request.user, warehouse, url
            )
        error_message = check_and_update_cache(cycle_to_approve, delete=True)
        if error:
            error_message = check_and_update_cache(cycle_ids, delete=True)
            return JsonResponse({'message': error}, status = 400)

    #Update Inventory Approval Remarks
    bulk_update_inventory_approval_remarks(warehouse, request.user, remarks_data, reason_dict, cycle_id_wise_approvel_user)
    serial_df = get_existing_serials(warehouse, cycle_ids)
    cycle_objects = CycleCount.objects.filter(
        id__in=cycle_ids, sku__user=warehouse.id
    ).exclude(status='3')
    if not cycle_objects.exists():
        if remove_cycle and cycle_to_approve:
            error_message = check_and_update_cache(cycle_ids, delete=True)
            return JsonResponse(
                {'message': 'Successfully updated entries with adjusted value in approval range'},
                status = 200
            )
        elif remove_cycle:
            error_message = check_and_update_cache(cycle_ids, delete=True)
            return JsonResponse(
               {'message': 'Adjustment Value for the selected entries are not defined in Approval Master'},
                status = 400
            )
        else:
            error_message = check_and_update_cache(cycle_ids, delete=True)
            if inventory_approval_config == 'true':
                return JsonResponse({'message': update_msg_const}, status = 200)
            return JsonResponse({'message': 'No Records to Update'}, status = 400)

    quantity_dict = update_stocks(warehouse, cycle_objects, quantity_dict, serial_df, allow_negative_inventory, allow_negative_inventory_in_zones)

    # Update Status in Cyclecount
    cycle_objects.update(status='3')

    #GET Inventory Adjustment Records to Update
    inv_objects, inventory_update = get_inv_adj_records_to_update(
        request, warehouse, cycle_ids, reason_dict,
        quantity_dict, inventory_update, enable_empty_location_cycle_count
    )

    # Update reason,adjusted_quantity & json_data in InventoryAdjustment
    InventoryAdjustment.objects.bulk_update_with_rounding(
        inventory_update, ['reason','json_data','adjusted_quantity'], batch_size=100
    )    

    if not skip_callback:
        #Preparing Inventory Adjustment Call Back Data
        inv_adj_call_back_data, sku_codes, sku_zones_dict = get_inventory_adjustment_call_back_data(inv_objects, warehouse)
        try:
            inventory_adjustment_call_back_3p_integration(warehouse, inv_adj_call_back_data, sku_codes, sku_zones_dict)
        except Exception as e:
            log.info('Exception Raised on inventory Adjustment CallBack :  ' + warehouse.username + " error"+ str(e))

    error_message = check_and_update_cache(cycle_ids, delete=True)
    if remove_cycle:
        return JsonResponse(
           {'message': 'Successfully updated entries with adjusted value in approval range'}, 
            status = 200
        )
    return JsonResponse({'message': update_msg_const}, status = 200)

def process_request_data(request_data, inventory_approval_config, confirm_adj_permission, internal_adjustment, request_user, time_zone):
    reason_dict, remarks_data = {}, {}
    cycle_ids, cycle_ids_to_adjust, remove_cycle = [], [], []
    cycle_id_wise_approvel_user = {}
    current_date = datetime.datetime.now()
    current_date = get_local_date_known_timezone(time_zone, current_date)
    for key, value in request_data.items():
        if key == 'data':
            for k, v in value.items():
                if k in ['', None]:
                    continue
                cycle_ids.append(k)
                reason_dict[k] = v.get('reason', '')
                internal_adjustment = bool(v.get('internal_adjustment'))
                remarks_data[k] = v.get('remarks', '')
            continue

        if inventory_approval_config == 'true' and not internal_adjustment:
            if key == 'button_permission':
                confirm_adj_permission = value
                continue

            available_levels = value.get('available_levels')
            current_level = value.get('current_level')
            remarks_data[key] = value.get('remarks', '')

            if current_level == '-' and available_levels:
                remove_cycle.append(key)
            elif current_level == f'level{len(available_levels)}':
                cycle_ids_to_adjust.append(key)

            if current_level:
                cycle_id_wise_approvel_user[key] = {current_level: {"approved_by": request_user.username, 
                                                                    "approved_at": current_date,
                                                                    "rejected_by": "", "rejected_at": ""}}

    return cycle_ids, cycle_ids_to_adjust, remove_cycle, reason_dict, remarks_data, confirm_adj_permission, internal_adjustment, cycle_id_wise_approvel_user

def fetch_and_prepare_serial_transaction_data(serial_transaction_data, cycle, serial_df, count, difference):
    batch_no = cycle['batch_detail__batch_no'] or ''
    transact_type = 'add' if difference > 0 else 'remove'
    serial_status = 1 if difference > 0 else 0
    filtered_serials = serial_df[(serial_df['transact_id'] == cycle['id'])]
    if filtered_serials.empty:
        return serial_transaction_data
    
    serial_numbers = filtered_serials['serial_numbers'].tolist()[0]
    serial_transaction_data.append({
        'transact_id': cycle['id'],
        'transact_type': transact_type,
        'serial_numbers': serial_numbers,
        'sku_code': cycle['sku__sku_code'],
        'sku_id': cycle['sku_id'],
        'stock_id': count.id,
        'lpn_number': cycle['lpn_number'],
        'batch_number': batch_no,
        'batch_detail_id': cycle['batch_detail_id'],
        'location': cycle['location__location'],
        'location_id': cycle['location_id'],
        'status': 0,
        'serial_status': serial_status
    })

    return serial_transaction_data

def update_stocks(warehouse, cycle_objects, quantity_dict, serial_df, allow_negative_inventory, allow_negative_inventory_in_zones):
    cycle_count_list = list(cycle_objects.values(
        'id','seen_quantity','quantity','location_id','supplier_id',
        'sku_id','sku__sku_code', 'sku__enable_serial_based', 'json_data',
        'stock_status', 'batch_detail_id', 'batch_detail__batch_no',
        'location__location', 'lpn_number', 'location__zone__zone',
        'remarks'
        )
    )
    serial_transaction_data = []
    for cycle in cycle_count_list:
        cycleid = cycle.get('id','')
        json_data = cycle.get('json_data') or {}
        if json_data:
            difference = json_data.get("adjusted_qty", "")
        else:
            seen_quantity = cycle.get("seen_quantity", "")
            quantity = cycle.get("quantity", "")
            difference = seen_quantity - quantity

        if cycleid not in quantity_dict:
            quantity_dict[cycleid] = difference

        location_id = cycle.get('location_id','')
        zone = cycle.get('location__zone__zone','')
        sku_id = cycle.get('sku_id','')
        filter_dict = {'location_id':location_id, 'sku_id':sku_id, 'sku__user': warehouse.id}
        if cycle.get('stock_status') not in ['', None]:
            filter_dict.update({'status': cycle.get('stock_status')})
        if cycle.get('batch_detail_id'):
            filter_dict.update({'batch_detail_id': cycle.get('batch_detail_id')})
        if cycle.get('lpn_number'):
            filter_dict.update({'lpn_number': cycle.get('lpn_number')})

        location_count = StockDetail.objects.filter(**filter_dict).order_by('creation_date')
        for count in location_count:
            serial_transaction_data = fetch_and_prepare_serial_transaction_data(
                serial_transaction_data, cycle, serial_df, count, difference
            )
            if difference > 0:
                count.quantity += difference
                count.save()
                break
            elif difference < 0:
                if (count.quantity + difference) >= 0:
                    count.quantity += difference
                    count.save()
                    break
                elif (count.quantity + difference) <= 0:
                    difference += count.quantity
                    count.quantity = 0
                    count.save()
                if difference == 0:
                    break

        if allow_negative_inventory and difference < 0 and zone in allow_negative_inventory_in_zones and cycle.get('remarks') != 'CCVL':
            first_count = location_count.first()  # Get the first stock entry
            if first_count:
                first_count.quantity += difference
                first_count.save()

    if serial_transaction_data:
        serial_dict = {'reference_number': 'cycle_count', 'reference_type': 'cycle_count', 'items': serial_transaction_data}
        SerialNumberTransactionMixin(None, warehouse, serial_dict).create_update_sn_transaction()

    return quantity_dict

def get_inv_adj_records_to_update(request, warehouse, cycle_ids, reason_dict, quantity_dict, inventory_update, enable_empty_location_cycle_count='false'):
    misc_keys = list(reason_dict.values())
    misc_reasons = dict(
         MiscDetailOptions.objects.filter(
            misc_detail__misc_type = 'inv_adj_reasons', misc_detail__user = warehouse.id, 
            misc_value__in = misc_keys, status = 1
        ).values_list('misc_value','misc_key')
    )
    inv_objects = InventoryAdjustment.objects.filter(cycle_id__in=cycle_ids, cycle__sku__user=warehouse.id).select_related('cycle__location')
    cycle_objs = []
    if enable_empty_location_cycle_count == "true":
        cycle_objs = CycleCount.objects.filter(run_type="empty_location", location__zone__user=warehouse.id, status=1)
    for inventory in inv_objects:
        cycleid = inventory.cycle.id
        inv_reason = reason_dict.get(str(cycleid), '')
        reason_code, reason_value = '', inv_reason,
        if misc_reasons.get(inv_reason):
            reason_value = inv_reason
            reason_code = misc_reasons.get(inv_reason)

        inventory.reason = reason_value
        json_data = inventory.json_data if inventory.json_data else {}
        json_data.update({'request_user': request.user.username, 'reason_code': reason_code})
        inventory.json_data = json_data
        inventory.adjusted_quantity = quantity_dict.get(cycleid,0)
        inventory_update.append(inventory)

        # Update cycle counts for empty locations if applicable
        cycle_location = inventory.cycle.location
        if cycle_objs and cycle_location:
            empty_location_qs = cycle_objs.filter(location=cycle_location.id)
            if empty_location_qs.exists():
                empty_location_qs.update(status=3, remarks="System Updated", updation_date=datetime.datetime.now())

    return inv_objects, inventory_update


def inventory_adjustment_call_back_3p_integration(warehouse, inv_adjustment_data, sku_codes, sku_zones_dict):

    #Inventory Callback on Inventory Adjustment
    filters = {
        'sku_codes': sku_codes,
        "zones_data": sku_zones_dict
    }
    webhook_integration_3p(warehouse.id, "inventory_adjustment", filters)

    #Inventory Adjustment Callback
    int_apis = UserIntegrationAPIS.objects.filter(
        user_integration__user_id=warehouse.id, trigger='inventory_adjustment', status=1
    )
    data_dict = {}
    new_created_ids, integration_call_objs = [], []
    for int_api in int_apis:
        if int_api.data_format == 'callback':
            if inv_adjustment_data:
                data_dict = inv_adjustment_data
            new_data_dict = {
                'user_integrationapis_id': int_api.id,
                'api_data': data_dict,
                'status': 1, 'account_id': warehouse.userprofile.id
            }
            integration_call_objs.append(UserIntegrationCalls(**new_data_dict))

    if integration_call_objs:
        objs = UserIntegrationCalls.objects.bulk_create_with_rounding(integration_call_objs)
        if objs:
            new_created_ids = [obj.id for obj in objs]
             
    filters ={"id__in":new_created_ids}
    async_run_3p_int_func(filters)


def approve_inventory_adjustment(cycle_to_approve, cycle_to_adjust, request_data, req_user, warehouse, url):
    create_next_level = []
    min_amt_list, max_amt_list = [], []
    approval_dict = {}
    to_be_approved_by = ''
    for key,each_data in request_data.items():
        if key not in ['data','button_permission']:
            min_amt_list.append(each_data.get('min_value'))
            max_amt_list.append(each_data.get('max_value'))

    pa_filter = {
        'user_id': warehouse.id,
        'approval_type': 'INV',
        'purchase_type': 'INV',
        'min_Amt__in': min_amt_list,
        'max_Amt__in': max_amt_list
    }
    approval_dict, config_name, users = prepare_inventory_approval_dict(warehouse, pa_filter)

    ## Update existing PurchaseApprovals
    for cycle_id in cycle_to_approve:
        validated_by = req_user.username
        current_level = request_data.get(cycle_id,{}).get('current_level')
        next_level = 'level'+str(int(current_level.split('level')[-1])+1)
        status = 'approved'
        min_value = float(request_data.get(cycle_id,{}).get('min_value'))
        max_value = float(request_data.get(cycle_id,{}).get('max_value'))
        key = (min_value,max_value)
        for levels in approval_dict.get(key,{}):
            if levels.get('level') == next_level:
                to_be_approved_by = ','.join(levels.get('users',[]))
                break
        next_level_dict = {
            'purchase_number': int(cycle_id),
            'configName': config_name,
            'approval_type': 'INV',
            'pr_user_id': warehouse.id,
            'level': next_level,
            'status': '',
            'validated_by': to_be_approved_by,
            'remarks': url,
            'account_id': warehouse.userprofile.id
        }
        new_purchase_approval = PurchaseApprovals(**next_level_dict)
        new_purchase_approval.save()

        for each_user_mail in levels.get('users',[]):
            newGenerateHashCodeForMail(new_purchase_approval, each_user_mail)

        PurchaseApprovals.objects.filter(
            pr_user_id=warehouse.id,approval_type='INV',
            purchase_number=cycle_id,level=current_level
        ).update(status=status,validated_by=validated_by)
    try:
        with transaction.atomic('default'):
            for cycle_id in cycle_to_adjust:
                validated_by = req_user.username
                current_level = request_data.get(cycle_id,{}).get('current_level')
                status = 'approved'
                PurchaseApprovals.objects.filter(
                    pr_user_id=warehouse.id,approval_type='INV', 
                    purchase_number=cycle_id,level=current_level
                ).update(status=status,validated_by=validated_by)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        error = 'Approval Failed'
        log.info('Inventory Approval failed. Data for insertion: PurchaseApprovals:%s \n Errror: %s'%(create_next_level,str(e)))
        return error

def create_serial_number_cycle_count(warehouse, serial_mapping_data):
    ''' Create SerialNumberMapping for new serial numbers '''
    log.info("Request Create SerialNumberMapping username %s and params are %s"% (
        str(warehouse.username),str(serial_mapping_data)
    ))
    reference_number = serial_mapping_data.get('reference_number', '')
    reference_type = serial_mapping_data.get('reference_type', '')
    sku_details = serial_mapping_data.get('sku_details', {})
    message = ''
    for stockid, sku_data in sku_details.items():
        stock_id = stockid
        skuid = sku_data.get('sku_id','')
        break
    if not stock_id:
        return
    available_serial_data = get_available_serial_data(warehouse, skuid)
    try:
        for stock_id, sku_data in sku_details.items():

            serial_creation_data = {'warehouse_id':warehouse.id,
            'sku_id' : sku_data.get('sku_id',''), 
            'serial_number' : sku_data.get('serial_number',''),
            'stock_id':stock_id,
            'status': 1}

            '''check for serial number entry in SerialNumberMapping model
                if present update the serial number data else create a new entry'''
            
            if available_serial_data.get(sku_data.get('serial_number','')):
                serial_id = available_serial_data.get(sku_data.get('serial_number',''),0)
                filter_params = {'id': serial_id, 'warehouse': warehouse.id}

                serial_check = SerialNumberMapping.objects.filter(**filter_params)
                if serial_check.exists():
                    serial_check.update(
                        stock_id = stock_id,
                        status = 1
                    )
            else:
                serial_data = SerialNumberMapping.objects.create(**serial_creation_data)
                serial_id = serial_data.id

            serial_transaction_creation = {'warehouse_id':warehouse.id,
            'serial_id': serial_id,
            'reference_number': reference_number,
            'reference_type':reference_type,
            'status':1}
            SerialNumberTransactionMapping.objects.create(**serial_transaction_creation)
            message = 'Serial number Mapping and transaction mapping created successfully'

        return message

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Create Serial Numbers failed! Error message  %s' % (str(e)))

def get_available_serial_data(warehouse,sku_id):
    serial_data = dict(SerialNumberMapping.objects.filter(
        warehouse= warehouse.id,sku__id = sku_id).values_list
        ('serial_number','id')
    )
    return serial_data

def bulk_update_inventory_approval_remarks(warehouse, req_user, remarks_data, reason_dict, cycle_id_wise_approvel_user):
    # Get all cycle_ids from reason_dict
    cycle_ids = reason_dict.keys()
    
    # Prefetch InventoryAdjustment objects for the given cycle IDs
    inventory_adjustments = InventoryAdjustment.objects.filter(
        cycle__sku__user=warehouse.id,
        cycle_id__in=cycle_ids
    )
    
    # Create a dictionary for quick access
    inventory_adjustment_map = {str(ia.cycle_id): ia for ia in inventory_adjustments}
    # Prepare objects to update
    objects_to_update = []
    for cycle_id, reason in reason_dict.items():
        if cycle_id in inventory_adjustment_map:
            inventory_adjustment_obj = inventory_adjustment_map[cycle_id]
            remarks = remarks_data.get(cycle_id, '') or ''
            approval_data = cycle_id_wise_approvel_user.get(cycle_id, {})
            for _, value in approval_data.items():
                value.update({'remarks': remarks})

            # Update json_data
            if not inventory_adjustment_obj.json_data:
                # If no JSON data, initialize with approved_users_data and remarks
                inventory_adjustment_obj.json_data = {
                    'remarks': {req_user.username: remarks},
                    "approved_users_data": approval_data
                }
            else:
                # Update existing json_data
                json_data = inventory_adjustment_obj.json_data

                # Update remarks
                if 'remarks' in json_data:
                    json_data['remarks'].update({req_user.username: remarks})
                else:
                    json_data['remarks'] = {req_user.username: remarks}
                
                # Update approved_users_data
                if 'approved_users_data' in json_data:
                    json_data['approved_users_data'].update(approval_data)
                else:
                    json_data['approved_users_data'] = approval_data
                inventory_adjustment_obj.json_data = json_data

            # Update reason if not already set
            if not inventory_adjustment_obj.reason:
                inventory_adjustment_obj.reason = reason

            # Add object to the update list
            objects_to_update.append(inventory_adjustment_obj)
    
    # Perform a bulk update
    InventoryAdjustment.objects.bulk_update(
        objects_to_update,
        fields=['json_data', 'reason']
    )

def update_inventory_approval_remarks(warehouse, req_user, remarks_data, reason_dict):
    for cycle_id, reason in reason_dict.items():
        remarks = remarks_data.get(cycle_id, '')
        inventory_adjustment_obj = InventoryAdjustment.objects.get(cycle__sku__user=warehouse.id, cycle_id=cycle_id)
        if not inventory_adjustment_obj.json_data:
            json_data = {
                'remarks': {
                    req_user.username: remarks
                }
            }
        else:
            json_data = inventory_adjustment_obj.json_data
            if json_data.get('remarks'):
                json_data.get('remarks').update({
                    req_user.username: remarks
                })
            else:
                json_data.update({
                    'remarks': {
                        req_user.username: remarks
                    }
                })
        inventory_adjustment_obj.json_data = json_data
        inventory_adjustment_obj.save()
        
        if not inventory_adjustment_obj.reason:
            inventory_adjustment_obj.reason = reason
            inventory_adjustment_obj.save()

def get_inventory_adjustment_call_back_data(inv_data, warehouse, data_only = False):
    data_list, sku_codes = [], []
    sku_zones_dict = defaultdict(list)
    inv_adjustment_data = {}
    timezone = get_user_time_zone(warehouse)
    dept_name = warehouse.username
    inv_data = inv_data.exclude(cycle__remarks = 'CCVL')
    inv_adj_data = inv_data.values(
        'cycle__sku__sku_code', 'cycle__sku__sku_desc','cycle__sku__sku_size',
        'cycle__sku__sku_category','cycle__sku__sub_category','cycle__sku__sku_brand',
        'cycle__location__location', 'cycle__quantity','cycle__seen_quantity', 'cycle_id',
        'creation_date','reason','adjusted_quantity', 'cycle__batch_detail__batch_no',
        'cycle__sku__skuwac__average_price','cycle__location__zone__zone', 'cycle__location__zone_id',
        'cycle__run_type', 'id', 'cycle__sku__measurement_type', 'json_data',
        'cycle__batch_detail__batch_reference', 'cycle__json_data', 'cycle__stock_status',
        'json_data__remarks', 'updation_date', 'json_data__request_user'
    )

    cycle_ids = [data.get('cycle_id') for data in inv_adj_data]
    serial_df = get_existing_serials(warehouse, cycle_ids)

    call_back_id = (InventoryAdjustment.objects.filter(cycle__sku__user=warehouse.id).\
            values_list('inventory_adjustment_id', flat=True).order_by('-inventory_adjustment_id').first() or 0) + 1
    
    for data in inv_adj_data:
        created_by, reason_code = '', ''
        run_type = data.get('cycle__run_type', '')
        if run_type:
            run_type = run_type.capitalize()
        uom = data.get('cycle__sku__measurement_type', '')
        sku_code = data.get('cycle__sku__sku_code','')
        sku_desc = data.get('cycle__sku__sku_desc','')
        sku_size = data.get('cycle__sku__sku_size','')
        sku_category = data.get('cycle__sku__sku_category','')
        sku_sub_category = data.get('cycle__sku__sub_category','')
        sku_brand = data.get('cycle__sku__sku_brand','')
        location = data.get('cycle__location__location','')
        zone = data.get('cycle__location__zone__zone', '')
        zone_id = data.get('cycle__location__zone_id', '')
        previous_quantity = data.get('cycle__quantity',0)
        updated_quantity = data.get('cycle__seen_quantity',0)
        base_quantity = data.get('adjusted_quantity',0)
        reason = data.get('reason')
        sku_average_price = data.get('cycle__sku__skuwac__average_price',0) if data.get('cycle__sku__skuwac__average_price') else 0 
        creation_date = data.get('creation_date','')
        batch_no = data.get('cycle__batch_detail__batch_no')
        batch_reference = data.get('cycle__batch_detail__batch_reference')
        uom_dict = get_uom_with_sku_code(warehouse, sku_code, uom_type='purchase')
        pcf = uom_dict['sku_conversion']
        quantity = base_quantity/pcf
        json_data = data.get('cycle__json_data','')
        cycle_id = data.get('cycle_id')
        serial_numbers = fetch_serial_numbers_for_cycle_id(cycle_id, serial_df)
        if json_data:
            created_by = json_data.get('requested_user','')

        inv_json_data = data.get('json_data')
        if inv_json_data:
            reason_code = inv_json_data.get('reason_code') if inv_json_data.get('reason_code') else ''

        stock_status = status_choices.get(data.get('cycle__stock_status'), {}) or ''

        #Custom Attributes
        custom_attributes = {}
        if data.get('json_data') and data.get('json_data').get('custom_fields'):
            custom_attributes = data.get('json_data').get('custom_fields')

        sku_codes.append(sku_code)
        sku_zones_dict[zone_id].append(sku_code)

        data_dict ={
            'id': data.get('id'),
            'warehouse': dept_name,
            'sku_code':sku_code,
            'sku_description': sku_desc,
            'sku_size': sku_size,
            'sku_category': sku_category,
            'sku_sub_category': sku_sub_category,
            'sku_brand':sku_brand,
            'zone': zone,
            'location': location,
            'previous_quantity':previous_quantity,
            'adjusted_quantity': quantity,
            'updated_quantity': updated_quantity,
            'adjustment_value': round(abs(quantity)*sku_average_price, 2),
            'created_at': get_local_date_known_timezone(timezone, creation_date),
            'adjusted_at': get_local_date_known_timezone(timezone, data.get('updation_date')), 
            'created_by':created_by,
            'adjusted_by': data.get('json_data__request_user') if data.get('json_data__request_user') else '',
            'adjustment_type': run_type,
            'batch_number':batch_no,
            'batch_reference': batch_reference,
            'serial_numbers': serial_numbers,
            'reason_code': reason_code,
            'reason_value': reason,
            'uom': uom,
            'custom_fields': custom_attributes,
            'stock_status': stock_status,
            'remarks': data.get('json_data__remarks','')
        }
        data_list.append(data_dict)
    inv_adjustment_data['inventory_adjustment_data'] = data_list
    if not data_only:
        inv_data.update(inventory_adjustment_id = call_back_id)
        inv_adjustment_data['id'] = call_back_id
    return inv_adjustment_data, sku_codes, sku_zones_dict

@get_warehouse
def get_inventory_adjustment_past_records(request, warehouse:User):
    '''
    Get Inventory Adjustment Past Records
    '''
    sku_code = request.GET.get('sku_code', '')
    limit = int(request.GET.get('limit', 5))
    inventory_adjusment_objs = InventoryAdjustment.objects.filter(cycle__sku__user = warehouse.id, cycle__status = 3, 
                                                                  cycle__sku__sku_code = sku_code).exclude(adjusted_quantity=0
                                                                ).order_by('-id')[:limit]
    
    inv_adj_call_back_data, _, _ = get_inventory_adjustment_call_back_data(inventory_adjusment_objs, warehouse, data_only = True)
    return inv_adj_call_back_data


class InventoryAdjustmentSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None

    def frame_date_filters(self, request_data, search_params):
        """
        Frame date filters based on the request data.

        Args:
            request_data (dict): The request data containing the date filter values.

        Returns:
            dict: The date filters dictionary.

        """
        try:
            # Parsing the date filters to convert them to the required format
            date_keys = {'from_date': 'creation_date__gte', 'to_date': 'creation_date__lte', 'updation_date': 'updation_date__gte', 'to_updation_date': 'updation_date__lte'}
            for key, filter_key in date_keys.items():
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    search_params[filter_key] = localized_date.astimezone(pytz.UTC)
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('GET Inventory Adjustment API Date Filters Failed for {} and params are {} and error statement is {}'.format(
                self.request.user.username, request_data, e))

        return search_params
    
    def prepare_search_dict(self, request_data, search_params):
        status_dict = {'open': 2, 'completed': 3, 'cancelled': 'inventory_delete'}
        skus ,limit = [], 10
        if request_data.get('limit'):
            limit = request_data['limit']

        if request_data.get('sku_code'):
            search_params['cycle__sku__sku_code'] = request_data['sku_code']

        skus = request_data.get('sku_list', [])
        if skus:
            search_params['cycle__sku__sku_code__in'] = skus

        search_params = self.frame_date_filters(request_data, search_params)

        status = request_data.get('status')
        if status:
            search_params['cycle__status'] = status_dict.get(status, status)

        return search_params, limit

    def get(self, *args, **kwargs):
        self.set_user_credientials()
        request = self.request
        data_list = []
        warehouse = self.warehouse
        limit = 10
        total_count = 0
        error_status = []
        request_data = request.GET
        search_params = {'cycle__sku__user__in': [warehouse.id]}
        self.timezone = get_user_time_zone(self.warehouse)

        if request_data:
            search_params, limit =  self.prepare_search_dict(request_data, search_params)

        #Exlcudes
        excludes = {'cycle__status': 'inventory_delete'}
        if search_params.get('cycle__status') and search_params.get('cycle__status') == 'inventory_delete':
            excludes = {}

        inventory_adjusment_objs = InventoryAdjustment.objects.exclude(**excludes).filter(**search_params)

        if request_data.get('adjusted', 'false') == 'true':
            inventory_adjusment_objs = inventory_adjusment_objs.exclude(cycle__json_data__adjusted_qty=0)

        # Add a consistent ordering by id to ensure reliable pagination
        inventory_adjusment_objs = inventory_adjusment_objs.order_by('id')

        total_count = inventory_adjusment_objs.count()

        page_info = scroll_data(request, inventory_adjusment_objs, limit=limit, request_type='GET')
        inventory_adjusment_objs = page_info['data']

        if inventory_adjusment_objs:
            inventory_adjusment_objs = list(inventory_adjusment_objs.values(
                'cycle__sku__sku_code', 'cycle__sku__sku_desc', 'cycle__sku__sku_size',
                'cycle__sku__sub_category', 'cycle__sku__sku_category', 'cycle__sku__sku_brand',
                'cycle__location__location', 'cycle__quantity', 'cycle__seen_quantity','json_data',
                'creation_date', 'cycle__json_data', 'id', 'reason', 'inventory_adjustment_id',
                'cycle__batch_detail__batch_no', 'cycle__batch_detail__manufactured_date',
                'cycle__batch_detail__expiry_date', 'cycle__batch_detail', 'cycle__location__zone__zone',
                'updation_date', 'json_data__approved_users_data'
            ))
            for data in inventory_adjusment_objs:
                updated_user_name, adjusted_qty, adjusted_value = '', 0, 0
                batch_no, mfg_date, exp_date = '', '', ''
                reason_code = ''
                json_data = data.get('cycle__json_data')
                if json_data:
                    updated_user_name = json_data.get('requested_user','') if json_data.get('requested_user') else ''
                    adjusted_qty = json_data.get('adjusted_qty', 0) if json_data.get('adjusted_qty') else 0
                    adjusted_value = json_data.get('adjusted_value', 0) if json_data.get('adjusted_value') else 0
                if data.get('cycle__batch_detail'):
                    batch_no = data.get('cycle__batch_detail__batch_no')
                    mfg_date = get_local_date_known_timezone(
                            self.timezone, data.get('cycle__batch_detail__manufactured_date'), send_date=True).strftime('%Y-%m-%d') if data.get('cycle__batch_detail__manufactured_date') else ''
                    exp_date = get_local_date_known_timezone(
                            self.timezone, data.get('cycle__batch_detail__expiry_date'), send_date=True).strftime('%Y-%m-%d') if data.get('cycle__batch_detail__expiry_date') else ''

                if data.get('json_data'):
                    reason_code = data.get('json_data').get('reason_code') if data.get('json_data').get('reason_code') else ''


                data_dict = OrderedDict((
                    ('id', data.get('id')),
                    ('warehouse', warehouse.username),
                    ('sku_code', data.get('cycle__sku__sku_code')),
                    ('sku_description', data.get('cycle__sku__sku_desc')),
                    ('sku_size', data.get('cycle__sku__sku_size')),
                    ('sku_category', data.get('cycle__sku__sku_category')),
                    ('sku_sub_category', data.get('cycle__sku__sub_category')),
                    ('sku_brand', data.get('cycle__sku__sku_brand')),
                    ('location', data.get('cycle__location__location')),
                    ('zone', data.get('cycle__location__zone__zone')),
                    ('batch_number', batch_no),
                    ('manufactured_date', mfg_date),
                    ('expiry_date', exp_date),
                    ('previous_quantity', data.get('cycle__quantity')),
                    ('adjusted_quantity', adjusted_qty),
                    ('updated_quantity', data.get('cycle__seen_quantity')),
                    ('adjustment_value', adjusted_value),
                    ('transaction_id', data.get('inventory_adjustment_id')),
                    ('transaction_users', data.get('json_data__approved_users_data') if data.get('json_data__approved_users_data') else {}),
                    ('created_at', get_local_date_known_timezone(self.timezone, data.get('creation_date'))),
                    ('updated_at', get_local_date_known_timezone(self.timezone, data.get('updation_date'))),
                    ('reason', data.get('reason')),
                    ('reason_code', reason_code),
                    ('user', updated_user_name)
                ))
                data_list.append(data_dict)
        page_info['data'] = data_list
        page_info['message'] = "Success"
        page_info['status'] = 200
        page_info['page_info']['total_count'] = total_count
        page_info['error'] = [{'message': error_status}]
        return page_info
    
@get_warehouse
def create_audit_cycle_count(request, warehouse:User):
    ''' Create Audit Cycle Count '''
    request_data = json.loads(request.body)
    log_message = (("Request for Audit Cycle Count for Username: %s and request params are: %s") % (str(warehouse.username), request_data))
    log.info(log_message)
    if request_data.get('button_permission') != True:
        return HttpResponse('This user does not have permission to create audit cycle count ')

    cycle_count_ids, sku_codes = [], []
    remarks_data, reason_dict = {}, {}
    sku_zones_dict = defaultdict(list)
    for key, value in request_data.items():
        if key == 'data':
            for k,v in value.items():
                if k == 'null':
                    continue
                cycle_count_ids.append(k)
                if key not in reason_dict:
                    reason_dict[k] = v.get('reason', '')

                remarks_data[k] = v.get('remarks', '')
     ##Update the InventoryAdjustment model with remarks
    update_inventory_approval_remarks(warehouse, request.user, remarks_data, reason_dict)

    cycle_count_create = []
    request_user = request.user.username
    #Fetching Cycle objects to create audit cycle count
    cycle_count_objs = CycleCount.objects.filter(sku__user=warehouse.id, id__in=cycle_count_ids).exclude(run_type='Audit').select_related('sku', 'location', 'location__zone')

    for cycle_obj in cycle_count_objs:
        #Updating status to existing cycle objects
        sku_codes.append(cycle_obj.sku.sku_code)
        sku_zones_dict[cycle_obj.location.zone.id].append(cycle_obj.sku.sku_code)
        cycle_obj.status = 5
        cycle_obj.remarks='Audit'
        json_data = cycle_obj.json_data or {}
        json_data.update({'updated_by': request_user})
        cycle_obj.json_data = json_data
        cycle_obj.save()
        #Creating audit cycle count
        cycle_dict = cycle_obj.__dict__
        cycle_dict.pop('id')
        cycle_dict.pop('_state', '')
        cycle_dict.pop('creation_date')
        cycle_dict.pop('updation_date')
        cycle_dict.pop('remarks','')
        cycle_dict['run_type'] = 'Audit'
        if cycle_dict['json_data']:
            cycle_dict['json_data'].pop('updated_by', '')
        cycle_dict['status'] = 1

        if cycle_dict['json_data']:
            cycle_dict['json_data'].update({'requested_user': request_user})

        new_cycle_obj = CycleCount(**cycle_dict)
        cycle_count_create.append(new_cycle_obj)

    #Bulk create for audit cycle count
    if cycle_count_create:
        cycle_id = get_incremental(warehouse, 'cycle_id', '', False, len(cycle_count_create))
        for cycle_obj in cycle_count_create:
            cycle_obj.cycle = cycle_id
            cycle_id += 1
        CycleCount.objects.bulk_create(cycle_count_create)
    
    #Inventory Callback
    filters = {
        'sku_codes': sku_codes,
        "zones_data": sku_zones_dict
    }
    webhook_integration_3p(warehouse.id, "cycle_count", filters)

    return JsonResponse({'message': "Created Successfully"}, status = 200)