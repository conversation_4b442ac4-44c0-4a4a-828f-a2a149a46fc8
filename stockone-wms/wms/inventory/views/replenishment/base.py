# python imports
import numpy as np
import pandas as pd
import math
import datetime
from collections import OrderedDict

# django imports
from django.db.models import Q, Sum, F, Max
from django.utils import timezone

# wms imports
from wms_base.models import User

# core imports
from core.models import (
    SKUMaster, TaxMaster,
)
# inventory imports
from inventory.models import (
    StockDetail, ReplenishmentMaster, ClosingStock,
    LocationMaster, ReplenishmentClassification
)
from inventory.views.locator.stock_detail import get_current_stock

# inbound imports
from inbound.models import SupplierMaster

# outbound imports
from outbound.models import (
    OrderDetail, CustomerMaster
)


class FMSClassificationMixin:
    def __init__(self, warehouse_id=None):
        self.warehouse_id = warehouse_id

    def get_order_data(self, sku_ids, days=30):
        default_values_list = ['sku_id', 'creation_date__date']

        sale_orders_qs = OrderDetail.objects.filter(
                sku_id__in=sku_ids,
                creation_date__gt=self.get_curr_time_zone(days=days),
            ).values(
                *default_values_list
            ).annotate(
                order_quantity=Sum('original_quantity')
            )

        return pd.DataFrame(sale_orders_qs)

    def get_closing_stock(self, sku_ids, days=30, zones=None):
        basic_filter = {
            'stock__sku_id__in': sku_ids,
            'closing_date__gt': self.get_curr_time_zone(days=days),
        }
        if zones:
            basic_filter['stock__location__zone__zone__in'] = zones

        # Fetching closing stock data
        stock_query = ClosingStock.objects.filter(
                **basic_filter
            ).values(
                creation_date__date=F('closing_date')
            ).annotate(
                sku_id=F('stock__sku_id'),
                closing_stock=Sum('quantity')
            ).filter(
                closing_stock__gt=0
            )
        return pd.DataFrame(stock_query)

    def get_curr_time_zone(self, days=30):
        """Get the current time zone adjusted for the given number of days."""
        return timezone.now() - datetime.timedelta(days=days)

class FMSClassification(FMSClassificationMixin):
    def __init__(self, warehouse_id):
        super().__init__(warehouse_id)

    def prerequisite_for_abc_calculation(self, orders, closing_stock, days=7):
        """Prepare data for ABC classification by merging order data with closing stock data.
        Args:
            orders (pandas.DataFrame): DataFrame containing order data.
            closing_stock (pandas.DataFrame): DataFrame containing closing stock data.
            days (int, optional): Number of days to consider for average sales calculation. Defaults to 7.
        
        Returns:
            pandas.DataFrame: Merged DataFrame with order and closing stock data.
        """
        if orders.empty or closing_stock.empty:
            return pd.DataFrame()

        results = closing_stock.merge(orders, on=['sku_id', 'creation_date__date'], how='left')
        results = results.fillna(0)
        results['transaction_date'] = pd.to_datetime(results['creation_date__date'])

        # Reset index before groupby to avoid duplicate index
        results = results.reset_index(drop=True)
        df = results.groupby(by=['sku_id']).apply(lambda x: x.set_index('transaction_date').resample('1D').first())
        df = df.fillna(0)
        df = df[df['creation_date__date'] != 0]
        avg_cal_days = f'avg_{days}_days'

        # Reset index to avoid duplicate index issues
        df = df.drop(columns=['sku_id']).reset_index()
        # Calculate rolling average of order quantity
        df1 = df.groupby('sku_id')['order_quantity'].apply(
            lambda x: x.shift().rolling(min_periods=3,window=days).mean()
            ).reset_index(name=avg_cal_days)
        # Ensure transaction_date is present in both dataframes
        df2 = pd.merge(results, df1, on='sku_id', how='left')
        df2 = df2.dropna()
        df3 = df2.loc[df2.groupby('sku_id').transaction_date.idxmax()]
        return df3

    def abc(self, df, metric_column, abc_class_name='class'):
        """Assign an ABC class and rank to a metric based on cumulative percentage contribution.
        Args:
            df: Pandas dataframe containing data. 
            metric_column (string): Name of column containing metric to calculate. 
            abc_class_name (string, optional): Name to assign to class column. 
        
        Return:
            Pandas dataframe containing original data, plus the metric class and rank. 
        """

        def _abc_segment(percentage):
            """Assign an ABC segment based on cumulative percentage contribution.
            Args:
                percentage (float): Cumulative percentage of ranked metric.
            Returns:
                segments: Pandas DataFrame
            """

            if 0 < percentage <= 40:
                return 'Fast'
            elif 40 < percentage <= 80:
                return 'Medium'
            else:
                return 'Slow'    

        df = df.fillna(0)
        data = df.sort_values(by=metric_column, ascending=False)
        data[metric_column+'_sum'] = data[metric_column].sum()
        data[metric_column+'_cumsum'] = data[metric_column].cumsum()
        data[metric_column+'_running_pc'] = (data[metric_column+'_cumsum'] / data[metric_column+'_sum']) * 100
        data[abc_class_name] = data[metric_column+'_running_pc'].apply(_abc_segment)
        data = data.fillna(0)
        data[abc_class_name+'_rank'] = data[metric_column+'_running_pc'].rank().astype(int)
        return data


class Replenishment():
    def __init__(self, user_id) -> None:
        self.user_id = user_id
        self.user = User.objects.get(id=user_id)

    def _add_default_replenishment_classification(self):
        replenish_data = pd.DataFrame(
            ReplenishmentMaster.objects.filter(
                user_id=self.user_id,
                size=3,
                classification__in=[
                    'Fast',
                    'Medium',
                    'Slow',
                    'Other',
                ]
            ).values(
                'classification',
                'size',
                'min_days',
                'max_days'
            ))

        if replenish_data.empty:
            replenish_data = pd.DataFrame([
                {
                    "classification": "Slow",
                    "min_days": 1,
                    "max_days": 2
                },
                 {
                    "classification": "Medium",
                    "min_days": 2,
                    "max_days": 4
                },
                {
                    "classification": "Fast",
                    "min_days": 4,
                    "max_days": 6
                }
            ])
        return replenish_data

    def _add_other_replenishment_classification(self, replenish_data):
        if replenish_data[replenish_data['classification'] == 'Other'].empty:
            replenish_data = pd.concat([
                replenish_data,
                pd.DataFrame([{
                    "classification": "Other",
                    "max_days": 30,
                    "min_days": 20
                }])
            ], ignore_index=True)

        return replenish_data

    def get_replenishment_data(self):
        """Get replenishment data for the user.
        Returns:
            pandas.DataFrame: DataFrame containing replenishment data.
        """
        from core_operations.views.common.main import get_ars_replenishment_strategy
        ars_fields = ['classification', 'min_days', 'max_days']
        ars_replenishment_strategy_data = get_ars_replenishment_strategy(
            'ars_replenishment_strategy', self.user, ars_fields)
        ars_rep_classifications = ars_replenishment_strategy_data.get('ars_rep_classifications') or []

        if ars_rep_classifications:
            replenish_data = pd.DataFrame(ars_rep_classifications)
        else:
            replenish_data = self._add_default_replenishment_classification()

        replenish_data = self._add_other_replenishment_classification(replenish_data)
        return replenish_data

    def replenishment(self, abcdf, current_stock, replenishment_master, on_key='avg_7_days', default_skus=None):
        if default_skus:
            rows = []
            for sku in default_skus:
                row = {
                    'sku_id': sku,
                    'classification': 'Other',
                    on_key: 1
                }
                rows.append(row)
            rowsdf = pd.DataFrame(rows)
            abcdf = pd.concat([abcdf, rowsdf], ignore_index = True)
            abcdf.reset_index()

        if not current_stock.empty:
            res = abcdf.merge(
                current_stock, on=['sku_id'],
                how='left'
            ).merge(
                replenishment_master, on='classification',
                how='left'
            )
        else:
            res = abcdf.merge(
                replenishment_master, on='classification',
                how='left'
            )
            res['available_qty'] = 0

        res = res.fillna(0)
        res['max_qty'] = res['max_days'] * res[on_key]
        res['min_qty'] = res['min_days'] * res[on_key]
        res['assumed_available_qty'] = np.where(res['available_qty'] == 1, 0, res['available_qty'])
        res['rep_qty'] = np.where(res['assumed_available_qty'] <= res['min_qty'], res['max_qty'] - res['assumed_available_qty'] , 0)

        res['remarks'] = np.where(res['assumed_available_qty'] > res['min_qty'],
                                  'Sale Area Has Sufficient Stock',
                                  'Sale Area Has No Stock'
                                )
        res = res.fillna(0)
        res['suggested_qty'] = res['rep_qty']

        if "assumed_po_inward_stock" in res.columns:
            res['rep_qty'] = res['rep_qty'] - res['assumed_po_inward_stock']
            res['rep_qty'] = np.where(res['rep_qty'] < 0, 0, res['rep_qty'])

        self.replenished_data = res
        return res

    def frame_sku_classification_data(self, sku_data, on_key):
        sku_classification_maps = [
            ('sku_id', 'sku_id', None),
            ('avg_sales_day', on_key, 0),
            ('cumulative_contribution', '%s_cumsum' % on_key, 0),
            ('classification', 'classification', None),
            ('min_stock_qty', 'min_qty', 0),
            ('max_stock_qty', 'max_qty', 0),
            ('replenishment_qty', 'rep_qty', 0),
            ('suggested_qty', 'suggested_qty', 0),
            ('reserved', 'reserved_qty', 0),
            ('sku_avail_qty', 'sku_avail_qty', 0),
            ('sku_assumed_avail_qty', 'assumed_available_qty', 0),
            ('remarks', 'remarks', ''),
            ('sku_pen_po_qty', 'assumed_po_qty', 0),
            ('sku_pen_putaway_qty', 'assumed_po_putaway_qty', 0),
        ]
        sku_classification_dict = {}
        for model_key, df_key, default_value in sku_classification_maps:
            sku_classification_dict[model_key] = sku_data.get(df_key, default_value)

        return sku_classification_dict

    def save_sku_classification_data(self, df, on_key='avg_7_days'):
        # fixing float values
        default_float_keys = ('rep_qty', 'min_qty', 'max_qty')
        for key in default_float_keys:
            df[key] = np.ceil(df[key]).astype(float)

        # processing
        sku_classification_objs = []
        for index, row in df.iterrows():
            sku_classification_dict = self.frame_sku_classification_data(row, on_key)
            rep_classification_instance = ReplenishmentClassification(**sku_classification_dict)
            rep_classification_instance.set_account_id(self.user)
            sku_classification_objs.append(rep_classification_instance)

        # Bulk delete existing ReplenishmentClassification objects for the user
        rep_cf_qs = ReplenishmentClassification.objects.filter(sku__user=self.user_id)
        ReplenishmentClassification.objects.perform_bulk_deletion(rep_cf_qs, chunk_size=1000)

        # Bulk create ReplenishmentClassification objects
        ReplenishmentClassification.objects.bulk_create_with_rounding(sku_classification_objs)


class ReplenishmentMixin:
    def __init__(self, user_id, automate=False) -> None:
        self.automate = automate
        self.user = user_id
        self.user_id = None

        if isinstance(user_id, User):
            self.user_id = user_id.id
        else:
            self.user = User.objects.get(id=user_id)
            self.user_id = self.user.id

        self.ars_warehouse_ids = getattr(self, 'ars_warehouse_ids', {})

    def set_destination_locations(self):
        sa_zones = self.misc_values_dict.get("pick_zones_list") or []
        if sa_zones:
            sa_zones = sa_zones.split(',')
        
        self.dest_locations = list(LocationMaster.objects.filter(
            zone__user=self.user.id,
            zone__zone__in=sa_zones,
            status=1)
        )

    def prepare_replenishment(self, central_warehouse=None, children_ids=None):
        children_requirement = self.get_replenishment_requirement(children_ids)
        children_requirement['main_user_id'] = central_warehouse.id if isinstance(central_warehouse, User) else central_warehouse
        children_requirement['main_user_username'] = central_warehouse.username if isinstance(central_warehouse, User) else central_warehouse

        self.pure_requirement = self.merge_with_master_sku(children_requirement)

    def set_replenishment(self, central_warehouse=None, children_ids=None):
       raise NotImplementedError("This method should be implemented in the subclass.")

    def get_tax_type(self):
        return pd.DataFrame(
            TaxMaster.objects.filter(user_id__in=self.children_ids, inter_state=1).values(
                "product_type",
                "min_amt", "max_amt", 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax'
            ).annotate(
                child_user_id=F("user_id")
            )
        )

    def set_tax_type(self, taxDf, rep_row):
        unit_price =  rep_row['ba_sku_buy_price'] * rep_row['replenushment_qty']
        newTaxDf = taxDf[(taxDf["min_amt"] < unit_price) & (taxDf["max_amt"] >= unit_price)]
        if newTaxDf.empty:
            return  {
                'cgst_tax': 0, 
                'sgst_tax': 0, 
                'igst_tax': 0, 
                'utgst_tax': 0
            }
        return newTaxDf.iloc[1].to_dict()

    def get_sku_classification(self, users):
        raise NotImplementedError("This method should be implemented in the subclass.")

    def get_replenishment_requirement(self, children_ids=None):
        users = children_ids or self.ars_warehouse_ids.keys()

        sku_classification_df = pd.DataFrame(
            self.get_sku_classification(users).values(
                'sku__id',
                'sku__user',
                'sku__sku_code',
                'sku__sku_category',
                'sku__mrp',
                'sku__cost_price',
                'sku__product_type',
                'sku__sku_desc',
                'avg_sales_day',
                'cumulative_contribution',
                'classification',
                'replenishment_qty',
                'sku_avail_qty',
                'min_stock_qty',
                'max_stock_qty',
                'remarks'
            )
        )
        if sku_classification_df.empty:
            return sku_classification_df

        sku_classification_df = sku_classification_df.rename(columns={
            'sku__sku_desc': 'sku_desc',
            'sku__id': 'sku_id',
            'sku__user': 'user_id',
            'sku__sku_code': 'sku_code',
            'sku__sku_category': 'sku_category',
            'sku__cost_price': 'cost_price',
            'sku__mrp': 'mrp'
        })

        return sku_classification_df

    def run_store_replenishment_workflow(self, user_id, average_days):
        bulk_zones = self.misc_values_dict.get("bulk_zones_list")
        if bulk_zones:
            bulk_zones = bulk_zones.split(',')
        sa_zones = self.misc_values_dict.get("pick_zones_list")
        if sa_zones:
            sa_zones = sa_zones.split(',')
        closing_stock_zones = self.misc_values_dict.get("closing_stock_zones")
        if closing_stock_zones:
            closing_stock_zones = closing_stock_zones.split(',')

        # Fetching SKU IDs for Replenishment
        sku_ids = self.get_replenishment_sku_ids(user_id)
        if not sku_ids:
            self.log.info("No SKUs Found For Replenishment for user: %s", user_id)

        ## initialize FMSC Classification
        fmsc = FMSClassification(user_id)

        # Fetching Order Data and Closing Stock Data
        average_days = average_days or 30
        orders_df = fmsc.get_order_data(sku_ids, days=average_days)
        closing_stock_df = fmsc.get_closing_stock(sku_ids, days=average_days, zones=closing_stock_zones)
        if orders_df.empty or closing_stock_df.empty:
            self.log.info("No Orders or Closing Stock Data Found For Replenishment for user: %s", user_id)
            return
        # Adding One Day , to make it opening stock of the next day
        closing_stock_df['creation_date__date'] = (pd.DatetimeIndex(
            closing_stock_df['creation_date__date']
            ) + pd.DateOffset(1)
        )
        orders_df['creation_date__date'] = pd.DatetimeIndex(orders_df['creation_date__date'])

        # Preparing Data For FMS Classification
        df = fmsc.prerequisite_for_abc_calculation(orders_df, closing_stock_df, days=average_days)
        # Running FMS Classification Custom For MB
        fmsdf = fmsc.abc(df, 'avg_%s_days' % average_days, abc_class_name='classification')
        if fmsdf.empty:
            self.log.info("No Data Found For FMS Classification for user: %s", user_id)
            return

        # Adding Replenishment Master Data
        rep = Replenishment(user_id)
        replenishment_master = rep.get_replenishment_data()
        current_stock = get_current_stock(sku_ids, zones=None, filter_dict={'status': 1})
        current_stock= self.get_sku_stock_data(current_stock, user_id, sku_ids)

        default_skus = list(set(sku_ids) - set(fmsdf.sku_id.to_list()))
        res = rep.replenishment(fmsdf, current_stock, replenishment_master,
            on_key='avg_%s_days' % average_days,
            default_skus=default_skus
        )
        # Store the replenishment data in the model
        rep.save_sku_classification_data(res, on_key='avg_%s_days' % average_days)
        self.log.info("Replenishment workflow completed successfully for user: %s", user_id)

    def get_sku_stock_data(self, current_stock, user_id, sku_ids):
        return current_stock