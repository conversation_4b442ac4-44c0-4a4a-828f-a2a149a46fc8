# python imports
import pandas as pd
import numpy as np
import traceback

# django imports
from django.db.models import F

# wms imports
from wms_base.models import User, UserAddresses
from wms_base.wms_utils import init_logger

# core imports
from core.models import SKUMaster
from core_operations.views.common.stock import StockDataMixIn

# inventory imports
from .stock_transfer_replenishment import STOReplenishment
from inventory.management.commands.min_max_based_auto_po import create_po_for_replenishment_qty

log = init_logger('logs/ars_stock_transfer_orders.log')


class ARSStockData(StockDataMixIn):
    """
    It collects data from various sources and merges them into a unified DataFrame
    for replenishment calculations.
    """

    def __init__(self, user_id, sku_ids):
        """
        Initialize the ARSStockData instance.

        Args:
            user_id (int): ID of the warehouse user
            sku_ids (list): List of SKU IDs to process
        """
        super().__init__(user_id, sku_ids)

    def merge_data(self, data=None):
        """
        Merge stock data from multiple sources into a unified DataFrame.

        This method collects data from various inventory sources and merges them
        to create a comprehensive view of stock status for replenishment calculations.

        Args:
            data (dict, optional): Pre-fetched data dictionary. If None, calls get_data()
                                  to retrieve it.

        Returns:
            pandas.DataFrame: Merged DataFrame with comprehensive stock data
        """
        if not data:
            data = self.get_data()

        # Start with pending putaway data
        df = data['pending_putaway_df']

        # Merge with other data sources
        df = df.merge(data['return_putaway_df'], on=['sku_code'], how='outer')
        df = df.merge(data['cancelled_putaway_df'], on=['sku_code'], how='outer')
        df = df.merge(data['non_sellable_df'], on=['sku_code'], how='outer')
        df = df.merge(data['jo_pending_putaway_df'], on=['sku_code'], how='outer')
        df = df.merge(data['pending_grn_df'], on=['sku_code'], how='outer')
        df = df.merge(data['po_pending_approval_df'], on=['sku_code'], how='outer')
        df = df.merge(data['open_order_df'], on=['sku_code'], how='outer')

        # Fill missing values with zeros
        df = df.fillna(0)

        # Get SKU master data
        sku_master_df = pd.DataFrame(
            SKUMaster.objects.filter(id__in=self.sku_ids).values(
                "sku_code"
            ).annotate(sku_id=F("id"))
        )
        # Merge with SKU master data
        df = df.merge(sku_master_df, on=['sku_code'], how='left')
        df = self.set_assumed_total_po_qty(df)
        return df

    def set_assumed_po_putaway_qty(self, df):
        putaway_keys = [
            'pending_putaway_qty', 'jo_pending_putaway_qty', 'cancelled_putaway_qty'
        ]
        df['assumed_po_putaway_qty'] = df[putaway_keys].sum(axis=1)

        return df

    def set_assumed_po_qty(self, df):
        po_keys = [
            'po_pending_approval_qty', 'pending_grn_qty',
        ]
        df['assumed_po_qty'] = df[po_keys].sum(axis=1)

        return df

    def set_assumed_total_po_qty(self, df):
        df = self.set_assumed_po_putaway_qty(df)
        df = self.set_assumed_po_qty(df)

        df['assumed_po_inward_stock'] = df['assumed_po_qty'] + df['assumed_po_putaway_qty']
        return df


class ARSReplenishment(STOReplenishment):
    """
    Implements the ARS-specific replenishment workflow for stock transfers.

    This class extends STOReplenushment to provide ARS-specific replenishment logic.
    It handles the workflow for creating stock transfer orders between warehouses
    based on replenishment requirements.

    Attributes:
        user_id (int): ID of the warehouse user
        suppliers (list): List of suppliers to filter by
        automate (bool): Flag indicating whether to automate the process
    """

    def __init__(self, user_id, suppliers=[], automate=False, log=None) -> None:
        """
        Initialize the ARSReplenushment instance.

        Args:
            user_id (int): ID of the warehouse user
            suppliers (list, optional): List of suppliers to filter by. Defaults to [].
            automate (bool, optional): Flag indicating whether to automate the process.
                                      Defaults to False.
        """
        super().__init__(user_id, suppliers, automate, log)

    def ars_replenishment_workflow(self, average_days):
        """
        Entry point for the ARS replenishment workflow.

        This method orchestrates the ARS replenishment process by delegating to
        the base replenishment workflow implementation.
        """

        return self._execute_replenishment_workflow(average_days)

    def set_replenishment(self, central_warehouse, ars_warehouses):
        """
        Execute replenishment by creating stock transfer orders.

        This method is called after replenishment requirements have been calculated.
        For main warehouses, it creates stock transfer orders to fulfill the
        replenishment requirements of child warehouses.

        Note: This implementation only handles main warehouses. For regular warehouses,
        the parent class implementation is used.
        """

        # Get pure requirements
        pure_req = self.pure_requirement

        # Group requirements by child SKU, child user, and SKU code
        pure_req = pure_req.groupby(by=['child_sku_id', 'child_user_id', 'sku_code']).max().reset_index()

        # Prepare STO purchase order payload
        sto_po_data = self.prepare_sto_po_payload(pure_req)

        self.sto_po_data = sto_po_data.copy()

        # Create STO purchase orders
        self.create_sto_po_replenushment(central_warehouse, ars_warehouses, sto_po_data)

    def get_replenishment_sku_ids(self, user_id):
        """
        Retrieve SKU IDs eligible for replenishment.
        """
        return super().get_replenishment_sku_ids(user_id)

    def get_sku_classification(self, users):
        """
        Retrieve SKU classification data for replenishment calculations.
        """
        return super().get_sku_classification(users)

    def get_sku_stock_data(self, current_stock_df, user_id, sku_ids):
        """
        Retrieve and process stock data for replenishment calculations.

        This method uses ARSStockData to get comprehensive stock data and
        adjusts available quantities based on pending operations.

        Args:
            current_stock_df (pandas.DataFrame): DataFrame with current stock data
            user_id (int): ID of the warehouse user
            sku_ids (list): List of SKU IDs to process

        Returns:
            pandas.DataFrame: Updated stock DataFrame with adjusted quantities
        """
        # Get stock data using ARSStockData
        stock_data_df = ARSStockData(user_id, sku_ids).merge_data()

        default_values = {'available_qty': 0, 'reserved': 0}
        current_stock_df.fillna(default_values, inplace=True)
        current_stock_df['sku_avail_qty'] = current_stock_df['available_qty']

        if not stock_data_df.empty:
            # Merge with current stock data
            current_stock_df = stock_data_df.merge(current_stock_df, on=['sku_id'], how='left')

            # Adjust available quantities based on pending operations
            current_stock_df['available_qty'] = current_stock_df['available_qty'] - current_stock_df['open_order_qty']

        return current_stock_df

    def prepare_sto_po_payload(self, pure_req):
        """
        Prepare payload for stock transfer purchase order creation.

        This method groups replenishment requirements by supplier, user, and
        expected delivery date, and formats them for purchase order creation.

        Args:
            pure_req (pandas.DataFrame): DataFrame with pure replenishment requirements

        Returns:
            dict: Dictionary with purchase order data by supplier
        """
        po_creation_data = {}
        group_keys = ['po_supplier_id', 'child_user_id', 'exp_delivery_date']

        # Group requirements by supplier, store_warehouse, and expected delivery date
        for (supplier_id, user, exp_delivery_date), group in pure_req.groupby(group_keys):
            # Format items for purchase order
            items = group[['sku_code', 'replenishment_qty']].rename(
                columns={
                    'sku_code': 'sku',
                    'replenishment_qty': 'order_quantity'
                }).to_dict(orient='records')

            # Get additional data from group
            exp_delivery_date = group['exp_delivery_date'].iloc[0]
            supplier_type = group['supplier_type'].iloc[0]

            # Create unique key for supplier
            po_supplier_uniq_key = f'{supplier_id}_:{user}_:{exp_delivery_date}'

            # Add to purchase order data
            po_creation_data[po_supplier_uniq_key] = {
                'exp_delivery_date': exp_delivery_date,
                'items': items,
                'supplier_type': supplier_type
            }

        return po_creation_data

    def create_sto_po_replenushment(self, central_warehouse, ars_warehouses, po_creation_data):
        """
        Create stock transfer purchase orders for replenishment.

        This method creates purchase orders for child warehouses based on the
        replenishment requirements. It retrieves shipping addresses and creates
        purchase orders for each supplier.

        Args:
            main_user (User): Main warehouse user
            po_creation_data (dict): Purchase order creation data by supplier
        """

        # Process each child warehouse
        if ars_warehouses and not isinstance(ars_warehouses[0], User):
            ars_warehouses = User.objects.filter(username__in=ars_warehouses)

        for ch_warehouse in ars_warehouses:
            # Get shipping address
            try:
                user_address = UserAddresses.objects.filter(user=ch_warehouse.id).first()
                if not user_address:
                    log.info(f"No shipping address found for warehouse {ch_warehouse.username}")
                    continue
            except IndexError:
                log.error(f"No address found for warehouse {ch_warehouse.username}")
                continue

            # Format shipping address
            ship_to = ('%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode))

            # Process each purchase order
            for po_data_key, po_data in po_creation_data.items():
                try:
                    supplier_id, user, _ = po_data_key.split('_:')
                    # Skip if not for this warehouse
                    if int(user) != ch_warehouse.id:
                        log.info(f"Skipping PO for {ch_warehouse.username} for {po_data_key}")
                        continue

                    # Create purchase order
                    response_dict = create_po_for_replenishment_qty(ch_warehouse, {supplier_id: po_data}, ship_to, response=True)
                    if response_dict:
                        self.log_po_errors(response_dict)
                    else:
                        log.info(f"PO created for {ch_warehouse.username} for {po_data_key} successfully")

                except Exception as e:
                    # Log error details
                    log.debug(traceback.format_exc())
                    log.error(f"Error in creating PO for {ch_warehouse.username} for {po_data_key}: {str(e)}")

    def log_po_errors(self, response_dict):
        """
        Log errors from the purchase order creation response.

        Args:
            response_dict (dict): Response dictionary from purchase order creation
        """

        if response_dict and 'error' in response_dict:
            for error in response_dict['error']:
                self.log.error(f"PO creation error: {error}")
