#package imports
import copy
from collections import OrderedDict, defaultdict
import json

#django imports
from django.db.models import Q
from django.http import HttpResponse, JsonResponse, HttpResponseBadRequest

#inventory imports
from inventory.models import (
    LocationMaster, ReplenishmentMaster, ZoneMaster, StockDetail, ReplenishmentClassification
)
from inventory.views.replenishment.uploads import validate_replenishment_form, create_nte_replenishment_master
#core imports
from core.models import (
    SKUMaster
)

#wms base imports
from wms_base.wms_utils import (
    init_logger, REPLENISHMNENT_DATA
)
from wms_base.models import User

#core common functions
from core_operations.views.common.main import frame_datatable_column_filter, get_warehouse

log = init_logger('logs/replenishment.log')

InventoryBasedRepHeaders = {
    'classification': 'Classification', 'size': 'Size',
    'min_days': 'SA Min Days', 'max_days': 'SA Max Days'
}

MinMaxBasedRepHeaders = {
    'sku_code': 'SKU Code', 'min_qty': 'Minimum Qty',
    'max_qty': 'Maximum Qty', 'zone': 'Zone', 'location': 'Location'
}

NTERepHeaders = {
    'sku_code': 'SKU Code', 'sku_desc' : 'SKU Description', 'sku_category': 'SKU Category',
    'source_zone' : 'Source Zone', 'source_location' : 'Source Location',
    'destination_zone' : 'Destination Zone', 'destination_location' : 'Destination Location',
}

ARS_REPLENISHMENT_CLASSIFICATION_HEADERS = {
    'sku_code': 'SKU Code', 'classification': 'Classification', 'avg_sales_day': 'Avg Sales/Day',
    'avg_sales_day_value': 'Avg Sales Value/Day', 'cumulative_contribution': 'Cumulative Contribution',
    'min_stock_qty': 'Min Stock Qty', 'max_stock_qty': 'Max Stock Qty',
    'replenishment_qty': 'Replenishment Qty', 'suggested_qty': 'Suggested Qty',
    'sku_avail_qty': 'SKU Available Qty', 'sku_pen_po_qty': 'SKU Pending PO Qty',
    'sku_pen_putaway_qty': 'SKU Pending Putaway Qty',
    'status': 'Status'
}

@get_warehouse
def validate_and_create_min_max_replenishment(request, warehouse:User):
    status= ''
    log.info("Request Insert Update min/max based replenushment request username %s and params are %s"% (str(request.user.username), str(request.POST)))

    error_list, data_dict, replenishment_obj  = validate_replenishment_data(request, warehouse)
    if error_list:
        error_message = ', '.join(error_list)
        return JsonResponse({'message': error_message}, status=400)
    else:
        status = insert_min_max_based_data(request, data_dict, replenishment_obj, warehouse)
    return JsonResponse({'message': [status]}, status = 200)

def insert_min_max_based_data(request, data_dict, replenishment_obj, warehouse):
    replenishment = {}
    status = ''
    if replenishment_obj.exists():
        replenishment_obj = replenishment_obj[0]
        replenishment_obj.min_qty = data_dict.get('min_qty')
        replenishment_obj.max_qty = data_dict.get('max_qty')
        replenishment_obj.zone_id = data_dict.get('zone_id')
        replenishment_obj.location_id = data_dict.get('location_id')
        replenishment_obj.from_zone_id = data_dict.get('from_zone_id')
        replenishment_obj.from_location_id = data_dict.get('from_location_id')
        replenishment_obj.user_id = warehouse.id
        replenishment_obj.status = 1
        replenishment_obj.save()
        status = 'Updated Successfully'
    else:
        replenishment['sku_id'] = data_dict['sku_id']
        replenishment ['min_qty'] = data_dict.get('min_qty')
        replenishment['max_qty'] = data_dict.get('max_qty')
        replenishment['zone_id'] = data_dict.get('zone_id')
        replenishment['location_id'] = data_dict.get('location_id')
        replenishment['from_zone_id'] = data_dict.get('from_zone_id')
        replenishment['from_location_id'] = data_dict.get('from_location_id')
        replenishment['user_id'] = warehouse.id
        replenishment['account_id'] = warehouse.userprofile.id
        status = 'Added Successfully'

        try:
            ReplenishmentMaster.objects.create(**replenishment)

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info(
                'Insert New Min Max Based Replenushment failed for %s and params are %s and error statement is %s' % (
                    str(warehouse.username), str(request.POST.dict()), str(e))
                )
            status = 'Something went wrong'

    return status

def validate_fields(sku_code, min_qty, max_qty, zone, location, error_list, from_zone, from_location, min_max):
    if min_max:
        validate_min_max(sku_code, min_qty, max_qty, error_list)
    elif not sku_code:
        error_list.append("SKU Code is mandatory")
    if not (zone or location):
        error_list.append("Destination Zone / Location is mandatory.")
    if not (from_zone or from_location):
        error_list.append("Source Zone /Source Location is Mandatory")
    if zone and from_zone and zone == from_zone:
        error_list.append('From Zone and To Zone cannot be the same.')
    if location and from_location and location == from_location:
        error_list.append("From Location and To Location cannot be the same.")

    return error_list

def validate_min_max(sku_code, min_qty, max_qty, error_list):
    if not (sku_code or min_qty or max_qty):
        error_list.append("SKU Code/ Min Qty/ Max Qty are mandatory.")
    if min_qty < 0:
        error_list.append("Min Qty should be greater than or equal to 0")
    if max_qty <= 0:
        error_list.append("Max Qty should be greater than 0")
    if min_qty > max_qty:
        error_list.append("Min Qty should be less than Max Qty.")

def validate_sku_code(sku_code, sku_master_dict, data_dict, error_list):
    if sku_master_dict.get(sku_code):
        data_dict['sku_id'] = sku_master_dict[sku_code]
    else:
        error_list.append("Invalid SKU Code")
    return data_dict, error_list

def validate_to_location(location, location_master_dict, data_dict, error_list):
    if location:
        if location_master_dict.get(location):
            data_dict['location_id'] = location_master_dict.get(location)
        else:
            error_list.append("Invalid Destination location")
    return data_dict, error_list

def validate_to_zone(zone, zone_master_dict, data_dict, error_list):
    if zone:
        if zone_master_dict.get(zone):
            data_dict['zone_id'] = zone_master_dict.get(zone)
        else:
            error_list.append("Invalid Destination Zone")
    return data_dict, error_list

def validate_from_location(from_location, location_master_dict, data_dict, error_list):
    if from_location:
        if location_master_dict.get(from_location):
            data_dict['from_location_id'] = location_master_dict.get(from_location)
        else:
            error_list.append("Invalid Source Location")
    return data_dict, error_list

def validate_from_zone(from_zone, zone_master_dict, data_dict, error_list):
    if from_zone:
        if zone_master_dict.get(from_zone):
            data_dict['from_zone_id'] = zone_master_dict.get(from_zone)
        else:
            error_list.append("Invalid Source Zone")
    return data_dict, error_list

def validate_replenishment_data(request, warehouse):
    error_list, data_dict = [], {}
    sku_code = request.POST.get('sku_code')
    zone = request.POST.get('zone')
    location = request.POST.get('location')
    from_zone = request.POST.get('from_zone')
    from_location = request.POST.get('from_location')
    min_qty = float(request.POST.get('min_qty', 0))
    max_qty = float(request.POST.get('max_qty', 0))
    rep_id = request.POST.get('id')
    replenishment_obj = None
    error_list = validate_fields(sku_code, min_qty, max_qty, zone, location, error_list, from_zone, from_location, min_max=True)
    location_master_data = list(LocationMaster.objects.filter(zone__user = warehouse.id).values_list('location', 'id', 'zone__zone','zone_id', 'zone__segregation'))
    sku_master_dict = dict(SKUMaster.objects.filter(sku_code = sku_code, user = warehouse.id).values_list('sku_code', 'id'))
    location_master_dict, zone_master_dict = dict(), dict()
    for loc, location_id, zon, zone_id, segregation in location_master_data:
        location_master_dict[loc] = location_id
        zone_master_dict[zon] = zone_id

    if not error_list:
        #Validate SKU Details
        data_dict, error_list = validate_sku_code(sku_code, sku_master_dict, data_dict, error_list)

        #Validate TO Location Details
        data_dict, error_list = validate_to_location(location, location_master_dict, data_dict, error_list)

        #Validate TO Zone Details
        data_dict, error_list = validate_to_zone(zone, zone_master_dict, data_dict, error_list)

        #Validate From Location Details
        data_dict, error_list = validate_from_location(from_location, location_master_dict, data_dict, error_list)

        #Validate From Zone Details
        data_dict, error_list = validate_from_zone(from_zone, zone_master_dict, data_dict, error_list)

        if error_list:
            return error_list, data_dict, replenishment_obj

        if rep_id:
            data_dict['id'] = rep_id  

        data_dict['min_qty'], data_dict['max_qty'] = float(min_qty), float(max_qty)
        error_list, filter_list = validate_data_dict_with_available_replenishment(error_list, [data_dict], warehouse)
        
        data_dict['classification'] = 'ba_to_sa'
        if filter_list[0]:
            replenishment_obj = ReplenishmentMaster.objects.select_related('sku', 'location', 'zone', 'from_zone', 'from_location').filter(**(filter_list[0]), classification = 'ba_to_sa')

    return error_list, data_dict, replenishment_obj

def validate_data_dict_with_available_replenishment(errors, data_dict_list, user):
    error_list = errors.copy()
    sku_ids = set()
    for data_dict in data_dict_list:
        sku_ids.add(data_dict['sku_id'])

    values_list = ['id','sku_id','from_zone','from_location_id','location_id','zone_id','user','min_qty','max_qty']
    replenushment_obj = ReplenishmentMaster.objects.select_related(
        'sku', 'location', 'zone'
        ).filter(sku_id__in = sku_ids,status = 1, user = user.id, classification = 'ba_to_sa').values(*values_list)

    rep_data = dict()
    repl_data = set()
    for rep in replenushment_obj:
        rep_data[(rep['sku_id'],rep['zone_id'],rep['location_id'])] = {
            'id' : rep['id'],
            'from_zone': rep['from_zone'],
            'from_location_id' : rep['from_location_id'],
            'min_qty' : rep['min_qty'],
            'max_qty' : rep['max_qty'] 
            }
        repl_data.add((rep['sku_id'],rep['zone_id'],rep['location_id'],rep['from_zone'],rep['from_location_id'],rep['user'],rep['min_qty'],rep['max_qty']))

    filter_list = []
    for data_dict in data_dict_list:
        rep_key  = data_dict.get('sku_id'),data_dict.get('zone_id',None),data_dict.get('location_id',None)
        if rep_key in rep_data and (not data_dict.get('id') or int(data_dict['id'])!=rep_data[rep_key].get('id','')):
            filter_data = (
                    data_dict['sku_id'],
                    data_dict.get('zone_id', None),
                    data_dict.get('location_id', None),
                    data_dict.get('from_zone_id',None),
                    data_dict.get('from_location_id',None),
                    user.id,
                    data_dict.get('min_qty'),
                    data_dict.get('max_qty')
                )
            if filter_data in repl_data:
                error_list.append('Entry Already Exists')
                filter_list.append(None)
                continue

            if (bool(rep_data.get(rep_key,{}).get('from_zone','')) ^ bool(data_dict.get('from_zone_id',''))): 
                error_list.append('Entry Already Exists for this SKU Kindly update the same')
                filter_list.append(None)
                continue

            if (rep_data.get(rep_key,{}).get('from_zone') == data_dict.get('from_zone_id')) and (rep_data.get(rep_key,{}).get('from_location_id') == data_dict.get('from_location_id')):
                error_list.append('Entry Already Exists for this SKU Kindly update the same')
                filter_list.append(None)
                continue

            if rep_data.get(rep_key,{}).get('max_qty',0) != data_dict.get('max_qty'):
                error_list.append('Maximum Quantity value should be ' + str(rep_data.get(rep_key,{}).get('max_qty',0)))
                filter_list.append(None)
                continue

        filter_dict = {
            'sku_id' : data_dict['sku_id'],
            'zone_id' : data_dict.get('zone_id', None),
            'location_id' : data_dict.get('location_id', None),
            'status': 1,
            'user' : user.id,
        }
        filter_list.append(filter_dict)

    return error_list, filter_list

@get_warehouse
def delete_min_max_based_replenishment(request, warehouse:User):
    status = "Updated Sucessfully"
    ids = request.GET.get('ids').split(',')
    try:
        ReplenishmentMaster.objects.filter(id__in = ids).update(status = 0)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Delete New Min Max Based replenishment failed for %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(request.POST.dict()), str(e)))
        status = "Failed"
    return HttpResponse(status)

def get_min_max_based_replenishment_master(
        start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters
    ):
    request_data = request.GET
    if not request_data:
        request_data = json.loads(request.body)
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    count = True if request.GET.get('count', '') == 'true' else False
    if order_data == "sku_code":
        order_data = "sku__sku_code"
    
    if sort_type == '1':
        order_data = '-%s' % order_data

    filters = {
        'status': 1,
        'user': warehouse.id,
        'classification': "ba_to_sa"
    }
    query_filter = Q()
    if search_term:
        query_filter = Q(sku__sku_code__icontains=search_term) | Q(zone__zone__icontains=search_term)\
            | Q(location__location__icontains=search_term) | Q(min_days__icontains=search_term) | Q(max_days__icontains=search_term)
    elif column_headers:
        column_filters_dict = {}
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {
            "zone__icontains":"zone__zone__icontains",
            "location__icontains":"location__location__icontains",
            "sku_code__icontains":"sku__sku_code__icontains",
            "from_zone__icontains": "from_zone__zone__icontains",
            "from_location__icontains": "from_location__icontains",
        }
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            else:
                column_filters_dict[key] = value
        filters.update(column_filters_dict)

    master_data = ReplenishmentMaster.objects.filter(query_filter, **filters).order_by(order_data)
    if count:
        temp_data['count'] = master_data.count()
        return
    master_data = master_data[start_index: stop_index]
    master_data = master_data.values(
        'id','sku__sku_code', 'zone__zone', 'min_qty', 'max_qty', 'location__location', 
        'from_location__location', 'from_zone__zone'
    )

    temp_data['recordsFiltered'] = len(master_data)
    temp_data['headers'] = MinMaxBasedRepHeaders
    
    for data in master_data:
        temp_data['aaData'].append(
            OrderedDict((
                ('sku_code', data.get('sku__sku_code')),
                ('min_qty', data.get('min_qty',0)),
                ('max_qty', data.get('max_qty',0)),
                ('zone', data.get('zone__zone', '')), 
                ('location', data.get('location__location','')),
                ('id', data.get('id')),
                ('from_location', data.get('from_location__location')),
                ('from_zone', data.get('from_zone__zone'))
            ))
        )

def get_nte_replenishment_master(
        start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters
    ):
    
    request_data = request.GET
    if not request_data:
        request_data = json.loads(request.body)
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    count = True if request.GET.get('count', '') == 'true' else False
    if order_data == "sku_code":
        order_data = "sku__sku_code"
    
    if sort_type == '1':
        order_data = '-%s' % order_data

    filters = {
        'user': warehouse.id,
        'classification': 'nte'
    }
    query_filter = Q()
    if search_term:
        query_filter = Q(sku__sku_code__icontains=search_term) | Q(zone__zone__icontains=search_term)\
            | Q(location__location__icontains=search_term) | Q(min_days__icontains=search_term) | Q(max_days__icontains=search_term)
    elif column_headers:
        column_filters_dict = {}
        column_filters = frame_datatable_column_filter(column_headers)
        custom_filter_dict = {
            "zone__icontains":"zone__zone__icontains",
            "location__icontains":"location__location__icontains",
            "sku_code__icontains":"sku__sku_code__icontains",
            "sku_desc__icontains":"sku__sku_desc__icontains",
            "sku_category__icontains":"sku__sku_category__icontains",
            "from_zone__icontains": "from_zone__zone__icontains",
            "from_location__icontains": "from_location__location__icontains"
        }
        for key, value in column_filters.items():
            if key in custom_filter_dict:
                column_filters_dict[custom_filter_dict.get(key)] = value
            else:
                if key == 'status__icontains':
                    value = 1 if value.lower() == 'active' else 0
                column_filters_dict[key] = value
        filters.update(column_filters_dict)
    master_data = ReplenishmentMaster.objects.filter(query_filter, **filters).order_by(order_data)
    if count:
        temp_data['count'] = master_data.count()
        return
    
    master_data = master_data[start_index: stop_index]
    master_data = master_data.values(
        'id','sku__sku_code', 'zone__zone', 'sku__sku_desc', 'sku__sku_category', 'location__location', 
        'from_location__location', 'from_zone__zone', 'status'
    )

    temp_data['recordsFiltered'] = len(master_data)
    temp_data['headers'] = NTERepHeaders
    status_dict = {1: 'Active', 0: 'Inactive'}
    for data in master_data:
        status = data['status']
        temp_data['aaData'].append(
            OrderedDict((
                ('id', data.get('id')),
                ('sku_code', data.get('sku__sku_code')),
                ('sku_desc', data.get('sku__sku_desc')),
                ('sku_category', data.get('sku__sku_category')),
                ('from_location', data.get('from_location__location')),
                ('from_zone', data.get('from_zone__zone')),
                ('zone', data.get('zone__zone', '')), 
                ('location', data.get('location__location','')),
                ('status', status_dict.get(status))
            ))
        )

def prepare_nte_request_data(request):
    data_dict = {}
    data_dict['SKU Code*'] = request.POST.get('sku_code')
    data_dict['Source Zone*'] = request.POST.get('from_zone')
    data_dict['Source Location'] = request.POST.get('from_location')
    data_dict['Destination Zone*'] = request.POST.get('zone')
    data_dict['Destination Location'] = request.POST.get('location')
    data_dict['Status(Active/Inactive)'] = request.POST.get('status')
    data_dict['id'] = request.POST.get('id')
    return data_dict

@get_warehouse
def validate_and_create_nte_replenishment(request, warehouse:User):
    status= ''
    try:
        log.info("Request Insert NTE replenishment request username %s and params are %s"% (str(request.user.username), str(request.POST)))

        data_dict = prepare_nte_request_data(request)
        error_data, data_list, error_list  = validate_replenishment_form(warehouse, [data_dict], min_max=False)
        if error_list:
            error_message = ', '.join(error_list)
            return JsonResponse({'message': error_message}, status=400)
        else:
            status = create_nte_replenishment_master(warehouse, data_list)
        return JsonResponse({'message': [status]}, status = 200)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Insert New NTE replenishment failed for %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(request.POST.dict()), str(e)))
        return JsonResponse({'message': 'Failed'}, status=400)

@get_warehouse
def update_replenishment_master(request, warehouse:User):
    try:
        log.info("Request Update NTE replenishment request username %s and params are %s"% (str(request.user.username), str(request.POST)))
        request_data = json.loads(request.body)
        data_dict = {
            'id' : request_data.get('id'),
            'sku__sku_code' : request_data.get('sku_code'),
            'zone__zone' : request_data.get('zone') or None,
            'location__location' : request_data.get('location') or None,
            'from_zone__zone' : request_data.get('from_zone') or None,
            'from_location__location' : request_data.get('from_location') or None,
            'user' : warehouse.id
        }
        status = request_data.get('status',"")
        status_dict = {'active':1, 'inactive':0}
        replenishment_obj = ReplenishmentMaster.objects.filter(**data_dict)
        if not replenishment_obj.exists():
            return JsonResponse({'message': 'Invalid Request Data'}, status=400)
        if status.lower() not in status_dict:
            return JsonResponse({'message': 'Invalid Status'}, status=400)
        status = status_dict[status.lower()]
        replenishment_obj = replenishment_obj[0]
        replenishment_obj.status = status
        replenishment_obj.save()
        return JsonResponse({'message': 'Updated Successfully'}, status=200)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Update NTE replenishment failed for %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(request.POST.dict()), str(e)))
        return JsonResponse({'message': 'Failed'}, status=400)

@get_warehouse
def insert_replenishmentmaster(request, warehouse:User):
    replenishment = copy.deepcopy(REPLENISHMNENT_DATA)
    classification = request.POST['classification']
    size = request.POST['size']
    min_days = request.POST['min_days']
    max_days = request.POST['max_days']
    if not classification:
        return HttpResponseBadRequest('Enter Classification')
    
    if min_days > max_days:
        return HttpResponseBadRequest('Invalid Min Max Days')
    
    replenishment_obj = ReplenishmentMaster.objects.filter(
        classification= classification, size=size, user = warehouse.id
    )
    if replenishment_obj.exists() :
        replenishment_obj = replenishment_obj[0]
        replenishment_obj.min_days = min_days
        replenishment_obj.max_days = max_days
        replenishment_obj.save()
    else:
        replenishment['classification'] = classification
        replenishment ['size'] = size
        replenishment ['min_days'] = min_days
        replenishment['max_days'] = max_days
        replenishment['user'] = warehouse
        replenishment['account_id'] = warehouse.userprofile.id

        try:
            ReplenishmentMaster.objects.create(**replenishment)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Insert New replenishment failed for %s and params are %s and error statement is %s' % (
                str(warehouse.username), str(request.POST.dict()),str(e)))
    return HttpResponse('Added Successfully')

def get_inv_replenishment_master(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters):
    request_data = request.GET
    if not request_data:
        request_data = json.loads(request.body)
    column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
    search_term = request_data.get('global_search', '')
    order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
    sort_type = request_data.get('sort_type', 0)
    if sort_type == '1':
        order_data = '-%s' % order_data
    if search_term:
            master_data = ReplenishmentMaster.objects.filter(
                Q(classification__icontains=search_term) | Q(size__icontains=search_term) |
                Q(min_days__icontains=search_term) | Q(max_days__icontains=search_term), sku__isnull=True,
                user=warehouse.id, classification__isnull = False).order_by(order_data)
    elif column_headers:
        column_filters = frame_datatable_column_filter(column_headers)
        master_data = ReplenishmentMaster.objects.filter(user=warehouse.id, sku__isnull=True, classification__isnull = False, **column_filters).\
            order_by(order_data)
    else:
        master_data = ReplenishmentMaster.objects.filter(user=warehouse.id, sku__isnull=True, classification__isnull = False).\
            order_by(order_data)

    temp_data['recordsTotal'] = len(master_data)
    temp_data['recordsFiltered'] = len(master_data)
    temp_data['headers'] = InventoryBasedRepHeaders

    for data in master_data[start_index: stop_index]:
        temp_data['aaData'].append(
            OrderedDict((('classification', data.classification), ('size', data.size),
                          ('min_days', data.min_days),('max_days', data.max_days))))


class ARSReplenishmentMasterDataTableView:
    def frame_rep_classification_data(self, temp_data, master_data):
        status_dict = {1: 'Active', 0: 'Inactive'}
        for data in master_data:
            status = data.get('status', 1)
            temp_data['aaData'].append(
                OrderedDict((
                    ('id', data.get('id')),
                    ('sku_code', data.get('sku__sku_code')),
                    ('classification', data.get('classification')),
                    ('avg_sales_day', data.get('avg_sales_day', 0)),
                    ('avg_sales_day_value', data.get('avg_sales_day_value', 0)),
                    ('cumulative_contribution', data.get('cumulative_contribution', 0)),
                    ('min_stock_qty', data.get('min_stock_qty', 0)),
                    ('max_stock_qty', data.get('max_stock_qty', 0)),
                    ('replenishment_qty', data.get('replenishment_qty', 0)),
                    ('suggested_qty', data.get('suggested_qty', 0)),
                    ('reserved', data.get('reserved', 0)),
                    ('sku_avail_qty', data.get('sku_avail_qty', 0)),
                    ('sku_pen_po_qty', data.get('sku_pen_po_qty', 0)),
                    ('sku_pen_putaway_qty', data.get('sku_pen_putaway_qty', 0)),
                    ('sku_assumed_avail_qty', data.get('sku_assumed_avail_qty', 0)),
                    ('remarks', data.get('remarks', '')),
                    ('status', status_dict.get(status, 'Active'))
                ))
            )

        return temp_data

    def parse_request(self, request):
        request_data = request.GET
        if not request_data:
            request_data = json.loads(request.body)

        column_headers = json.loads(request_data.get("columnFilter")) if request_data.get("columnFilter", "") else {}
        search_term = request_data.get('global_search', '')
        order_data = request_data.get('sort_by_column') if request_data.get('sort_by_column') else "-creation_date"
        sort_type = request_data.get('sort_type', 0)
        count = True if request.GET.get('count', '') == 'true' else False

        return column_headers, search_term, order_data, sort_type, count

    def sort_data(self, order_data, sort_type):
        # Handle special ordering for SKU code
        if order_data == "sku_code":
            order_data = "sku__sku_code"

        if sort_type == '1':
            order_data = '-%s' % order_data

        return order_data

    def get_query_filter(self, warehouse, search_term, column_headers):
        # Base filter for ARS classification - filter by user through SKU relationship
        base_filter = {
            'sku__user': warehouse.id, 'status': 1
        }
        query_filter = Q()
        if search_term:
            query_filter = (
                Q(sku__sku_code__icontains=search_term) |
                Q(classification__icontains=search_term) |
                Q(dest_location__location__icontains=search_term) |
                Q(avg_sales_day__icontains=search_term) |
                Q(replenishment_qty__icontains=search_term)
            )
        elif column_headers:
            column_filters_dict = {}
            column_filters = frame_datatable_column_filter(column_headers)
            custom_filter_dict = {
                "sku_code__icontains": "sku__sku_code__icontains",
                "classification__icontains": "classification__icontains",
            }
            for key, value in column_filters.items():
                if key in custom_filter_dict:
                    column_filters_dict[custom_filter_dict.get(key)] = value
                else:
                    column_filters_dict[key] = value

            base_filter.update(column_filters_dict)

        return base_filter, query_filter

    def get_ars_replenishment_master(self, start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters):
        """
        Get ARS Replenishment Master data for datatable using ReplenishmentClassification model
        Filters for ARS classification specifically
        """

        column_headers, search_term, order_data, sort_type, count = self.parse_request(request)
        order_data = self.sort_data(order_data, sort_type)

        base_filter, query_filter = self.get_query_filter(warehouse, search_term, column_headers)

        master_data = ReplenishmentClassification.objects.filter(query_filter, **base_filter).order_by(order_data)
        # Return total count if requested
        if count:
            temp_data['count'] = master_data.count()
            return

        # Apply pagination
        master_data = master_data[start_index: stop_index]
        master_data = master_data.values(
            'id', 'sku__sku_code', 'classification', 'avg_sales_day', 'avg_sales_day_value',
            'cumulative_contribution','min_stock_qty', 'max_stock_qty',
            'replenishment_qty', 'suggested_qty',
            'reserved', 'sku_avail_qty', 'sku_assumed_avail_qty',
            'sku_pen_po_qty', 'sku_pen_putaway_qty',
            'status', 'remarks'
        )

        temp_data['recordsTotal'] = len(master_data)
        temp_data['recordsFiltered'] = len(master_data)
        temp_data['headers'] = ARS_REPLENISHMENT_CLASSIFICATION_HEADERS
        temp_data = self.frame_rep_classification_data(temp_data, master_data)

def  get_ars_replenishment_classificaion_master(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters):
    return ARSReplenishmentMasterDataTableView().get_ars_replenishment_master(
        start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters
    )
