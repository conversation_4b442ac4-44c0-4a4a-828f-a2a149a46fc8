#python imports
import json
import copy
from collections import OrderedDict

#django imports
from django.http import HttpResponse, HttpResponseBadRequest, JsonResponse
from django.db.models import Q, Sum

#inventory imports
from inventory.models import (
    SELLABLE_CHOICES, LocationMaster, ZoneMaster,
    SubZoneMapping, LocationGroups, StockDetail, LocationType,
    HierarchyMaster, CycleCount
)

#inbound imports
from inbound.models import StagingRoute

#core imports
from core.models import (
    SKUMaster, SKUGroups
)

#outbound imports
from outbound.models import OrderTypeZoneMapping

#wms base imports
from wms_base.wms_utils import (
    LOCATION_GROUP_FIELDS, LOC_DATA, init_logger
)
from wms_base.models import User

#core function imports
from core_operations.views.common.main import (
    get_user_time_zone, get_local_date_known_timezone,
    validate_special_characters, get_user_ip, frame_datatable_column_filter,
    get_warehouse, get_misc_value
)

from inbound.models import POLocation, StagingRoute

zone_types_dict = {'zone': 'Zone', 'sub_zone': 'Sub Zone'}

log = init_logger('logs/location_master.log')

date_format_const = '%Y-%m-%d %I:%M %p'
                        
def get_location_details(
        start_index, stop_index, temp_data, global_search_term, 
        order_term, col_num, request, warehouse, filter
    ):

    count = True if request.GET.get('count', '') == 'true' else False
    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'zone__zone', '0'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = request.GET.get('sort_by_column')

    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_column_filter(json.loads(column_headers))
    
    column_filters.update({'zone__user': warehouse.id})

    sort_type = request.GET.get('sort_type')
    timezone = get_user_time_zone(warehouse)

    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    label_dict = {'bulk_area': 'Bulk Area', 'reject_area': 'Reject Area', 'qc_area': 'QC Area', 'wip_area': 'WIP Area'}
    segregation_options = dict(SELLABLE_CHOICES)

    all_groups = list(SKUGroups.objects.filter(user=warehouse.id).values_list('group', flat=True))

    location_status = {0: 'Inactive', 1: 'Active'}

    try:
        if search_term:
            loc = LocationMaster.objects.select_related('zone').filter(**column_filters).filter(
                Q(zone__zone__icontains=search_term) | Q(location__icontains=search_term)).order_by(sort_by_column)
        else:
            loc = LocationMaster.objects.select_related('zone').filter(**column_filters).order_by(sort_by_column)
        
        if count:
            temp_data['count'] = loc.count()
            return temp_data
        loc = loc[start_index:stop_index]
        temp_data['recordsFiltered'] = len(loc)
        
        for loc_location in loc:
            zone = loc_location.zone.zone
            sub_zone = loc_location.sub_zone.zone if loc_location.sub_zone else ''
            restrict_1_location_to_1_sku = loc_location.zone.restrict_one_location_to_one_sku
            button,creation_date,updation_date, created_by, updated_by = '','','','',''
            if not request.GET.get('excel'):
                button = '<button type="button" name="edit_zone" ng-click="showCase.edit_zone("'" loc_location.zone.zone"'")" ng-disabled="showCase.button_edit"  class="btn btn-primary ng-click-active" id="edit_zone" >Edit Zone</button>'
            if loc_location.creation_date:
                creation_date = get_local_date_known_timezone(timezone, loc_location.creation_date,send_date=True).strftime(date_format_const)
            if loc_location.updation_date:
                updation_date = get_local_date_known_timezone(timezone, loc_location.updation_date,send_date=True).strftime(date_format_const)
            hierarchy_data = {}
            if loc_location.json_data:
                if loc_location.json_data.get('created_by'):
                    created_by = loc_location.json_data.get('created_by')
                if loc_location.json_data.get('updated_by'):
                    updated_by = loc_location.json_data.get('updated_by')
                if loc_location.json_data.get('hierarchy_data'):
                    hierarchy_data = loc_location.json_data.get('hierarchy_data')

            sub_zone_type = ''
            segregation = loc_location.zone.segregation
            zone_segregations = ['non_sellable','inbound_staging','outbound_staging']
            if segregation in zone_segregations:
                sub_zone_type = loc_location.zone.storage_type
                sub_zone_type = label_dict.get(sub_zone_type, sub_zone_type)
            segregation = segregation_options.get(segregation)
            temp_data['aaData'].append(
                OrderedDict((('zone__zone', zone),
                             ('sub_zone__zone', sub_zone),
                             ('location', loc_location.location),
                             ('zone__segregation', segregation),
                             ('zone__storage_type', sub_zone_type),
                             ('max_capacity', loc_location.max_capacity), 
                             ('lock_status', loc_location.lock_status),
                             ('fill_sequence', loc_location.fill_sequence),
                             ('pick_sequence', loc_location.pick_sequence),
                             ('status', location_status.get(loc_location.status, '')),
                             ('all_groups', all_groups), 
                             ('location_group', []),
                             ('pallet_capacity', loc_location.pallet_capacity), 
                             ('uom', loc_location.uom),
                             ('location_type', loc_location.location_type.location_type if loc_location.location_type else ''),
                             ('carton_capacity', loc_location.carton_capacity),
                             ('restrict_one_location_to_one_sku', restrict_1_location_to_1_sku),
                             ('creation_date', creation_date),
                             ('updation_date', updation_date),
                             ('json_data__created_by', created_by), 
                             ('json_data__updated_by', updated_by),
                             ('hierarchy_data', hierarchy_data),
                             ('check_digit', loc_location.check_digit),
                             ('is_check_digit', loc_location.zone.is_check_digit),
                             (' ', button))))

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Get Zone details failed for  %s and params are %s and error statement is %s' % (
            str(warehouse.username), str(request.GET.dict()), str(e)))

    return temp_data

def get_staging_route_details(
        start_index, stop_index, temp_data, global_search_term, 
        order_term, col_num, request, warehouse, filter
    ):

    count = True if request.GET.get('count', '') == 'true' else False
    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'id', '0'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = request.GET.get('sort_by_column')
    
    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})
    if column_headers:
        column_filters = frame_datatable_column_filter(json.loads(column_headers))
    
    column_filters.update({'warehouse': warehouse.id})

    sort_type = request.GET.get('sort_type')
    timezone = get_user_time_zone(warehouse)

    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column
    
    if search_term:
        staging_route = StagingRoute.objects.filter(**column_filters).filter(
            Q(put_zone__zone__icontains=search_term) | Q(stage__icontains=search_term)).order_by(sort_by_column)
    else:
        staging_route = StagingRoute.objects.filter(**column_filters).order_by(sort_by_column)
    
    if count:
        temp_data['count'] = staging_route.count()
        return temp_data
    staging_route = staging_route[start_index:stop_index]
    values_list = ['transaction', 'attribute_name', 'attribute_value', 'stage', 'put_zone__zone', 'staging_location__location', 'creation_date', 'created_by__username', 'updation_date', 'updated_by__username', 'id']
    staging_route = staging_route.values(*values_list)
    temp_data['recordsFiltered'] = len(staging_route)

    for route in staging_route:
        creation_date = get_local_date_known_timezone(timezone, route['creation_date'],send_date=True).strftime(date_format_const)
        updation_date = get_local_date_known_timezone(timezone, route['updation_date'],send_date=True).strftime(date_format_const)
        temp_data['aaData'].append(
            OrderedDict((('id', route['id']),
                         ('transaction', route['transaction']),
                         ('attribute_name', route['attribute_name']),
                         ('attribute_value', route['attribute_value']),
                         ('stage', route['stage']),
                         ('put_zone__zone', route['put_zone__zone']),
                         ('staging_location__location', route['staging_location__location']),
                         ('creation_date', creation_date),
                         ('created_by', route['created_by__username']),
                         ('updation_date', updation_date),
                         ('updated_by', route['updated_by__username']))))

@get_warehouse
def delete_staging_route(request, warehouse: User):
    ids = request.GET.get('ids', '')
    if ids:
        ids = [int(i) for i in ids.split(',') if i.isdigit()]
        StagingRoute.objects.filter(id__in=ids, warehouse=warehouse.id).delete()
        return HttpResponse('Deleted Successfully')
    else:
        return HttpResponseBadRequest("Invalid Request")
        
def validate_request_data(request):
    request_data = {}
    try:
        request_data = request.POST
        if not request_data:
            request_data = json.loads(request.body)
    except ValueError:
        pass

    return request_data

def is_location_valid(loc_id):
    is_valid = True
    if loc_id:
        status = validate_special_characters(loc_id,'^[a-zA-Z0-9-_/|. ]*$')
        if status != "Success":
            is_valid = False
    return is_valid
    
def validate_existing_location_type_location_combination(data, user, location_type, zone_level=False):
    "validate location type to map given location type to Location master"
    locations, error_message, location_type_id = [], "", []
    if zone_level:
        ### fetching zone object and obtaining locations using reverse query
        locations = data.values_list('locationmaster__location',flat = True)
    else:
        ### fetching location objects and obtaining locations
        locations = data.values_list('location',flat = True)

    pending_putaway_filters = {'sku__user' : user.id, 'status' : 1, 'location__location__in' : locations}
    pending_putaway_data = list(POLocation.objects.filter(**pending_putaway_filters).values_list('location',flat = True))
    if pending_putaway_data:
        error_message = "Putaway is pending for this zone/location please clear that before updating location type"
    
    location_filters = {'warehouse' : user.id, 'location_type' : location_type}
    location_type_id = list(LocationType.objects.filter(**location_filters).values_list('id',flat=True))
    return error_message, location_type_id, locations

def validate_location(warehouse, request_data):
    error_message, data = '', None
    location_type_id, locations = '', []
    loc_id = request_data.get('location')
    if loc_id != loc_id.strip():
        return "Location cannot have spaces at the start or end", data, loc_id, location_type_id, locations

    loc_id = loc_id.strip()
    if not loc_id:
        return "Missing required parameters", data, loc_id, location_type_id, locations

    is_valid = is_location_valid(loc_id)
    if not is_valid:
        return "Location cannot have special characters", data, loc_id, location_type_id, locations

    data = LocationMaster.objects.filter(location__iexact=loc_id, zone__user=warehouse.id)
    if data:
        return "Location Already Exists",  data, loc_id, location_type_id, locations
    
    location_type = request_data.get('location_type', "")
    location_types = get_misc_value('location_types', warehouse.id)
    if location_types == 'true' and location_type:
        error_message, location_type_id, locations = \
            validate_existing_location_type_location_combination(data, warehouse, location_type, zone_level=False)
        if error_message and data[0].location_type and data[0].location_type.location_type != location_type:
            return error_message, data, loc_id, location_type_id, locations
        else:
            location_type_id = location_type_id[0] if location_type_id else 0
    return error_message, data, loc_id, location_type_id, locations

def validate_check_digit_limit(request_data, warehouse, update=False):
    '''This function validate the check digit and compare it with the limit'''

    check_digit = request_data if update else request_data.get('check_digit')
    # Only convert to string if it's not None and not already a string
    if check_digit is not None and not isinstance(check_digit, str):
        check_digit = str(check_digit)
    check_digit_limit = get_misc_value('check_digit_limit', warehouse.id)
    # Try to convert limit to int, but handle non-numeric values
    try:
        check_digit_limit = int(check_digit_limit) if check_digit_limit not in ['false', '0'] else 0
    except (ValueError, TypeError):
        check_digit_limit = 0
    # If no limit is set, no validation needed
    if not check_digit_limit:
        return ''
    # If check_digit is missing but required
    if not check_digit:
        return "check digit is mandatory"
    # If check_digit length doesn't match the requirement
    if len(check_digit) != check_digit_limit:
        return f"check digit should be {check_digit_limit} characters"
        
    return ''

def validate_sequence(key, value):
    key_mapping = {'fill_sequence': 'PUT Sequence', 'pick_sequence':'GET Sequence'}
    try:
        value = value or 0
        error_message = ""

        # Convert value to integer for comparison
        value = int(value)

        if key in key_mapping and value > 999999999:
            error_message = f"{key_mapping[key]} cannot exceed the maximum allowed value"

        return error_message, value  # Return error message and validated value

    except (ValueError, TypeError):
        return f"Invalid {key_mapping[key]}", value

@get_warehouse
def add_location(request, warehouse: User):
    request_data = validate_request_data(request)

    log_message = (("Request Location Master for User %s, IPAddress %s, request params are %s") % (
        str(request.user.username), (str(get_user_ip(request))), (str(request_data))
    ))
    log.info(log_message)
    error_message, data, loc_id, location_type_id, _ = validate_location(warehouse, request_data)
    if error_message:
        return HttpResponseBadRequest(error_message)
    
    if not data and loc_id:
        location_dict = copy.deepcopy(LOC_DATA)
        json_data = {
            'created_by': request.user.username, 
            'updated_by': request.user.username,
            'created_from': "WEB"
        }
        zone_obj = None
        for key, value in request_data.items():
            if key == 'status':
                value = validate_location_status(value)
            
            elif key in ['zone_id', 'sub_zone_id'] :
                # Skip validation for 'sub_zone_id' if the value is empty or None
                if key == 'sub_zone_id' and not value:
                    continue

                error_message, value, obj = validate_zones(warehouse, key, value)
                if error_message:
                    return HttpResponseBadRequest(error_message)
                
                if key == 'zone_id':
                    zone_obj = obj

            elif key in ["fill_sequence", "pick_sequence"]:
                error_message, value = validate_sequence(key, value)
                if error_message:
                    return HttpResponseBadRequest(error_message)

            elif key == 'lower_hierarchy_level_id' and value:
                hierarchy_obj = HierarchyMaster.objects.filter(id = value)
                if not hierarchy_obj.exists():
                    return HttpResponseBadRequest("Invalid Hierarchy")

            #Update the hierarchy in json_data
            update_heirarchy(key, value, json_data)

            if not value or key in ['location_group', 'sku_1mapping', 'sku_mapping', 'hierarchy']:
                continue

            location_dict[key] = value

        if zone_obj and getattr(zone_obj, 'is_check_digit') is not False:
            error_message = validate_check_digit_limit(request_data, warehouse)
            if error_message:
                return HttpResponseBadRequest(error_message)
        else:
            if request_data.get('check_digit'):
                error_message = 'Check digit is disabled for this zone'
                return HttpResponseBadRequest(error_message)

        #Validate Subzone Mapping
        error_msg = valudate_subzone_mapping(warehouse, location_dict)
        if error_msg:
            return HttpResponseBadRequest(error_msg)

        #Update the location type id in location_dict
        update_location_type_id(location_type_id, location_dict)

        location_dict.update({
            'account_id' : warehouse.userprofile.id,
            'json_data': json_data
        })

        loc_master = LocationMaster(**location_dict)
        loc_master.save()
        status = 'Added Successfully'

    log_message = (("Response Location Master for User %s, IPAddress %s, location added is %s at zone id %s") % (
        str(request.user.username), (str(get_user_ip(request))), (str(request_data.get('location'))),(str(request_data.get('zone_id')))
    ))
    log.info(log_message)
    return HttpResponse(status)

def valudate_subzone_mapping(warehouse, location_dict):
    subzone = location_dict.get('sub_zone_id', '') or ''
    if subzone:
        zone_id = location_dict.get('zone_id', '') or ''
        error_msg = validate_sub_zone_mapping(warehouse, subzone, zone_id)
        if error_msg:
            return error_msg
    return None

def validate_sub_zone_mapping(warehouse, subzone, zone_id):
    """Subzone mapping validation"""
    loc_obj = LocationMaster.objects.filter(
            status = 1, zone__user = warehouse.id, sub_zone_id = subzone
        )
    if loc_obj.exists():
        loc_obj = loc_obj[0]
        if loc_obj.zone.id != zone_id:
            return "Subzone is already mapped to " + loc_obj.zone.zone
    return None

def validate_subzone_mapping_update(warehouse, data):
    subzone = data.sub_zone_id or ''
    if subzone:
        zone_id = data.zone_id or ''
        error_msg = validate_sub_zone_mapping(warehouse, subzone, zone_id)
        if error_msg:
            return error_msg
    return None

def validate_location_status(value):
    value = 1 if value == 'Active' else 0
    return value

def validate_zones(warehouse, key, value):
    error_message, return_value = '', ''
    filter_dict = {'zone': value.upper(), 'user': warehouse.id, 'zone_type': 'zone'}
    if key == 'sub_zone_id':
        filter_dict['zone_type'] = 'sub_zone'

    zone_obj = ZoneMaster.objects.filter(**filter_dict)
    if zone_obj.exists():
        return_value = zone_obj[0].id
        return error_message, return_value, zone_obj[0]
    else:
        error_message = "Invalid Zone / Subzone"
    return error_message, return_value, None

def update_heirarchy(key, value, json_data):
    if key == 'hierarchy' and value:
        json_data.update({'hierarchy_data': value})

def update_location_type_id(location_type_id, location_dict):
    if location_type_id:
        location_dict['location_type_id'] = location_type_id
        del location_dict['location_type']

def get_json_data(request, data, hierarchy):
    if data.json_data and data.json_data.get('updated_by'):
        data.json_data.update({'updated_by': request.user.username})
    else:
        data.json_data = {'updated_by': request.user.username}
    if hierarchy:
        data.json_data.update({'hierarchy_data': hierarchy})
    return data


def validate_status(warehouse, data, loc_id, value):
    error_msg = ''
    value = 1 if value == 'Active' else 0
    if data.status != value:
        stock_qty =  StockDetail.objects.exclude(receipt_number=0).filter(
            sku__user=warehouse.id, location__location=loc_id
        ).distinct().aggregate(total=Sum('quantity')).get('total', 0)
        if stock_qty:
            error_msg = "Stock available in the selected Location, Move the Stock & try again"
        
        open_inventory_adjustments = CycleCount.objects.filter(
            sku__user = warehouse.id, location__location = loc_id, status = 2
        )
        if open_inventory_adjustments:
            error_msg = "Inventory adjustments are pending for this location, Please clear them before deactivating the location"

    return value, error_msg


def get_location_type(request_data, loc_data, warehouse):
    """Get location types"""
    location_type = request_data.get('location_type', "")
    location_types = get_misc_value('location_types', warehouse.id)
    location_type_id, error_message = '', ''
    if location_types == 'true' and location_type:
        error_message, location_type_id, _ = \
            validate_existing_location_type_location_combination(loc_data, warehouse, location_type)
        if not error_message:
            location_type_id = location_type_id[0] if location_type_id else 0
    return error_message, location_type_id

@get_warehouse
def update_location(request, warehouse: User):
    request_data = validate_request_data(request)
    loc_id = request_data.get("location")
    loc_data = LocationMaster.objects.filter(location=loc_id, zone__user=warehouse.id)

    error_message, location_type_id = get_location_type(request_data, loc_data, warehouse)
    if error_message:
        return HttpResponseBadRequest(error_message)

    data = loc_data[0]
    hierarchy = ''
    for key, value in request_data.items():
        if key in ['max_capacity', 'pallet_capacity', 'carton_capacity']:
            value = get_capacity_values(value)

        elif key in ["fill_sequence", "pick_sequence"]:
            error_message, value = validate_sequence(key, value)
            if error_message:
                return HttpResponseBadRequest(error_message)

        elif key == 'status':
            value, error_msg = validate_status(warehouse, data, loc_id, value)
            if error_msg:
                return HttpResponseBadRequest(error_msg)

        elif key == 'zone_id':
            continue

        elif key == 'check_digit':
            if data.zone and getattr(data.zone, 'is_check_digit') is not False:
                error_msg = validate_check_digit_limit(value, warehouse, True)
                if error_msg:
                    return HttpResponseBadRequest(error_msg)
            else:
                if value:
                    error_msg = 'Check digit is disabled for this zone'
                    return HttpResponseBadRequest(error_msg)

        elif key == 'sub_zone_id':
            if key == 'sub_zone_id' and not value:
                continue
            
            error_message, value, _ = validate_zones(warehouse, key, value)
            if error_message:
                return HttpResponseBadRequest(error_message)

        elif key == 'lower_hierarchy_level_id' and value:
            hierarchy_obj = HierarchyMaster.objects.filter(id=value)
            if not hierarchy_obj.exists():
                return HttpResponseBadRequest("Invalid Hierarchy")

        elif key == 'uom' and "string:?" in value:
            value = ""

        elif key == 'hierarchy':
            hierarchy = value
            continue

        elif key == 'location_type':
            if not location_type_id:
                continue
            key = 'location_type_id'
            value = location_type_id

        setattr(data, key, value)

    #Validate Subzone Mapping
    error_msg = validate_subzone_mapping_update(warehouse, data)
    if error_msg:
        return HttpResponseBadRequest(error_msg)

    data = get_json_data(request, data, hierarchy)
    data.save()
    return HttpResponse('Updated Successfully')

def get_capacity_values(value):
    try:
        value = float(value) if value else 0.0
        return value
    except (ValueError, TypeError):
        return 0.0

@get_warehouse
def get_locations(request, warehouse: User):
    filter_params={'location__zone__user': warehouse.id}
    locations_filter = request.GET.get('locations', "")
    order_type = request.GET.get('order_type', "")

    if locations_filter=="inv_adj":
        filter_params["quantity__gte"]=0
    elif locations_filter=="all":
        filter_params = {'zone__user': warehouse.id}
        if request.GET.get('zone'):
            filter_params['zone__zone__in'] = request.GET.get('zone').split(',')

        location_list = list(LocationMaster.objects.exclude(zone__storage_type = 'cc_variance').filter(status = 1, **filter_params).\
                             values_list('location',flat=True).distinct())
        return HttpResponse(json.dumps({'locations': location_list}))
    elif order_type:
        zones = set(OrderTypeZoneMapping.objects.filter(order_type=order_type, user=warehouse.id, status=1, receipt_type='PICKLIST', zone__isnull=False).values_list('zone_id', flat=True))
        if zones:
            filter_params['location__zone_id__in'] = zones
        else:
            filter_params['location__zone__segregation'] = 'sellable'
        filter_params['quantity__gt']=0
    else:
        filter_params['quantity__gt']=0

    if request.GET.get('sku_code',''):
        filter_params['sku__sku_code'] = request.GET.get('sku_code','')
    
    exclusion_conditions = Q(location__zone__segregation__in=['inbound_staging', 'outbound_staging']) | \
                           Q(location__zone__storage_type__in=['dock', 'qc', 'pnd', 'wip_area', 
                                                               'nte_staging_area', 'replenishment_staging_area', 'cc_variance']) | \
                           Q(status=2)
    
    if locations_filter=="inv_adj":
        location_list = list(
            StockDetail.objects.select_related('location', 'location__zone')
            .exclude(exclusion_conditions)
            .filter(**filter_params)
            .values_list('location__location', flat=True)
            .distinct()
        )
    else:
        location_list = list(
            StockDetail.objects.exclude(location__zone__storage_type = 'cc_variance').filter(**filter_params, location__status=1)
            .values_list('location__location', flat=True)
            .distinct()
        )
    return HttpResponse(json.dumps({'locations': location_list}))

@get_warehouse
def get_location_capacity(request, warehouse: User):
    wms_code = request.GET.get('wms_code')
    location = request.GET.get('location')
    capacity = 0
    if wms_code:
        sku_master = SKUMaster.objects.filter(user=warehouse.id, wms_code=wms_code)
        if not sku_master:
            return HttpResponse(json.dumps({'message': 'Invalid WMS code'}))

    if location:
        location_master = LocationMaster.objects.filter(zone__user=warehouse.id, location=location)
        if not location_master:
            return HttpResponse(json.dumps({'message': 'Invalid Location'}))
        filled_capacity = int(location_master[0].filled_capacity)
        max_capacity = int(location_master[0].max_capacity)
        capacity = max_capacity - filled_capacity
        if capacity < 0:
            location_master[0].max_capacity = int(location_master[0].max_capacity) + int(abs(capacity))
            location_master[0].save()
            capacity = 0

    return HttpResponse(json.dumps({'capacity': capacity, 'message': 'Success'}))


def get_staging_route_location_details(warehouse, putzones, transaction, current_stage=None, get_insp = False):
    """
    Retrieves staging route details for a given warehouse and set of put zones.

    Args:
        warehouse (Warehouse): The warehouse object, with the ID used for filtering.
        putzones (list): A list of zones where the putaway process occurs.
        current_stage (str, optional): The current stage to filter routes. If not provided, defaults to 'REC' stage.

    Returns:
        dict: A dictionary where keys are tuples of (put_zone, attribute_value, stage) and values are the corresponding staging location.
    """
    # Define initial filters based on warehouse, transaction type, and put zones
    filters = {
        'warehouse': warehouse.id,
    }
    if putzones:
        filters['put_zone__zone__in'] = putzones
    if transaction:
        filters['transaction__in'] = transaction
    
    # If get_insp is True, return the staging location for the 'INSP' stage
    if get_insp:
        stag_route = StagingRoute.objects.filter(stage = 'INSP', **filters).first()
        if stag_route:
            return stag_route.staging_location.location
        return None

    # If current_stage is not provided, default to 'REC' stage
    if not current_stage:
        filters['stage'] = 'REC'
    elif current_stage == 'REC':
        filters['stage__in'] = ['REPACK','PUT']
    elif current_stage in ['REPACK', 'INSP']:
        filters['stage'] = 'PUT'

    # Initialize the return dictionary
    return_dict = {}

    if current_stage != 'PUT':
        # Query the StagingRoute model for relevant details
        staging_route_details = StagingRoute.objects.filter(**filters).values(
            'put_zone__zone', 'attribute_value', 'stage', 'staging_location__location', 'staging_location_id',
            'transaction'
        )

        # Process each route detail and store it in the dictionary
        for route_details in staging_route_details:
            put_zone = route_details.get('put_zone__zone')
            attribute_value = route_details.get('attribute_value')
            stage = route_details.get('stage')
            staging_location = route_details.get('staging_location__location')
            staging_location_id = route_details.get('staging_location_id')
            transaction = route_details.get('transaction')

            # Use a tuple (put_zone, attribute_value, stage) as the key and staging_location as the value
            staging_route_key = (put_zone, attribute_value, stage, transaction)
            return_dict[staging_route_key] = {'location': staging_location, 'location_id': staging_location_id}

    return return_dict

def get_next_suggested_staging_location(staging_route_data, current_key):
    if staging_route_data and len(current_key) == 4:
        zone, barcoding, current_stage, transaction = current_key

        # Mapping for the next stage based on barcoding and current stage
        next_stage_mapping = {
            'yes': {'REC': 'REPACK', 'REPACK': 'PUT', '': 'REC'},
            'no': {'REC': 'PUT', '': 'REC', 'INSP': 'PUT'}
        }

        # Determine the next stage based on barcoding and current stage
        next_stage = next_stage_mapping.get(barcoding, {}).get(current_stage, '')

        #Suggested Location from the data or default to an empty dictionary
        suggested_loc = staging_route_data.get((zone, barcoding, next_stage, transaction), {})

        if not suggested_loc and current_stage == 'REC' and barcoding == 'yes':
            suggested_loc = staging_route_data.get((zone, barcoding, 'PUT', transaction), {})

        return suggested_loc

    return {}

@get_warehouse
def get_location_label_data(request, warehouse=User):
    location = request.GET.get('location', '')
    zone = request.GET.get('zone', '')
    sub_zone = request.GET.get('sub_zone', '')
    filter_params = {'zone__user': warehouse.id}
    
    if location:
        filter_params['location'] = location
    if zone:
        filter_params['zone__zone'] = zone
    if sub_zone:
        filter_params['sub_zone_id'] = sub_zone
        
    locations = LocationMaster.objects.select_related('zone', 'sub_zone', 'location_type').filter(**filter_params, status=1)
    
    items = []
    for location_obj in locations:
        item = {
            "location": location_obj.location,
            "zone": location_obj.zone.zone if location_obj.zone else None,
            "sub_zone": location_obj.sub_zone.sub_zone if location_obj.sub_zone else None,
            "location_type": location_obj.location_type.name if location_obj.location_type else None,
            "max_capacity": location_obj.max_capacity,
            "filled_capacity": location_obj.filled_capacity,
            "pallet_capacity": location_obj.pallet_capacity,
            "pallet_filled": location_obj.pallet_filled,
            "carton_capacity": location_obj.carton_capacity,
            "carton_filled": location_obj.carton_filled,
            "fill_sequence": location_obj.fill_sequence,
            "pick_sequence": location_obj.pick_sequence,
            "lock_status": location_obj.lock_status,
            "uom": location_obj.uom,
            "status": location_obj.status,
            "check_digit": location_obj.check_digit,
        }
        
        # Add zone-specific fields if zone exists
        if location_obj.zone:
            item.update({
                "zone_type": location_obj.zone.zone_type,
                "storage_type": location_obj.zone.storage_type,
                "level": location_obj.zone.level,
                "segregation": location_obj.zone.segregation,
                "unique_batch": location_obj.zone.unique_batch,
                "restrict_one_location_to_one_sku": location_obj.zone.restrict_one_location_to_one_sku,
                "suggestion_type": location_obj.zone.suggestion_type,
                "carton_managed": location_obj.zone.carton_managed,
                "put_sequence": location_obj.zone.put_sequence,
                "get_sequence": location_obj.zone.get_sequence,
                "zone_check_digit": "Enabled" if location_obj.zone.is_check_digit else "Disabled",
            })
        
        items.append(item)
    
    if not items:
        return JsonResponse({"error": "LOCATION not found"}, status=400)
    
    return JsonResponse({"data": items})