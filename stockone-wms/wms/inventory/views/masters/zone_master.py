#python packages
import copy
import json
from collections import OrderedDict, defaultdict

#django imports
from django.http import HttpResponse, HttpResponseBadRequest
from django.db.models import Q

#inventory imports
from inventory.models import (
    SELLABLE_CHOICES, ZoneMaster, LocationType,
    SubZoneMapping, ZoneMarketplaceMapping, TableLists,
    LocationMaster, StockDetail
)
#wms_base imports
from wms_base.models import User


#core imports
from core.models import TaxMaster


#wms base imports
from wms_base.wms_utils import (
    ZONE_DATA, init_logger, allowed_inbound_storage_type, allowed_outbound_storage_type, allowed_non_sell_storage_type
)

from core_operations.views.integration.integration import webhook_integration_3p


log = init_logger('logs/location_master.log')

from core_operations.views.common.main import (
    validate_special_characters, get_user_ip,
    get_local_date_known_timezone,
    get_user_time_zone, frame_datatable_column_filter,
    get_warehouse, get_decimal_value, truncate_float,
    get_misc_value
)


from .location_master import validate_existing_location_type_location_combination

special_char_regex = '^[a-zA-Z0-9-_/|.]*$'


zone_types_dict = {'zone': 'Zone', 'sub_zone': 'Sub Zone'}


def get_request_data(request):
    request_data = {}
    try:
        request_data = request.POST
        if not request_data:
            request_data = json.loads(request.body)
    except ValueError:
        pass
    return request_data

def validate_zone(request_data):
    zone = request_data.get('zone', '')
    if not zone:
        return "Please enter a zone"
    error_message = False
    if zone:
        status = validate_special_characters(zone, special_char_regex)
        if status != "Success":
            error_message = "Zone ID cannot have special character"
    return error_message, zone


def create_zone(
        warehouse, zone, level, unique_batch, restrict_one_location_to_one_sku,
        sub_zone, segregation, carton_managed, categories, location_type_id, zone_type,
        put_sequence, get_sequence, is_check_digit=False
    ):
    location_dict = copy.deepcopy(ZONE_DATA)
    location_dict['user'] = warehouse.id
    location_dict['zone'] = zone.upper()
    location_dict['level'] = level
    location_dict['unique_batch'] = unique_batch
    location_dict['account_id'] = warehouse.userprofile.id
    location_dict['restrict_one_location_to_one_sku']= restrict_one_location_to_one_sku
    location_dict['zone_type'] = zone_type
    location_dict['put_sequence'] = put_sequence
    location_dict['get_sequence'] = get_sequence
    location_dict['is_check_digit'] = is_check_digit
    if sub_zone:
        location_dict['storage_type'] = sub_zone
    if segregation:
        location_dict['segregation'] = segregation
    location_dict['carton_managed'] = carton_managed
    if location_type_id:
        location_dict['location_type_id'] = location_type_id
    loc_master = ZoneMaster.objects.filter(zone = location_dict['zone'], user = location_dict['user'])
    if not loc_master.exists():
        loc_master = ZoneMaster(**location_dict)
        loc_master.save()
    else:
        loc_master = loc_master[0]
    update_zone_category_list(loc_master, categories)

def update_zone(
        data, segregation, categories, unique_batch, restrict_one_location_to_one_sku,
        suggestion_type, sub_zone, carton_managed, zones_list, location_type_id, zone_type,
        level, put_sequence, get_sequence, is_check_digit=None
    ):
    data = data[0]
    if segregation:
        if data.segregation != segregation and segregation in ['sellable','non_sellable']:
            zones_list.append(data.zone)
        data.segregation = segregation
        data.save()
        update_sub_zone_segregation(data)
        update_zone_category_list(data, categories)
    data.unique_batch = unique_batch
    data.restrict_one_location_to_one_sku= restrict_one_location_to_one_sku
    if location_type_id:
        data.location_type_id = location_type_id
    if suggestion_type:
        data.suggestion_type = suggestion_type
    if sub_zone:
        data.storage_type = sub_zone
    if level:
        data.level = level
    if is_check_digit is not None:
        data.is_check_digit = is_check_digit
    data.carton_managed = carton_managed
    data.zone_type = zone_type
    data.put_sequence = put_sequence
    data.get_sequence = get_sequence
    data.save()


@get_warehouse
def add_zone(request, warehouse: User):

    #Process Request Data
    request_data = get_request_data(request)

    log_message = (
        ("Request Zone Updation Username %s, IP Address %s, and request params are %s") %(
            str(request.user.username), str(get_user_ip(request)), str(request_data)
        )
    )
    log.info(log_message)

    error_status, zone = validate_zone(request_data)
    if error_status:
        return HttpResponseBadRequest("Zone ID cannot have special character")

    sub_zone = request_data.get('sub_zone','')
    data = ZoneMaster.objects.filter(zone__iexact=zone, user=warehouse.id)
    update = request_data.get('update', '')
    level = request_data.get('level', 0)
    segregation = request_data.get('segregation', '')
    seg_options = dict(SELLABLE_CHOICES).keys()
    categories = request_data.get('categories', '')
    categories = categories.split(',') if categories else []
    unique_batch = request_data.get('unique_batch', 0)
    restrict_one_location_to_one_sku = request_data.get('restrict_one_location_to_one_sku', 0)
    restrict_one_location_to_one_sku = restrict_one_location_to_one_sku if restrict_one_location_to_one_sku else 0
    unique_batch = unique_batch if unique_batch else 0
    suggestion_type = request_data.get('suggestion_type', '')
    location_type = request_data.get('location_type', "")
    zone_type = request_data.get('zone_type',"zone")
    put_sequence = request_data.get('put_sequence', 0)
    get_sequence = request_data.get('get_sequence', 0)
    carton_managed = request_data.get('carton_managed', False)
    is_check_digit = request_data.get('is_check_digit', False)
    if carton_managed:
        carton_managed = 1
    else:
        carton_managed = 0
    zones_list = []
    if segregation and segregation not in seg_options:
        return HttpResponseBadRequest("Invalid Segragation Option")

    error_status = validate_sequence_details(warehouse, put_sequence, get_sequence, zone, zone_type)
    if error_status:
        return HttpResponseBadRequest(error_status)
    
    error_message, location_type_id, locations = '', '', []
    location_types = get_misc_value('location_types', warehouse.id)
    if location_types == 'true' and location_type:
        error_message, location_type_id, locations = \
            validate_existing_location_type_location_combination(data, warehouse, location_type, zone_level=True)
        if error_message and data[0].location_type and data[0].location_type.location_type != location_type:
            return HttpResponseBadRequest(error_message)
        else:
            location_type_id = location_type_id[0] if location_type_id else 0
    
    if level == '':
        level = 0
    if update == 'true':
        if not data:
            status = 'ZONE not found'
        else:
            #Update Zone if exists already
            update_zone(
                data, segregation, categories, unique_batch, restrict_one_location_to_one_sku, 
                suggestion_type, sub_zone, carton_managed, zones_list, location_type_id, zone_type,
                level, put_sequence, get_sequence, is_check_digit
            )
            status = 'Update Successfully'
    else:
        if not data:
            #Creates New Zone
            create_zone(
                warehouse, zone, level, unique_batch, restrict_one_location_to_one_sku, 
                sub_zone, segregation, carton_managed, categories, location_type_id, zone_type,
                put_sequence, get_sequence, is_check_digit
            )
            status = 'Added Successfully'
        else:
            return HttpResponseBadRequest('Zone Already Exists')
    
    if location_type_id:
        data.location_type_id = location_type_id
        LocationMaster.objects.filter(
            zone__user = warehouse.id,location__in = locations).update(location_type = location_type_id)

    #Inventory Callback
    inventory_call_back_on_zone_updation(warehouse, zones_list)

    return HttpResponse(status)

def inventory_call_back_on_zone_updation(warehouse, zones_list):
    '''
    Inventory Callback for Zone Segregation Update
    '''
    if not zones_list:
        return None

    stock_objs = StockDetail.objects.select_related(
        'sku', 'location__zone'
    ).filter(
        sku__user = warehouse.id, location__zone__zone__in=zones_list
    ).values_list('sku__sku_code', 'location__zone_id')

    sku_codes = []
    stock_zone_dict = defaultdict(list)

    for sku_code, zone_id in stock_objs:
        sku_codes.append(sku_code)
        stock_zone_dict[zone_id].append(sku_code)

    filters = {
        "sku_codes": sku_codes,
        "zones_data": stock_zone_dict
    }
    try:
        webhook_integration_3p(warehouse.id, 'zone_updation', filters)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(
            f'Async Inventory update webhook failed for Zone Segregation Update for sku_codes {list(sku_codes)} and exception is {str(e)}'
        )

def validate_sequence_details(warehouse, put_sequence, get_sequence, zone, zone_type):
    error_list = []
    if zone_type == 'sub_zone' and zone:
        put_sequence_data, get_sequence_data = get_existing_sequence_details(warehouse)
        if not (put_sequence or get_sequence):
            error_list.append('Put Sequence and Get Sequence are Mandatory')
        if put_sequence in put_sequence_data and put_sequence_data[put_sequence] != zone:
            error_list.append('Put Sequence already exists for another subzone')
        if get_sequence in get_sequence_data and get_sequence_data[get_sequence] != zone:
            error_list.append('Get Sequence already exists for another subzone')
    return ', '.join(error_list)

def get_existing_sequence_details(warehouse):
    put_sequence_data, get_sequence_data = {}, {}
    zone_data = list(ZoneMaster.objects.filter(
        user = warehouse.id, zone_type = 'sub_zone'
    ).values('zone', 'put_sequence', 'get_sequence'))
    for data in zone_data:
        put_sequence = data.get('put_sequence')
        get_sequence = data.get('get_sequence')
        zone = data.get('zone')
        if put_sequence:
            put_sequence_data[put_sequence] = zone
        if get_sequence:
            get_sequence_data[get_sequence] = zone
    return put_sequence_data, get_sequence_data


def update_zone_marketplace_mapping(zone, marketplace_list):
    mappings = ZoneMarketplaceMapping.objects.filter(zone__user=zone.user, zone__zone=zone.zone)
    marketplace = []
    resp = ''
    if marketplace_list:
        marketplace = marketplace_list.split(",")
    if mappings:
        for mapping in mappings:
            status = 0
            if mapping.marketplace in marketplace:
                marketplace.remove(mapping.marketplace)
                status = 1
            mapping.status = status
            mapping.save()
        resp = 'updated'
    if marketplace:
        for mkp in marketplace:
            zone_marketplace = ZoneMarketplaceMapping(**{'zone_id': zone.id, 'marketplace': mkp})
            zone_marketplace.save()
        resp = 'created'
    return resp

def update_zone_category_list(model_obj, elements):
    exist_element_list = model_obj.sku_category.filter().values_list('name', flat=True)
    exist_elements = [(str(e_elem)).lower() for e_elem in exist_element_list]
    for elem in elements:
        element_obj, created = TableLists.objects.get_or_create(name=elem, account_id=model_obj.user)
        model_obj.sku_category.add(element_obj)
        if elem.lower() in exist_elements:
            exist_elements.remove(elem.lower())
    for exist_elem in exist_elements:
        elem_obj = TableLists.objects.filter(name=exist_elem)
        if elem_obj:
            model_obj.sku_category.remove(elem_obj[0])


def update_sub_zone_segregation(zone_obj):
    sub_zone_ids = SubZoneMapping.objects.filter(zone_id=zone_obj.id).values_list('sub_zone_id', flat=True)
    ZoneMaster.objects.filter(user=zone_obj.user, id__in=sub_zone_ids).update(segregation=zone_obj.segregation)

@get_warehouse
def get_zones_list(request, warehouse=User):
    zone = request.GET.get('zone', '')
    zone_type = request.GET.get('zone_type', 'zone')
    with_id = request.GET.get('with_id', False)
    exclude_unmapped_subzones = request.GET.get('exclude_unmapped_subzones', 'false')
    if zone:
        resp = {'zone': zone}
        zone_obj = ZoneMaster.objects.filter(user = warehouse.id, zone = zone, zone_type = zone_type)
        if zone_obj.exists():
            zone_obj = zone_obj[0]
            status = 'Success'
            resp.update({
                'level': zone_obj.level, 'unique_batch': zone_obj.unique_batch,
                'restrict_one_location_to_one_sku': zone_obj.restrict_one_location_to_one_sku,
                'segregation': zone_obj.segregation, 'sub_zone': zone_obj.storage_type,
                'suggestion_type': zone_obj.suggestion_type, 'carton_managed': zone_obj.carton_managed,
                'put_sequence': zone_obj.put_sequence, 'get_sequence': zone_obj.get_sequence,
                'is_check_digit': zone_obj.is_check_digit
            })
        else:
            status = 'ZONE not found'
        resp['msg'] = status
        return HttpResponse(json.dumps(resp))
    else:
        zones_list, check_digit_dict = get_user_zones(warehouse, zone_type, with_id=with_id, exclude_unmapped_subzones=exclude_unmapped_subzones, include_check_digit=True)
        zone_seq_dict = {}
        if zone_type == 'sub_zone':
            zone_seq_dict = zones_list.copy()
            zones_list = list(zone_seq_dict.keys())
        product_types = list(TaxMaster.objects.filter(user_id=warehouse.id).values_list('product_type', flat=True).distinct())
        return HttpResponse(json.dumps({'zones': zones_list, 'zone_seq_mapping': zone_seq_dict, 'product_types': product_types, 'zone_level_check_digit': check_digit_dict}))
    
def get_user_zones(warehouse: User, zone_type, level='', exclude_mapped=False, exclude_zone_types=[], with_id=False, exclude_unmapped_subzones=False, include_check_digit=False):
    """ Get Zones based on the filters"""
    if zone_type == 'sub_zone' and exclude_unmapped_subzones == 'true':
        location_filter = {'sub_zone__user': warehouse.id, 'status': 1, 'sub_zone__zone_type': 'sub_zone'}
        return dict(LocationMaster.objects.filter(**location_filter).values_list('sub_zone__zone', 'sub_zone__get_sequence')), {}
    zone_filter = {'user': warehouse.id, 'zone_type': zone_type}
    if level:
        zone_filter['level'] = level
    zone_master = ZoneMaster.objects.filter(**zone_filter)
    if exclude_mapped:
        excl_list = SubZoneMapping.objects.filter(zone__user=warehouse.id).values_list('sub_zone_id', flat=True)
        zone_master = zone_master.exclude(id__in=excl_list)
    if exclude_zone_types:
        zone_master = zone_master.exclude(segregation__in=exclude_zone_types)
    zone_master = zone_master.exclude(storage_type__in = ['wip_area'])
    check_digit_dict = {}
    if include_check_digit:
        check_digit_dict = dict(zone_master.values_list('zone', 'is_check_digit'))
    if with_id == 'true':
        return dict(zone_master.values_list('id','zone')), check_digit_dict
    elif zone_type == 'sub_zone':
        return dict(zone_master.values_list('zone', 'get_sequence')), check_digit_dict
    else:
        return list(zone_master.values_list('zone', flat=True)), check_digit_dict

@get_warehouse
def search_location_data(request, warehouse:User):
    request_data = request.GET
    search_key = request_data.get('q', '')
    zone = request_data.get('type' ,'')
    storage_type = request_data.get('storage_type' ,'')
    lpn_managed = request_data.get('lpn_managed', False)
    exclude_check = request_data.get('exclude_check' ,'false')
    exact_search = request_data.get('exact_search', 'false').lower() == 'true'

    total_data = []

    filter_params  = {'zone__user':warehouse.id,'status':1}
    if zone:
        filter_params['zone__zone'] = zone
    if storage_type:
        filter_params['zone__storage_type'] = storage_type
    if lpn_managed:
        filter_params['zone__carton_managed'] = True

    if search_key:
        if exact_search:
            filter_params['location'] = search_key
        else:
            filter_params['location__icontains'] = search_key

    exclude_dict = {}
    if exclude_check == 'true':
        exclude_dict = {'location__exact': '', 'lock_status__in': ['Inbound', 'Inbound and Outbound'], 'zone__zone__in':['QC_ZONE']}

    master_data = LocationMaster.objects.filter(
        **filter_params
    ).exclude(**exclude_dict).order_by('fill_sequence').values('location', 'zone__zone', 'zone__carton_managed', 'check_digit', 'pick_sequence')
    for data in master_data[:30]:
        total_data.append({'location': data.get('location', ''), 'zone': data.get('zone__zone', ''), 'lpn_managed': data.get('zone__carton_managed', ''), 'check_digit': data.get('check_digit', ''), 'pick_sequence': data.get('pick_sequence', '')})
    return HttpResponse(json.dumps(total_data))


def get_location_types(start_index, stop_index, temp_data, search_term, order_term, col_num, request, user, filters):
    #start_index, stop_index, search_term, order_term, col_num, 
    sort_type = request.GET.get('sort_type', 0)
    sort_on = request.GET.get('sort_by_column') or '-po_number'
    column_headers = request.GET.get('columnFilter', {})

    order_by = []
    if sort_type == '1':
        sort_on = '-%s' % sort_on
    if sort_on:
        order_by.append(sort_on)

    filters = {'warehouse' : user.id}
    
    # Framing Filters for Column Search
    if column_headers:
        filters.update(json.loads(column_headers))
    
    master_data = list(LocationType.objects.filter(**filters).values()[start_index: stop_index])
    temp_data['recordsTotal'] = len(master_data)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    temp_data['headers'] = {}

    date_fields = ['creation_date', 'updation_date']
    quantity_keys = ['height', 'weight', 'breadth']
    timezone = get_user_time_zone(user)
    decimal_limit = get_decimal_value(user.id)
    for each_row in master_data:
        for key in date_fields:
            if each_row[key]:
                each_row[key] = get_local_date_known_timezone(timezone, each_row[key])
        for key in quantity_keys:
            if each_row[key]:
                each_row[key] = truncate_float(float(each_row[key]), decimal_limit)
    
        each_row['DT_RowClass'] = 'results'
        each_row['DT_RowId'] = each_row['location_type']
        temp_data['aaData'].append(each_row)




def get_zone_details(start_index, stop_index, temp_data, global_search_term, order_term, col_num, request, warehouse, filters):

    count = True if request.GET.get('count', '') == 'true' else False
    #search terms
    search_term = request.GET.get('global_search', '')
    sort_by_column, sort_type = 'zone', '0'
    if request.GET.get('sort_by_column') != '':
        sort_by_column = request.GET.get('sort_by_column')
    
    #Header Search
    column_filters = {}
    column_headers = request.GET.get('columnFilter', {})

    if column_headers:
        column_filters = frame_datatable_column_filter(json.loads(column_headers))

    #Zone Type filter should be exact
    for key in list(column_filters):
        if key in ['zone_type__icontains']:
            zone_type = column_filters.pop(key)
            column_filters['zone_type'] = zone_type

    sort_type = request.GET.get('sort_type')
    timezone = get_user_time_zone(warehouse)

    if sort_type == '1':
        sort_by_column = '-%s' % sort_by_column

    lis = [
        'zone', 'storage_type', 'segregation', 'restrict_one_location_to_one_sku', 'location_type__location_type',
        'carton_managed', 'unique_batch', 'carton_managed', 'creation_date', 'updation_date', 'zone_type', 'level',
        'put_sequence', 'get_sequence', 'is_check_digit'
    ]
    
    if search_term:
            master_data = ZoneMaster.objects.filter(
                Q(zone__icontains=search_term) | Q(storage_type__icontains=search_term) |
                Q(segregation__icontains=search_term), 
                user=warehouse.id).order_by(sort_by_column)
    else:
        master_data = ZoneMaster.objects.filter(user=warehouse.id, **column_filters).\
            order_by(sort_by_column)
    
    if count:
        temp_data['count'] = master_data.count()
        return temp_data

    master_data = master_data[start_index: stop_index].values(*lis)
    temp_data['recordsFiltered'] = len(master_data)
    
    for data in master_data:
        creation_date = get_local_date_known_timezone(
            timezone, data.get('creation_date'), send_date=True
        ).strftime('%Y-%m-%d %I:%M %p')
        updation_date = get_local_date_known_timezone(
            timezone, data.get('updation_date'), send_date=True
        ).strftime('%Y-%m-%d %I:%M %p')

        storage_type = data.get('storage_type') if data.get('segregation') != 'sellable' else ''

        temp_data['aaData'].append(
            OrderedDict((
                ('zone', data.get('zone')),
                ('zone_type', zone_types_dict.get(data.get('zone_type'))),
                ('storage_type', storage_type),
                ('segregation', data.get('segregation', '')),
                ('restrict_one_location_to_one_sku', data.get('restrict_one_location_to_one_sku', '')), 
                ('carton_managed', data.get('carton_managed','')),
                ('location_type', data.get('location_type__location_type','')),
                ('level', data.get('level', '')),
                ('creation_date', creation_date),
                ('updation_date', updation_date),
                ('put_sequence', data.get('put_sequence', '')),
                ('get_sequence', data.get('get_sequence', '')),
                ('is_check_digit', data.get('is_check_digit', False))
        )))

@get_warehouse
def get_zones(request, warehouse: User):
    level = request.GET.get('level', '')
    exclude_mapped = request.GET.get('exclude_mapped', [])
    exclude_zone_types = request.GET.get('exclude_zone_types', '').split(',')
    send_zone_with_id = request.GET.get('send_zone_id', False)
    zone_type = request.GET.get('zone_type', 'zone')
    zones_list, _ = get_user_zones(
        warehouse, zone_type = zone_type, level=level, exclude_mapped=exclude_mapped, exclude_zone_types=exclude_zone_types, 
        with_id=send_zone_with_id
    )
    zone_seq_dict = {}
    if zone_type == 'sub_zone':
        zone_seq_dict = zones_list.copy()
        zones_list = list(zone_seq_dict.keys())
    return HttpResponse(json.dumps({'zones_list': zones_list, 'zone_seq_mapping': zone_seq_dict}))

def get_data_from_input(zones_data, zone_type):
    zones, subzones = set(), set()
    for data in zones_data:
        if zone_type == 'zone' and data.get('zone'):
            zones.add(data.get('zone'))
        elif data.get('sub_zone'):
            subzones.add(data.get('sub_zone'))
    return zones, subzones

def get_zones_data(warehouse, zones, subzones, zone_type):

    existing_zone_objs = ZoneMaster.objects.filter(user=warehouse.id)

    #Get and Put Sequence Data
    existing_sequence_data = existing_zone_objs.filter(zone_type='sub_zone').values('zone', 'get_sequence', 'put_sequence')
    get_sequence_data, put_sequence_data = {}, {}
    for data in existing_sequence_data:
        if data['get_sequence']:
            get_sequence_data[data['get_sequence']] = data['zone']
        if data['put_sequence']:
            put_sequence_data[data['put_sequence']] = data['zone']

    filter_dict = {
        'zone_type': zone_type
    }
    if zone_type == 'zone':
        filter_dict['zone__in'] = zones
    else:
        filter_dict['zone__in'] = subzones

    zone_objs = existing_zone_objs.filter(**filter_dict)

    return_existing_zones_data = {obj.zone: obj for obj in zone_objs}

    return return_existing_zones_data, get_sequence_data, put_sequence_data

def convert_zones_to_uppercase(zones_data, zone_type):
    for data in zones_data:
        if zone_type == 'zone':
            data['zone'] = data['zone'].upper()
        else:
            data['sub_zone'] = data['sub_zone'].upper()
    return zones_data

def validate_zones_data(warehouse, zones_data, zone_type='zone'):
    zones_data = convert_zones_to_uppercase(zones_data, zone_type)
    duplicate_zones_set, duplicate_get_sequence_set, duplicate_put_sequence_set = set(), set(), set()
    check_zone_type = 'zone' if zone_type == 'sub_zone' else 'sub_zone'

    subzones, zones = get_data_from_input(zones_data, zone_type)
    existing_zones_data, get_sequence_data, put_sequence_data = get_zones_data(warehouse, zones, subzones, check_zone_type)


    for data in zones_data:
        if zone_type == 'zone':
            status, duplicate_zones_set = validate_zones(data, duplicate_zones_set, existing_zones_data)
        else:
            status, duplicate_zones_set, duplicate_get_sequence_set, duplicate_put_sequence_set = validate_subzones(
                data, duplicate_zones_set, existing_zones_data, get_sequence_data,
                put_sequence_data, duplicate_get_sequence_set, duplicate_put_sequence_set
            )

        if status:
            data['Status'] = status

    error_entries = []
    if any('Status' in entry for entry in zones_data):
        error_entries = zones_data
    return error_entries if error_entries else 'Success'

def validate_zone_field(status, zone, duplicate_zones_set, existing_zones_data):
    if not zone:
        status.append('Zone is Mandatory for Creation')
        return status, duplicate_zones_set

    if validate_special_characters(zone, special_char_regex) != "Success":
        status.append("Zone ID Cannot have Special Characters")

    if zone in duplicate_zones_set:
        status.append("Duplicate Zone Found")
    else:
        duplicate_zones_set.add(zone)

    if zone in existing_zones_data:
        status.append("Zone already exists in SubZones. Please rectify.")

    return status, duplicate_zones_set

def validate_segregation(status, segregation):
    seg_options = dict(SELLABLE_CHOICES).keys()
    if segregation and segregation.lower() not in seg_options:
        status.append("Invalid Segragation Option")
    return status

def validate_restrict_one_location_to_one_sku(status, restrict_one_location_to_one_sku):
    if restrict_one_location_to_one_sku and restrict_one_location_to_one_sku.lower() not in ['yes', 'no']:
        status.append("Invalid Value for Restrict One Location to One SKU")
    return status

def validate_batch_restriction(status, unique_batch_restriction):
    if unique_batch_restriction and unique_batch_restriction.lower() not in ['no', 'yes']:
        status.append("Invalid Value for Unique Batch Restriction")
    return status

def validate_lpn_managed(status, lpn_managed):
    if lpn_managed and lpn_managed.lower() not in ['yes', 'no']:
        status.append("Invalid Value for LPN Managed")
    return status

def validate_storage_type(status, staging_type, storageType):
    allowed_storage_types = {
        "inbound_staging": allowed_inbound_storage_type.split(", "),
        "Outbound_Staging": allowed_outbound_storage_type.split(", "),
        "Non_Sellable": allowed_non_sell_storage_type.split(", "),
    }

    if staging_type in allowed_storage_types:
        if not storageType:
            status.append(f"Subzone Type is mandatory for {staging_type.replace('_', ' ')} Zone Type")
        elif storageType not in allowed_storage_types[staging_type]:
            status.append(
                f"Invalid Subzone Type '{storageType}'. Allowed values: {', '.join(allowed_storage_types[staging_type])}"
            )
    elif storageType:
        if not staging_type:
            status.append("SubZone Type should be specified for Zone type")
        else:
            status.append("SubZone Type should only be specified for Outbound/Inbound/Non Sellable Zone Type")

    return status

def validate_subzone_field(status, subzone, duplicate_zones_set, existing_zones_data):
    if not subzone:
        status.append("Subzone is Mandatory for Creation")
        return status

    is_valid = validate_special_characters(subzone, special_char_regex)
    if is_valid != "Success":
        status.append("Subzone ID Cannot have Special Characters")

    if subzone in duplicate_zones_set:
        status.append("Duplicate Zone Found")
    else:
        duplicate_zones_set.add(subzone)

    if subzone in existing_zones_data:
        status.append("Subzone already exists in Zones. Please rectify.")

    return status

def validate_get_sequence(status, get_sequence, get_sequence_data, subzone, duplicate_get_sequence_set):
    if not get_sequence:
        status.append('Get Sequence is Mandatory')
        return status
    try:
        get_sequence = float(get_sequence)
        if get_sequence < 0:
            status.append('Invalid Get Sequence: must be non-negative')
        if get_sequence in get_sequence_data and get_sequence_data[get_sequence] != subzone:
            status.append('Get Sequence already exists for another SubZone')
        if get_sequence in duplicate_get_sequence_set:
            status.append('Duplicate Get Sequence Found')
        else:
            duplicate_get_sequence_set.add(get_sequence)
    except ValueError:
        status.append('Invalid Get Sequence: must be a number')

    return status, duplicate_get_sequence_set

def validate_put_sequence(status, put_sequence, put_sequence_data, subzone, duplicate_put_sequence_set):
    if not put_sequence:
        status.append('Put Sequence is Mandatory')
        return status
    try:
        put_sequence = float(put_sequence)
        if put_sequence < 0:
            status.append('Invalid Put Sequence: must be non-negative')
        if put_sequence in put_sequence_data and put_sequence_data[put_sequence] != subzone:
            status.append('Put Sequence already exists for another SubZone')
        if put_sequence in duplicate_put_sequence_set:
            status.append('Duplicate Put Sequence Found in the sheet')
        else:
            duplicate_put_sequence_set.add(put_sequence)
    except ValueError:
        status.append('Invalid Put Sequence: must be a number')

    return status, duplicate_put_sequence_set
def validate_zones(data, duplicate_zones_set, existing_zones_data):
    status = []
    zone = data.get('zone', '')
    segregation = data.get('zone_type', '')
    restrict_one_location_to_one_sku = data.get('restrict_one_location_to_one_sku', '')
    unique_batch_restriction = data.get('unique_batch_restriction', '')
    lpn_managed = data.get('lpn_managed', '')
    
    subzone_type = data.get('storage_type', '')
    status, duplicate_zones_set = validate_zone_field(status, zone, duplicate_zones_set, existing_zones_data)
    status = validate_segregation(status, segregation)
    status = validate_storage_type(status, segregation, subzone_type)
    status = validate_restrict_one_location_to_one_sku(status, restrict_one_location_to_one_sku)
    status = validate_batch_restriction(status, unique_batch_restriction)
    status = validate_lpn_managed(status, lpn_managed)

    return status, duplicate_zones_set

def validate_subzones(
        data, duplicate_zones_set, existing_zones_data, get_sequence_data, put_sequence_data,
        duplicate_get_sequence_set, duplicate_put_sequence_set
    ):
    status = []
    subzone = data.get('sub_zone', '')
    get_sequence = data.get('get_sequence')
    put_sequence = data.get('put_sequence')
    segregation = data.get('zone_type')
    restrict_one_location_to_one_sku = data.get('restrict_one_location_to_one_sku')
    unique_batch_restriction = data.get('unique_batch_restriction')
    lpn_managed = data.get('lpn_managed')

    validations = [
        (validate_subzone_field, [subzone, duplicate_zones_set, existing_zones_data]),
        (validate_segregation, [segregation]),
        (validate_restrict_one_location_to_one_sku, [restrict_one_location_to_one_sku]),
        (validate_batch_restriction, [unique_batch_restriction]),
        (validate_lpn_managed, [lpn_managed])
    ]

    for validation_func, args in validations:
        status = validation_func(status, *args)
    
    status, duplicate_get_sequence_set = validate_get_sequence(status, get_sequence, get_sequence_data, subzone, duplicate_get_sequence_set)
    status, duplicate_put_sequence_set = validate_put_sequence(status, put_sequence, put_sequence_data, subzone, duplicate_put_sequence_set)

    return status, duplicate_zones_set, duplicate_get_sequence_set, duplicate_put_sequence_set

def create_or_update_zones(warehouse, zones_data, zone_type = 'zone'):
    try:
        zones, subzones = get_data_from_input(zones_data, zone_type)

        existing_zones_data, _, _ = get_zones_data(warehouse, zones, subzones, zone_type)

        bulk_create_zones, bulk_update_zones, bulk_create_subzones, bulk_update_subzones = [], [], [], []
        enable_check_digit = get_misc_value('enable_check_digit', warehouse.id) == 'true'
        for data in zones_data:
            if zone_type == 'zone':
                #Validates whether the Zone exists or not, If not it will return the objs of zones
                # else returns bulk_update_zones objects
                bulk_create_zones, bulk_update_zones = validate_create_or_update_zone(
                    warehouse, bulk_create_zones, bulk_update_zones, data, existing_zones_data, enable_check_digit
                )
            else:
                #Validates whether the Sub Zone exists or not, If not it will return the objs of sub zones 
                # else returns bulk_update_subzones objects
                bulk_create_subzones, bulk_update_subzones = validate_create_or_update_subzone(
                    warehouse, bulk_create_subzones, bulk_update_subzones, data, existing_zones_data
                )

        #Creating Zones and SubZones
        if bulk_create_zones or bulk_create_subzones:
            create_zones = bulk_create_zones + bulk_create_subzones
            ZoneMaster.objects.bulk_create_with_rounding(create_zones)
        
        #Updating Zones and SubZones
        update_fields_list = [
            'get_sequence', 'put_sequence', 'segregation', 'restrict_one_location_to_one_sku',
            'carton_managed', 'unique_batch', 'is_check_digit'
        ]
        if bulk_update_zones or bulk_update_subzones:
            update_zones = bulk_update_zones + bulk_update_subzones
            ZoneMaster.objects.bulk_update_with_rounding(update_zones, update_fields_list)
        return 'Success'

    except Exception as e:
        log.info("Zone/ SubZone Creation Failed for Username %s and Data %s with exception %s", str(warehouse.username), zones_data, str(e))
        return 'Failed'

def validate_create_or_update_zone(warehouse, bulk_create_zones, bulk_update_zones, data, existing_zones_data, enable_check_digit=False):
    zone = data.get('zone')
    common_fields = {
        'segregation': (data.get('zone_type', 'sellable') or 'sellable').lower(), 
        'storage_type': data.get('storage_type', ''), 
        'restrict_one_location_to_one_sku': int((data.get('restrict_one_location_to_one_sku', '') or '').lower() == 'yes'),
        'unique_batch': int((data.get('unique_batch_restriction', '') or '').lower() == 'yes'),
        'carton_managed': int((data.get('lpn_managed', '') or '').lower() == 'yes'),
    }
    if enable_check_digit and data.get('is_check_digit') is not None:
        common_fields['is_check_digit'] = True if str(data.get('is_check_digit')).lower() == 'active' else False

    if zone in existing_zones_data:
        zone_obj = existing_zones_data[zone]
        for field, value in common_fields.items():
            setattr(zone_obj, field, value)
        bulk_update_zones.append(zone_obj)
    else:
        creation_dict = {
            'zone': zone,
            'user': warehouse.id,
            'account_id': warehouse.userprofile.id,
            **common_fields
        }
        bulk_create_zones.append(ZoneMaster(**creation_dict))

    return bulk_create_zones, bulk_update_zones

def validate_create_or_update_subzone(warehouse, bulk_create_subzones, bulk_update_subzones, data, existing_zones_data):
    subzone = data.get('sub_zone')
    common_fields = {
        'segregation': (data.get('zone_type', 'sellable') or 'sellable').lower(),
        'restrict_one_location_to_one_sku': int(data.get('restrict_one_location_to_one_sku') == 'yes'),
        'unique_batch': int(data.get('unique_batch_restriction') == 'yes'),
        'carton_managed': int(data.get('lpn_managed') == 'yes'),
        'get_sequence': data.get('get_sequence'),
        'put_sequence': data.get('put_sequence')
    }

    if subzone in existing_zones_data:
        sub_zone_obj = existing_zones_data[subzone]
        for field, value in common_fields.items():
            setattr(sub_zone_obj, field, value)
        bulk_update_subzones.append(sub_zone_obj)
    else:
        creation_dict = {
            'zone': subzone,
            'zone_type': 'sub_zone',
            'user': warehouse.id,
            'account_id': warehouse.userprofile.id,
            **common_fields
        }
        bulk_create_subzones.append(ZoneMaster(**creation_dict))

    return bulk_create_subzones, bulk_update_subzones