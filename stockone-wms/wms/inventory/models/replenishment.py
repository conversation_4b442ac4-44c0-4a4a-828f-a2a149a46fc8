#django imports
from django.db import models
from django.db.models import ForeignKey

#wms base imports
from wms_base.models import TenantBaseModel, User

#inventory imports
from .locator import (
    LocationMaster, ZoneMaster,
    StockDetail
)

class BAtoSADetail(TenantBaseModel):
    Status_Choices = (
        (0, 'Closed'),
        (1, 'Pending'),
        (2, 'Picked'),
        (3, 'Putaway Done'),
        (4, 'Deleted'),
        (5, 'Short Pick')
    )

    batosa_reference = models.CharField(max_length=64, default='', db_index=True)
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE)
    dest_zone = ForeignKey(ZoneMaster,on_delete=models.CASCADE, default=None, blank=True, null=True)
    dest_location = ForeignKey(LocationMaster,on_delete=models.CASCADE, related_name='basa_location', blank=True, null=True)
    replenishment_qty = models.FloatField(default=0)
    picked_qty = models.FloatField(default=0)
    putaway_qty = models.FloatField(default=0)
    status = models.IntegerField(choices=Status_Choices, default= 1)
    reason = models.CharField(max_length=128,default='')
    carton = ForeignKey('core.AuthorizedBins', on_delete=models.CASCADE, blank=True, null=True)
    transact_type = models.CharField(max_length=32, default='BATOSA')
    json_data= models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'BA_TO_SA_DETAIL'
        index_together = (('sku', 'status'), ('dest_location', 'status'))

    round_fields = ['replenishment_qty', 'picked_qty', 'putaway_qty']

class ReplenishmentMaster(TenantBaseModel):

    user = ForeignKey(User,on_delete=models.CASCADE)
    classification = models.CharField(max_length=64, default='ba_to_sa')
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, blank=True, null=True, related_name='replenishment_sku')
    from_zone = ForeignKey(ZoneMaster,on_delete=models.CASCADE, related_name="replenishment_from_zone", default=None, blank=True, null=True)
    from_location = ForeignKey(LocationMaster,on_delete=models.CASCADE, related_name="replenishment_from_location", default=None, blank=True, null=True)
    zone = ForeignKey(ZoneMaster,on_delete=models.CASCADE, default=None, blank=True, null=True)
    location = ForeignKey(LocationMaster,on_delete=models.CASCADE, default=None, blank=True, null=True)
    lead_time = models.FloatField(default=0)
    size = models.CharField(max_length=32, default='')
    min_days = models.FloatField(default=0)
    max_days = models.FloatField(default=0)
    min_qty = models.FloatField(default=0)
    max_qty = models.FloatField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    status = models.IntegerField(default=1) #1-Active 0-Inactive

    class Meta:
        db_table = 'REPLENISHMNENT_MASTER'

    round_fields = ['min_qty', 'max_qty']


class ReplenishmentClassification(TenantBaseModel):
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, db_index=True)
    avg_sales_day = models.FloatField(default=0)
    avg_sales_day_value = models.FloatField(default=0)
    cumulative_contribution = models.FloatField(default=0)
    classification = models.CharField(max_length=64, default='')
    dest_location = ForeignKey(LocationMaster, on_delete=models.CASCADE, blank=True, null=True, related_name='dest_location')
    seller = ForeignKey('outbound.SellerMaster', on_delete=models.CASCADE, blank=True, null=True, related_name='seller')
    source_stock = ForeignKey(StockDetail, on_delete=models.CASCADE, blank=True, null=True, related_name='source_stock')
    min_stock_qty = models.FloatField(default=0)
    max_stock_qty = models.FloatField(default=0)
    replenishment_qty = models.FloatField(default=0)
    reserved = models.FloatField(default=0)
    suggested_qty = models.FloatField(default=0)
    sku_assumed_avail_qty = models.FloatField(default=0, help_text='Assumed available quantity based on replenishment logic')
    sku_avail_qty = models.FloatField(default=0, help_text='Available quantity in the stock after considering the reserved quantity')
    sku_pen_po_qty = models.FloatField(default=0, help_text='Pending purchase order quantity including pending approval and GRN quantities')
    sku_pen_putaway_qty = models.FloatField(default=0, help_text='Pending putaway quantity including JO and CP quantities')
    modified_suggested_qty = models.FloatField(default=0)
    remarks = models.CharField(max_length=64, default='')
    status = models.IntegerField(default=1)

    class Meta:
        db_table = 'REPLENISHMENT_CLASSIFICATION'
        indexes = [
            # Optimized index for: sku__user__in=users, creation_date__gte=date, replenishment_qty__gt=0
            models.Index(fields=['sku', 'creation_date', 'replenishment_qty'], name='replenish_sku_date_qty_idx'),
            # Alternative index for date-first queries (if creation_date is highly selective)
            models.Index(fields=['creation_date', 'sku', 'replenishment_qty'], name='replenish_date_sku_qty_idx'),
        ]

    def __str__(self):
        return f"{self.sku} - {self.classification} - {self.avg_sales_day}"

    round_fields = ['replenishment_qty', 'min_stock_qty', 'max_stock_qty']

    def set_account_id(self, user):
        self.account_id = user.userprofile.id
