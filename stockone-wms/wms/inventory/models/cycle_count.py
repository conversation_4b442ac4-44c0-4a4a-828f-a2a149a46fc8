#django imports
from django.db import models
from django.db.models import ForeignKey
from simple_history.models import HistoricalRecords

#wms base imports
from wms_base.models import TenantBaseModel, User

#inventory imports
from .locator import LocationMaster, BatchDetail

#history imports
from core_operations.history import CustomHistoricalRecords

#lms imports
# from lms.models import 'lms.EmployeeMaster'

class CycleCountSchedule(TenantBaseModel):
    user = ForeignKey(User,default='',on_delete=models.CASCADE)
    cycle_type = models.CharField(max_length=50, default='')
    cycle_value = models.CharField(max_length=50, default='')
    frequency_days = models.IntegerField(default=0)
    status = models.IntegerField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'CYCLECOUNT_SCHEDULE'


class MasterCycleZone(TenantBaseModel):
    user = ForeignKey(User,default='',on_delete=models.CASCADE)
    master_id = models.IntegerField(default=0)
    cycle_type = models.CharField(max_length=49, default='')
    cycle_value = models.CharField(max_length=50, default='')
    start_date = models.DateField()
    status = models.CharField(max_length=50)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'MASTER_CYCLE_ZONE'


class CycleCount(TenantBaseModel):
    
    history = CustomHistoricalRecords()
    history_disable = True
    master_zone = ForeignKey(MasterCycleZone,on_delete=models.CASCADE, null=True)
    supplier = ForeignKey('inbound.SupplierMaster',on_delete=models.CASCADE, null=True, blank=True, default=None)
    batch_detail = ForeignKey(BatchDetail,on_delete=models.CASCADE, blank=True, null=True, db_index=True)
    lpn_number = models.CharField(max_length=128, blank=True, null=True, db_index=True)
    run_type = models.CharField(max_length=32, default='')
    cycle = models.PositiveIntegerField()
    sku = ForeignKey('core.SKUMaster',on_delete=models.CASCADE, blank=True, null=True)
    location = ForeignKey(LocationMaster,on_delete=models.CASCADE, db_index=True)
    quantity = models.FloatField(default=0)
    seen_quantity = models.FloatField(default=0)
    status = models.CharField(max_length=32)
    remarks = models.CharField(max_length=32, default='')
    employee = models.ForeignKey('lms.EmployeeMaster', related_name='cycle_count_task_assigned', null=True, blank=True, default=None, on_delete=models.CASCADE)
    start_time = models.DateTimeField(default=None,null=True)
    adjusted_value = models.FloatField(default=0)
    json_data = models.JSONField(null=True, blank=True)
    stock_status = models.IntegerField(null=True, blank=True)
    price = models.FloatField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'CYCLE_COUNT'
        unique_together = ('cycle', 'sku', 'location', 'creation_date')
        indexes = [
            models.Index(fields=['cycle']),
            models.Index(fields=['run_type', 'status', 'sku']),
            models.Index(fields=['cycle', 'run_type', 'status']), 
        ]

    round_fields = ['quantity', 'seen_quantity']

    def __str__(self):
        return str(self.cycle)

class CCVLTransaction(TenantBaseModel):
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    cycle = ForeignKey(CycleCount, on_delete=models.CASCADE)
    source_location = ForeignKey(LocationMaster, on_delete=models.CASCADE)
    quantity = models.FloatField(default=0)

    STATUS_CHOICES = [
        (0, 'Completed'),
        (1, 'Cancelled'),  # Optional: Adding cancelled if you need in future
    ]
    status = models.IntegerField(choices=STATUS_CHOICES, default=0)
    json_data = models.JSONField(default=dict, null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'CCVL_TRANSACTION'

    round_fields = ['quantity']