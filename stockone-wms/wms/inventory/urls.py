from django.urls import re_path, path, include
from rest_framework import routers

#stock summary
from inventory.views.locator.stock_detail import (
    InventoryViewSet, StockReversalAPIView, get_sku_stock_check,
    check_sku_pack_scan, get_inventory_api, get_stock_status_list,
    get_serial_numbers_list,StockStatusUpdateView,
    batch_level_stock_details, get_batch_details, BatchDetailSet,
    CustomStockLedger, InventoryDetailSet, get_user_incremental_batch_no,
    get_inventory, InventorySyncView, get_stocks, stock_status_update
)
#zone master
from .views.masters.zone_master import (
    add_zone, get_zones_list, search_location_data, get_zones
)
#location master
from .views.masters.location_master import (
    add_location, update_location, get_locations,
    get_location_capacity, delete_staging_route, get_location_label_data
)
from .views.masters.location_types import LocationTypeSet

#Hierarchy master
from .views.masters.hierarchy_master import (
    HierarchyMasterSet, get_child_hierarchy, get_parent_details
)

#replenishment master
from .views.masters.replenishment_master import (
    validate_and_create_min_max_replenishment, validate_and_create_nte_replenishment,
    insert_replenishmentmaster, delete_min_max_based_replenishment, update_replenishment_master
)
from .views.replenishment.datatable import calculate_ba_to_sa, delete_ba_to_sa

#sku pack master
from .views.masters.sku_pack_master import insert_sku_pack, search_sku_pack, SKUPackMasterViewSet

#cycle count
from .views.cycle_count.datatable import (
    get_confirmed_cycle, multi_select_and_delete_cycle_count,
    run_scheduled_cycle_count, update_cycle_count_picker
)
from .views.cycle_count.cycle_count import (
    generate_cycle_count, submit_cycle_count,
    ScheduledCycleCountSet, get_category_list, CycleCountSet,
    aggregated_cycle_count
)
from .views.adjustment.inventory_adjustment import (
   confirm_inventory_adjustment, delete_inventory_adjustment,
   insert_inventory_adjustment, InventoryAdjustmentSet, create_audit_cycle_count,
   get_inventory_adjustment_past_records, get_mobile_inventory_adjustment
)
from .views.adjustment.inventory_approval import (
    InventoryApprovalView
)
from inventory.views.move.move_inventory import (
    insert_move_inventory, get_sku_batches, MoveInventorySet,
    get_sku_location_data, get_move_inventory_reasons,
    aggregated_move_inventory_count, get_move_inventory_task_data,
    delete_move_inventory_tasks, location_movement, update_move_inventory_status
)
from inventory.views.move.staging_lanes import (
    StagingLaneSet, StagingLocationSet
)
from inventory.views.planning.datatable import (
    run_min_max_based_auto_po, create_min_max_based_auto_po,
    insert_forecast_master, create_po_for_mrp, delete_forecast_master
)
from inventory.views.planning.mrp import (
    run_mrp_planning, get_forecast_names
)
from inventory.views.cycle_count.adhoc_cycle_count import adhoc_cycle_count

from core_operations.views.common.main import get_marketplaces_list

from inventory.views.barcodes.unique_code import UniqueCodeViewSet

from inventory.views.serial_numbers.serial_number import SerialNumberSet, generate_serial
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionSet

from inventory.views.seller_usage.seller_usage_data import CostDimensionsViewSet
from inventory.views.seller_usage.seller_usage_cost import CostDimensionMasterListCreateView, CostDimensionMasterRetrieveUpdateDeleteView, UniqueSkuCostCategoryView

from inventory.views.cycle_count.empty_location_cycle_count import EmptyLocationCycleCountCreateView

router = routers.DefaultRouter()
router.register(r'stock', InventoryViewSet, basename='stock')
router.register(r'pack', SKUPackMasterViewSet, basename='SKU Pack Master')
router.register(r'location_types', LocationTypeSet, basename='location_types')
router.register(r'cost_dimensions', CostDimensionsViewSet, basename='cost_dimensions')


urlpatterns = [
    path('', include(router.urls)),

    #zone and location masters
    re_path('zone/$', add_zone, name="Zone"),
    re_path('location/$', add_location, name="Location"),
    re_path('get_zones_list/$', get_zones_list, name="Zones List"),
    re_path('update_location/$', update_location, name="Location Updation"),
    re_path('search_location_data/$', search_location_data, name="Location Details"),
    re_path('get_marketplaces_list/$', get_marketplaces_list, name="Marketplace List"),
    re_path('get_locations/$', get_locations, name="Locations View"),
    re_path('get_location_capacity/$', get_location_capacity, name="Location Capacity"),
    re_path('get_location_data/$', get_location_label_data, name="Location Label Data"),

    #hierarchy master
    re_path('hierarchy/$', HierarchyMasterSet.as_view(), name="Hierarchy"),
    re_path('get_child_hierarchy/$', get_child_hierarchy, name="Child Hierarchy"),
    re_path('get_parent_details/$', get_parent_details, name="Parent Details"),

    #replenishment master
    re_path('replenishment/$', validate_and_create_min_max_replenishment, name="ReplenishMent"),
    re_path('inv_replenishment/$', insert_replenishmentmaster, name="Inventory ReplenishMent"),
    re_path('delete_replenishment/$', delete_min_max_based_replenishment, name="Replenishment Deletion"),
    re_path('create_nte_replenishment/$', validate_and_create_nte_replenishment, name="NTE ReplenishMent"),
    re_path('update_nte_replenishment/$', update_replenishment_master, name="Replenishment Master Updation"),

    #sku pack master
    re_path('sku_pack/$', insert_sku_pack, name="SKU Pack"),
    re_path('search_sku_pack/$', search_sku_pack, name="SKU Pack View"),

    #ba to sa
    re_path('calculate_ba_to_sa/$', calculate_ba_to_sa, name="Calculate BatoSa"),
    re_path('delete_ba_to_sa/$', delete_ba_to_sa, name="Delete BatoSa"),

    #cycle count
    re_path('get_confirmed_cycle/$', get_confirmed_cycle, name="Confirmed Cycle View"),
    re_path('generate_cycle_count/$', generate_cycle_count, name="Generate CycleCount"),
    re_path('submit_cycle_count/$', submit_cycle_count, name="Submit CycleCount"),
    re_path('update_cycle_count_picker/$', update_cycle_count_picker, name="Update CycleCount Picker"),
    re_path('delete_cycle_count/$', multi_select_and_delete_cycle_count, name="CycleCount Deletion"),
    re_path('get_category_list/$', get_category_list, name="CycleCount View"),
    re_path('run_scheduled_cycle_count/$', run_scheduled_cycle_count, name="Run Scheduled CycleCount"),
    re_path('cycle_count/$', CycleCountSet.as_view(), name="CycleCount"),
    re_path('adhoc_cycle_count/$', adhoc_cycle_count, name="Adhoc CycleCount"),
    re_path('create_audit_cycle_count/$', create_audit_cycle_count, name="Audit CycleCount"),
    re_path('aggregated_cycle_count/$', aggregated_cycle_count, name="Aggregated CycleCount"),

    #inventory adjustment
    re_path('confirm_inventory_adjustment/$', confirm_inventory_adjustment, name="Inventory Adjustment Updation"),
    re_path('delete_inventory_adjustment/$', delete_inventory_adjustment, name="Inventory Adjustment Deletion"),
    re_path('insert_inventory_adjustment/$', insert_inventory_adjustment, name="Inventory Adjustment Creation"),
    re_path('inventory_adjustment/$', InventoryAdjustmentSet.as_view(), name="Inventory Adjustment"),
    re_path('inventory_adjustment_approval/$', InventoryApprovalView.as_view(), name="InventoryApproval"),
    re_path('inventory_adjustment_past_records/$', get_inventory_adjustment_past_records, name="Inventory Adjustment Past Records"),
    re_path('get_inventory_adjustment/$', get_mobile_inventory_adjustment, name="Get Inventory Adjustment"),


    #move inventory
    re_path('move/$', insert_move_inventory, name="Move Inventory Creation"),
    re_path('location/move/$', location_movement, name="Location to Location Movement Creation"),
    re_path('get_sku_batches/$', get_sku_batches, name="SKU Batches View"),
    re_path('move_inventory/$', MoveInventorySet.as_view(), name="Move Inventory"),
    re_path('get_location_scan/$', get_sku_location_data, name="SKU Location Details"),
    re_path('get_move_inventory_reasons/$', get_move_inventory_reasons, name="Move Inventory Reasons View"),
    re_path('aggregated_move_inventory_count/$', aggregated_move_inventory_count, name="Aggregated Move Inventory Count"),
    re_path('get_move_inventory_task_data/$', get_move_inventory_task_data, name="Move Inventory Task Data"),
    re_path('delete_move_inventory_tasks/$', delete_move_inventory_tasks, name="Move Inventory Task Deletion"),
    re_path('update_move_inventory_status/$', update_move_inventory_status, name="Updating Move Inventory Status"),

    #staging lanes
    re_path('delete_staging_route/$', delete_staging_route, name="Staging Route Deletion"),
    re_path('staging_lane/$', StagingLaneSet.as_view(), name="Staging Lane"),
    re_path('staging_location/$', StagingLocationSet.as_view(), name="Staging Location Set"),

    #inbound planning
    re_path('run_min_max_based_auto_po/$', run_min_max_based_auto_po, name="MinMax Based AutoPO"),
    re_path('create_min_max_based_auto_po/$', create_min_max_based_auto_po, name="MinMax Based AutoPO Creation"),
    re_path('forecast_masters/$', insert_forecast_master, name="ForeCast"),
    re_path('delete_forecast_master/$', delete_forecast_master, name="ForeCast"),
    re_path('run_mrp_planning/$', run_mrp_planning, name="Mrp Planning"),
    re_path('create_po_for_mrp/$', create_po_for_mrp, name="PurchaseOrder for Mrp"),
    re_path('get_forecast_names/$', get_forecast_names, name="Forecast Names"),

    #cycle count
    re_path('schedule_cycle_count/$', ScheduledCycleCountSet.as_view(), name="Schedule CycleCount"),
    re_path('empty_location_cycle_count/$', EmptyLocationCycleCountCreateView.as_view(), name="Empty Location Cycle Count"),

    #stock summary popup
    re_path('check_sku_stock/$', get_sku_stock_check, name="SKU Stock Status"),
    re_path('check_sku_pack_scan', check_sku_pack_scan, name="SKU Pack Scan Status"),
    
    #stock detail related
    re_path('get_inventory_api/$', get_inventory_api, name="Inventory Details"),
    re_path('get_serial_numbers_list/$', get_serial_numbers_list, name="SerialNumbers View"),
    re_path('update_stock_status/$', StockStatusUpdateView.as_view(), name="Stock Status Update"),
    re_path('stock_status_update/$', stock_status_update, name="Stock Status Update Internally"),
    re_path('stock_status_reversal/$', StockReversalAPIView.as_view(), name="Stock Status Reversal"),
    re_path('inventory/$', get_inventory_api, name="Inventory"),
    re_path('batch_level_stock_details/$', batch_level_stock_details, name="Batch Level Stock Details"),
    re_path('get_stock_status/$', get_stock_status_list, name="Get stock status list"),
    re_path('inventory_callback/$', InventoryDetailSet.as_view(), name="Inventory Callback"),
    re_path('get_inventory/$', get_inventory, name="Get inventory"),
    re_path('get_stocks/$', get_stocks, name = "Get Stock Data"),

    #batch detail
    re_path('batch_detail/$', get_batch_details, name='Batch Details View'),
    re_path('batch_details/$', BatchDetailSet.as_view(), name="Batch Details"),
    re_path('get_user_incremental_batch_no/$', get_user_incremental_batch_no, name='Incremental Batch No'),

    #Unique bar code generation
    re_path('unique_code/$', UniqueCodeViewSet.as_view(), name="Unique Code Generation"),

    #integration urls
    re_path('get_zones/$', get_zones, name="Integrations Zones List"),

    # custom Stock Ledger
    re_path('stock_ledger/$', CustomStockLedger.as_view(), name="Stock Ledger"),

    #inventory creation/ updation
    re_path('sync/$', InventorySyncView.as_view(), name="Inventory Creation"),

    #serial numbers apis
    re_path('serial_number/$', SerialNumberSet.as_view(), name="Serial Number"),
    re_path('serial_number/generate/$', generate_serial, name="Generate Serial Numbers"),
    re_path('serial_number_transaction/$', SerialNumberTransactionSet.as_view(), name="Serial Number Transaction"),

    #cost dimensions
    re_path('cost_dimensions/aggregated/', CostDimensionsViewSet.as_view({'get': 'aggregated'}), name="Aggregated Cost Dimensions"),
    path('cost_dimension_master/', CostDimensionMasterListCreateView.as_view(), name='Cost Master List'),
    path('cost_dimension_master/<int:pk>/', CostDimensionMasterRetrieveUpdateDeleteView.as_view(), name='Cost Master Detail'),
    path('unique_sku_cost_category/', UniqueSkuCostCategoryView.as_view(), name='Unique SKU Cost Category'),
]
