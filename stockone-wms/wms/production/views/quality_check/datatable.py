#package imports
from json import dumps, loads
from collections import OrderedDict

#django imports
from django.db.models import Q, Max, F
from django.http import HttpResponse, HttpRequest

#production imports
from inbound.models import SellerPOSummary

#wms base imports
from wms_base.models import User
from wms_base.wms_utils import (
    init_logger
)

#quality control
from quality_control.views import (
    QualityControlSet, QCConfiguration
)
from inbound.views.quality_check.common import fetch_pending_stock_for_quality_control

#core common functions
from core_operations.views.common.main import (
    get_filtered_params, get_user_time_zone,
    get_local_date_known_timezone, get_warehouse,
    get_decimal_value, truncate_float
)

#core imports
from core.models import MasterDocs

log = init_logger('logs/jo_quality_check.log')


def get_jo_grn_qc_data(start_index, stop_index, temp_data, search_term, order_term, col_num, request, user, filters):
    lis = ['creation_date_only', 'grn_number', 'job_order__job_code', 'job_order__jo_reference', 'job_order__order_type']
    result_values = ['grn_number', 'job_order__job_code', 'job_order__jo_reference', 'job_order__order_type', 'grn_reference']
    order_data = lis[col_num]
    qc_order_by = 'reference_number'
    if order_term == 'desc':
        order_data = '-%s' % order_data
        qc_order_by = '-%s' % qc_order_by
    search_params = get_filtered_params(filters, lis)
    user_timezone = get_user_time_zone(user)
    
    has_qc_staging_lane, sps_filter, staging_filter, pending_qc_dict, qc_stock_dict = fetch_pending_stock_for_quality_control('', 'jo_grn', user.id)
    log.info("has_qc_staging_lane: %s, pending_qc_dict: %s, qc_stock_dict: %s" % (has_qc_staging_lane, pending_qc_dict, qc_stock_dict))

    staging_filter, sps_filter = {}, {}
    quality_control = QualityControlSet()
    qc_request = HttpRequest()
    qc_request.user = request.user
    qc_request.warehouse = user
    qc_request.GET = {'status': 0, 'transaction_type': 'after_jo_grn', **staging_filter}
    if search_term:
        qc_request.GET['transaction_id'] = list(
            SellerPOSummary.objects.filter(Q(grn_number__icontains=search_term) |
                                Q(job_order__job_code__icontains=search_term), **search_params, **sps_filter)\
                                .annotate(creation_date_only=Max('creation_date')).\
                                order_by(order_data).values_list('id', flat=True))
    elif search_params:
        qc_request.GET['transaction_id'] = list(
            SellerPOSummary.objects.filter(**search_params, **sps_filter).\
            annotate(creation_date_only=Max('creation_date')).\
            order_by(order_data).values_list('id', flat=True))
    if stop_index:
        limit = stop_index - start_index
        offset = start_index/limit
        qc_request.GET['limit'] = limit
        qc_request.GET['offset'] = offset
    quality_control.request = qc_request
    distinct_values = ['reference_number']
    qc_data = quality_control.get(distinct_values, qc_order_by)
    qc_data = loads(qc_data.content)
    search_parameters = {
        'user': user.id, 
        'grn_number__in' : [dat['reference_number'] for dat in qc_data['data']['data']] 
    }
    master_data = SellerPOSummary.objects.filter(**search_parameters)\
                                   .values(*result_values).distinct()\
                                   .annotate(creation_date_only=Max('creation_date'))\
                                   .order_by(order_data)
    temp_data['recordsTotal'] = qc_data['data'].get('page_info',{}).get('count', 10)
    temp_data['recordsFiltered'] = temp_data['recordsTotal']
    for data in master_data:
        grn_number, grn_reference = data['grn_number'], data['grn_reference']
        job_code, jo_reference = data['job_order__job_code'], data['job_order__jo_reference']
        create_date_value = ''
        if data.get('creation_date_only',''):
            create_date_value = get_local_date_known_timezone(user_timezone, data.get('creation_date_only',''),True)
            create_date_value = create_date_value.strftime('%d-%m-%Y %H:%M')
        temp_data['aaData'].append(
            OrderedDict((
                         ('Warehouse Id', user.id),
                         ('GRN Date', create_date_value),
                         ('GRN Number', grn_number),
                         ('GRN Reference', grn_reference),
                         ('JOB Code', job_code),
                         ('JO Reference', jo_reference),
                         ('jo_display_key', jo_reference or job_code),
                         ('grn_display_key', grn_reference or grn_number),
                         ('Order Type', data['job_order__order_type'])
                        )))
@get_warehouse
def get_jo_qc_pop_up_data(request, warehouse: User):
    """ JO QC Pop Up Data """
    timezone = get_user_time_zone(warehouse)
    decimal_limit = get_decimal_value(warehouse.id)
    quality_control = QualityControlSet()
    qc_request = HttpRequest()
    qc_request.user = request.user
    qc_request.warehouse = warehouse
    qc_request.GET = {'status': 0, 'transaction_type': 'after_jo_grn'}
    grn_number = request.GET['grn_number']
    qc_request.GET['reference_number'] = grn_number
    distinct_values = ['id','warehouse_id','transaction_type','reference_number', 
                       'transaction_id','location_id','total_quantity','sampled_quantity', 
                       'approved_quantity','rejected_quantity', 'remarks']
    quality_control.request = qc_request
    qc_data = quality_control.get(distinct_values)
    qc_data = loads(qc_data.content)['data']['data']

    qc_ids = []
    jo_grn_ids = []
    for qc in qc_data:
        qc_ids.append(qc['id'])
        jo_grn_ids.append(qc['transaction_id'])
    date_keys = ['manufactured_date', 'expiry_date']
    values_list = ['grn_number', 'grn_reference']
    values_dict =  {'jogrn_id': F('id'),
                    'job_code': F('job_order__job_code'),
                    'jo_reference': F('job_order__jo_reference'),
                    'sku_code': F('job_order__product_code__sku_code'), 
                    'sku_desc': F('job_order__product_code__sku_desc'), 
                    'sku_size': F('job_order__product_code__sku_size'), 
                    'mrp': F('batch_detail__mrp'),
                    'batch_no': F('batch_detail__batch_no'),
                    'manufactured_date': F('batch_detail__manufactured_date'),
                    'expiry_date': F('batch_detail__expiry_date'),
                    'put_zone': F('json_data__put_zone'),
                    'batch_reference': F('batch_detail__batch_reference'),
                }
    jo_data = list(SellerPOSummary.objects.filter(
        user=warehouse.id, grn_number=grn_number, id__in=jo_grn_ids
    ).values(*values_list, **values_dict))
    jo_dict = {}
    for dat in jo_data:
        jo_dict[dat['jogrn_id']] = dat

    uploaded_file_dicts = {}
    master_docs = MasterDocs.objects.filter(
        master_id__in=qc_ids, master_type='JOQualityControl', user_id=warehouse.id, extra_flag='pending'
    )
    for master_doc in master_docs:
        uploaded_file_dicts[master_doc.master_id] = {
            'file_name': master_doc.uploaded_file.name.split('/')[-1],
            'id': master_doc.id,
            'file_url': '/' + master_doc.uploaded_file.name
            }
    
    #Fetching QC Rejected Reasons
    qc_config_filter = {'warehouse_id': warehouse.id, 'transaction_type' : 'after_jo_grn'}
    qc_reject_reasons = list(QCConfiguration.objects.filter(**qc_config_filter).\
                             values_list('reasons', flat=True))

    data_list = []
    for data in qc_data:
        data_dict = data
        data_dict.update(jo_dict.get(data['transaction_id'], {}))
        for key in date_keys:
            if data_dict[key]:
                data_dict[key] = get_local_date_known_timezone(
                    timezone, data_dict[key], send_date=True
                ).strftime('%m/%d/%Y')
            else:
                data_dict[key] = ''
        data_dict['pending_quantity'] = truncate_float(
            data_dict['total_quantity'] - (data_dict['approved_quantity'] + data_dict['rejected_quantity'])
            ,decimal_limit)
        data_dict['batch_display_key'] = data_dict['batch_reference'] or data_dict['batch_no']
        data_dict['grn_display_key'] = data_dict['grn_reference'] or data_dict['grn_number']
        data_dict['jo_display_key'] = data_dict['jo_reference'] or data_dict['job_code']
        data_dict['approved_quantity'] = 0
        data_dict['rejected_quantity'] = 0
        data_dict['short_quantity'] = 0
        data_dict['qc_reject_reasons'] = qc_reject_reasons[0].split(',') if qc_reject_reasons else []
        data_dict['uploaded_file_dict'] = uploaded_file_dicts.get(str(data['id']), {})
        data_list.append(data_dict)
    
    return HttpResponse(dumps({'data':data_list}))