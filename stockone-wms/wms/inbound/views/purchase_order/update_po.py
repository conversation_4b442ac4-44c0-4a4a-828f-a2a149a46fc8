import pytz
import datetime
import json
import copy
from dateutil import parser
from collections import OrderedDict

from django.http import HttpResponse
from django.db.models import Q

from django.test.client import RequestFactory

#Model Imports
from wms_base.models import User
from inbound.models import (
    PurchaseOrder, OpenPO
)

#Method Imports
from wms_base.wms_utils import init_logger

from core_operations.views.common.main import get_warehouse

from inbound.views.common.constants import DEFAULT_DATETIME_FORMAT
from inbound.views.purchase_order.common import update_po_header

from .constants import PO_ORDER_TYPES

from core_operations.views.common.main import (
    get_uom_with_sku_code, get_local_date_known_timezone, create_or_update_master_attributes
)


today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/update_po' + today + '.log')
log_err = init_logger('logs/update_po.log')


ALLOWED_PO_UPDATE_FIELDS = [
    'reason',
    'status',
    'po_date',
    'remarks',
    'updation_date',
    'cancelled_quantity',
]

ALLOWED_OPO_UPDATE_FIELDS = [
    'mrp',
    'terms',
    'price',
    'remarks',
    'ship_to',
    'tax_type',
    'json_data',
    'delivery_date',
    'updation_date',
    'order_quantity',
    'measurement_unit',
    'cgst_tax',
    'sgst_tax',
    'igst_tax',
    'cess_tax',
    'utgst_tax',
    'apmc_tax',
    'free_quantity',
]

@get_warehouse
def po_update(request, warehouse: User):
    try:        
        myDict = dict(request.POST.lists())
        terms_condition = request.POST.get('terms_condition','')
        wrong_wms = []
        po_number = myDict['po_number'][0]
        for i in range(0, len(myDict['sku_code'])):
            if not myDict['sku_code'][i]:
                continue
            sku_code = myDict['sku_code'][i]
            record = PurchaseOrder.objects.filter(po_number = po_number, open_po__sku__sku_code = sku_code, open_po__sku__user=warehouse.id)
            if record.exists():
                record = record[0].open_po
                if 'order_quantity' in myDict.keys():
                    setattr(record, 'order_quantity', myDict['order_quantity'][i])
                if 'price' in myDict.keys():
                    setattr(record, 'price', myDict['price'][i])
                if 'mrp' in myDict.keys():
                    setattr(record, 'mrp', myDict['mrp'][i])
                if 'remarks' in myDict.keys():
                    setattr(record, 'remarks', myDict['remarks'][i])
                if 'sgst' in myDict.keys():
                    setattr(record, 'sgst_tax', myDict['sgst'][i] if myDict['sgst'][i] else 0)
                if 'cgst' in myDict.keys():
                    setattr(record, 'cgst_tax', myDict['cgst'][i] if myDict['cgst'][i] else 0)
                if 'igst' in myDict.keys():
                    setattr(record, 'igst_tax', myDict['igst'][i] if myDict['igst'][i] else 0)
                if 'cess' in myDict.keys():
                    setattr(record, 'cess_tax', myDict['cess'][i] if myDict['cess'][i] else 0)
                if 'ship_to' in myDict.keys():
                    setattr(record, 'ship_to', myDict['ship_to'][0])
                if 'terms_condition' in myDict.keys():
                    setattr(record, 'terms', myDict['terms_condition'][0])
                record.json_data.update({"updated_by": request.user.username, "updated_from": "WEB"})
                record.save()
        return HttpResponse('Updated Successfully')
    except:
        return HttpResponse('Failed')

def add_new_items_to_po(request_user,warehouse_user,sku_data,source='API',extra_data={},existing_skus=set()):
    """
    Add new items to a purchase order.

    Args:
        request_user (User): The user making the request.
        warehouse_user (User): The user associated with the warehouse.
        sku_data (list): A list of dictionaries containing SKU data for the new items.
        source (str, optional): The source of the request. Defaults to 'API'.
        extra_data (dict, optional): Additional data for the purchase order. Defaults to {}.
        existing_skus (set, optional): A set of existing SKUs. Defaults to set().

    Returns:
        list: A list of OpenPO objects created for the new items.
    """
    opo_objs = []
    po_suggestions = {}

    # get updated header level data
    terms = extra_data.get('terms')
    prefix = extra_data.get('prefix')
    ship_to = extra_data.get('ship_to')
    order_id = extra_data.get('order_id')
    po_reference = extra_data.get('po_reference')
    wms_code = extra_data.get('wms_code')
    po_type = extra_data.get('po_type')
    po_delivery_date = extra_data.get('delivery_date')

    line_extra_list = []
    item_id_list = []
    for entry in sku_data:
        sku_id = entry['sku_id']
        line_reference = entry.get('aux_data',{}).get('line_reference')
        line_extra_fields = entry.get('line_extra_fields', {})
        if line_reference:
            line_reference = str(line_reference)
        if (sku_id, line_reference) in existing_skus:
            continue
            
        po_aux_data = entry.get('aux_data',{})
        if line_reference:
            po_aux_data['line_reference'] = line_reference

        po_suggestions['sku_id'] = sku_id
        po_suggestions['supplier_id'] = entry['supplier_id']
        po_suggestions['order_quantity'] = entry['order_quantity']
        po_suggestions['free_quantity'] = entry.get('free_quantity', 0)
        po_suggestions['po_name'] = entry['po_reference'] or po_reference
        po_suggestions['supplier_code'] = entry['supplier_code']
        po_suggestions['price'] = float(entry['price'])
        po_suggestions['status'] = 'Manual'
        po_suggestions['remarks'] = entry['remarks']
        po_suggestions['measurement_unit'] = "UNITS"
        po_suggestions['mrp'] = float(entry['mrp'])
        po_suggestions['json_data'] ={
            "weight": entry.get("weight", ""),
            "created_by": request_user,
            "created_from": source,
            "po_type": po_type,
            "po_currency": entry.get('supplier_currency','')
        }
        po_suggestions['json_data'].update(po_aux_data)
        po_suggestions['sgst_tax'] = entry['sgst_tax']
        po_suggestions['cgst_tax'] = entry['cgst_tax']
        po_suggestions['igst_tax'] = entry['igst_tax']
        po_suggestions['cess_tax'] = entry['cess_tax']
        po_suggestions['utgst_tax'] = entry['utgst_tax']
        po_suggestions['apmc_tax'] = entry['apmc_tax']
        po_suggestions['ship_to'] = ship_to
        po_suggestions['wms_code'] = wms_code
        po_suggestions['terms'] = terms
        if po_delivery_date:
            if isinstance(po_delivery_date, str):
                try:
                    po_delivery_date = datetime.datetime.strptime(po_delivery_date, DEFAULT_DATETIME_FORMAT)
                except ValueError:
                    if '-' in po_delivery_date:
                        po_delivery_date = datetime.datetime.strptime(po_delivery_date, '%m-%d-%Y')
                    elif '/' in po_delivery_date:
                        po_delivery_date = datetime.datetime.strptime(po_delivery_date, '%m/%d/%Y')

            po_suggestions['delivery_date'] = po_delivery_date

        po_suggestions['measurement_unit'] = entry['measurement_unit'] if entry['measurement_unit'] else 'UNITS'

        po_suggestions['account_id'] = warehouse_user.userprofile.id
        opo_obj = OpenPO(**po_suggestions)
        opo_obj.save()
        opo_objs.append(opo_obj)


        po_data = {}
        open_po_obj = OpenPO.objects.get(id=opo_obj.id, sku__user=warehouse_user.id)
        sup_id = open_po_obj.id
        po_data['open_po_id'] = sup_id
        po_data['order_id'] = int(order_id)
        po_data['prefix'] = prefix
        po_data['po_number'] = entry['po_number']
        po_data['po_type'] = po_type
        po_data['line_reference'] = line_reference        
        po_data['account_id'] = warehouse_user.userprofile.id

        uom_dict = get_uom_with_sku_code(warehouse_user, open_po_obj.sku.sku_code, uom_type='purchase')
        po_data['pcf'] = uom_dict.get('sku_conversion', 1)
        order = PurchaseOrder(**po_data)
        order.save()
        line_extra_dict = {str(order.id):line_extra_fields}
        line_extra_list.append(line_extra_dict)
        item_id_list.append(str(order.id))
    if line_extra_fields:
        create_or_update_master_attributes(item_id_list, 'po_line_extra_fields', line_extra_list, warehouse_user)
    

def parse_po_date_formats(po_delivery_date, po_date):
    '''Parse po date and po delivery date to datetime objects'''
    if po_delivery_date and isinstance(po_delivery_date, str):
        po_delivery_date = parser.parse(po_delivery_date)

    if po_date and isinstance(po_date, str):
        po_date = parser.parse(po_date)

    return po_date, po_delivery_date

def update_po_po_object_for_cancel_status(po_obj, opo_obj, new_values):
    order_quantity = new_values.get('order_quantity', 0)
    free_quantity = new_values.get('free_quantity', 0) or 0
    cancelled_quantity = new_values.get('cancelled_quantity', 0)
    cancelled_quantity = float(cancelled_quantity) + float(free_quantity)
    status = new_values.get('status', '').lower()
    reason = new_values.get('reason', '')

    # Case when order_quantity is 0 or "0" and po_obj status is empty
    if order_quantity in [0, "0"] and po_obj.status == '' and free_quantity == 0:
        po_obj.order_quantity = 0
        po_obj.status = 'location-assigned'
        po_obj.cancelled_quantity = cancelled_quantity

    elif float(order_quantity) > float(opo_obj.order_quantity):
        po_obj.cancelled_quantity = 0
        opo_obj.order_quantity = order_quantity or opo_obj.order_quantity

    # Case when po_obj status is in specific states
    elif po_obj.status in ['grn-generated', 'confirmed-putaway', 'location-assigned']:
        if cancelled_quantity:
            po_obj.cancelled_quantity = cancelled_quantity

    # Update opo_obj's order_quantity if no other conditions are met
    else:
        opo_obj.order_quantity = order_quantity or opo_obj.order_quantity

    # Handle cancel status and cancelled_quantity logic
    if status == 'cancel' and cancelled_quantity:
        if opo_obj.order_quantity <= cancelled_quantity:
            po_obj.status = 'location-assigned'
            po_obj.cancelled_quantity = cancelled_quantity
        else:
            po_obj.status = 'grn-generated'
        po_obj.reason = reason

def update_existing_po_details(new_data, related_po_objs, requested_user):
    NOW = datetime.datetime.now()
    pos_updation_list = []
    opos_updation_list = []
    po_ids = []
    po_numbers = []
    for po_obj in related_po_objs:
        po_num = po_obj.po_number
        po_ref = po_obj.open_po.po_name
        sku_code = po_obj.open_po.sku.sku_code
        line_reference = po_obj.line_reference
        po_id = po_obj.id
        new_values = (
            new_data.get((po_num,sku_code, line_reference),'') or
            new_data.get((po_ref,sku_code, line_reference),'') or new_data.get(po_id)
        )

        if not new_values:
            continue

        # po_obj.po_date
        update_info = {
            'updated_by': requested_user,
            'updated_from': 'API',
        }

        po_ids.append(po_id)
        po_numbers.append(po_num)
        po_obj.updation_date = NOW
        po_date = new_values.get('po_date','') or po_obj.po_date
        po_delivery_date = new_values.get('delivery_date','')
        po_date, po_delivery_date = parse_po_date_formats(po_delivery_date, po_date)
        line_reference = str(new_values.get('json_data',{}).get('line_reference', ''))
        new_po_aux_data = new_values.get('json_data',{})
        if line_reference:
            new_po_aux_data['line_reference'] = line_reference

        opo_obj = po_obj.open_po
        opo_obj.mrp = new_values.get('mrp',0) or opo_obj.mrp
        opo_obj.price = new_values.get('price',0) or opo_obj.price
        opo_obj.terms = new_values.get('terms','') or opo_obj.terms
        opo_obj.remarks = new_values.get('remarks','') or opo_obj.remarks
        opo_obj.ship_to = new_values.get('ship_to','') or opo_obj.ship_to
        opo_obj.apmc_tax = new_values.get('apmc_tax',0) or opo_obj.apmc_tax
        opo_obj.sgst_tax = new_values.get('sgst_tax',0) or opo_obj.sgst_tax
        opo_obj.cgst_tax = new_values.get('cgst_tax',0) or opo_obj.cgst_tax
        opo_obj.igst_tax = new_values.get('igst_tax',0) or opo_obj.igst_tax
        opo_obj.cess_tax = new_values.get('cess_tax',0) or opo_obj.cess_tax
        opo_obj.utgst_tax = new_values.get('utgst_tax',0) or opo_obj.utgst_tax
        opo_obj.json_data = new_po_aux_data or opo_obj.json_data
        opo_obj.free_quantity = new_values.get('free_quantity', 0) or opo_obj.free_quantity
        
        opo_obj.measurement_unit = new_values.get('measurement_unit','') or opo_obj.measurement_unit
        opo_obj.delivery_date = po_delivery_date or opo_obj.delivery_date
        update_po_po_object_for_cancel_status(po_obj, opo_obj, new_values)
        opo_obj.json_data.update(update_info)
        opo_obj.updation_date = NOW

        pos_updation_list.append(po_obj)
        opos_updation_list.append(opo_obj)


    if pos_updation_list or opos_updation_list:
        PurchaseOrder.objects.bulk_update_with_rounding(pos_updation_list,ALLOWED_PO_UPDATE_FIELDS)
        OpenPO.objects.bulk_update_with_rounding(opos_updation_list, ALLOWED_OPO_UPDATE_FIELDS)

    return pos_updation_list,opos_updation_list, po_ids, po_numbers

def cancel_po_entries(user,cancel_pos_dict,related_po_objs,requested_user,cancel_po_nums,cancel_po_refs, po_ids, replace_check=False):
    NOW = datetime.datetime.now()
    if related_po_objs:
        if cancel_pos_dict:
            related_po_objs = related_po_objs.filter(
                Q(po_number__in=cancel_po_nums) |
                Q(open_po__po_name__in=cancel_po_refs),
                open_po__sku__user=user.id
            )
        elif replace_check:
            related_po_objs = related_po_objs.exclude(id__in=po_ids)

        json_data = {
            'updated_by': requested_user,
            'updated_from': 'API',
        }
        pos_updation_list, opos_updation_list = [], []

        for po_obj in related_po_objs:
            #Getting current PO canceling reason
            cancel_reason = (
                cancel_pos_dict.get(po_obj.po_number, {}).get('reason', '') or
                cancel_pos_dict.get(po_obj.open_po.po_name, {}).get('reason', '')
            )
            to_cancel = (
                po_obj.po_number in cancel_po_nums or
                po_obj.open_po.po_name in cancel_po_refs
            )

            if not to_cancel and not replace_check:
                continue
            cancelled_quantity = po_obj.open_po.order_quantity - (po_obj.received_quantity+po_obj.saved_quantity)
            if cancelled_quantity != po_obj.open_po.order_quantity:
                po_obj.status = 'grn-generated'
            else:
                po_obj.status = 'location-assigned'
            po_obj.reason = cancel_reason
            po_obj.updation_date = NOW
            po_obj.cancelled_quantity = cancelled_quantity
            pos_updation_list.append(po_obj)

            opo_obj = po_obj.open_po

            opo_obj.json_data.update(json_data)
            opo_obj.updation_date = NOW
            opos_updation_list.append(opo_obj)

        if pos_updation_list or opos_updation_list:
            PurchaseOrder.objects.bulk_update_with_rounding(pos_updation_list, ['status', 'reason', 'updation_date', 'cancelled_quantity'])
            OpenPO.objects.bulk_update_with_rounding(opos_updation_list, ['json_data', 'updation_date'])

    return

def update_po(po_data_list, po_entries, wh_user, requested_user):
    ''' Update PO '''

    '''
    1. stocktransfer case not handled [po update to be avoided]
    2. PO approval flow to be handled
    3. 3P callbacks to be handled
    '''
    if isinstance(po_data_list, dict):
        po_data_list = [po_data_list]

    update_po_dict = {}
    cancel_po_dict = {}

    all_skus = []

    all_po_nums = []
    all_po_refs = []

    cancel_po_nums = []
    cancel_po_refs = []
    po_numbers = []
    try:
        for each_data in po_data_list:
            status = each_data.get('status', '').lower()
            reason = each_data.get('reason' ,'')
            replace_check = each_data.get('replace', False)
            po_number = each_data.get('po_number', '')
            po_reference = each_data.get('po_reference', '')

            if po_number: all_po_nums.append(po_number)
            if po_reference: all_po_refs.append(po_reference)

            po_key = po_number or po_reference

            if status == 'cancel':
                if po_number: cancel_po_nums.append(po_number)
                if po_reference: cancel_po_refs.append(po_reference)
                cancel_po_dict[po_key] = {'reason' :reason}
                continue

            po_date = each_data.get('po_date', '')
            ship_to = each_data.get('ship_to', '')
            terms = each_data.get('terms_condition', '')
            delivery_date = each_data.get('po_delivery_date', '')

            items = each_data.get('items',[])
            for item in items:
                sku = item.get('sku','')
                line_reference = item.get('aux_data', {}).get('line_reference')
                if line_reference:
                    line_reference = str(line_reference)
                all_skus.append(sku)
                po_unique_key = item['id'] if item.get('id') else(po_key,sku,line_reference)
                update_po_dict[po_unique_key] = {
                    "terms": terms,
                    "po_date" : po_date,
                    "ship_to" : ship_to,
                    "po_number" : po_number,
                    "po_reference" : po_reference,
                    "delivery_date" : delivery_date,
                    "mrp" : item.get('mrp',''),
                    "price" : item.get('price',''),
                    "remarks" : item.get('remarks',''),
                    "sgst_tax" : item.get('sgst_tax',''),
                    "cgst_tax" : item.get('cgst_tax',''),
                    "igst_tax" : item.get('igst_tax',''),
                    "ugst_tax" : item.get('ugst_tax',''),
                    "cess_tax" : item.get('cess_tax',''),
                    "apmc_tax" : item.get('apmc_tax',''),
                    "order_quantity" : item.get('order_quantity',''),
                    "free_quantity": item.get('free_quantity', 0),
                    "measurement_unit" : item.get('measurement_unit',''),
                    "aux_data" : item.get('aux_data',''),
                    "status": item.get('status',''),
                    "cancelled_quantity": item.get('cancelled_quantity',0),
                    "line_extra_fields": item.get('line_extra_fields',''),
                }

        related_po_qs = PurchaseOrder.objects.select_related('open_po').filter(
            Q(po_number__in=all_po_nums) |
            Q(open_po__po_name__in=all_po_refs),
            open_po__sku__user=wh_user.id
        )

        existing_skus = set(related_po_qs.values_list('open_po__sku', 'line_reference'))
        pos_updated_list, opos_updated_list, po_ids, po_numbers = update_existing_po_details(
            update_po_dict,
            related_po_qs,
            requested_user
        )
        #Header Level Cancellation
        cancel_po_entries(
            wh_user,
            cancel_po_dict,
            related_po_qs,
            requested_user,
            cancel_po_nums,
            cancel_po_refs,
            po_ids,
            replace_check
        )

        if po_entries:
            if pos_updated_list or opos_updated_list:
                po_obj = pos_updated_list[0]
            else:
                po_obj = related_po_qs.first()

            extra_data = {
                'ship_to' : po_obj.open_po.ship_to,
                'delivery_date' : po_obj.open_po.delivery_date,
                'terms' : po_obj.open_po.terms,
                'order_id' : po_obj.order_id,
                'prefix' : po_obj.prefix,
                'po_reference': po_obj.po_number,
                'wms_code': po_obj.open_po.wms_code,
                'po_type': po_obj.po_type,

            }


            #Adding New Entries to the Given Purchase Order
            add_new_items_to_po(
                request_user=requested_user,
                warehouse_user=wh_user,
                sku_data= po_entries,
                extra_data=extra_data,
                existing_skus=existing_skus
            )
        from core_operations.views.integration.integration import webhook_integration_3p
        filters ={"po_numbers": po_numbers}
        webhook_integration_3p(wh_user.id, "po_callback", filters)
        update_po_header(po_numbers, wh_user.id)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Update PO failed for %s and params are %s and error statement is %s' % (
        str(requested_user), str(po_data_list), str(e)))
        return "Update PO failed"

    return "Success"

def get_raise_po_grouped_data(request_data, mrp_check=False, timezone=None):
    all_data = OrderedDict()
    show_cess_tax, show_apmc_tax = False, False
    for each_row in request_data.get('items', []):
        sgst_tax, cgst_tax, igst_tax, cess_tax, utgst_tax, apmc_tax = 0, 0, 0, 0, 0, 0
        price = each_row.get('price', 0)
        mrp = each_row.get('mrp', 0)
        aux_data = each_row.get('aux_data', {})
        line_extra_fields = each_row.get('line_extra_fields', {})
        line_reference = str(aux_data.get('line_reference', ''))
        supplier_payment_id = request_data.get('payment_term', '')
        if supplier_payment_id:
            supplier_payment_id = supplier_payment_id[0].split(':')[0]
        if request_data.get('po_reference'):
            po_name = request_data.get('po_reference')
        else:
            po_name = each_row.get('po_name', '')
        
        if each_row.get('cgst_tax'):
            cgst_tax = float(each_row.get('cgst_tax'))
        if each_row.get('sgst_tax'):
            sgst_tax = float(each_row.get('sgst_tax'))
        if each_row.get('igst_tax'):
            igst_tax = float(each_row.get('igst_tax'))
        if each_row.get('cess_tax'):
            cess_tax = float(each_row.get('cess_tax'))
            show_cess_tax = True
        if each_row.get('utgst_tax'):
            utgst_tax = float(each_row.get('utgst_tax'))
        if each_row.get('apmc_tax'):
            apmc_tax = float(each_row.get('apmc_tax')) 
            show_apmc_tax = True   
        po_delivery_date , pr_delivery_date, po_date = "", "", ""
        if request_data.get('po_delivery_date'):
            ist_timezone = pytz.timezone(timezone)
            parsed_date = datetime.datetime.strptime(request_data['po_delivery_date'], "%Y-%m-%d %H:%M:%S")
            po_delivery_date = ist_timezone.localize(parsed_date)
        if 'pr_delivery_date' in request_data and request_data['pr_delivery_date']:
            pr_delivery_date = datetime.datetime.strptime(str(request_data['pr_delivery_date'][0]), "%m-%d-%Y")
        if request_data.get('po_date'):
            ist_timezone = pytz.timezone(timezone)
            parsed_date = datetime.datetime.strptime(request_data['po_date'], "%Y-%m-%d %H:%M:%S")
            po_date = ist_timezone.localize(parsed_date)
        if mrp_check:
            cond = (each_row['sku'], line_reference, price, mrp)
        else:
            cond = (each_row['sku'], line_reference, price)
        all_data.setdefault(cond, {
            'order_quantity': 0, 'price': each_row.get('price', 0), 
            'supplier_id': request_data.get('supplier_id', ''), 'supplier_payment': supplier_payment_id,
            'supplier_code': request_data.get('supplier_code', ''), 'po_name': po_name, 
            'po_type': request_data.get('po_type', ''), 
            'receipt_type': request_data.get('receipt_type', ''),
            'remarks': each_row.get('remarks', ''), 'measurement_unit': each_row.get('measurement_unit', ''),
            "weight":each_row.get('weight', ''),
            'vendor_id': request_data.get('vendor_id', ''), 'ship_to': request_data.get('ship_to',''),
            'sellers': {}, 'data_id': each_row.get('data_id', ''),
            'order_type': request_data.get('order_type'), 'mrp': mrp, 'sgst_tax': sgst_tax, 'cgst_tax': cgst_tax,
            'igst_tax': igst_tax, 'cess_tax': cess_tax,
            'utgst_tax': utgst_tax, 'apmc_tax': apmc_tax, 'po_delivery_date': po_delivery_date,
            'approval_remarks': request_data.get('approval_remarks',''), 'pr_delivery_date': pr_delivery_date,
            'product_category': each_row.get("product_category", ''), 'priority_type': request_data.get("priority_type", ""),
            'description': each_row.get('description', ''), 'service_start_date': request_data.get('service_start_date', ''),
            'service_end_date': request_data.get('service_end_date', ''), 'description_edited': request_data.get('description_edited',''),
            'sku_category': each_row.get('sku_category', ''), 'temp_price': each_row.get('temp_price', ''), 'temp_tax': each_row.get('temp_tax', ''),
            'temp_cess_tax': each_row.get('temp_cess_tax'), 'aux_data' : aux_data, 'line_extra_fields' : line_extra_fields, 'free_quantity': each_row.get('free_quantity', 0), 'po_date': po_date})
        order_qty = each_row.get('order_quantity', 0) or 0
        all_data[cond]['order_quantity'] += float(order_qty)

    return all_data, show_cess_tax, show_apmc_tax


def get_min_max_id(my_dict, i):
    min_max_id = ''
    if 'min_max_id' in my_dict.keys():
        min_max_id = my_dict['min_max_id'][i]
    return min_max_id

def get_po_free_quantity(my_dict, i):
    free_quantity = 0
    if 'free_quantity' in my_dict.keys() and len(my_dict['free_quantity']) > i:
        free_quantity = my_dict['free_quantity'][i]
        if not free_quantity:
            free_quantity = 0
    return free_quantity

def get_line_reference(my_dict, i):
    line_reference = ''
    if 'line_reference' in my_dict.keys():
        line_reference = my_dict['line_reference'][i]
    return line_reference

def get_raisepo_group_data(user, data_dict, mrp_check=False, timezone=None):
    """Get the grouped data for raise PO."""
    all_data = OrderedDict()
    show_cess_tax = False
    show_apmc_tax = False

    def get_value(key, index, default=None, single_value=False):
        """Get the value from the data_dict."""
        float_fields = [
            'price', 'order_quantity', 'free_quantity', 'mrp',
            'sgst_tax', 'apmc_tax', 'apmc_tax', 'cgst_tax', 'igst_tax',
            'utgst_tax']
        values = []
        result = ''
        if key in data_dict:
            values = data_dict[key]
        if single_value and len(values) > 0:
            result = values[0]
        elif len(values) > index:
            result = values[index]
        if not result:
            result = default
        if key in float_fields:
            result = float(result)

        return result

    def parse_date(date_str, format_str, tz=None):
        """Parse the date string to datetime object."""
        if date_str:
            parsed_date = datetime.datetime.strptime(date_str, format_str)
            return tz.localize(parsed_date) if tz else parsed_date
        return ''

    ist_timezone = pytz.timezone(timezone)

    for i in range(len(data_dict['wms_code'])):
        cond = (data_dict['wms_code'][i], get_line_reference(data_dict, i))

        if 'mrp' in data_dict and mrp_check:
            cond = (cond[0], cond[1], float(data_dict['mrp'][i] or 0))

        aux_data = data_dict['aux_data'][i] if 'aux_data' in data_dict and data_dict['aux_data'][i] else {}

        all_data.setdefault(cond, {
            'order_quantity': 0,
            'price': get_value('price', i, 0),
            'supplier_id': get_value('supplier_id', 0, ''),
            'supplier_payment': get_value('payment_term', 0, '').split(':')[0],
            'supplier_code': get_value('supplier_code', i, ''),
            'po_name': get_value('po_name', 0, '', True),
            'po_type': get_value('po_type', 0, '', True),
            'receipt_type': get_value('receipt_type', 0, '', True),
            'remarks': get_value('remarks', i, ''),
            'measurement_unit': get_value('measurement_unit', i, ''),
            'weight': get_value('weight', i, ''),
            'vendor_id': get_value('vendor_id', 0, '', True),
            'ship_to': get_value('ship_to', 0, '', True),
            'sellers': {},
            'data_id': get_value('data-id', i, ''),
            'order_type': 'VR' if get_value('vendor_id', 0, '', True) else 'SR',
            'mrp': get_value('mrp', i, 0),
            'sgst_tax': get_value('sgst_tax', i, 0),
            'cgst_tax': get_value('cgst_tax', i, 0),
            'igst_tax': get_value('igst_tax', i, 0),
            'cess_tax': get_value('cess_tax', i, 0),
            'utgst_tax': get_value('utgst_tax', i, 0),
            'apmc_tax': get_value('apmc_tax', i, 0),
            'po_delivery_date': parse_date(get_value('po_delivery_date', 0, '', True), "%Y-%m-%d %H:%M:%S", ist_timezone),
            'approval_remarks': get_value('approval_remarks', 0, '', True),
            'pr_delivery_date': parse_date(get_value('pr_delivery_date', 0, '', True), "%d-%m-%Y"),
            'product_category': get_value('product_category', 0, '', True),
            'priority_type': get_value('priority_type', 0, '', True),
            'description': get_value('description', i, ''),
            'description_edited': get_value('description_edited', i, ''),
            'service_start_date': get_value('service_start_date', i, ''),
            'service_end_date': get_value('service_end_date', i, ''),
            'sku_category': get_value('sku_category', 0, '', True),
            'temp_price': get_value('temp_price', i, ''),
            'temp_tax': get_value('temp_tax', i, ''),
            'temp_cess_tax': get_value('temp_cess_tax', i, ''),
            'aux_data': aux_data,
            'min_max_id': get_min_max_id(data_dict, i),
            'free_quantity': get_po_free_quantity(data_dict, i),
            'user': user,
            'uom_qty' : int(get_value('uom_qty', i ,0))
        })

        order_qty = float(get_value('order_quantity', i, 0))
        all_data[cond]['order_quantity'] += order_qty

        if data_dict['cess_tax'][i]:
            show_cess_tax = True
        if data_dict['apmc_tax'][i]:
            show_apmc_tax = True

    return all_data, show_cess_tax, show_apmc_tax

def frame_order_update_payload(request_data):
    """Frame the order update payload."""
    dest_user = ''
    if request_data.get('po_type', '').lower() == 'stocktransfer':
        dest_user = User.objects.filter(username = request_data.get('supplier_id')).first()
        if not dest_user:
            return {}, None
    else:
        return {}, None

    for each_data in request_data.get('items', []):
        if each_data.get('order_quantity', 0):
            each_data.pop('id', '')
            each_data['quantity'] = each_data['order_quantity']
    request_data['order_reference'] = request_data.get('po_reference', '') or request_data.get('po_number', '')
    request_data['warehouse'] = dest_user.username
    request_data['status'] = 'update'
    request_data['update_po'] = False
    request_data['order_type'] = 'StockTransfer'
    request_data['reason'] = 'PO Updated'
    return request_data, dest_user

def update_stock_transfer_order(request, request_data, update_type='update'):
    """Update the stock transfer order."""
    from outbound.views.orders.orders import SellerOrderSet
    order_data = copy.deepcopy(request_data)
    order_update_payload, dest_user = frame_order_update_payload(order_data)
    if not order_update_payload:
        return
    if update_type == 'cancel':
        order_update_payload.update({
            'status': 'cancel',
            'reason': 'PO Cancelled',
            'items': [],
            'replace': True
        })

    request_user = request.user if request else dest_user
    #Order update request
    request_factory = RequestFactory()
    request_factory.user = request_user
    request_factory.warehouse = dest_user
    request_factory.method = 'PUT'
    request_factory.body = json.dumps(order_update_payload)
    order = SellerOrderSet()
    order.request = request_factory
    order.put()