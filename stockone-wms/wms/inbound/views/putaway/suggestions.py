import datetime
import copy
import pytz
import math
import re
from itertools import chain
import pandas as pd

from django.db.models import F, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Value, Case, When
from django.db.models.functions import Concat

from django.db.models.query import QuerySet

from wms_base.wms_utils import REJECT_REASONS

from core_operations.views.common.main import (
    get_misc_value, get_dictionary_query, get_multiple_misc_values,
    create_default_zones, get_uom_with_sku_code,
    truncate_float, get_uom_decimals, generate_log_message
    )
from inventory.views.locator.stock_detail import get_or_create_batch
from wms_base.wms_utils import init_logger
today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/putaway_suggestions' + today + '.log')
log_err = init_logger('logs/putaway_suggestions.log')

from core.models import SKUMaster, AuthorizedBins, SKUAttributes, MiscDetail
from inventory.models import (
    CycleCount, ZoneMaster, LocationMaster, StockDetail,
    BAtoSADetail
    )

from inbound.models import POLocation, OrderTypeLocationMapping, PutawayMapping, SellerPOSummary
from quality_control.models import QualityControlSummary
from outbound.models import OrderTypeZoneMapping, Picklist

from inbound.views.common.constants import INBOUND_STAGING_LANES_MAPPING

def save_remaining_putaway_quantity(po_loc: QuerySet[POLocation], quantity: int, remaining_quantity: int, location_id: int = 0,  json_data: dict={}) -> POLocation:
    ''' Save POLocation Remaining Quantity '''
    try:
        po_location_dict = po_loc[0].__dict__
    except:
        po_location_dict = po_loc.__dict__
    del po_location_dict["id"]
    del po_location_dict['_state']
    if location_id:
        po_location_dict["quantity"]= quantity
        po_location_dict["location_id"]= location_id
        po_location_dict["status"]= 1
        if po_location_dict["json_data"]:
            po_location_dict["json_data"].update(json_data)
        else:
            po_location_dict["json_data"] = json_data

    else:
        po_loc.update(quantity=quantity)
        po_location_dict["quantity"]= remaining_quantity
    
    po_location_dict["original_quantity"]= remaining_quantity
    new_po_loc = POLocation.objects.create(**po_location_dict)
    new_po_loc.quantity = 0
    new_po_loc.status = 0
    new_po_loc.save(update_fields=['quantity', 'status', 'updation_date'])
    return new_po_loc

def create_auto_putaway_stock_detail(stock_detail_dict, batch_id, quantity, original_quantity):
    stock_detail_dict.update({
        'original_quantity': original_quantity,
        'quantity': quantity,
        'batch_detail_id': batch_id
    })
    stock_detail_obj = StockDetail.objects.create(**stock_detail_dict)
    return stock_detail_obj

def update_category_based_zone(include_dict, warehouse, sku_master, received_quantity, carton, extra_dat):
    sugg_list = []
    sku_category_list_pf = extra_dat.get('sku_category_list_pf',[])
    sku_category_list_ba = extra_dat.get('sku_category_list_ba',[])
    pick_zones_list = extra_dat.get('pick_zones_list',[])
    if sku_master.sku_category.lower() in sku_category_list_pf:
        include_dict['zone__zone__in'] = pick_zones_list
        sugg_list.append({'zones': pick_zones_list, 'received_quantity': received_quantity})
    elif sku_master.sku_category.lower() in sku_category_list_ba:
        putaway_sa_time_from, putaway_sa_time_to, bulk_zones_list= [""]*3
        misc_data = list(MiscDetail.objects.filter(
                user=warehouse.id, misc_type__in=["putaway_sa_time_from", "putaway_sa_time_to", "bulk_zones_list"]
            ).values("misc_value", "misc_type"))
        for misc_row in misc_data:
            if misc_row["misc_value"] not in ['false', '']:
                if misc_row["misc_type"] == "putaway_sa_time_from":
                    putaway_sa_time_from =" " +misc_row["misc_value"]
                elif misc_row["misc_type"] == "putaway_sa_time_to":
                    putaway_sa_time_to =" " +misc_row["misc_value"]
                elif misc_row["misc_type"] == "bulk_zones_list":
                    bulk_zones_list = misc_row["misc_value"]
        if putaway_sa_time_from and putaway_sa_time_to:
            tom_date = (datetime.datetime.now(pytz.timezone('Asia/Kolkata')) + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
            today_date = (datetime.datetime.now(pytz.timezone('Asia/Kolkata'))).strftime('%Y-%m-%d')
            time_check = datetime.datetime.strptime(today_date+putaway_sa_time_from, "%Y-%m-%d %H:%M") < datetime.datetime.now(pytz.timezone('Asia/Kolkata')).now() < \
                    datetime.datetime.strptime(tom_date+putaway_sa_time_to, "%Y-%m-%d %H:%M")
            if bulk_zones_list not in ['false', '']:
                bulk_zones_list = bulk_zones_list.split(',')
            if not time_check:
                sugg_list.append({'zones': bulk_zones_list, 'received_quantity': received_quantity})
                return sugg_list
            open_orders = sku_master.orderdetail_set.filter(status=1).aggregate(Sum('quantity'))['quantity__sum']
            open_order_qty = open_orders if open_orders else 0
            if open_order_qty:
                pending_putaway = POLocation.objects.filter(status=1, purchase_order__open_po__sku_id=sku_master.id,
                                                            location__zone__zone__in=pick_zones_list).\
                    aggregate(Sum('quantity'))['quantity__sum']
                pending_putaway = pending_putaway if pending_putaway else 0
                sku_stock = sku_master.stockdetail_set.filter(quantity__gt=0,
                                                              location__zone__zone__in=pick_zones_list
                                                    ).aggregate(Sum('quantity'))['quantity__sum']
                sku_stock = sku_stock if sku_stock else 0
                pick_reserved = Picklist.objects.filter(
                    order__sku_id=sku_master.id, status='open', stock__location__zone__zone__in=pick_zones_list
                ).aggregate(Sum('reserved_quantity'))['reserved_quantity__sum']
                pick_reserved = pick_reserved if pick_reserved else 0
                replenish_qty = BAtoSADetail.objects.filter(
                        location__zone__zone__in=pick_zones_list, status=1, replenishment_qty__gt=0, sku_id= sku_master.id,
                    sku__user=warehouse.id).aggregate(Sum('replenishment_qty'))['replenishment_qty__sum'] or 0
                pick_sugg = open_order_qty - (sku_stock - pick_reserved + pending_putaway + replenish_qty)
                if pick_sugg > 0:
                    if received_quantity < pick_sugg or carton:
                        sugg_list.append({'zones': pick_zones_list, 'received_quantity': received_quantity})
                    else:
                        sugg_list.append({'zones': pick_zones_list, 'received_quantity': pick_sugg})
                        received_quantity -= pick_sugg
                        sugg_list.append({'zones': bulk_zones_list, 'received_quantity': received_quantity})
                else:
                    sugg_list.append({'zones': bulk_zones_list, 'received_quantity': received_quantity})
            else:
                sugg_list.append({'zones': bulk_zones_list, 'received_quantity': received_quantity})
    return sugg_list

def suggest_location_in_next_lane(warehouse, stock_ids, quantity, next_stage=''):
    lanes_mapping = INBOUND_STAGING_LANES_MAPPING
    staging_lane_config = get_misc_value('inbound_staging_lanes', warehouse.id)
    staging_lanes_order = staging_lane_config.split(',')

    stock_objs = StockDetail.objects.filter(id__in=stock_ids)
    if stock_objs.exists():
        stock_obj = stock_objs[0]
        current_stage = stock_obj.receipt_type.lower()
        if not next_stage:
            current_index = staging_lanes_order.index(current_stage.lower())
            next_index = current_index+1
            next_stage = staging_lanes_order[next_index]
    
        rev_dict = {'Dock Staging': 'dock', 'QC Staging': 'qc', 'Putaway Staging': 'pnd'}
        staging_name = lanes_mapping[rev_dict[next_stage]]['name']
        default_zone = lanes_mapping[rev_dict[next_stage]]['default_zone']
        default_loc = lanes_mapping[rev_dict[next_stage]]['default_loc']
        sub_zone_type = lanes_mapping[rev_dict[next_stage]]['sub_zone_type']
    
        loc_objs = LocationMaster.objects\
            .filter(zone__user=warehouse.id, status=1, zone__storage_type = sub_zone_type).order_by('fill_sequence')
        if not loc_objs.exists():
            create_default_zones(warehouse, default_zone, default_loc, 99999, 'inbound_staging', sub_zone_type)
            location = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone = default_zone)[0]
        else:
            location = loc_objs[0]
        return location
    return False

def remaining_location_carton_quantity(location_obj, warehouse):
    putaway_quantity = POLocation.objects.filter(
        location_id=location_obj.id, status=1, location__zone__user=warehouse.id).values("carton_id").distinct().count()
    filled_capacity = StockDetail.objects.filter(
        location_id=location_obj.id, quantity__gt=0, sku__user=warehouse.id).values("carton_id").distinct().count()
    max_capacity = location_obj.carton_capacity
    filled_capacity += putaway_quantity
    remaining_capacity = max_capacity - filled_capacity
    return remaining_capacity

def update_new_carton_location(warehouse, include_dict, exclude_dict, lpn_number,
                               cartons_location_mapping_dict):
    locations = LocationMaster.objects.filter(zone__user=warehouse.id, **include_dict).exclude(
        get_dictionary_query(exclude_dict)).order_by('fill_sequence')
    locations = list(
        chain(locations, LocationMaster.objects.filter(zone__zone='DEFAULT', zone__user=warehouse.id)))
    for loc in locations:
        remaining_capacity = remaining_location_carton_quantity(loc, warehouse)
        if remaining_capacity <= 0 and loc.zone.zone.upper() not in ['DEFAULT']:
            continue
        cartons_location_mapping_dict[lpn_number] = loc.id
        break
    return locations

def validate_location_to_one_sku(quantity_data, sku_id, sku_key='sku_id'):
    valid_location = False
    filled_quantity = 0
    if quantity_data.exists():
        if quantity_data.count() == 1 and quantity_data[0][sku_key] == sku_id:
            valid_location = True
            filled_quantity = quantity_data[0]['quantity_sum']
    else:
        valid_location = True

    return filled_quantity, valid_location

def fetch_default_locations(current_lane, segregation, warehouse):
    #Fetching Default Locations based on Lane and Segreagtion
    lane = INBOUND_STAGING_LANES_MAPPING[current_lane]
    defualt_locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone = lane['default_zone'])
    if not defualt_locations.exists():
        create_default_zones(warehouse, lane['default_zone'], lane['default_loc'], 99999, segregation, lane['sub_zone_type'])
        defualt_locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone = lane['default_zone'])
    return defualt_locations

def get_replenish_locations(replenish_data, locations):
    replenish_locations = []
    locs, zones = [], []
    replenish_locations1, replenish_locations2 = [], []
    if replenish_data:
        for each_replenish in replenish_data:
            location_id = each_replenish.get('dest_location')
            dest_zone = each_replenish.get('dest_zone')
            if location_id:
                locs.append(location_id)
            elif dest_zone:
                zones.append(dest_zone)
        if locs:
            replenish_locations1 = locations.filter(id__in=locs)
        if zones:
            replenish_locations2 = locations.filter(zone_id__in=zones)
        replenish_locations = list(chain(replenish_locations1, replenish_locations2))
    return replenish_locations

def get_eligible_locations(po_location_dict, extra_dict, misc_permission_dict, warehouse):
    ''' Retutns Eligible Locations to Do Putaway based on Configurations and Pending Putaway Stock, Current Stock'''

    #Fetching configurations
    express_putaway_type = misc_permission_dict.get('express_putaway')
    location_sku_mapping = misc_permission_dict.get('location_sku_mapping')
    putaway_strategy_config = misc_permission_dict.get('putaway_strategy', "").split(',')

    #Fetching Required from POLocation Dict
    putaway_type = po_location_dict.get("putaway_type", '')
    lpn_number = po_location_dict.get("lpn_number")
    sku_id = po_location_dict.get("sku_id")

    #Fetching Required from Extra Data
    include_dict = extra_dict.get('include_dict', {})
    exclude_dict = extra_dict.get('exclude_dict', {})   
    put_zone = extra_dict.get('put_zone', '')
    putaway_strategies = extra_dict.get('putaway_strategies', [])
    replenish_data = po_location_dict.get('replenish_data', [])

    cartons_location_mapping_dict = extra_dict.get('cartons_location_mapping_dict', {})
    next_lane = extra_dict.get('next_lane', 'dflt')
    segregation = 'sellable' if next_lane == 'dflt' else 'inbound_staging'
    if segregation == 'inbound_staging':
        include_dict['zone__segregation'] = segregation
        include_dict['zone__storage_type'] = next_lane
        
    stock_detail = StockDetail.objects.filter(sku__user=warehouse.id, sku_id=sku_id, quantity__gt=0)
    po_locations = POLocation.objects.filter(sku__user=warehouse.id, status=1, sku_id=sku_id, quantity__gt=0)
    locations = LocationMaster.objects.filter(zone__user=warehouse.id, status=1, **include_dict).exclude(**exclude_dict)
    
    #Drop Ship Suggestions
    if putaway_type.lower()=="dropship":
        locations = locations.filter(zone__segregation='drop_ship').order_by('fill_sequence')
        if not locations.exists():
            locations = create_default_zones(warehouse, 'DROP_SHIP', 'DROP_SHIP1', 99999, segregation='drop_ship')    
    #Full Carton Putaway Suggestions
    elif express_putaway_type == 'full_carton_putaway' and lpn_number and cartons_location_mapping_dict.get(lpn_number):
        po_location_dict['location_id'] = cartons_location_mapping_dict[lpn_number]
        locations = locations.filter(id=po_location_dict.get("location_id"))

    #Location SKU Mapping Suggestions
    elif location_sku_mapping == 'true':
        fixed_bin_locations, dynamic_bin_locations = [], []
        put_locations = locations.filter(zone__zone=put_zone).order_by('fill_sequence')
        replenish_locations = get_replenish_locations(replenish_data, locations)
        if 'fixed_bin_mapping' in putaway_strategy_config and 'fixed_bin_mapping' in putaway_strategies:
            fixed_bin_locations = extra_dict.get('mapped_locations', [])
        if ('dynamic_bin_top_up' in putaway_strategy_config and 'dynamic_bin_top_up' in putaway_strategies) or ('dynamic_replenishment' in putaway_strategy_config and 'dynamic_replenishment' in putaway_strategies):
            mapped_zones = extra_dict.get('mapped_zones', [])
            priority_order = Case(
                *[When(zone_id=zone, then=idx) for idx, zone in enumerate(mapped_zones)]
            )
            dynamic_bin_locations = list(LocationMaster.objects.filter(zone_id__in=mapped_zones, status=1).exclude(id__in=fixed_bin_locations).order_by(priority_order,'fill_sequence').values_list('id', flat=True))
        mapped_locations = list(chain(fixed_bin_locations, dynamic_bin_locations))
        stock_locations = stock_detail.filter(location__in=mapped_locations).order_by('location__fill_sequence').values_list('location_id', flat=True)
        suggested_locations = po_locations.filter(location__in=mapped_locations).order_by('location__fill_sequence').values_list('location_id', flat=True)
        seen = set()
        location_ids = [location for location in chain(stock_locations, suggested_locations, mapped_locations) if location not in seen and not seen.add(location)]
        loc_filters = {}
        locations_dict = LocationMaster.objects.filter(id__in=location_ids, **loc_filters).in_bulk()
        locations = [locations_dict[loc_id] for loc_id in location_ids if loc_id in locations_dict]
        locations = list(chain(replenish_locations, locations, put_locations))

    elif put_zone:
        locations = locations.filter(zone__zone=put_zone).order_by('fill_sequence')
    else:
        locations = locations.order_by('fill_sequence')


    #Appending Default Locations to Suggested Locations
    default_locations = fetch_default_locations(next_lane, segregation, warehouse)
    locations = list(chain(locations, default_locations))

    return locations

def get_eligible_locations_from_defined_rules(rule_type, receiving_stock, current_lane, warehouse):
    ''' Returns List of Location Query Objects that satisfies the Defined Rules'''
    sugg_locations = []
    
    #Mandatory Default Filters
    filters = {'rule_type':rule_type, 'status':1}
    if receiving_stock.get('receipt_type'):
        receipt_type_map = {'po_grn' : 'PO GRN', 'jo_grn': 'JO GRN', 'sr_grn' : 'SR GRN'}
        filters['receipt_type'] = receipt_type_map[receiving_stock.get('receipt_type')]
    if receiving_stock.get('order_type'):
        filters['order_type'] = receiving_stock.get('order_type')

    sku_attributes = receiving_stock.get('sku_attr')

    satisfied_rules, satisfield_rule_ids = [], []
    #Priority Wise Parameters to Filter the Best Rule
    priority_paramters = ['put_zone', 'sku_category']
    #Receving Stock and Rules Field Mapping
    additional_filters = {'put_zone' : 'put_zone', 'sku_category' : 'sku_category'}

    #Filtering with Basic Mandatory Fields
    all_rule_mappings = OrderTypeZoneMapping.objects.filter(**filters).order_by('-priority')
    if all_rule_mappings.exists():
        #Fetching Satisfied Rules based on Priority
        for param in priority_paramters:
            rule_mappings = copy.deepcopy(all_rule_mappings)
            for filter_key, filter_value in additional_filters.items():
                if receiving_stock[filter_value]:
                    rule_mapping_filter = {filter_key : receiving_stock[filter_value]}
                    if rule_mappings.filter(**rule_mapping_filter).exists():
                        rule_mappings = rule_mappings.filter(**rule_mapping_filter)
            
            #Checking with SKU Extra Attributes and Fetching Satisfied Rule IDs
            rule_ids = []
            for each_rule in rule_mappings.values('id', 'json_data').order_by('-priority'):
                rule_id, rule_json_data = each_rule['id'], each_rule['json_data']
                sku_attrs = rule_json_data.get('skuAttr')
                is_rule_satisfied = True
                for attr_name, attr_value in sku_attrs.items():
                    if sku_attributes.get(attr_name) != attr_value:
                        is_rule_satisfied = False
                        continue
                if is_rule_satisfied:
                    rule_ids.append(rule_id)
               
            #rule_ids = rule_mappings.values_list('id', flat=True).order_by('priority')
            
            #Appending New Rules IDs, Duplicate IDs are omitted
            new_rule_ids = list(set(rule_ids) - set(satisfield_rule_ids))
            satisfield_rule_ids.extend(new_rule_ids)
            new_rules = list(OrderTypeZoneMapping.objects.filter(
                id__in=new_rule_ids).values_list('id', 'rule_name', 'zone_id').order_by('-priority'))
            
            #Chaining New Satisfied Rules
            satisfied_rules = list(chain(satisfied_rules, new_rules))

            #Popping the Top Priority Parameter to Fetch Next Satisfying Rules
            additional_filters.pop(param)
    
        #Fetching All Location which satisfy the rules along with Priority
        for id, rule_name, zone_id in satisfied_rules:
            location_ids = list(OrderTypeLocationMapping.objects.filter(
                zone_mapping_id=id, status=1).values_list('location', flat=True))
            if location_ids:
                locations = LocationMaster.objects.filter(id__in=location_ids).order_by('fill_sequence')
            else:
                locations = LocationMaster.objects.filter(
                    zone_id=zone_id, status=1, zone__user=warehouse.id).order_by('fill_sequence')
            if current_lane:
                locations = locations.filter(zone__storage_type=current_lane)
            sugg_locations = list(chain(sugg_locations, locations))
            

    #Appending Default Locations to Suggested Locations
    default_locations = fetch_default_locations(current_lane, 'inbound_staging', warehouse)
    sugg_locations = list(chain(sugg_locations, default_locations))
    return sugg_locations

def fetch_location_pending_stock_quantity(loc_id, user_id):
    filters = {'location_id':loc_id, 'status' : 1, 'location__zone__user' : user_id}
    pending_stock = POLocation.objects.filter(**filters).aggregate(Sum('quantity'))['quantity__sum'] or 0
    return pending_stock

def fetch_location_current_stock_quantity(loc_id, user_id):
    filters = {'location_id':loc_id, 'quantity__gt':0, 'sku__user':user_id}
    current_stock = StockDetail.objects.filter(**filters).aggregate(Sum('quantity'))['quantity__sum'] or 0
    return current_stock

def fetch_sku_wise_location_pending_stock_quantity(loc_id, user_id):
    filters = {'location_id':loc_id, 'status' : 1, 'location__zone__user' : user_id, 'location__zone__segregation':'sellable'}
    pending_stock = POLocation.objects.filter(**filters).values('purchase_order__open_po__sku_id')\
        .annotate(quantity_sum=Sum('quantity') or 0)
    return pending_stock

def fetch_sku_wise_location_current_stock_quantity(loc_id, user_id):
    filters = {'location_id':loc_id, 'quantity__gt':0, 'sku__user':user_id, 'location__zone__segregation': 'sellable'}
    current_stock = StockDetail.objects.filter(**filters).values('sku_id').annotate(quantity_sum=Sum('quantity') or 0)
    return current_stock

def get_capacity_and_uom(loc, sku_code, location_capcity_dict):
    max_capacity, loc_uom_type = loc.max_capacity, loc.uom
    unique_key = (sku_code, loc.id)
    if unique_key in location_capcity_dict:
        max_capacity = location_capcity_dict.get(unique_key, 0)
    return max_capacity, loc_uom_type

def validate_location_to_one_sku_one_batch(quantity_data, sku_id, batch_no, uniq_sku):
    """
        Validates whether a Location has unique SKU<>Batch.
        Also returns Filled capacity of the location either in Stock or Pending Putaway
    """
    valid_location = True
    filled_quantity = 0
    sku_batch_key = str(sku_id) + '<<>>' + batch_no
    for each_row in quantity_data:
        row_id, row_batch = each_row['sku_batch'].split('<<>>')
        if (uniq_sku and each_row['sku_batch'] != sku_batch_key) or (not uniq_sku and str(sku_id) == row_id and batch_no != row_batch):
            valid_location = False
            break
        filled_quantity += each_row['total_qty']
    return filled_quantity, valid_location

def validate_restrict_location_one_sku_unique_batch(location_id, sku_id, batch_no, user_id, uniq_sku=False):
    """
        Validates a location has unique SKU<>Batch.
        Also returns Pending Putaway Quantity, Filled Quantity in that Location
    """
    # Pending Putaway Quantity of unique SKU<>Batch
    sku_batch_pending_putaway_qty = POLocation.objects.filter(location_id=location_id, status=1, location__zone__user=user_id)\
                                                        .values(sku_batch=Concat('sku_id', Value('<<>>'), 'seller_po_summary__batch_detail__batch_no', output_field=CharField()))\
                                                        .annotate(total_qty=Sum('quantity') or 0)
    putaway_quantity, valid_location = validate_location_to_one_sku_one_batch(sku_batch_pending_putaway_qty, sku_id, batch_no, uniq_sku)
    if not valid_location:
        return False, 0, 0

    # Stock Quantity of unique SKU<>Batch
    sku_batch_stock_qty = StockDetail.objects.filter(location_id=location_id, quantity__gt=0, sku__user=user_id) \
                                            .annotate(sku_batch=Concat('sku_id', Value('<<>>'), 'batch_detail__batch_no', \
                                            output_field=CharField())) \
                                            .values('sku_batch').annotate(total_qty=Sum('quantity') or 0)
    filled_capacity, valid_location = validate_location_to_one_sku_one_batch(sku_batch_stock_qty, sku_id, batch_no, uniq_sku)

    return valid_location, putaway_quantity, filled_capacity

def validate_fixed_bin_mapping_location(misc_permission_dict, batch_no, loc, warehouse, sku_id):
    putaway_quantity, filled_capacity, replenish_qty =0, 0, 0
    do_continue = False
    if loc.zone.unique_batch:
        valid_location, putaway_quantity, filled_capacity = validate_restrict_location_one_sku_unique_batch(loc.id, sku_id, batch_no, warehouse.id)
        if not valid_location:
            do_continue = True
    elif misc_permission_dict.get('restrict_location_to_one_sku') == 'True':
        #Fetching SKU Wise Pending Putaway Quantities in the Iterating Location 
        sku_wise_pending_stock = fetch_sku_wise_location_pending_stock_quantity(loc.id, warehouse.id)
        putaway_quantity, pending_valid_location = validate_location_to_one_sku(sku_wise_pending_stock, sku_id, sku_key='purchase_order__open_po__sku_id')
        if not pending_valid_location:
            do_continue = True

        #Fetching SKU Wise Current Stock Quantities in the Iterating Location
        sku_wise_current_stock = fetch_sku_wise_location_current_stock_quantity(loc.id, warehouse.id)
        filled_capacity, stock_valid_location = validate_location_to_one_sku(sku_wise_current_stock, sku_id)
        if not stock_valid_location:
            do_continue = True

        #Fetching SKU Wise BAtoSA Suggested Quantities in the Iterating Location
        BAtoSADetail.objects.filter(
                dest_location_id=loc.id, status=0, replenishment_qty__gt=0, sku__user=warehouse.id
            ).values('sku').annotate(quantity_sum=Sum('replenishment_qty'))
        replenish_qty = 0
    else:
        #Fetching Pending Stock and Current Stock Quantities in the Iteraing Location
        sku_wise_pending_stock = fetch_sku_wise_location_pending_stock_quantity(loc.id, warehouse.id)
        sku_stock = sku_wise_pending_stock.filter(purchase_order__open_po__sku_id=sku_id)
        if sku_stock.exists():
            putaway_quantity = sku_stock[0]['quantity_sum']

        sku_wise_current_stock = fetch_sku_wise_location_current_stock_quantity(loc.id, warehouse.id)
        sku_stock = sku_wise_current_stock.filter(sku_id=sku_id)
        if sku_stock.exists():
            filled_capacity = sku_stock[0]['quantity_sum']

    return putaway_quantity, filled_capacity, replenish_qty, do_continue

def check_location_restrcitions(misc_permission_dict, sku_id, loc, warehouse, extra_dict):
    #When Location has One Location to One SKU Restriction checkt
    putaway_strategies = extra_dict.get('putaway_strategies', [])
    mapped_locations = extra_dict.get('mapped_locations', [])
    mapped_zones = extra_dict.get('mapped_zones', [])
    batch_no = extra_dict.get('batch_no', '')

    do_continue = False
    putaway_quantity, filled_capacity, replenish_qty = 0, 0, 0
    if 'fixed_bin_mapping' in putaway_strategies and loc.id in mapped_locations:
        putaway_quantity, filled_capacity, replenish_qty, do_continue = \
            validate_fixed_bin_mapping_location(misc_permission_dict, batch_no, loc, warehouse, sku_id)

    elif 'dynamic_bin_top_up' in putaway_strategies and loc.zone_id in mapped_zones:
        if loc.zone.restrict_one_location_to_one_sku:
            if loc.zone.unique_batch:
                # If SKU is batch based and Zone has Unique Batch Restriction
                valid_location, putaway_quantity, filled_capacity = validate_restrict_location_one_sku_unique_batch(loc.id, sku_id, batch_no, warehouse.id, uniq_sku=True)
                if not valid_location:
                    do_continue = True
                replenish_qty = 0
            else:
                #total quantity available in pending putaway
                pending_putaway_sku_quantity = POLocation.objects.filter(location_id=loc.id, status=1, location__zone__user=warehouse.id) \
                                                .values('purchase_order__open_po__sku_id').annotate(quantity_sum=Sum('quantity') or 0)
                putaway_quantity, valid_location = validate_location_to_one_sku(pending_putaway_sku_quantity, sku_id, sku_key='purchase_order__open_po__sku_id')
                if not valid_location:
                    do_continue = True
                #total quantity available in location
                location_sku_quantity_details = StockDetail.objects.filter(location_id=loc.id, quantity__gt=0, sku__user=warehouse.id) \
                                                .values('sku_id').annotate(quantity_sum=Sum('quantity'))
                filled_capacity, valid_location = validate_location_to_one_sku(location_sku_quantity_details, sku_id)
                if not valid_location:
                    do_continue = True

                replenish_qty = 0
        elif loc.zone.unique_batch:
            valid_location, putaway_quantity, filled_capacity = validate_restrict_location_one_sku_unique_batch(loc.id, sku_id, batch_no, warehouse.id)
            if not valid_location:
                do_continue = True
        else:
            #Fetching Pending Stock and Current Stock Quantities in the Iteraing Location
            putaway_quantity = fetch_location_pending_stock_quantity(loc.id, warehouse.id)
            filled_capacity = fetch_location_current_stock_quantity(loc.id, warehouse.id)
    else:
        #Fetching Pending Stock and Current Stock Quantities in the Iteraing Location
        putaway_quantity = fetch_location_pending_stock_quantity(loc.id, warehouse.id)
        filled_capacity = fetch_location_current_stock_quantity(loc.id, warehouse.id)

    return putaway_quantity, filled_capacity, replenish_qty, do_continue

def carton_check_in_stock(inb_packing, carton, loc, warehouse):
    cbn_check = False
    if inb_packing == 'true' and not carton and loc.zone.zone.upper() not in ['DEFAULT', 'QC_ZONE', 'DAMAZED_ZONE']:
        cbn_check = StockDetail.objects.filter(location_id=loc.id, quantity__gt=0, sku__user=warehouse.id,
                                                    carton__isnull=False)
    return cbn_check

def get_location_data(warehouse, locations, sku_master, misc_permission_dict):
    putaway_config = False
    location_type_dict, sku_data = {}, {}
    loc_capacity_calc_options = misc_permission_dict.get('location_capacity_calculation', '').split(',')
    if 'volume_utilization' in loc_capacity_calc_options or 'weight_utilization' in loc_capacity_calc_options:
        putaway_config = True
        location_type_check = LocationMaster.objects.filter(location__in=locations,location_type__isnull=False, zone__user=warehouse.id)\
                                .values('location', location_length=F('location_type__length'),
                                        location_breadth=F('location_type__breadth'),
                                        location_height=F('location_type__height'),
                                        location_weight=F('location_type__weight'),
                                        weight_utilization=F('location_type__weight_utilization'),
                                        volume_utilization=F('location_type__volume_utilization'))

        location_type_dict = {}
        for each_loc in location_type_check:
            location_type_dict[each_loc['location']] = each_loc

        sku_data = {'sku_code': sku_master.sku_code, 'sku_length': sku_master.length,
                    'sku_breadth':sku_master.breadth, 'sku_height': sku_master.height,
                    'sku_weight': sku_master.weight}

    return putaway_config, location_type_dict, sku_data, loc_capacity_calc_options

def suggest_location_wise_quantities(locations, received_quantity, extra_dict, misc_permission_dict, warehouse, sku_master):
    location_wise_qty = {}
    po_location_check = False

    #Fetching configurations
    inb_packing = misc_permission_dict.get('inbound_packing', "")
    express_putaway_type = misc_permission_dict.get('express_putaway', '')
    
    #Fetching Required Data
    putaway_type = extra_dict.get('putaway_type', '')
    lpn_number = extra_dict.get('lpn_number')
    sku_code = extra_dict.get('sku_code')
    sku_id = extra_dict.get('sku_id')
    location_capcity_dict = extra_dict.get('location_mapping_capacity', {})

    cartons_location_mapping_dict = extra_dict.get('cartons_location_mapping_dict', {})

    #Send Round off while calling save suggested po location
    round_off = extra_dict.get('round_off', 2)
    if round_off and isinstance(round_off, str):
        round_off = int(round_off)

    #Get Location data for weight and volume utilization calculation
    putaway_config, location_type_dict, sku_data, loc_capacity_calc_options = get_location_data(warehouse, locations, sku_master, misc_permission_dict)
    for loc in locations:
        is_full_carton_based = all([express_putaway_type == 'full_carton_putaway', lpn_number])
        #for BULK Zones Keeping Whole Stock in one location(without checking capacities)
        if loc.zone.zone in ['DEFAULT', 'QC_ZONE', 'DAMAZED_ZONE'] \
            or putaway_type.lower()=="backorder" or is_full_carton_based:
            location_wise_qty.update({loc.id : received_quantity})
            po_location_check= True

            if is_full_carton_based:
                if lpn_number not in cartons_location_mapping_dict:
                    cartons_location_mapping_dict[lpn_number] = loc.id
            break
                
        putaway_quantity, filled_capacity, replenish_qty, do_continue = \
            check_location_restrcitions(misc_permission_dict, sku_id, loc, warehouse, extra_dict)

        if do_continue:
            continue

        cbn_check = carton_check_in_stock(inb_packing, lpn_number, loc, warehouse)
        if cbn_check:
            continue
        
        do_continue, max_capacity = get_weight_and_volume_based_max_capacity(warehouse, putaway_config, loc, location_type_dict, sku_data, loc_capacity_calc_options)
        if do_continue:
            continue
        
        if not putaway_config:
            #Fetching Maximum Location Capacity and UOM
            max_capacity, loc_uom_type = get_capacity_and_uom(loc, sku_code, location_capcity_dict)

            if loc_uom_type and sku_code:
                loc_uom_dict = get_uom_with_sku_code(warehouse, sku_code, uom_type=loc_uom_type)
                loc_uom = loc_uom_dict.get('sku_conversion') if loc_uom_dict.get('sku_conversion') else 1
                max_capacity = max_capacity * loc_uom
        else:
            filled_capacity, putaway_quantity, replenish_qty = 0, 0, 0

        #Finding Remaining Capacity in the Iterating Location
        remaining_capacity = truncate_float(max_capacity - (filled_capacity + putaway_quantity + replenish_qty), round_off)

        if remaining_capacity <= 0:
                continue
        elif remaining_capacity < received_quantity:                
            location_wise_qty.update({loc.id : remaining_capacity})
            
            received_quantity = round(received_quantity - remaining_capacity, round_off)
        elif remaining_capacity >= received_quantity:
            location_wise_qty.update({loc.id : received_quantity})
            po_location_check=True
        
        if po_location_check:
            break
            
    return location_wise_qty, cartons_location_mapping_dict
    

def update_sku_for_mrp_weight_logic(warehouse, sku_id, sku_mrp, batch_dict, putaway_type,sku_buy_price,include_dict):
    sku_weight = batch_dict.get('weight', "")
    sku_update_dict = {}
    if sku_buy_price != batch_dict.get("buy_price"):
        sku_update_dict['price'] = batch_dict['buy_price']

    if batch_dict.get('weight'):
        #Updating Weight in SKU Attributes If New Weight Comes
        sku_weight = SKUAttributes.objects.filter(sku_id=sku_id, attribute_name='weight')
        if sku_weight.exists():
            if sku_weight[0].attribute_value != batch_dict.get('weight'):
                sku_weight.update(attribute_value = batch_dict['weight'])
    if putaway_type.lower() in ['stocktransfer']:
        putaway_zone = "DEFAULT"
    else:
        putaway_zone = "BULK ZONE"

    #Checking MRP and Weights with Last Received Stock to Suggest Location
    stock_obj = StockDetail.objects.filter(
        sku__user=warehouse.id, sku_id=sku_id, location__zone__zone__in=['DEFAULT', 'BULK ZONE'], quantity__gt=0).last()
    include_dict["zone__zone"] = putaway_zone
    if stock_obj:
        if stock_obj.batch_detail:
            sku_mrp= stock_obj.batch_detail.mrp
            sku_weight = stock_obj.batch_detail.weight
        #If MRP or Weight Doesn't Match with Current Stock, Suggesting MRP OFFER Zone
        if sku_mrp != batch_dict.get("mrp") or sku_weight != batch_dict.get("weight"):
            include_dict["zone__zone"] = "MRP OFFER"
            sku_update_dict['mrp'] = batch_dict['mrp']
    else:
        if sku_mrp != batch_dict.get("mrp"):
            include_dict["zone__zone"] = "MRP OFFER"
            sku_update_dict['mrp'] = batch_dict['mrp']

    if sku_update_dict:
        SKUMaster.objects.filter(id=sku_id).update(**sku_update_dict)

def update_staging_information(next_lane, stage_stock_id, grn_number, sku_id, qc_staging, seller_po_summary_id, next_location, batch_obj, staging_lanes_mapping):
    #Framing Dict to Keep Stock in Putaway Staging Lane
    staging_dict = {}
    if next_lane == 'pnd':
        if not stage_stock_id:
            stage_objs = StockDetail.objects.filter(receipt_type=qc_staging, receipt_number=seller_po_summary_id)
            if stage_objs.exists():
                stage_stock_id = stage_objs[0].id

        staging_dict = {
            "grn_number": grn_number,
            "sku_id": sku_id,
            "receipt_type": staging_lanes_mapping.get(next_lane, {}).get('name', ''),
            "location" : next_location if next_location else None,
            }

        if batch_obj:
            staging_dict['batch_detail_id'] = batch_obj.id

        if stage_stock_id:
            staging_dict['stage_stock_id'] = stage_stock_id
    return staging_dict

def create_po_location_for_suggested_locations(warehouse, location_wise_qty, auto_picklist, location_data, stock_detail_dict, staging_dict, staging_data):
    for location_id, loc_quantity in location_wise_qty.items():
        #for Auto Picklist Creating POLocation with Putaway Done Status
        if auto_picklist:
            location_data['status'] = 0
            location_data['quantity'] = 0
            stock_detail_dict['location_id'] = location_id
            stock_detail_dict['account_id'] = warehouse.userprofile.id

        #Updaing Quantity and Location
        location_data.update({
            'quantity' : loc_quantity,
            'original_quantity' : loc_quantity,
            'location_id': location_id,
            'account_id' : warehouse.userprofile.id,
            })
        po_loc = POLocation.objects.create(**location_data)
        if staging_dict:
            staging_dict.update({
                "po_loc_id": po_loc.id, "quantity":loc_quantity,
                "receipt_number": po_loc.id,
                "account_id" : warehouse.userprofile.id,
                })
            staging_data.append(staging_dict)

        if po_loc:
            batch_detail_id = po_loc.seller_po_summary.batch_detail_id if po_loc.seller_po_summary and po_loc.seller_po_summary.batch_detail_id else None
            #Add Account_id if needed(CHECK_LATER)
            if auto_picklist:
                create_auto_putaway_stock_detail(stock_detail_dict, batch_detail_id, loc_quantity, loc_quantity)

    return staging_data

def create_batch_detail_for_polocation(sku_master, batch_obj, batch_dict, warehouse):
    error_list = []
    if sku_master.batch_based and not batch_obj:
        batch_update = False
        batch_dict['sku_id'] = sku_master.id
        error_list, batch_obj = get_or_create_batch(
                warehouse, batch_dict, update=batch_update
                )
    return error_list, batch_obj

def frame_filters_based_on_carton_putaway_type(lpn_number, express_putaway_type, include_dict):
    if lpn_number and express_putaway_type == 'full_carton_putaway':
        include_dict['carton_capacity__gt'] = 0
        include_dict['zone__carton_managed'] = True
    else:
        include_dict['carton_capacity'] = 0
        include_dict['zone__carton_managed'] = False
    return include_dict

def save_suggested_po_location(put_zone, po_location_dict, batch_dict, warehouse, cartons_location_mapping_dict, sku_master,
                               extra_dat, misc_permission_dict={}, job_order=False, batosa=False):
    staging_data = []
    sku_id = po_location_dict.get("sku_id")
    sku_code = po_location_dict.get("sku_code")
    sku_uom = po_location_dict.get("sku_uom")
    po_id = po_location_dict.get("purchase_order_id",None)
    jo_id = po_location_dict.get("job_order_id",None)
    receipt_number = po_location_dict.get("receipt_number")
    received_quantity = po_location_dict.get("grn_quantity")
    batch_no = batch_dict.get("batch_no")
    seller_po_summary_id = po_location_dict.get("seller_summary_id", None)
    jo_grn_id = po_location_dict.get("jo_grn_id", None)
    carton = po_location_dict.get("carton", None)
    putaway_type = po_location_dict.get("putaway_type", '')
    sku_mrp = po_location_dict.get("sku_mrp",0)
    sku_buy_price = po_location_dict.get("sku_buy_price",0)
    grn_number = po_location_dict.get("grn_number")
    receipt_type = po_location_dict.get("receipt_type")
    supplier_id = po_location_dict.get("supplier_id")
    batch_obj = po_location_dict.get("batch_obj")
    company_id = po_location_dict.get('company_id')
    lpn_number = po_location_dict.get('lpn_number')

    next_lane = po_location_dict.get("next_lane")
    next_location = po_location_dict.get("next_location")
    stage_stock_id = po_location_dict.get("stage_stock_id")

    #fetching uom qty
    po_uom_qty = po_location_dict.get("uom_qty", 1)
    base_quantity = po_uom_qty * received_quantity

    express_putaway_type = misc_permission_dict.get('express_putaway')
    inb_packing = misc_permission_dict.get('inbound_packing', "")

    staging_lanes_mapping = INBOUND_STAGING_LANES_MAPPING
    qc_staging = staging_lanes_mapping.get('qc', {}).get('name', '')

    uom_decimals = get_uom_decimals(company_id, uom_codes = [sku_uom])
    uom_decimal_limit = uom_decimals.get(sku_uom)
    round_off = uom_decimal_limit or po_location_dict.get("decimal_limit")
    reference_number = po_location_dict.get('reference_number', '')
    
    stock_detail_dict = {}
    auto_picklist = False
    if putaway_type.lower() in ['dropship']:
        auto_picklist = True
        stock_detail_dict = {
            'receipt_number': receipt_number,
            'grn_number': grn_number,
            'receipt_date': datetime.datetime.now(),
            'supplier_id': supplier_id,
            'sku_id': sku_id,
            'status': 1,
            'receipt_type': receipt_type
        }

    locations = []
    include_dict = {}
    sugg_list =[{'zones': [], 'received_quantity': base_quantity}]
    
    #Framing Include Dict for Cartons
    # include_dict = frame_filters_based_on_carton_putaway_type(lpn_number, express_putaway_type, include_dict)

    #Fetching Zone Suggestions(BA to SA)
    if extra_dat.get("sku_category_list_pf") not in ["false", ""] or extra_dat.get("sku_category_list_ba") not in ["false", ""]:
        sugg_list = update_category_based_zone(include_dict, warehouse, sku_master, base_quantity, carton,
                                            extra_dat)
        if not sugg_list:
            sugg_list = [{'zones': [], 'received_quantity': base_quantity}]
    
    
    #Creating Batch Detail for PO Location
    error_list, batch_obj = create_batch_detail_for_polocation(sku_master, batch_obj, batch_dict, warehouse)
    if error_list:
        raise ValueError(error_list)

    #MRP-Weight Logic 
    if str(warehouse.userprofile.sap_code).lower()=="milkbasket":
        update_sku_for_mrp_weight_logic(
            warehouse, sku_id, sku_mrp, batch_dict, putaway_type,sku_buy_price,include_dict
        )

    exclude_dict = {
        'zone__segregation__in' : ['outbound_staging', 'sorting', 'inbound_staging']
        }
    if not put_zone:
        exclude_dict['zone__segregation__in'].append('non_sellable')

    #Fetching Zone-SkuCategory Mappings
    if sku_master.sku_category.lower():
        zones= list(ZoneMaster.objects.filter(
            user=warehouse.id, sku_category__name= sku_master.sku_category.lower()).values_list("zone", flat=True))
        if zones:
            include_dict["zone__zone__in"]= zones
    
    log.info(generate_log_message("PutawayFilters", sugg_list=sugg_list, include_dict=include_dict, exclude_dict=exclude_dict))
    for sugg_dict in sugg_list:
        if sugg_dict.get('zones') not in ['false','',[]]:
            include_dict['zone__zone__in'] = sugg_dict['zones']
        received_quantity = sugg_dict['received_quantity']

        #Fetching Eligible Locations
        extra_data = {
            'include_dict' : include_dict, 'exclude_dict' : exclude_dict,
            'put_zone' : put_zone, 'sugg_dict' : sugg_dict,
            'batch_no' : batch_no, 'lpn_number' : lpn_number,
            'cartons_location_mapping_dict' : cartons_location_mapping_dict,
            'putaway_type' : putaway_type, 'auto_picklist' : auto_picklist,
            'carton' : carton, 'sku_code' : sku_code, 'sku_id' : sku_id,
            "round_off" : round_off, **extra_dat
            }
        locations = get_eligible_locations(po_location_dict, extra_data, misc_permission_dict, warehouse)
        location_wise_qty, cartons_location_mapping_dict = \
            suggest_location_wise_quantities(locations, received_quantity, extra_data, misc_permission_dict, warehouse, sku_master)
        location_data = {
            'status' : 1, 
            "receipt_number":receipt_number,
            'json_data': po_location_dict.get("json_data", {}), 
            "account_id"  : warehouse.userprofile.id,
            "sku_id" : sku_id,
            "reference_number": reference_number
        }

        if sku_master.batch_based and batch_obj:
            location_data['batch_detail_id'] = batch_obj.id

        if job_order:
            location_data.update({
                'job_order_id': jo_id, 'seller_po_summary_id': jo_grn_id,
                "unit_price": sku_buy_price, 'putaway_type': 'job_order'
                })
        elif batosa:
            location_data.update({
                "unit_price": sku_buy_price, "putaway_type": putaway_type,
                'seller_po_summary_id':seller_po_summary_id,
                })
        else:
            location_data.update({
                'purchase_order_id': po_id, 'carton_id': carton,
                'seller_po_summary_id':seller_po_summary_id,
                })
        
            grn_putaway_type = po_location_dict.get("json_data", {}).get('grn_type')
            if grn_putaway_type:
                type_dict = {'PO':'po_grn', 'SR':'sales_return', 'ST': 'stock_transfer'}
                location_data.update({'putaway_type': type_dict.get(grn_putaway_type)})

        staging_dict = update_staging_information(
            next_lane, stage_stock_id, grn_number, sku_id, qc_staging,
            seller_po_summary_id, next_location, batch_obj, staging_lanes_mapping
        )

        log.info("Suggested locations %s GRN Number = %s"%(str(locations), str(receipt_number)))
        
        #Creaing POLocation Based on Configurations and Location Capacity & Available Stocks with the Eligible Locations 
        staging_data = create_po_location_for_suggested_locations(
            warehouse, location_wise_qty, auto_picklist, location_data,
            stock_detail_dict, staging_dict, staging_data
        )
    return True, staging_data

def fetch_grn_df(warehouse, grn_number, grn_ids):
    grn_values = ['id', 'sku_id', 'batch_detail_id', 'quantity', 'grn_type', 
                  'accepted_quantity', 'remarks', 'receipt_number', 'grn_number',
                  'purchase_order_id', 'account_id']
    grn_values_dict = {
        'sps_id': F('id'),
        'sku_code' : F('sku__sku_code'),
        'rejected_quantity' : F('damaged_quantity'),
        'sku_category' : F('sku__sku_category'),
        'lpns' : F('json_data__lpns'),
        'sku_put_zone_id' : F('sku__zone_id'),
        'sku_put_zone' : F('sku__zone__zone'),
        'restrict_one_location_to_one_sku' : F('sku__zone__restrict_one_location_to_one_sku'),
        'unique_batch' : F('sku__zone__unique_batch'),
        'batch_no' : F('batch_detail__batch_no'),
        'pcf': F('json_data__pcf'),
        'sku_landed_cost': F('json_data__sku_landed_cost'),
        'sku_length': F('sku__length'),
        'sku_breadth': F('sku__breadth'),
        'sku_height': F('sku__height'),
        'sku_weight': F('sku__weight'),
        'sku_class': F('sku__sku_class'),
    }
    grn_filters = {'sku__user': warehouse.id, 'grn_number': grn_number}
    grn_filters['id__in'] = grn_ids

    sps_df = pd.DataFrame(SellerPOSummary.objects.exclude(remarks = 'Shortage').filter(**grn_filters).values(*grn_values, **grn_values_dict))
    lpn_sps_df = pd.DataFrame(columns=grn_values + list(grn_values_dict.keys()) + ['lpn_number'])
    #New lpn_sps_df by normalising lpns in sps_df if lpns are present
    if not sps_df.empty:
        lpn_sps_df = sps_df.explode('lpns').reset_index(drop=True)
        lpn_sps_df['lpn_number'] = lpn_sps_df['lpns'].apply(lambda x: x['lpn_number'] if x else '')
        lpn_sps_df = lpn_sps_df[lpn_sps_df['lpn_number'].notna()]
        
    return sps_df, lpn_sps_df

def fetch_grn_df_from_qc(warehouse, qc_number):
    qc_values = ['approved_quantity', 'qc_number', 'id']
    qc_values_dict = {
        'grn_number': F('quality_control__reference_number'),
        'sps_id': F('quality_control__transaction_id'),
        'qc_rejected_quantity': F('rejected_quantity'),
        'lpn_number': F('json_data__lpn_number'),
        'to_location_id': F('json_data__to_location_id'),
        'qc_remarks': F('remarks'),
    }

    qc_filters = {'quality_control__warehouse_id': warehouse.id, 'qc_number': qc_number}
    qc_df = pd.DataFrame(QualityControlSummary.objects.filter(**qc_filters).values(*qc_values, **qc_values_dict))
    if qc_df.empty:
        cols = qc_values + list(qc_values_dict.keys())
        qc_df = pd.DataFrame(columns=cols)
        return qc_df, pd.DataFrame(columns=cols + ['lpn_number'])
    #fetching seller po summary data based on sps_id
    grn_number = qc_df['grn_number'].iloc[0]
    grn_ids = list(qc_df['sps_id'].unique())
    sps_df, _ = fetch_grn_df(warehouse, grn_number, grn_ids)

    #joining qc_df with sps_df on sps_id and replacing accepted_quantity with approved_quantity and rejected_quantity with rejected_quantity from qc_df
    qc_df = qc_df.merge(sps_df, left_on='sps_id', right_on='id', how='left', suffixes=('', '_sps'))
    qc_df['accepted_quantity'] = qc_df['approved_quantity']
    qc_df['rejected_quantity'] = qc_df['qc_rejected_quantity']
    qc_df['remarks'] = qc_df['qc_remarks']
    #delete lpns from qc_df as it is not needed
    qc_df.drop(columns=['lpns', 'qc_rejected_quantity', 'qc_remarks'], inplace=True, errors='ignore')

    return qc_df, qc_df

def fetch_stock_df(requirements):
    locations = requirements['locations']
    location_df = requirements['location_df']
    location_ids = list(location_df['id'].unique()) + list(locations)
    stock_values = ['id', 'sku_id', 'location_id', 'quantity', 'lpn_number', 'batch_detail_id']
    stock_values_dict = {
        'sku_code' : F('sku__sku_code'),
        'sku_category' : F('sku__sku_category'),
        'zone_id' : F('location__zone'),
        'zone' : F('location__zone__zone'),
        'batch_no' : F('batch_detail__batch_no'),
        'sku_class' : F('sku__sku_class'),
    }
    stock_filters = {'location_id__in': location_ids, 'quantity__gt': 0, 'status': 1}
    stock_df = pd.DataFrame(StockDetail.objects.filter(**stock_filters).values(*stock_values, **stock_values_dict))
    if stock_df.empty:
        cols = stock_values + list(stock_values_dict.keys())
        stock_df = pd.DataFrame(columns=cols)
        
    requirements['stock_df'] = stock_df
    suggested_df = pd.DataFrame(columns=['sku_id', 'location_id', 'quantity', 'batch_detail_id', 'lpn_number', 'zone_id'])
    requirements['suggested_df'] = suggested_df
    
    return requirements


def fetch_polocation_df(requirements):
    locations = requirements['locations']
    location_df = requirements['location_df']
    location_ids = list(location_df['id'].unique()) + list(locations)
    poloc_values = ['id', 'sku_id', 'location_id', 'quantity', 'batch_detail_id']
    poloc_values_dict = {
        'sku_code' : F('sku__sku_code'),
        'sku_category' : F('sku__sku_category'),
        'zone_id' : F('location__zone'),
        'zone' : F('location__zone__zone'),
        'batch_no' : F('batch_detail__batch_no'),
        'lpn_number' : F('json_data__lpn_number'),
        'sku_class' : F('sku__sku_class'),
    }
    poloc_filters = {'location_id__in': location_ids, 'status': 1}
    poloc_df = pd.DataFrame(POLocation.objects.filter(**poloc_filters).values(*poloc_values, **poloc_values_dict))
    if poloc_df.empty:
        cols = poloc_values + list(poloc_values_dict.keys())
        poloc_df = pd.DataFrame(columns=cols)

    requirements['poloc_df'] = poloc_df
    return requirements

def fetch_open_adjustment_df(requirements):
    sps_df = requirements['sps_df']
    open_adj_values = ['location_id']
    open_adj_filters = {'sku_id__in': sps_df['sku_id'], 'status': 2, 'quantity__gt': 0}
    open_adj_df = pd.DataFrame(CycleCount.objects.filter(**open_adj_filters).values(*open_adj_values))
    if open_adj_df.empty:
        open_adj_df = pd.DataFrame(columns=open_adj_values)
    requirements['open_adj_df'] = open_adj_df
    return requirements

def fetch_putaway_strategies(requirements):
    #if the putaway strategy is dynamic bin top up then we need to get the zone capacity and add it to the capacity and requirements
    warehouse, grn_type, po_type, sps_df = requirements['warehouse'], requirements['grn_type'], requirements['po_type'], requirements['sps_df']
    filters = {'warehouse': warehouse, 'status': 1, 'transaction_type': grn_type, 'process_type': po_type}
    loc_capacity_calc_options = requirements.get('loc_capacity_calc_options', [])
    need_dimensions = ('volume_utilization' in loc_capacity_calc_options or 
                      'weight_utilization' in loc_capacity_calc_options)
    values_list = ['quantity_type', 'entity_name', 'strategy', 'location_id', 'zone_id', 'capacity', 'location__location_type__lpn_capacity',
                   'level', 'zone__restrict_one_location_to_one_sku', 'zone__unique_batch', 'zone__carton_managed', 'reason']
    if requirements['putaway_class_category'] == 'true':
        values_list.append('sku_class')
    if need_dimensions:
        additional_fields = [
            'location__location_type__length',
            'location__location_type__breadth',
            'location__location_type__height',
            'location__location_type__weight',
            'location__location_type__volume_utilization',
            'location__location_type__weight_utilization'
        ]
        values_list.extend(additional_fields)

    sku_and_categories = list(sps_df['sku_code']) + list(sps_df['sku_category']) + ['']
    sku_codes = list(sps_df['sku_code']) + ['']
    sku_mapping = list(PutawayMapping.objects.filter(entity_name__in=sku_codes, strategy='fixed_bin_mapping', **filters).order_by('priority').values(*values_list))
    category_mapping = list(PutawayMapping.objects.filter(entity_name__in=sku_and_categories, **filters).exclude(strategy='fixed_bin_mapping').order_by('priority').values(*values_list))
    sku_mapping_dict, category_mapping_dict, empty_location_category_mapping_dict, zone_capacity_dict, location_type_dict = {}, {}, {}, {}, {}
    unique_loc_sku_zones, unique_batch_zones, zones, locations = set(), set(), set(), set()
    for mapping in sku_mapping:
        locations.add(mapping['location_id'])
        key = (mapping['quantity_type'], mapping['entity_name'], mapping['reason'])
        sku_mapping_dict.setdefault(key, [])
        mapping_data = {
            'location_id': mapping['location_id'], 
            'zone_id': mapping['zone_id'], 
            'capacity': mapping['capacity'],
            'carton_managed': mapping['zone__carton_managed'],
            'lpn_capacity': mapping['location__location_type__lpn_capacity']
        }
        
        if need_dimensions:
            location_type_data = {
                'location_length': mapping.get('location__location_type__length'),
                'location_breadth': mapping.get('location__location_type__breadth'),
                'location_height': mapping.get('location__location_type__height'),
                'location_weight': mapping.get('location__location_type__weight'),
                'volume_utilization': mapping.get('location__location_type__volume_utilization'),
                'weight_utilization': mapping.get('location__location_type__weight_utilization')
            }

            if all(location_type_data.get(dim) for dim in ['location_length', 'location_breadth', 'location_height']):
                location_type_dict[mapping['location_id']] = location_type_data

            mapping_data.update(location_type_data)
        
        sku_mapping_dict[key].append(mapping_data)
        
    for mapping in category_mapping:
        key = (mapping['quantity_type'], mapping['entity_name'], mapping['reason'])
        if requirements['putaway_class_category'] == 'true':
            class_key = key + (mapping['sku_class'],)
        if mapping['strategy'] == 'dynamic_bin_empty_location':
            if requirements['putaway_class_category'] == 'true' and mapping['sku_class']:
                empty_location_category_mapping_dict.setdefault(class_key, []).append(mapping['zone_id'])
            else:
                empty_location_category_mapping_dict.setdefault(key, []).append(mapping['zone_id'])
        else:
            if requirements['putaway_class_category'] == 'true' and mapping['sku_class']:
                category_mapping_dict.setdefault(class_key, []).append(mapping['zone_id'])
            else:
                category_mapping_dict.setdefault(key, []).append(mapping['zone_id'])
        if mapping['capacity']:
            zone_capacity_dict[mapping['zone_id']] = mapping['capacity']
        zones.add(mapping['zone_id'])
        if mapping['zone__restrict_one_location_to_one_sku']:
            unique_loc_sku_zones.add(mapping['zone_id'])
        if mapping['zone__unique_batch']:
            unique_batch_zones.add(mapping['zone_id'])
    unique_loc_sku_zones_in_sps = sps_df[sps_df['restrict_one_location_to_one_sku']==True]['sku_put_zone_id'].unique()
    unique_loc_sku_zones = unique_loc_sku_zones.union(set(unique_loc_sku_zones_in_sps))
    unique_batch_zones_in_sps = sps_df[sps_df['unique_batch']==True]['sku_put_zone_id'].unique()
    unique_batch_zones = unique_batch_zones.union(set(unique_batch_zones_in_sps))
    zones_in_sps = sps_df['sku_put_zone_id'].unique()
    zones = zones.union(set(zones_in_sps))
    requirements['sku_mapping'] = sku_mapping_dict
    requirements['category_mapping'] = category_mapping_dict
    requirements['empty_location_category_mapping'] = empty_location_category_mapping_dict
    requirements['zone_capacity_dict'] = zone_capacity_dict
    requirements['unique_loc_sku_zones'] = unique_loc_sku_zones
    requirements['unique_batch_zones'] = unique_batch_zones
    requirements['zones'] = zones
    requirements['locations'] = locations
    requirements['location_type_dict'] = location_type_dict
    return requirements

def fetch_locations_for_zones(requirements):
    warehouse = requirements['warehouse']
    zones = requirements['zones']
    zones = [zone for zone in zones if pd.notna(zone)]
    values_list = ['id', 'zone_id', 'location', 'max_capacity', 'uom', 'fill_sequence', 'location_type__lpn_capacity']
    values_dict = {'carton_managed': F('zone__carton_managed')}
    location_df = pd.DataFrame(LocationMaster.objects.filter(zone__in=zones, status=1, zone__user=warehouse.id).values(*values_list, **values_dict))
    if location_df.empty:
        location_df = pd.DataFrame(columns=values_list)
    location_df = location_df.sort_values('fill_sequence')
    requirements['location_df'] = location_df
    return requirements

def calculate_volume_weight_capacity(location_data, sku_data, loc_capacity_calc_options):
    """
    Calculate the maximum capacity based on volume and weight utilization.
    
    Args:
        location_data: DataFrame row or dict containing location dimensions and utilization percentages
        sku_data: Dict containing SKU dimensions and weight
        loc_capacity_calc_options: List of enabled capacity calculation options
        
    Returns:
        tuple: (continue_flag, max_capacity) 
               continue_flag: True if location should be skipped
               max_capacity: Calculated maximum capacity based on dimensions
    """
    continue_flag = False
    max_capacity = 0
    
    # Check if SKU dimensions fit within location dimensions
    try:
        sku_max_size = max(float(sku_data.get('sku_length') or 0),
                          float(sku_data.get('sku_breadth') or 0), 
                          float(sku_data.get('sku_height') or 0))
        loc_max_size = max(float(location_data.get('location_height') or 0), 
                          float(location_data.get('location_length') or 0), 
                          float(location_data.get('location_breadth') or 0))
        
        if sku_max_size > loc_max_size:
            return True, 0  # SKU doesn't fit in location
    except (ValueError, TypeError):
        # If dimensions are missing or invalid, continue with normal capacity
        return False, 0
    
    # Calculate volume-based capacity
    volume_capacity = 0
    if 'volume_utilization' in loc_capacity_calc_options:
        try:
            location_values = sorted([
                float(location_data.get('location_length') or 0), 
                float(location_data.get('location_breadth') or 0),
                float(location_data.get('location_height') or 0)
            ])
            sku_values = sorted([
                float(sku_data.get('sku_length') or 0), 
                float(sku_data.get('sku_breadth') or 0),
                float(sku_data.get('sku_height') or 0)
            ])
            
            if all(sku_values) and all(location_values):  # Check all dimensions are present
                max_val = math.floor(location_values[2] / float(sku_values[2]))
                min_val = math.floor(location_values[0] / float(sku_values[0]))
                mid_val = math.floor(location_values[1] / float(sku_values[1]))
                
                volume_utilization = float(location_data.get('volume_utilization') or 0)
                volume_capacity = math.floor(max_val * min_val * mid_val * (volume_utilization / 100))
        except (ValueError, TypeError, ZeroDivisionError):
            volume_capacity = 0
    
    # Calculate weight-based capacity
    weight_capacity = 0
    if 'weight_utilization' in loc_capacity_calc_options:
        try:
            # Extract numerical value from weight string if needed
            sku_weight_str = str(sku_data.get('sku_weight') or 0)
            sku_weight_match = re.search(r'[\d.]+', sku_weight_str)
            sku_weight = float(sku_weight_match.group()) if sku_weight_match else 0
            
            location_weight = float(location_data.get('location_weight') or 0)
            weight_utilization = float(location_data.get('weight_utilization') or 0)
            
            if sku_weight and location_weight:
                weight_ratio = location_weight / sku_weight
                weight_capacity = math.floor(weight_ratio * (weight_utilization / 100))
        except (ValueError, TypeError, ZeroDivisionError):
            weight_capacity = 0
    
    # Use the smaller of volume and weight capacity if both are calculated
    if volume_capacity > 0 and weight_capacity > 0:
        max_capacity = min(volume_capacity, weight_capacity)
    elif volume_capacity > 0:
        max_capacity = volume_capacity
    elif weight_capacity > 0:
        max_capacity = weight_capacity
    
    # If we attempted calculations but got zero capacity, flag to skip this location
    if max_capacity == 0 and (volume_capacity != 0 or weight_capacity != 0):
        continue_flag = True
    
    return continue_flag, max_capacity

def add_locations(zone_ids, location_df, locations, lpn_putaway, check_empty=False):
    for zone_id in zone_ids:
        filtered_locations = location_df[location_df["zone_id"] == zone_id]
        for loc in filtered_locations.itertuples(index=False):
            loc_data = {
                'location_id': loc.id,
                'capacity': loc.max_capacity,
                'lpn_capacity': loc.location_type__lpn_capacity,
                'carton_managed': loc.carton_managed,
                'zone_id': zone_id
            }
            if check_empty:
                loc_data['empty_location'] = True
            locations.append(loc_data)

def validate_empty_putaway_locations(location_id, stock_df, poloc_df, open_adj_df, suggested_df):
    is_valid = False
    locations_with_stock = set(stock_df["location_id"] if stock_df is not None else []) | \
                           set(poloc_df["location_id"] if poloc_df is not None else []) | \
                           set(open_adj_df["location_id"] if open_adj_df is not None else []) | \
                           set(suggested_df["location_id"] if suggested_df is not None else [])
    if location_id not in locations_with_stock:
        is_valid = True
    return is_valid

def get_locations_available_for_sku(requirements, row, lpn):
    locations = []
    sku_mapping, category_mapping, lpn_location_mapping, empty_location_category_mapping= (
        requirements['sku_mapping'], requirements['category_mapping'],
        requirements['lpn_location_mapping'], requirements['empty_location_category_mapping']
    )
    warehouse, location_df = requirements['warehouse'], requirements['location_df']
    lpn_putaway = requirements['lpn_putaway']
    sku_code, sku_category, reason, sku_class = row['sku_code'], row['sku_category'], row['remarks'], row['sku_class'] or ''
    quantity_type, lpn_number = lpn['quantity_type'], lpn['lpn_number']
    quantity_type = 'accepted' if quantity_type == 'accepted_quantity' else 'rejected'
    reason = '' if quantity_type == 'accepted' else reason
    sku_key = (quantity_type, sku_code, reason)
    location_mapping = sku_mapping.get(sku_key, {})
    #One LPN to One Location Mapping
    if lpn_number:
        location = lpn_location_mapping.get(lpn_number)
        if location:
            return [location]

    if location_mapping:
        locations.extend(location_mapping)
    key = (quantity_type, sku_category, reason)
    if requirements['putaway_class_category'] == 'true':
        if sku_class:
            sku_class_key = key+(sku_class,)
            if sku_class_key in category_mapping:
                add_locations(category_mapping[sku_class_key], location_df, locations, lpn_putaway)

    if sku_key in category_mapping:
        add_locations(category_mapping[sku_key], location_df, locations, lpn_putaway)

    if key in category_mapping:
        add_locations(category_mapping[key], location_df, locations, lpn_putaway)

    if quantity_type == 'rejected':
        rej_key = (quantity_type, '', reason)
        if requirements['putaway_class_category'] == 'true':
            if sku_class:
                rej_key+=(sku_class,)
                if rej_key in category_mapping:
                    add_locations(category_mapping[rej_key], location_df, locations, lpn_putaway)
        if rej_key in category_mapping:
            add_locations(category_mapping[rej_key], location_df, locations, lpn_putaway)
        if rej_key in sku_mapping:
            add_locations(sku_mapping[rej_key], location_df, locations, lpn_putaway)

    if sku_key in empty_location_category_mapping:
        add_locations(empty_location_category_mapping[sku_key], location_df, locations, lpn_putaway, check_empty=True)

    if key in empty_location_category_mapping:
        add_locations(empty_location_category_mapping[key], location_df, locations, lpn_putaway, check_empty=True)

    if pd.notna(row['sku_put_zone_id']) and quantity_type=='accepted':
        zone_id = row['sku_put_zone_id']
        if zone_id in location_df['zone_id'].values:
            for _, loc in location_df[location_df['zone_id']==zone_id].iterrows():
                location_id = loc['id']
                locations.append({'location_id': location_id, 'capacity': loc['max_capacity'], 'zone_id': zone_id, 'lpn_capacity': loc['location_type__lpn_capacity'], 'carton_managed': loc['carton_managed']})

    if reason == 'wrong_sku' and quantity_type=='rejected':
        if requirements.get('wrong_sku_location'):
            location = requirements['wrong_sku_location']
        else:
            location = create_default_zones(warehouse, 'WRONG_SKU', 'WRONG', 99999, segregation='non_sellable')
            requirements['wrong_sku_location'] = location
        locations.extend([{'location_id': loc.id, 'capacity': 'default', 'zone_id': loc.zone.id} for loc in location])
    elif quantity_type=='rejected':
        if requirements.get('damaged_location'):
            location = requirements['damaged_location']
        else:
            location = create_default_zones(warehouse, 'DAMAGED_ZONE', 'D1', 99999, segregation='non_sellable')
            requirements['damaged_location'] = location
        locations.extend([{'location_id': loc.id, 'capacity': 'default', 'zone_id': loc.zone.id} for loc in location])

    if requirements.get('default_location'):
        location = requirements['default_location']
    else:
        location = create_default_zones(warehouse, 'DEFAULT', 'DFLT1', 99999)
        requirements['default_location'] = location
    locations.extend([{'location_id': loc.id, 'capacity': 'default', 'zone_id': loc.zone.id} for loc in location])
    return locations

def validate_lpn_capacity(lpn_capacity, stock_df, poloc_df, suggested_df, location_id):
    is_valid = True
    #Count of distinct lpn numbers in location
    lpn_count = stock_df[stock_df['location_id']==location_id]['lpn_number'].nunique() + \
                poloc_df[poloc_df['location_id']==location_id]['lpn_number'].nunique() + \
                suggested_df[suggested_df['location_id']==location_id]['lpn_number'].nunique()
    if lpn_count >= lpn_capacity:
        is_valid = False
    return is_valid
        

def get_available_locations_for_putaway(locations, requirements, row, lpn):
    stock_df, poloc_df, open_adj_df, suggested_df, lpn_sps_df = (
        requirements['stock_df'], requirements['poloc_df'], requirements['open_adj_df'], requirements['suggested_df'], requirements['lpn_sps_df']
    )
    unique_loc_sku_zones, unique_batch_zones = requirements['unique_loc_sku_zones'], requirements['unique_batch_zones']
    lpn_location_mapping, zone_capacity_dict, lpn_putaway = requirements['lpn_location_mapping'], requirements['zone_capacity_dict'], requirements['lpn_putaway']
    lpn_number = lpn['lpn_number']
    sku_id = row['sku_id']
    loc_capacity_calc_options = requirements.get('loc_capacity_calc_options', [])
    need_dimensions = ('volume_utilization' in loc_capacity_calc_options or 
                      'weight_utilization' in loc_capacity_calc_options)
    location_type_dict = requirements.get('location_type_dict', {})

    final_locations = []
    for location in locations:
        capacity = location['capacity']
        zone_capacity = zone_capacity_dict.get(location['zone_id'], 0)
        location_id = location['location_id']
        lpn_capacity = location.get('lpn_capacity', 0)
        carton_managed = location.get('carton_managed', False)
        zone_id = location['zone_id']
        is_empty_location = True if location.get('empty_location') else False
        if capacity == 'default' or lpn_number in lpn_location_mapping:
            final_locations.append(location)
            continue
        # Volume/weight based capacity check
        if need_dimensions:
            loc_data = location.copy()
            has_dimensions = None
            
            if location_id in location_type_dict:
                loc_data.update(location_type_dict[location_id])
                has_dimensions = True
            # Calculate capacity based on volume/weight if dimensions are available
            if has_dimensions:
                continue_flag, max_capacity = calculate_volume_weight_capacity(
                    loc_data, row, loc_capacity_calc_options
                )
                if continue_flag:
                    continue
                
                if max_capacity > 0:
                    capacity = max_capacity

        if lpn_capacity and carton_managed and lpn_number:
            if not validate_lpn_capacity(lpn_capacity, stock_df, poloc_df, suggested_df, location_id):
                continue
        if zone_id in unique_loc_sku_zones:
            #Checking if location is having any other sku in stock_df, poloc_df, suggested_df and skipping the location
            if not stock_df[(stock_df['location_id']==location_id) & (stock_df['sku_id']!=sku_id)].empty:
                continue
            if not poloc_df[(poloc_df['location_id']==location_id) & (poloc_df['sku_id']!=sku_id)].empty:
                continue
            if not suggested_df[(suggested_df['location_id']==location_id) & (suggested_df['sku_id']!=sku_id)].empty:
                continue
            #Checking if more than one SKU present in LPN and skipping the location
            if lpn_number and not lpn_sps_df[(lpn_sps_df['lpn_number']==lpn_number) & (lpn_sps_df['sku_id']!=sku_id)].empty:
                continue
        if zone_id in unique_batch_zones:
            #Checking if location is having any other batch in stock_df, poloc_df, suggested_df and skipping the location
            if not stock_df[(stock_df['location_id']==location_id) & (stock_df['sku_id']==sku_id) & (stock_df['batch_detail_id']!=row['batch_detail_id'])].empty:
                continue
            if not poloc_df[(poloc_df['location_id']==location_id) & (poloc_df['sku_id']==sku_id) & (poloc_df['batch_detail_id']!=row['batch_detail_id'])].empty:
                continue
            if not suggested_df[(suggested_df['location_id']==location_id) & (suggested_df['sku_id']==sku_id) & (suggested_df['batch_detail_id']!=row['batch_detail_id'])].empty:
                continue
            #Checking if more than one Batch present in LPN and skipping the location
            if lpn_number and not lpn_sps_df[(lpn_sps_df['lpn_number']==lpn_number) & (lpn_sps_df['sku_id']==sku_id) & (lpn_sps_df['batch_detail_id']!=row['batch_detail_id'])].empty:
                continue

        zone_available_capacity = capacity
        if zone_capacity:
            #SKU capacity by zone. Get the capacity of that sku of the zone if exceeds the capacity for the sku for that zone then continue that zone.
            zone_available_capacity = validate_sku_capacity_by_zone(sku_id, zone_id, stock_df, poloc_df, suggested_df, zone_capacity)
        #Empty Location Putaway
        if is_empty_location:
            is_location_valid = validate_empty_putaway_locations(location_id, stock_df, poloc_df, open_adj_df, suggested_df)
            if not is_location_valid: continue

        stock_quantity = stock_df[(stock_df['location_id']==location_id)]['quantity'].sum()
        poloc_quantity = poloc_df[(poloc_df['location_id']==location_id)]['quantity'].sum()
        suggested_quantity = suggested_df[(suggested_df['location_id']==location_id)]['quantity'].sum()
        total_quantity = stock_quantity + poloc_quantity + suggested_quantity
        
        available_quantity = capacity - float(total_quantity)
        available_quantity = min(available_quantity, zone_available_capacity)
        if available_quantity > 0:
            if (lpn_number and lpn_putaway) and available_quantity < lpn['packed_quantity']:
                continue
            location['available_capacity'] = available_quantity
            final_locations.append(location) 
    return final_locations

def validate_sku_capacity_by_zone(sku_id,zone_id, stock_df, poloc_df, suggested_df, zone_capacity):
    # Calculate total quantity of the SKU in the zone
    total_quantity = get_available_quantity_for_sku_in_zone(sku_id, zone_id, stock_df, poloc_df, suggested_df)
    # Check if total quantity exceeds zone_capacity 
    
    return max(zone_capacity - float(total_quantity), 0)

def get_available_quantity_for_sku_in_zone(sku_id, zone_id, stock_df, poloc_df, suggested_df):
    stock_quantity = stock_df[(stock_df['zone_id']==zone_id) & (stock_df['sku_id']==sku_id)]['quantity'].sum()
    poloc_quantity = poloc_df[(poloc_df['zone_id']==zone_id) & (poloc_df['sku_id']==sku_id)]['quantity'].sum()
    suggested_quantity = suggested_df[(suggested_df['zone_id']==zone_id) & (suggested_df['sku_id']==sku_id)]['quantity'].sum()
    total_quantity = stock_quantity + poloc_quantity + suggested_quantity
    return total_quantity
    

def get_po_location_objects(requirements, final_locations, row, lpn, request_user):
    #Creating POLocation Objects for Bulk Creation
    lpn_location_mapping = requirements['lpn_location_mapping']
    lpn_putaway = requirements['lpn_putaway']
    type_dict = {'PO':'po_grn', 'SR':'sales_return', 'ST': 'stock_transfer'}
    putaway_type = type_dict.get(row['grn_type'])
    lpn_number, quantity_type = lpn['lpn_number'], lpn['quantity_type']
    quantity_type = 'accepted' if quantity_type == 'accepted_quantity' else 'rejected'
    json_data = {'lpn_number': lpn_number, 'pcf': row['pcf'], 'sku_landed_cost': row['sku_landed_cost'], 'request_user': request_user, 'quantity_type': quantity_type}
    if row.get('to_location_id'):
        json_data['to_location_id'] = row['to_location_id']
    po_location_objects = []
    pending_quantity = lpn['packed_quantity'] * (row.get('pcf', 1) or 1)
    for location in final_locations:
        location_id = location['location_id']
        zone_id = location.get('zone_id')
        purchase_order_id = None if pd.isna(row['purchase_order_id']) else row['purchase_order_id']
        batch_detail_id = None if pd.isna(row['batch_detail_id']) else row['batch_detail_id']
        available_capacity = location.get('available_capacity', 0)
        capacity = location['capacity']
        if capacity == 'default' or (lpn_number and lpn_putaway):
            quantity = pending_quantity
        else:
            quantity = min(pending_quantity, available_capacity)
        location_data = {
            'purchase_order_id': purchase_order_id,
            'seller_po_summary_id': row['sps_id'],
            'sku_id': row['sku_id'],
            'batch_detail_id': batch_detail_id,
            'location_id': location_id,
            'quantity': quantity,
            'original_quantity': quantity,
            'status': 1,
            'receipt_number': row['receipt_number'],
            'putaway_type': putaway_type,
            'json_data': json_data,
            'reference_number': row['grn_number'],
            'account_id': row['account_id']
        }
        po_location_objects.append(POLocation(**location_data))
        location_data['lpn_number'] = lpn_number
        location_data['zone_id'] = zone_id
        requirements['suggested_df'] = requirements['suggested_df']._append(location_data, ignore_index=True)
        pending_quantity -= quantity
        if lpn_number and lpn_putaway:
            lpn_location_mapping[lpn_number] = location
        if pending_quantity == 0:
            break
    return po_location_objects

def get_putaway_suggestions(requirements):
    suggestions = []
    sps_df, request_user = requirements['sps_df'], requirements['request_user']
    requirements['lpn_location_mapping'] = {}

    for _, row in sps_df.iterrows():
        accepted_quantity, rejected_quantity, = row['accepted_quantity'], row['rejected_quantity']
        if row.get('lpns', ''):
            lpns = row['lpns']
        else:
            lpns = []
            lpns.append({'lpn_number': row.get('lpn_number', ''), 'quantity_type': 'accepted_quantity', 'packed_quantity': accepted_quantity}) if accepted_quantity else None
            lpns.append({'lpn_number': row.get('lpn_number', ''), 'quantity_type': 'rejected_quantity', 'packed_quantity': rejected_quantity}) if rejected_quantity else None
        for lpn in lpns:
            locations = get_locations_available_for_sku(requirements, row, lpn)
            final_locations = get_available_locations_for_putaway(locations, requirements, row, lpn)
            po_location_objects = get_po_location_objects(requirements, final_locations, row, lpn, request_user)
            suggestions.extend(po_location_objects)
    return suggestions

def create_po_location_objects(suggestions):
    from inbound.views.putaway.confirmation import create_putaway_task_for_multiple
    #Bulk Creation of POLocation Objects
    pol_objs = POLocation.objects.bulk_create_with_rounding(suggestions, batch_size=500)
    create_putaway_task_for_multiple(pol_objs)
    po_loc_ids = [pol.id for pol in pol_objs]
    return po_loc_ids

def fetch_configurations(requirements):
    misc_types = ['express_putaway', 'location_capacity_calculation', 'putaway_class_category']
    misc_dict = get_multiple_misc_values(misc_types, requirements['warehouse'].id)
    lpn_putaway = True if misc_dict.get('express_putaway') == 'full_carton_putaway' else False
    requirements['lpn_putaway'] = lpn_putaway
    loc_capacity_calc_options = misc_dict.get('location_capacity_calculation', '').split(',')
    requirements['loc_capacity_calc_options'] = loc_capacity_calc_options
    putaway_class_category = misc_dict.get('putaway_class_category', '')
    requirements['putaway_class_category'] = putaway_class_category
    return requirements

def putaway_suggestions_for_grn(warehouse, user, grn_type, po_type, grn_number, grn_ids=None, suggested_zone='', suggested_location='', extra_params=None, qc_number=''):
    from inbound.views.grn.create_grn import create_grn_lpn_transactions
    is_qc = True if qc_number else False
    grn_ids = grn_ids or []
    po_loc_ids = []
    if qc_number:
        sps_df, lpn_sps_df = fetch_grn_df_from_qc(warehouse, qc_number)
    else:
        sps_df, lpn_sps_df = fetch_grn_df(warehouse, grn_number, grn_ids)
    if sps_df.empty:
        create_grn_lpn_transactions(user.id, warehouse.id, grn_number=grn_number, pol_ids=po_loc_ids, extra_params=extra_params, grn_ids=grn_ids, is_qc=is_qc)
        return

    if not (suggested_location or suggested_zone):
        requirements = {
                        'sps_df': sps_df, 'warehouse': warehouse, 'po_type': po_type, 
                        'grn_type': grn_type, 'request_user': user.username, 
                        'lpn_sps_df': lpn_sps_df
                        }
        requirements = fetch_configurations(requirements)
        requirements = fetch_putaway_strategies(requirements)
        requirements = fetch_open_adjustment_df(requirements)
        requirements = fetch_locations_for_zones(requirements)
        requirements = fetch_stock_df(requirements)
        requirements = fetch_polocation_df(requirements)
        suggestions = get_putaway_suggestions(requirements)
        po_loc_ids = create_po_location_objects(suggestions)
        create_grn_lpn_transactions(user.id, warehouse.id, grn_number=grn_number, pol_ids=po_loc_ids, extra_params=extra_params, grn_ids=grn_ids, is_qc=is_qc)

def caclculate_sku_location_capacity(loc_capacity_calc_options, sku_data, location_data):
    '''Calculate the capacity of a SKU-Location pair based on their sizes and volumes.'''
    loc_volume_capacity, loc_wight_capacity = 0, 0
    volume_utilization = location_data.get('volume_utilization', 0)
    weight_utilization = location_data.get('weight_utilization', 0)

    if 'volume_utilization' in loc_capacity_calc_options:
        # Sort location and SKU dimensions for capacity calculation
        location_values = sorted([
            location_data['location_length'], location_data['location_breadth'],
            location_data['location_height']
        ])
        sku_values = sorted(
            [sku_data['sku_length'] or 0, sku_data['sku_breadth'] or 0,
            sku_data['sku_height'] or 0
        ])
        try:
            # Calculate maximum, minimum, and middle ratios for capacity calculation
            max_val = math.floor(location_values[2] / float(sku_values[2]))
            min_val = math.floor(location_values[0] / float(sku_values[0]))
            mid_val = math.floor(location_values[1] / float(sku_values[1]))
            # Calculate total capacity considering volume utilization percentage
            loc_volume_capacity = math.floor(max_val * min_val * mid_val * (volume_utilization / 100))
        except Exception:
            loc_volume_capacity = 0

    if 'weight_utilization' in loc_capacity_calc_options:
        try:
            sku_weight = re.search(r'[\d.]+', sku_data.get('sku_weight', 0))
            sku_weight = float(sku_weight.group())
        except Exception:
            sku_weight = 0
        if sku_weight:
            weight_ratio = location_data.get('location_weight', 0) / sku_weight
            loc_wight_capacity = math.floor(weight_ratio * (weight_utilization / 100))

    return loc_volume_capacity, loc_wight_capacity

def get_remaining_sku_location_capacity(loc_capacity_calc_options, location_data, location_sku_data):
    '''' Calculate the remaining capacity percentage of
         a location based on SKU-Location dimentions.'''
    total_volume_occupied, total_weight_occupied = 0, 0
    for sku_data in location_sku_data:
        # Calculate the capacity for the SKU at the location
        volume_capacity, weight_capacity = caclculate_sku_location_capacity(loc_capacity_calc_options, sku_data, location_data)
        # Calculate the percentage of occupied capacity by the SKU
        if volume_capacity:
            total_volume_occupied += (sku_data['quantity'] / volume_capacity)
        if weight_capacity:
            total_weight_occupied += (sku_data['quantity'] / weight_capacity)
    # Calculate the remaining capacity percentage
    rem_volume_percent = 1 - total_volume_occupied
    rem_weight_percent = 1 - total_weight_occupied

    return rem_volume_percent, rem_weight_percent

def get_weight_and_volume_based_max_capacity(warehouse, putaway_config, loc, location_type_dict, sku_data, loc_capacity_calc_options):
    """Calculate Maximum capcity based on Weight and Volume utilization of a location"""
    continue_check = False
    max_capacity = 0
    if putaway_config and loc.location in location_type_dict:
        loc_data = location_type_dict[loc.location]
        try:
            sku_max_size = max(float(sku_data['sku_height']), float(sku_data['sku_length']), float(sku_data['sku_breadth']))
        except Exception:
            sku_max_size = 0
        loc_max_size = max(loc_data['location_height'], loc_data['location_length'], loc_data['location_breadth'])
        if sku_max_size > loc_max_size:
            continue_check = True

        #Fetch Location SKU wise Pending Putaway quantity
        sku_wise_pending_qty = POLocation.objects.filter(location_id=loc.id, status=1, location__zone__user=warehouse.id)\
                                .values(sku_code=F('sku__sku_code'), sku_length=F('sku__length'), sku_weight=F('sku__weight'),
                                    sku_height=F('sku__height'), sku_breadth=F('sku__breadth')).annotate(quantity=Sum('quantity'))
        #Fetch Location SKU wise Stock Quantity
        sku_wise_stock_qty = StockDetail.objects.filter(location_id=loc.id, quantity__gt=0, sku__user=warehouse.id)\
                                .values(sku_code=F('sku__sku_code'), sku_length=F('sku__length'), sku_weight=F('sku__weight'),
                                    sku_height=F('sku__height'), sku_breadth=F('sku__breadth')).annotate(quantity=Sum('quantity'))

        #Total Putaway Quantity
        sku_wise_qty_data = list(chain(sku_wise_pending_qty, sku_wise_stock_qty))

        volume_capacity, weight_capacity = caclculate_sku_location_capacity(loc_capacity_calc_options, sku_data, loc_data)
        if weight_capacity > 0:
            max_capacity = min(volume_capacity, weight_capacity)
        else:
            max_capacity = volume_capacity
        if sku_wise_qty_data:
            #Calculate remaing capacity when stock exists in loccation
            rem_volume_percent, rem_weight_percent = get_remaining_sku_location_capacity(loc_capacity_calc_options, loc_data, sku_wise_qty_data)
            max_volume_capacity = math.ceil(rem_volume_percent * volume_capacity)
            max_weight_capacity = math.ceil(rem_weight_percent * weight_capacity)
            if max_weight_capacity != 0:
                max_capacity = min(max_weight_capacity, max_volume_capacity)
            else:
                max_capacity = max_volume_capacity
        if max_capacity <= 0:
            continue_check = True

    return continue_check, max_capacity
