#package imports
import datetime
import secrets
import os
import base64
import io
import pytz
from django.conf import settings
from dateutil import parser

import pandas as pd
from json import loads
from collections import defaultdict


#django imports
from django.db import transaction
from django.db.models import F, Q
from django.http import JsonResponse
from django.template import loader
from django.shortcuts import render
from django.core.files.uploadedfile import InMemoryUploadedFile

#Model Imports
from wms_base.models import User, UserProfile
from inbound.models import ReturnToVendor, SellerPOSummary, PurchaseOrder
from inventory.models import (
    LocationMaster,
    StockDetail
)
from inventory.views.serial_numbers.serial_number import SerialNumberMixin
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
#core operations imports
from core.models import TempJson, MasterEmailMapping, InvoiceForms, UOMMaster,MasterDocs
from outbound.models import IRN
from outbound.views.picklist.picklist_generation import PicklistMixin

#Method Imports
from core_operations.views.common.main import (
    WMSListView, get_warehouse, create_default_zones,
    get_multiple_misc_values,get_warehouses_list, get_user_time_zone,
    get_decimal_value, truncate_float, get_local_date_known_timezone, 
    number_in_words, convert_qr_code_image, get_rendered_invoice_data,
    xcode, get_parent_company, get_auth_signature, scroll_data,
    get_misc_value,get_user_attributes
    )
from core_operations.views.common.user_attributes import validate_attributes
from core_operations.views.integration.integration import webhook_integration_3p

from inbound.views.common.mail_pdf import write_and_mail_pdf
from inbound.views.common.constants import DT_DATE_FORMAT, INVALID_PAYLOAD_CONST, SLASH_DATE_FORMAT
from inbound.views.grn.common import  save_grn_extra_fields
from inbound.views.purchase_order.common import update_po_header


from .common import (
    prepare_rtv_json_data, rtv_3p_integration, update_stock_detail,
    get_sku_codes_from_items,get_rtv_extra_attributes, get_existing_serials, fetch_serial_numbers_for_rtv_id
)
from .save_rtv import save_update_rtv
from inbound.views.common.constants import DEFAULT_DATETIME_FORMAT

from wms_base.wms_utils import init_logger, folder_check
from outbound.views.shipment.shipment_invoice import insert_shipment_invoice_data_fun

today = datetime.datetime.now().strftime("%Y%m%d")
log_mail_info = init_logger('logs/inbound_mail_info.log')
log = init_logger('logs/create_rtv' + today + '.log')
log_err = init_logger('logs/create_rtv.log')

RTV_STATUS_CODE_MAPPING = {
    0: 'Completed',
    1: 'Saved',
    2: 'Open',
    3: 'Cancelled'
}

RTV_STATUS_FILTER_MAPPING = {
    'completed': 0,
    'saved': 1,
    'open': 2,
    'cancelled':4
}

class RTVSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None):
        return None

    def get_search_params(self, request_data, field_mapping):
        search_params = {'seller_po_summary__purchase_order__open_po__sku__user': self.warehouse.id}
        date_filters = {}
        try:
            # Handle date parsing and localization
            if request_data.get('from_date'):
                parsed_from_date = parser.parse(request_data['from_date'])
                localized_from_date = pytz.timezone(self.timezone).localize(parsed_from_date)
                date_filters['from_date'] = localized_from_date.astimezone(pytz.UTC)

            if request_data.get('to_date'):
                parsed_to_date = parser.parse(request_data['to_date'])
                localized_to_date = pytz.timezone(self.timezone).localize(parsed_to_date)
                date_filters['to_date'] = localized_to_date.astimezone(pytz.UTC)

        except Exception as e:
            log.info('GET RTV API Date Filters Failed for %s and params are %s and error statement is %s' % (
                str(self.request.user.username), str(request_data), str(e)))

        # Map request data fields to search_params
        if 'supplier_id' in request_data:
            search_params[field_mapping['supplier_id']] = request_data['supplier_id']
        if 'rtv_number' in request_data:
            search_params['rtv_number'] = request_data['rtv_number']
        if 'po_number' in request_data:
            search_params[field_mapping['po_number']] = request_data['po_number']
        if 'invoice_number' in request_data:
            search_params[field_mapping['invoice_number']] = request_data['invoice_number']
        if 'sku_code' in request_data:
            search_params[field_mapping['sku_code']] = request_data['sku_code']

        # Handle the from_date and to_date if available
        if 'from_date' in date_filters:
            search_params['creation_date__gte'] = date_filters['from_date']

        if 'to_date' in date_filters:
            # Adding one day to the 'to_date' to include the whole day in the search
            search_params['creation_date__lte'] = date_filters['to_date'] + datetime.timedelta(days=1)

        return search_params


    def get_rtv_data_values_dict(self):
        values_dict = {
            'po_number': F('seller_po_summary__purchase_order__po_number'),
            'po_reference': F('seller_po_summary__purchase_order__open_po__po_name'),
            'po_date': F('seller_po_summary__purchase_order__creation_date'),
            'po_qty': F('seller_po_summary__purchase_order__open_po__order_quantity'),
            'grn_number': F('seller_po_summary__grn_number'),
            'grn_reference': F('seller_po_summary__grn_reference'),
            'grn_date': F('seller_po_summary__creation_date'),
            'grn_qty': F('seller_po_summary__purchase_order__received_quantity'),
            'rtv_date': F('creation_date'),
            'rtv_qty': F('quantity'),
            'batch_grn_qty': F('seller_po_summary__quantity'),
            'supplier_id': F('seller_po_summary__purchase_order__open_po__supplier__supplier_id'),
            'supplier_name': F('seller_po_summary__purchase_order__open_po__supplier__name'),
            'price': F('seller_po_summary__price'),
            'invoice_number': F('seller_po_summary__invoice_number'),
            'invoice_date': F('seller_po_summary__invoice_date'),
            'invoice_value': F('seller_po_summary__asn__invoice_value'),
            'sku_code': F('seller_po_summary__purchase_order__open_po__sku__sku_code'),
            'sku_desc': F('seller_po_summary__purchase_order__open_po__sku__sku_desc'),
            'hsn_code': F('seller_po_summary__purchase_order__open_po__sku__hsn_code'),
            'mrp': F('seller_po_summary__purchase_order__open_po__mrp'),
            'batch_mrp': F('seller_po_summary__batch_detail__mrp'),
            'batch_number': F('seller_po_summary__batch_detail__batch_no'),
            'batch_reference': F('seller_po_summary__batch_detail__batch_reference'),
            'mfg_date': F('seller_po_summary__batch_detail__manufactured_date'),
            'exp_date': F('seller_po_summary__batch_detail__expiry_date'),
            'cgst_tax': F('seller_po_summary__purchase_order__open_po__cgst_tax'),
            'sgst_tax': F('seller_po_summary__purchase_order__open_po__sgst_tax'),
            'igst_tax': F('seller_po_summary__purchase_order__open_po__igst_tax'),
            'cess_tax': F('seller_po_summary__cess_tax'),
            'apmc_tax': F('seller_po_summary__apmc_tax'),
            'tax_type': F('seller_po_summary__purchase_order__open_po__supplier__tax_type'),
            'created_user': F('created_by__username'),
            'eff_rate': F('seller_po_summary__asn__eff_rate'),
            'cd_percent': F('seller_po_summary__asn__cash_discount_percent'),
            'sd_percent': F('seller_po_summary__asn__scheduled_percent'),
            'invoice_free_quantity': F('seller_po_summary__asn__invoice_free_quantity'),
            'line_invoice_free_quantity': F('seller_po_summary__asn__line_invoice_free_quantity'),
            'asn_data': F('seller_po_summary__asn'),
            'discount_amount': F('seller_po_summary__asn__json_data__discount_amount'),
            'cn_amount': F('seller_po_summary__asn__json_data__cn_amount'),
            'additional_cost': F('seller_po_summary__asn__json_data__additional_cost'),
            'tcs': F('seller_po_summary__asn__tcs_value'),
            'pcf': F('seller_po_summary__purchase_order__pcf'),
            'po_uom': F('seller_po_summary__purchase_order__open_po__measurement_unit'),

        }
        return values_dict


    def get(self, *args, **kwargs):
        ''' RTV - GET api '''
        self.set_user_credientials()
        request = self.request
        request_data = request.GET
        page_info = {}

        # Field Mapping
        field_mapping = {
            'po_number': 'seller_po_summary__purchase_order__po_number',
            'supplier_id': 'seller_po_summary__purchase_order__open_po__supplier__supplier_id',
            'supplier_name': 'seller_po_summary__purchase_order__open_po__supplier__name',
            'invoice_number': 'seller_po_summary__invoice_number',
            'sku_code': 'seller_po_summary__purchase_order__open_po__sku__sku_code',
        }

        self.timezone = 'Asia/Calcutta'
        if self.warehouse.userprofile.timezone:
            self.timezone = self.warehouse.userprofile.timezone

        #decimal limit
        decimal_limit = get_decimal_value(self.warehouse.id, 1)
        # Search Parameters
        search_params = self.get_search_params(request_data, field_mapping)
        eff_rate_in_rtv = get_misc_value('eff_rate_in_rtv', self.warehouse.id)

        # Status Filter
        req_status = request_data.get('status')
        if req_status and req_status not in ['all']:
            search_params['status'] = RTV_STATUS_FILTER_MAPPING.get(req_status.lower(), 0)

        limit = int(request_data.get('limit', 10))
        pagenum = int(request_data.get('pagenum', 1))

        # Values List and Dictionary for Query
        values_list = ['id', 'rtv_number','rtv_reference', 'quantity', 'location__location', 'return_reason', 'creation_date', 'status']
        values_dict = self.get_rtv_data_values_dict()

        # Fetching RTVs
        all_returns = ReturnToVendor.objects.filter(**search_params)
        rtv_numbers = list(all_returns.values_list('rtv_number', flat=True).distinct())

        # Pagination Info
        page_info = scroll_data(request, rtv_numbers, limit=limit, request_type='GET', page_num=pagenum)
        rtv_numbers = page_info['data']

        all_returns = all_returns.filter(rtv_number__in=rtv_numbers).values(*values_list, **values_dict)
        rtv_attrs = {}
        rtv_attrs = get_rtv_extra_attributes(self.warehouse, rtv_numbers)

        po_data_dict = {}
        
        for each_row in all_returns:
            rtv_number = each_row['rtv_number']
            grn_number = each_row['grn_number']
            po_number = each_row['po_number']
            sku_code = each_row['sku_code']
            batch_number = each_row['batch_number']
            status = each_row['status']

            # Create key for the RTV
            rtv_key = (rtv_number, status)

            # Initialize RTV entry if not already present
            if rtv_key not in po_data_dict:
                po_data_dict[rtv_key] = {
                    'warehouse': self.warehouse.username,
                    'rtv_number': rtv_number,
                    'rtv_reference': each_row['rtv_reference'],
                    'rtv_date': get_local_date_known_timezone(self.timezone, each_row['creation_date'], send_date=True).strftime(DT_DATE_FORMAT) if each_row['creation_date'] else '',
                    'supplier_id': each_row['supplier_id'],
                    'supplier_name': each_row['supplier_name'],
                    'status': RTV_STATUS_CODE_MAPPING.get(each_row['status'], ''),
                    'rtv_quantity': truncate_float(each_row['rtv_qty'], decimal_limit),
                    'rtv_amount_without_tax_total': 0.0,
                    'rtv_amount_with_tax_total': 0.0,
                    'grns': {},
                    'created_by': each_row.get('created_user', ''),
                    'extra_fields': rtv_attrs.get(rtv_number, {})
                }
            else:
                po_data_dict[rtv_key]['rtv_quantity'] += each_row['rtv_qty']

            # Get RTV dictionary   
            rtv_dict = po_data_dict[rtv_key]

            # Create key for the GRN + PO combination
            grn_po_key = (grn_number, po_number)

            # Initialize GRN entry if not already present for the RTV
            if grn_po_key not in rtv_dict['grns']:
                rtv_dict['grns'][grn_po_key] = {
                    'po_number': po_number,
                    'po_reference': each_row['po_reference'],
                    'po_display_key': each_row['po_reference'] or po_number,
                    'po_date': get_local_date_known_timezone(self.timezone, each_row['po_date'], send_date=True).strftime(DT_DATE_FORMAT) if each_row['po_date'] else '',
                    'po_quantity': truncate_float(each_row['po_qty'], decimal_limit),
                    'grn_number': grn_number,
                    'grn_reference': each_row['grn_reference'],
                    'grn_display_key': each_row['grn_reference'] or grn_number,
                    'grn_date': get_local_date_known_timezone(self.timezone, each_row['grn_date'], send_date=True).strftime(DT_DATE_FORMAT) if each_row['grn_date'] else '',
                    'invoice_number': each_row['invoice_number'],
                    'invoice_date':  get_local_date_known_timezone(self.timezone, datetime.datetime.combine(each_row['invoice_date'], datetime.datetime.min.time()), send_date=True).strftime(DT_DATE_FORMAT) if each_row['invoice_date'] else '',
                    'invoice_value':each_row['invoice_value'],
                    'grn_quantity': truncate_float(each_row['batch_grn_qty'], decimal_limit),
                    'rtv_quantity': truncate_float(each_row['rtv_qty'], decimal_limit),
                    'discount_amount': each_row['discount_amount'] or 0,
                    'cn_amount': each_row['cn_amount'] or 0,
                    'additional_cost': each_row['additional_cost'] or 0,
                    'tcs': each_row['tcs'] or 0,
                    'items': {}
                }
            else:
                # Update GRN level fields
                rtv_dict['grns'][grn_po_key]['grn_quantity'] += each_row['batch_grn_qty']
                rtv_dict['grns'][grn_po_key]['po_quantity'] += each_row['po_qty']
                rtv_dict['grns'][grn_po_key]['rtv_quantity'] += each_row['rtv_qty']

            # Get GRN dictionary
            grn_dict = rtv_dict['grns'][grn_po_key]
            price = each_row['price']
            if eff_rate_in_rtv=='true' and each_row['asn_data']:
                price = each_row.get('eff_rate',0) or 0

            # Create key for the SKU and Batch combination
            sku_batch_key = (sku_code, batch_number)
            rtv_amt_pre_tax = each_row['rtv_qty'] * price
            cd_percent = each_row['cd_percent'] or 0
            sd_percent = each_row['sd_percent'] or 0
            sd_amount = truncate_float((sd_percent / 100) * rtv_amt_pre_tax, decimal_limit)
            cd_amount = truncate_float((cd_percent / 100) * rtv_amt_pre_tax, decimal_limit)
            invoice_free_quantity = each_row['invoice_free_quantity'] or each_row['line_invoice_free_quantity']
            # Initialize SKU + Batch entry if not already present
            if sku_batch_key not in grn_dict['items']:
                grn_dict['items'][sku_batch_key] = {
                    'sku_code': sku_code,
                    'sku_desc': each_row['sku_desc'],
                    'hsn_code': each_row['hsn_code'],
                    'mrp': truncate_float(each_row.get('batch_mrp') or each_row['mrp'], decimal_limit),
                    'batch_details': [],
                    'po_quantity': truncate_float(each_row['po_qty'], decimal_limit),
                    'received_quantity': truncate_float(each_row['batch_grn_qty'], decimal_limit),
                    'rtv_quantity': truncate_float(each_row['rtv_qty'], decimal_limit),
                    'invoice_free_quantity': invoice_free_quantity,
                    'rtv_amount_without_tax': 0.0,
                    'rtv_amount_with_tax': 0.0,
                    'cgst_tax': 0.0,
                    'sgst_tax': 0.0,
                    'igst_tax': 0.0,
                    'cess_tax': each_row['cess_tax'],
                    'apmc_tax': each_row['apmc_tax'],
                    'cash_discount_percent': cd_percent,
                    'cash_discount_amount': cd_amount,
                    'scheduled_percent': sd_percent,
                    'scheduled_amount': sd_amount,
                    'po_uom': each_row['po_uom'],
                    'po_uom_qty': each_row['pcf'] * each_row['rtv_qty'],
                }               
            else:
                # Update SKU + Batch level fields
                grn_dict['items'][sku_batch_key]['po_quantity'] += each_row['po_qty']
                grn_dict['items'][sku_batch_key]['received_quantity'] += each_row['batch_grn_qty']
                grn_dict['items'][sku_batch_key]['rtv_quantity'] += each_row['rtv_qty']
                grn_dict['items'][sku_batch_key]['scheduled_amount']+= sd_amount
                grn_dict['items'][sku_batch_key]['cash_discount_amount']+= cd_amount

            # Get SKU + Batch dictionary
            sku_batch_dict = grn_dict['items'][sku_batch_key]
            each_row['cgst_tax'] = each_row.get('cgst_tax', 0) or 0
            each_row['sgst_tax'] = each_row.get('sgst_tax', 0) or 0
            each_row['igst_tax'] = each_row.get('igst_tax', 0) or 0
            grn_dict['items'][sku_batch_key]['cgst_tax'] = each_row['cgst_tax']
            grn_dict['items'][sku_batch_key]['sgst_tax'] = each_row['sgst_tax']
            grn_dict['items'][sku_batch_key]['igst_tax'] = each_row['igst_tax']

            temp_tax = (float(each_row['cgst_tax']) + float(each_row['sgst_tax']) + float(each_row['igst_tax']) + float(each_row['cess_tax']))
            tax_amt = rtv_amt_pre_tax * temp_tax / 100
            rtv_amt_with_tax = rtv_amt_pre_tax + tax_amt


            # Add batch details (no need to create a separate key for batch since we already included batch_number in the sku_batch_key)
            sku_batch_dict['batch_details'].append({
                'location': each_row['location__location'],
                'batch_no': each_row['batch_number'],
                'batch_reference': each_row['batch_reference'],
                'batch_display_key': each_row['batch_reference'] or each_row['batch_number'],
                'manufactured_date': get_local_date_known_timezone(self.timezone, each_row['mfg_date'], send_date=True).strftime(DT_DATE_FORMAT) if each_row['mfg_date'] else '',
                'expiry_date': get_local_date_known_timezone(self.timezone, each_row['exp_date'], send_date=True).strftime(DT_DATE_FORMAT) if each_row['exp_date'] else '',  
                'mrp': truncate_float(each_row.get('batch_mrp') or each_row['mrp'], decimal_limit),
                'unit_price': truncate_float(price, decimal_limit),
                'rtv_quantity': truncate_float(each_row['rtv_qty'], decimal_limit),
                'received_quantity': truncate_float(each_row['batch_grn_qty'], decimal_limit),
                'unit_price_without_tax': truncate_float(price, decimal_limit),
                'unit_price_with_tax': truncate_float(price + (float(price / 100) * temp_tax), decimal_limit),
                'reason':each_row['return_reason'],
                'eff_rate': truncate_float(each_row.get('eff_rate',0) or 0, decimal_limit),
            })

           
            sku_batch_dict['rtv_amount_without_tax'] += truncate_float(rtv_amt_pre_tax, decimal_limit)
            sku_batch_dict['rtv_amount_with_tax'] += truncate_float(rtv_amt_with_tax, decimal_limit)
            

            # Update RTV level fields
            rtv_dict['rtv_amount_without_tax_total'] += truncate_float(rtv_amt_pre_tax, decimal_limit)
            rtv_dict['rtv_amount_with_tax_total'] += truncate_float(rtv_amt_with_tax, decimal_limit)

        # Formatting final data
        returns_data = []
        for (rtv_number, status), rtv_data in po_data_dict.items():
            rtv_data['grns'] = [grn for grn_key, grn in rtv_data['grns'].items()]  # Flattening GRNs to list
            rtv_data['grns'] = [{**grn, 'items': [item for item_key, item in grn['items'].items()]} for grn in rtv_data['grns']]  # Flattening items to list
            returns_data.append(rtv_data)

        # Update the page_info with processed data
        page_info['data'] = returns_data
        page_info['status'] = 200
        page_info['page_info']['total_count'] = len(returns_data)

        return JsonResponse(page_info)
            
    def put(self, request, *args, **kwargs):
        """
        Handle the HTTP PUT request for updating  RTV /DEBIT Note.
        Args:
            request (HttpRequest): The HTTP request object.
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        Returns:
            JsonResponse: The JSON response containing the updated data and status code.
        """
        self.set_user_credientials()
        log.info("Request update rtv username %s and params are %s"% (str(self.request.user.username),str(self.request.body)))
        self.errors,master_docs_list,bulk_rtv_objs = [],[],[]
        error_resp={'message':self.errors, 'status':400}
        self.warehouse = self.request.warehouse
        try:
            request_data = loads(self.request.body)
        except Exception:
            request_data = []
        if not request_data:
            return JsonResponse({'message': ['Invalid Payload'], 'status': 400},status=400)
        if request_data and not isinstance(request_data, list):
            request_data = [request_data]
        try:
            self.prepare_unique_values(request_data)
            self.prepare_available_rtv_references()
            self.rtv_data_dict = dict(ReturnToVendor.objects.filter(seller_po_summary__purchase_order__open_po__sku__user=self.warehouse.id, rtv_number__in=self.unique_rtv_no).values_list('rtv_number','rtv_reference').distinct())
            self.validate_request_data(request_data)
            if self.errors:
                return JsonResponse(error_resp,status=400)
            for each_record in request_data:
                rtv_number = each_record.get('rtv_number', '')
                rtv_reference = each_record.get('rtv_reference', '')
                debit_note_url = each_record.get('debit_note_url', '')
                encoded_string = each_record.get('encoded_debit_note','')
                # Frame extra fields
                extra_fields = {}
                rtv_user_attributes = [rtv_attr.get('attribute_name') for rtv_attr in get_user_attributes(self.warehouse, 'rtv')]
                rtv_extra_fields = each_record.get('extra_fields', {})
                for field_key, value in rtv_extra_fields.items():
                    if field_key.startswith('attr_'):
                        field_key = field_key.split('attr_')[1]
                    if field_key in rtv_user_attributes:
                        extra_fields[field_key] = value

                if self.rtv_data_dict.get(rtv_number) and rtv_reference and  self.rtv_data_dict.get(rtv_number) != rtv_reference:
                    if rtv_reference in self.available_rtv_reference_list:
                        self.errors.append('RTV Reference - %s Already Exists, Please provide unique RTV Reference' % rtv_reference)
                        continue
                    rtv_objs = ReturnToVendor.objects.filter(seller_po_summary__purchase_order__open_po__sku__user=self.warehouse.id, rtv_number=rtv_number)
                    for rtv_obj in rtv_objs:
                        rtv_obj.rtv_reference = rtv_reference
                        bulk_rtv_objs.append(rtv_obj)
                if encoded_string:
                    mesg,master_docs_list = update_encoded_debit(self.warehouse,encoded_string,debit_note_url,rtv_number,rtv_reference,master_docs_list)
                    if mesg:
                        self.errors.append(mesg)
                #Update rtv extra fields
                if extra_fields:
                    error_messages, extra_fields = validate_attributes(self.warehouse, extra_fields, attribute_model='rtv')
                    if error_messages:
                        return JsonResponse({'message': ', '.join(error_messages)},status=500)
                    save_grn_extra_fields(self.warehouse,rtv_number, extra_fields,transaction="rtv")

            if master_docs_list:
                master_docs_status = create_master_doc(master_docs_list)
                if not master_docs_status:
                    return JsonResponse({'message': ['Failed'], 'status': 500},status=500)
            if self.errors:
                return JsonResponse(error_resp,status=400)
            if bulk_rtv_objs :
                ReturnToVendor.objects.bulk_update(bulk_rtv_objs,['rtv_reference'])
            return JsonResponse({"message":"Sucess",'status':200})
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log_err.error("Error in updating RTV %s"%str(e))
            return JsonResponse({'message': ['Failed'], 'status': 500},status=500)

    def validate_request_data(self,request_data):
        for each_record in request_data:
            rtv_number = each_record.get('rtv_number', '')
            rtv_reference = each_record.get('rtv_reference', '')
            debit_note_url = each_record.get('debit_note_url', '')
            encoded_pdf = each_record.get('encoded_debit_note','')
            extra_fields = each_record.get('extra_fields', {})
            if not rtv_number:
                self.errors.append('RTV Number is mandatory')
                continue
            elif rtv_number not in self.rtv_data_dict:
                self.errors.append('No Data Found for RTV:'+ rtv_number)
                continue
            if not rtv_reference and not debit_note_url and not encoded_pdf and not extra_fields:
                self.errors.append('Either RTV Reference or Debit Note URL or encoded pdf or extra fields is Mandatory for RTV:'+ rtv_number)
                continue

            master_docs_objs = MasterDocs.objects.filter(user_id=self.warehouse.id, master_type='debit_note', master_id=rtv_number)
            if master_docs_objs.exists():
                self.errors.append("Debit Note already updated for RTV:"+ rtv_number)


    def prepare_unique_values(self, request_data):
        '''This method is used to prepare unique references from request data'''
        self.unique_rtv_ref,self.unique_rtv_no = [],[]
        self.rtv_ref_no_dict = {}
        for each_item in request_data:
            if each_item.get('rtv_reference', ''):
                self.unique_rtv_ref.append(each_item.get('rtv_reference', ''))
            if each_item.get('rtv_number',''):
                self.unique_rtv_no.append(each_item.get('rtv_number',''))
                self.rtv_ref_no_dict[each_item.get('rtv_number','')] = each_item.get('rtv_reference', '')

    def prepare_available_rtv_references(self):
        self.available_rtv_reference_list = list(ReturnToVendor.objects.filter(seller_po_summary__purchase_order__open_po__sku__user=self.warehouse.id).values_list('rtv_reference').distinct())

def gather_uom_master_for_sku(user, sku_code):
    UOMs = UOMMaster.objects.filter(sku_code=sku_code, company=get_parent_company(user.userprofile.company))
    dataDict = {}
    dataDict['uom_items'] = [
        {
            'unit_name': 'base',
            'unit_conversion': 1,
            'is_base': True
        }
    ]
    for uom in UOMs:
        dataDict['uom_items'][0]['unit_name'] = uom.base_uom
        dataDict['name'] = '%s-%s' % (sku_code, uom.base_uom)
        uom_item = {
            'unit_name': uom.uom,
            'unit_conversion': uom.conversion,
            'unit_type': uom.uom_type
        }

        dataDict['uom_items'].append(uom_item)

    return dataDict

def get_debit_note_data(request, rtv_number, user):
    user_list = [user.id]
    warehouses_ids, warehouse_users = get_warehouses_list(user)
    if warehouses_ids:
        user_list = warehouses_ids
    user_list.append(user.id)
    timezone = get_user_time_zone(user)
    eff_rate_in_rtv = get_misc_value('eff_rate_in_rtv', user.id)
    return_to_vendor = ReturnToVendor.objects.select_related('seller_po_summary__purchase_order__open_po__sku').\
                                                        filter(rtv_number=rtv_number,
                                                     seller_po_summary__purchase_order__open_po__sku__user__in=user_list)
    data_dict = {}
    total_invoice_value, total_qty = 0, 0
    total_with_gsts, total_without_discount, total_only_discount, total_taxable_value, total_amount_, total_tax_amount = 0, 0, 0, 0, 0, 0
    total_cgst_value, total_sgst_value, total_igst_value, total_utgst_value, total_cess_value, total_apmc_value = 0, 0, 0, 0, 0, 0
    url = request.META.get('HTTP_REFERER')
    wh_currency = ''
    rtv_created_by = ''
    remarks = ''
    ware_house = UserProfile.objects.filter(user = user)\
                            .values('company__company_name', 'cin_number', 'location', 'city',\
                                    'state', 'country', 'phone_number', 'pin_code',\
                                    'gst_number', 'address', 'pan_number', 'base_currency')
    data_dict.setdefault('warehouse_details', [])
    if len(ware_house):
        wh_currency = ware_house[0]['base_currency']
        data_dict['warehouse_details'] = ware_house[0]
        if data_dict['warehouse_details']['phone_number'] == 0 or data_dict['warehouse_details']['phone_number'] == '0':
            data_dict['warehouse_details']['phone_number'] = ''
        else:
            data_dict['warehouse_details']['phone_number'] = data_dict['warehouse_details'].get('phone_number', '')
    data_dict.setdefault('supplier_details', {})
    supplier_details = return_to_vendor.values('seller_po_summary__purchase_order__open_po__supplier__pincode',\
                        'seller_po_summary__purchase_order__open_po__supplier__state',\
                        'seller_po_summary__purchase_order__open_po__supplier__tin_number',\
                        'seller_po_summary__purchase_order__open_po__supplier__city', \
                        'seller_po_summary__purchase_order__open_po__supplier__country').distinct()
    if len(supplier_details):
        data_dict['supplier_details']['pincode'] = supplier_details[0].get('seller_po_summary__purchase_order__open_po__supplier__pincode', '')
        data_dict['supplier_details']['state'] = supplier_details[0].get('seller_po_summary__purchase_order__open_po__supplier__state', '')
        data_dict['supplier_details']['supplier_gstin'] = supplier_details[0].get('seller_po_summary__purchase_order__open_po__supplier__tin_number', '')
        data_dict['supplier_details']['city'] = supplier_details[0].get('seller_po_summary__purchase_order__open_po__supplier__city', '')
        data_dict['supplier_details']['country'] = supplier_details[0].get('seller_po_summary__purchase_order__open_po__supplier__country', '')
    decimal_limit = get_decimal_value(user.id, price=True)
    sku_count = return_to_vendor.values('seller_po_summary__purchase_order__open_po__sku__sku_code').distinct().count()
    for obj in return_to_vendor:
        rtv_created_by = obj.created_by.username
        remarks = obj.json_data.get('remarks', '')
        data_dict['return_reason'] = obj.return_reason
        get_po = obj.seller_po_summary.purchase_order.open_po
        data_dict['supplier_name'] = get_po.supplier.name
        data_dict['supplier_address'] = get_po.supplier.address
        data_dict['supplier_email'] = get_po.supplier.email_id
        data_dict['supplier_id'] = get_po.supplier.id
        data_dict['supplier_gstin'] = get_po.supplier.tin_number
        data_dict['phone_number'] = get_po.supplier.phone_number
        data_dict['sku_count'] = sku_count
        data_dict['city'] = get_po.supplier.city
        data_dict['state'] = get_po.supplier.state
        data_dict['pincode'] = get_po.supplier.pincode
        data_dict['pan'] = get_po.supplier.pan_number
        data_dict['po_currency'] = get_po.json_data.get('po_currency', wh_currency)
        data_dict.setdefault('item_details', [])
        item_dict = {'sku_code': get_po.sku.sku_code, 'sku_desc': get_po.sku.sku_desc,\
                          'hsn_code': str(get_po.sku.hsn_code).split("_")[0] if get_po.sku.hsn_code else "",\
                          'order_qty': obj.quantity, 'mrp':get_po.sku.mrp, 'unit_price': get_po.sku.cost_price if get_po.sku.cost_price else 0}

        user_obj = user
        unitdata = gather_uom_master_for_sku(user_obj, get_po.sku.sku_code)
        unitexid = unitdata.get('name', None)
        purchaseUOMname = None
        for row in unitdata.get('uom_items', []):
            if row.get('unit_type', '') == 'Purchase':
                purchaseUOMname = row.get('unit_name', False)
        item_dict.update({
            'unitypeexid': unitexid,
            'uom_name': purchaseUOMname
        })
        if obj.seller_po_summary.batch_detail:
            mfg_date= obj.seller_po_summary.batch_detail.manufactured_date
            exp_date= obj.seller_po_summary.batch_detail.expiry_date
            if(mfg_date):
                mfg_date = get_local_date_known_timezone(timezone, mfg_date, send_date=True).strftime(SLASH_DATE_FORMAT)
            if(exp_date):
                exp_date = get_local_date_known_timezone(timezone, exp_date, send_date=True).strftime(SLASH_DATE_FORMAT)
            item_dict.update({
                "batch_no": obj.seller_po_summary.batch_detail.batch_no,
                "mfg_date": mfg_date,
                "exp_date" : exp_date
            })
            item_dict['mrp'] = obj.seller_po_summary.batch_detail.mrp
        if str(user.userprofile.sap_code).lower()== "milkbasket":
            item_dict['price'] = 0
        else:
            if obj.seller_po_summary.price:
                item_dict['price'] = obj.seller_po_summary.price
            else:
                item_dict['price'] = get_po.price
        item_dict['measurement_unit'] = get_po.measurement_unit or get_po.sku.measurement_type
        item_dict['discount'] = obj.seller_po_summary.discount_percent
        data_dict['invoice_num'] = obj.seller_po_summary.invoice_number
        item_dict['invoice_num'] = data_dict['invoice_num']
        item_dict['cgst'] = get_po.cgst_tax
        item_dict['sgst'] = get_po.sgst_tax
        item_dict['igst'] = get_po.igst_tax
        item_dict['utgst'] = get_po.utgst_tax
        item_dict['cess'] = get_po.cess_tax
        item_dict['apmc'] = get_po.apmc_tax
        item_dict['total_tax'] = get_po.cgst_tax + get_po.sgst_tax + get_po.igst_tax + get_po.utgst_tax + get_po.apmc_tax + get_po.cess_tax
        cd_percent = obj.seller_po_summary.asn.cash_discount_percent  if obj.seller_po_summary.asn else 0
        sd_percent = obj.seller_po_summary.asn.scheduled_percent  if obj.seller_po_summary.asn else 0
        rtv_qty = obj.quantity
        rtv_amt_pre_tax = rtv_qty * item_dict['price']
        sd_amount = truncate_float((sd_percent / 100) * rtv_amt_pre_tax, decimal_limit)
        cd_amount = truncate_float((cd_percent / 100) * rtv_amt_pre_tax, decimal_limit)
        item_dict['cash_discount_percent'] = cd_percent
        item_dict['cash_discount_amount'] = cd_amount
        item_dict['scheduled_percent'] = sd_percent
        item_dict['scheduled_amount'] = sd_amount
        if obj.seller_po_summary:
            item_dict['cess'] = obj.seller_po_summary.cess_tax
            item_dict['apmc'] = obj.seller_po_summary.apmc_tax
        if eff_rate_in_rtv=='true' and obj.seller_po_summary.asn:
            item_dict['price'] = obj.seller_po_summary.asn.eff_rate
        item_dict['return_reason'] = obj.return_reason
        item_dict['total_amt'] = truncate_float(item_dict['price'] * item_dict['order_qty'], decimal_limit)
        item_dict['discount_amt'] = truncate_float(((item_dict['total_amt'] * item_dict['discount'])/100), decimal_limit)
        item_dict['taxable_value'] = truncate_float(item_dict['total_amt'] - item_dict['discount_amt'], decimal_limit)
        cgst_value = ((item_dict['taxable_value'] * item_dict['cgst'])/100)
        item_dict['cgst_value'] = truncate_float(cgst_value, 2)
        item_dict['igst_value'] = truncate_float(((item_dict['taxable_value'] * item_dict['igst'])/100), decimal_limit)
        sgst_value = ((item_dict['taxable_value'] * item_dict['sgst'])/100)
        item_dict['sgst_value'] = truncate_float(sgst_value, 2)
        item_dict['utgst_value'] = truncate_float(((item_dict['taxable_value'] * item_dict['utgst'])/100), decimal_limit)
        item_dict['cess_value'] = truncate_float(((item_dict['taxable_value'] * item_dict['cess'])/100), decimal_limit)
        item_dict['apmc_value'] = truncate_float(((item_dict['taxable_value'] * item_dict['apmc']) / 100), decimal_limit)
        item_dict['total_with_gsts'] = item_dict['taxable_value'] + item_dict['cgst_value'] + \
                                            item_dict['igst_value'] + item_dict['sgst_value'] + item_dict['utgst_value'] + \
                                            item_dict['cess_value'] + item_dict['apmc_value']
        item_dict['total_with_gsts'] = truncate_float(item_dict['total_with_gsts'], decimal_limit)
        item_dict['total_tax_amount'] = truncate_float(item_dict['total_with_gsts'] - item_dict['taxable_value'], decimal_limit)
        data_dict['rtv_creation_date'] = get_local_date_known_timezone(timezone, obj.creation_date)
        data_dict['grn_date'] = get_local_date_known_timezone(timezone, obj.seller_po_summary.creation_date)
        data_dict['date_of_issue_of_original_invoice'] = ''
        inv_date = obj.seller_po_summary.invoice_date
        if inv_date:
            data_dict['date_of_issue_of_original_invoice'] = get_local_date_known_timezone(timezone, datetime.datetime.combine(inv_date, datetime.datetime.min.time()), send_date=True).strftime(SLASH_DATE_FORMAT)
            item_dict['date_of_issue_of_original_invoice'] = data_dict['date_of_issue_of_original_invoice']
        total_with_gsts = total_with_gsts + item_dict['total_with_gsts']
        total_qty = total_qty + item_dict['order_qty']
        total_invoice_value = total_invoice_value + item_dict['total_with_gsts']
        total_without_discount = total_without_discount + item_dict['total_amt']
        total_only_discount = total_only_discount + item_dict['discount']
        total_taxable_value = total_taxable_value + item_dict['taxable_value']
        total_cgst_value = total_cgst_value + item_dict['cgst_value']
        total_sgst_value = total_sgst_value + item_dict['sgst_value']
        total_igst_value = total_igst_value + item_dict['igst_value']
        total_utgst_value = total_utgst_value + item_dict['utgst_value']
        total_cess_value = total_cess_value + item_dict['cess_value']
        total_apmc_value = total_cess_value + item_dict['apmc_value']
        data_dict['grn_no'] = obj.seller_po_summary.grn_number
        data_dict['item_details'].append(item_dict)
    data_dict['total_qty'] = total_qty
    data_dict['total_without_discount'] = truncate_float(total_without_discount, decimal_limit)
    data_dict['total_only_discount'] = truncate_float(total_only_discount, decimal_limit)
    data_dict['total_taxable_value'] = truncate_float(total_taxable_value, decimal_limit)
    data_dict['total_cgst_value'] = truncate_float(total_cgst_value, decimal_limit)
    data_dict['total_sgst_value'] = truncate_float(total_sgst_value, decimal_limit)
    data_dict['total_igst_value'] = truncate_float(total_igst_value, decimal_limit)
    data_dict['total_utgst_value'] = truncate_float(total_utgst_value, decimal_limit)
    data_dict['total_cess_value'] = truncate_float(total_cess_value, decimal_limit)
    data_dict['total_apmc_value'] = truncate_float(total_apmc_value, decimal_limit)
    data_dict['total_with_gsts'] = truncate_float(total_with_gsts, decimal_limit)
    data_dict['total_round_value'] = data_dict['total_with_gsts']
    data_dict['round_down_value'] = truncate_float((data_dict['total_with_gsts'] - total_with_gsts + total_tax_amount), decimal_limit)
    data_dict['total_invoice_value'] = truncate_float(total_invoice_value, decimal_limit)
    data_dict['print_total_invoice_value'] = number_in_words(truncate_float(total_with_gsts, decimal_limit))+' only'
    total_tax_amount = data_dict['total_invoice_value'] - data_dict['total_taxable_value']
    data_dict['total_tax_amount'] = truncate_float(total_tax_amount, decimal_limit)
    data_dict['rtv_number'] = rtv_number
    data_dict['rtv_created_by'] = rtv_created_by
    data_dict['remarks'] = remarks
    if rtv_number:
        irn_number,qr_code_data,ack_number,ack_date, irn_status = '','','','',''
        irn_obj = list(IRN.objects.filter(user=user.id, transact_id=rtv_number, transact_type='edebitnote')\
                .values('irn', 'ackno', 'ack_date', 'qr_code', 'status'))
        if irn_obj:
            irn_number = irn_obj[0].get('irn', '')
            qr_code_data = irn_obj[0].get('qr_code', '')
            ack_number = irn_obj[0].get('ackno', '')
            ack_date = irn_obj[0].get('ack_date', '')
            irn_status = irn_obj[0].get('status', 0)
        if not irn_status:
            data_dict['IRN'] = irn_number
            data_dict['ackno'] = ack_number
            data_dict['ack_date'] = get_local_date_known_timezone(timezone, ack_date) if ack_date else ''
            data_dict['irn_status'] = irn_status
        else:
            data_dict['IRN'] = ''
            data_dict['ackno'] = ''
            data_dict['ack_date'] = ''
            data_dict['irn_status'] = ''
        if irn_number and qr_code_data:
            data_dict['qr_code_image'] = convert_qr_code_image(user, qr_code_data, rtv_number, url, transact_type='edebitnote')
    return data_dict


def write_html_to_pdf(f_name, html_data):
    attachments = []
    try:
        if not isinstance(html_data, list):
            html_data = [html_data]
        for data in html_data:
            temp_name = f_name + str(secrets.randbelow(9999))
            file_name = '%s.html' % temp_name
            pdf_file = '%s.pdf' % temp_name
            path = 'static/temp_files/'
            folder_check(path)
            file = open(path + file_name, "w+b")
            file.write(xcode(data))
            file.close()
            os.system(
                "./phantom/bin/phantomjs ./phantom/examples/rasterize.js ./%s ./%s A4" % (path + file_name, path + pdf_file))
            attachments.append({'path': path + pdf_file, 'name': pdf_file})
    except Exception as e:
        import traceback
        log_mail_info.debug(traceback.format_exc())
        log_mail_info.info('PDF file genrations failed for ' + str(xcode(html_data)) + ' error statement is ' + str(e))
    return attachments

def get_rtv_configurations(warehouse, request_data):
    enable_dc_returns = request_data.get('enable_dc_returns', '')
    misc_types = ['rtv_prefix_code', 'rtv_mail', 'DEBIT NOTE', 'allow_grn_for_rtv_qty','enable_purchase_uom','eff_rate_in_rtv']
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    rtv_prefix_code = misc_dict.get('rtv_prefix_code', 'false')
    if not rtv_prefix_code or rtv_prefix_code == 'false':
        rtv_prefix_code = 'RTV'
    if enable_dc_returns == 'true':
        return_type = 'DC'
    else:
        return_type = 'Invoice'
    return misc_dict, rtv_prefix_code, return_type

def validate_rtv_configs(request_data, misc_dict):
    picklist_enabled, send_rtv_mail = True, False
    req_picklist_enabled = request_data.get('picklist_enabled', False)
    if req_picklist_enabled in ['false', False, '', 'null']:
        picklist_enabled = False

    if misc_dict.get('rtv_mail', 'false') == 'true':
        send_rtv_mail = True
    return picklist_enabled, send_rtv_mail

def create_update_rtv(warehouse, final_dict, picklist_enabled, return_type, invoice_number, rtv_number, stock_df, po_wise_rtv_qty_dict):
    if float(final_dict['needed_stock_quantity']) > 0 and not picklist_enabled and stock_df.empty:
        update_stock_detail(final_dict['stocks'], float(final_dict['needed_stock_quantity']), warehouse,
                            final_dict['rtv_id'], stock_serial_count_dict=final_dict.get('stock_serial_count_dict', {}))
    rtv_obj = ReturnToVendor.objects.get(id=final_dict['rtv_id'])
    if send_rtv_mail and not invoice_number:
        invoice_number = rtv_obj.seller_po_summary.invoice_number
    if rtv_obj.rtv_number:
        rtv_number = rtv_obj.rtv_number
    if not rtv_obj.rtv_reference:
        rtv_obj.rtv_reference = rtv_number
    final_dict['rtv_number'] = rtv_obj.rtv_number = rtv_number
    rtv_obj.rtv_reference = final_dict.get('reference_number','') if final_dict.get('reference_number','') else rtv_number
    rtv_obj.return_type = return_type
    rtv_obj.status=0
    if picklist_enabled:
        rtv_obj.status=2
    rtv_obj.save()
    # Update rtv quantity for the each po_id in the dictionary.
    po_id = rtv_obj.seller_po_summary.purchase_order_id
    po_wise_rtv_qty_dict[po_id] = po_wise_rtv_qty_dict.get(po_id, 0) + rtv_obj.quantity
    stock_df = prepare_rtv_confirmation_stock_data(rtv_obj.id, rtv_obj.quantity, stock_df)
    
    if final_dict.get('serial_numbers', []):
        create_rtv_serial_numbers(warehouse, final_dict, rtv_number, 1, True)

    return invoice_number, rtv_number


def create_rtv_serial_numbers(warehouse, final_dict, rtv_number, status, final_rtv):
    sn_item_list = prepare_serial_item_list(final_dict['serial_numbers'], final_dict, status)
    response = ''
    try:
        create_serial_number_mapping(warehouse,sn_item_list, rtv_number, final_dict)
    except ValueError as e:
        return JsonResponse({"errors": str(e)}, status=500)
    if final_rtv == True:
        request_dict = {
        'items': []
        }
        for serial_number in final_dict['serial_numbers']:
            item = {
                'serial_numbers': [serial_number],
                'sku_id': final_dict['sku_id'],
                'status': 0
            }
            request_dict['items'].append(item)
        serial_mixin = SerialNumberMixin(
            user=None,
            warehouse=warehouse,
            request_data=request_dict
        )
        response = serial_mixin.update_serial_numbers()
    return response
    

def prepare_serial_item_list(serial_numbers, stock_data, status):
    sn_item_list = []
    if serial_numbers:
        sn_item_list.append({
            'transact_id': stock_data['rtv_id'], #RTV ID
            'transact_type': 'rtv',
            'serial_numbers': serial_numbers, #list of serial numbers
            'sku_code': stock_data['sku_code'], #sku code
            'location_id': stock_data['stocks'][0].location_id, #location id
            'zone': stock_data['zone'],
            'batch_number': stock_data['batch_number'] if stock_data['batch_number'] else '',
            'lpn_number': stock_data['stocks'][0].lpn_number if stock_data['stocks'][0].lpn_number else '',
            'batch_detail_id': stock_data['stocks'][0].batch_detail_id if stock_data['stocks'][0].batch_detail_id else None,
            'status': status
        })
    return sn_item_list

def create_serial_number_mapping(warehouse, sn_item_list, rtv_number, final_dict=None):
    if not sn_item_list:
        raise ValueError(f"Serial number and RTV Quantity mismatch")
    
    serial_df = get_existing_serials(warehouse, [str(final_dict['rtv_id'])], 1)
    existing_serial_numbers_list = fetch_serial_numbers_for_rtv_id(final_dict['rtv_id'], serial_df)

    for item in sn_item_list:
        for serial_number in item['serial_numbers']:
            if serial_number in existing_serial_numbers_list:
                raise ValueError(f"Serial number {serial_number} is already associated with this RTV")
    
    sn_transact_dict = {
        'reference_number': rtv_number,
        'reference_type': 'rtv',
        'items': sn_item_list
    }
    final_dict = SerialNumberTransactionMixin(None, warehouse, sn_transact_dict).create_update_sn_transaction()
    return final_dict

def generate_rtv_picklist(request, warehouse, data_list):
    picklist_items = prepare_picklist_details(data_list)
    if picklist_items:
        picklist_data = {
            "pick_type": "default",
            "data": picklist_items
        }
        misc_types = [
            'rtv_for_blocked_stock', 'restrict_picklist_on_cycle_count_creation_options',
            'restrict_picklist_on_cycle_count_pending_approval_options'
        ]
        misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
        extra_params = {"switch_values": {"is_full_picking": True, **misc_dict}}
        rtv_for_blocked_stock = misc_dict.get('rtv_for_blocked_stock', 'false')
        if rtv_for_blocked_stock not in ['false', '', None, 'False', False]:
            extra_params['stock_status'] = [6]
        else:
            extra_params['ignore_stock_status'] = True
        picklist = PicklistMixin(request.user, warehouse, picklist_data, extra_params=extra_params)
        picklist_response_data = picklist.generate_picklist_process()
        picklist_generation_errors = picklist_response_data.get('errors', [])
        if picklist_generation_errors:
            raise Exception('Picklist Generation is failed! with exception - %s'%str(picklist_generation_errors))

def send_rtv_mail(request, warehouse, invoice_number, show_data_invoice):
    supplier_email = show_data_invoice.get('supplier_email', '')
    supplier_id = show_data_invoice.get('supplier_id', '')
    owner_email = show_data_invoice.get('owner_email', '')
    supplier_email_id = []
    supplier_email_id.insert(0, supplier_email)
    if supplier_id:
        secondary_supplier_email = list(
            MasterEmailMapping.objects.filter(master_id=supplier_id, user=warehouse.id,
                                            master_type='supplier').values_list(
                'email_id', flat=True).distinct())

        supplier_email_id.extend(secondary_supplier_email)
    if owner_email:
        supplier_email_id.append(owner_email)
    data_dict_po = {'po_date': show_data_invoice.get('grn_date',''),
                    'po_reference': show_data_invoice.get('grn_no',''),
                    'invoice_number': invoice_number,
                    'supplier_name':show_data_invoice.get('supplier_name',''),
                    'rtv_number':show_data_invoice.get('rtv_number',''),
                        }
    t = loader.get_template('templates/toggle/rtv_mail.html')
    rendered_mail = t.render({'show_data_invoice': [show_data_invoice]})
    supplier_phone_number = show_data_invoice.get('phone_number', '')
    company_name = show_data_invoice.get('warehouse_details', '').get('company__company_name', '')
    write_and_mail_pdf(
        'Return_to_Vendor', rendered_mail, warehouse, supplier_email_id,
        supplier_phone_number, company_name + 'Return to vendor order',
        '', False, False, 'rtv_mail' ,data_dict_po
        )

def post_rtv_creation(request, warehouse, rtv_number, sku_codes, show_data_invoice, misc_dict):
    action_dict = {'type' : 'rtv', 'number' : rtv_number}
    extra_params = {'request_user': request.user.username, 'Authorization': request.META.get('HTTP_AUTHORIZATION'), 'Warehouse': warehouse.username}
    insert_shipment_invoice_data_fun(warehouse.id, rtv_number, "rtv", extra_params = extra_params)
    show_data_invoice['sale_signature'] = get_auth_signature(request, warehouse)
    invoice_format = misc_dict.get('DEBIT NOTE', 'false')
    debit_note_data = InvoiceForms.objects.filter(document_type="DEBIT NOTE", invoice_format=invoice_format, status=1).values('output_data')
    try:
        rtv_3p_integration(request, warehouse, rtv_number)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(("Webhook failed for %s in rtv and params are %s and error statement is %s") % (str(warehouse.username), str(sku_codes), str(e)))

    if invoice_format not in ['', None, 'null', 'None', 'false'] and debit_note_data.exists():
        rendered_data = get_rendered_invoice_data(debit_note_data[0].get('output_data'), {"show_data_invoice": [show_data_invoice]})
    else:
        t = loader.get_template('templates/toggle/debit_note.html')
        rendered_data = t.render({'show_data_invoice' : [show_data_invoice]})
        # rendered_data = render(request, 'templates/toggle/debit_note.html', {'show_data_invoice' : [show_data_invoice]})
    return rendered_data

def reopen_po_for_rtv_qty(po_wise_rtv_qty_dict, misc_dict, warehouse_id):
    """
    Allows purchase order (PO) receipt for RTV (Return to Vendor) quantity by adjusting
    the received quantity, and storing rtv qty in po
    """
    # allowing po receiopt for rtv qty based on config
    if misc_dict.get('allow_grn_for_rtv_qty', 'false') == 'true':
        po_objects = PurchaseOrder.objects.filter(id__in=po_wise_rtv_qty_dict.keys())
        if po_objects.exists():
            updated_pos = []
            po_numbers = []
            for po in po_objects:
                rtv_quantity = po_wise_rtv_qty_dict.get(po.id, 0)
                remaining_rtv = rtv_quantity
                
                if po.received_quantity > 0:
                    if remaining_rtv <= po.received_quantity:
                        po.received_quantity -= remaining_rtv
                        remaining_rtv = 0
                    else:
                        remaining_rtv -= po.received_quantity
                        po.received_quantity = 0
                
                if remaining_rtv > 0 and po.received_free_quantity:
                    if po.received_free_quantity >= remaining_rtv:
                        po.received_free_quantity -= remaining_rtv
                    else:
                        po.received_free_quantity = 0
                
                po.rtv_quantity += rtv_quantity
                
                total_received = po.received_quantity + po.received_free_quantity
                if total_received == 0:
                    po.status = ''
                else:
                    po.status = 'grn-generated'
                
                updated_pos.append(po)
                po_numbers.append(po.po_number)

            # Bulk update only after modifying all PO objects
            if updated_pos:
                PurchaseOrder.objects.bulk_update_with_rounding(updated_pos, ['received_quantity', 'rtv_quantity', 'status'])
            
            # Update POHeader status after RTV completion
            if po_numbers:
                update_po_header(po_numbers, warehouse_id)

@get_warehouse
def create_rtv(request, warehouse):
    """
    Create a Return to Vendor (RTV) based on the request data.

    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (Warehouse): The warehouse object.

    Returns:
        JsonResponse: The response containing the created RTV details or an error message.

    Raises:
        Exception: If there is an error while creating the RTV.

    """
    try:
        request_data = loads(request.body)
    except Exception:
        return JsonResponse({'errors': [INVALID_PAYLOAD_CONST]}, status=400)

    log.info('Request params for Create RTV' + warehouse.username + ' is ' + str(request_data))
    items = request_data.get('data', [])
    data_list = []
    if not items:
        return JsonResponse({'errors': ['Items are mandatory for create RTV!']}, status=400)

    sku_codes = get_sku_codes_from_items(items)
    
    ## validate if putaway is completed against the given GRN
    status, seller_po_summary_objs = validate_grn_status(request_data)
    if status:
        return JsonResponse({'errors': [status]}, status=400)
    
    ### add sps id to the request data
    misc_dict, _, return_type = get_rtv_configurations(warehouse, request_data)
    status, request_data = validate_and_add_sps_id(request_data, seller_po_summary_objs, misc_dict)
    if status:
        return JsonResponse({'errors': [status]}, status=400)
    
    
    picklist_enabled, send_mail = validate_rtv_configs(request_data, misc_dict)
    
    try:
        show_data_invoice, rtv_number = {}, ""
        extra_fields = {}
        po_wise_rtv_qty_dict = {}
        rtv_user_attributes = [rtv_attr.get('attribute_name') for rtv_attr in get_user_attributes(warehouse, 'rtv')]
        rtv_extra_fields = request_data.get('extra_fields', {})
        for field_key, value in rtv_extra_fields.items():
            if field_key.startswith('attr_'):
                field_key = field_key.split('attr_')[1]
            if field_key in rtv_user_attributes:
                extra_fields[field_key] = value
        with transaction.atomic('default'):
            data_list, status = prepare_rtv_json_data(warehouse, request_data, sku_codes=sku_codes,extra_fields=extra_fields,misc_dict=misc_dict)
            if status:
                if isinstance(status, list):
                    return JsonResponse({'errors': status}, status=400)
                return JsonResponse({'errors': [status]}, status=400)

            if data_list:
                data_list,rtv_number = save_update_rtv(data_list, warehouse=warehouse, picklist_enabled=picklist_enabled,sku_codes=sku_codes)

                invoice_number= ''
                reference_ids = [final_dict['rtv_id'] for final_dict in data_list]

                #get stocks
                stock_df = get_rtv_stocks(warehouse, reference_ids=reference_ids)
                for final_dict in data_list:
                    invoice_number, rtv_number = create_update_rtv(warehouse, final_dict, picklist_enabled, return_type, invoice_number, rtv_number, stock_df, po_wise_rtv_qty_dict)
                # Update received qty to re-open PO for RTV quantity
                reopen_po_for_rtv_qty(po_wise_rtv_qty_dict, misc_dict, warehouse.id)

                rtv_ids = [each['rtv_id'] for each in data_list]
                saved_serial_data = TempJson.objects.filter(model_id__in=rtv_ids, model_name='RTV')
                if saved_serial_data.exists():
                    saved_serial_data.delete()

                #saving extra fields
                extra_fields = request_data.get('extra_fields',{})
                save_grn_extra_fields(warehouse,rtv_number, extra_fields,transaction="rtv")
                show_data_invoice = {}
                if not picklist_enabled:
                    show_data_invoice = get_debit_note_data(request, rtv_number, warehouse)
                else:
                    generate_rtv_picklist(request, warehouse, data_list)

                if send_mail and show_data_invoice:
                    send_rtv_mail(request, warehouse, invoice_number, show_data_invoice)

        if not stock_df.empty:
            update_rtv_stock_quantity(stock_df)
        if rtv_number and not show_data_invoice:
            return JsonResponse({'message': 'RTV created!', 'rtv_number': rtv_number}, status=200)
        elif rtv_number:
            rendered_data = post_rtv_creation(request, warehouse, rtv_number, sku_codes, show_data_invoice, misc_dict)

            #Inventory Callback
            inv_filters = get_sku_zones_dict_for_inventory_callback(data_list, sku_codes)
            webhook_integration_3p(warehouse.id, "rtv_creation", inv_filters)

            return JsonResponse({"data":str(rendered_data),"rtv_number":rtv_number}, status=200)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info("Exception raised while creating RTV for user %s and request data is %s and error is %s" %
                 (str(warehouse.username), str(request.POST.dict()), str(e)))
        return JsonResponse({"errors": ["Create RTV Failed"]}, status=500)
    return JsonResponse({"errors": ["Missing required data"]}, status=400)

def get_sku_zones_dict_for_inventory_callback(data_list, sku_codes):
    sku_zones_dict = defaultdict(list)
    for item in data_list:
        sku_code = item.get('sku_code')
        location = item.get('location')
        if location and location.zone:
            zone_id = location.zone.id
            sku_zones_dict[zone_id].append(sku_code)

    filters = {'sku_codes': sku_codes, 'sku_zones_dict': dict(sku_zones_dict)}
    return filters


def validate_grn_status(request_data):
    """
    Validates the GRN status for RTV creation for each GRN number in the list without querying in a loop.
    Args:
        request_data (dict): The request data containing the list of GRN numbers.
    Returns:
        tuple: A tuple containing the error message (if any) and the filtered SellerPOSummary queryset.
            If all GRN numbers are valid, the error message will be False.
    """

    grn_numbers = [i.get('grn_number') for i in request_data.get('data',[]) if i.get('grn_number') is not None]
    if not grn_numbers:
        return 'RTV Creation Failed, GRN Numbers are Mandatory, Please Rectify and Retry', []

    # Query once for all GRN numbers
    seller_po_summary = SellerPOSummary.objects.filter(
        Q(grn_number__in=grn_numbers) | Q(grn_reference__in=grn_numbers)
    )

    if not seller_po_summary.exists():
        return 'RTV Creation Failed, Given GRN Numbers Not Found, Please Rectify and Retry', []

    # Filter the queryset for GRN numbers with status = 0
    valid_seller_po_summary = seller_po_summary.filter(status=0)
    valid_grn_numbers = set(valid_seller_po_summary.values_list('grn_number', flat=True)) | \
                        set(valid_seller_po_summary.values_list('grn_reference', flat=True))

    # Check if all provided GRN numbers are in the valid set
    for grn_number in grn_numbers:
        if grn_number not in valid_grn_numbers:
            return f'RTV Creation Failed, For the Given GRN Number {grn_number} Putaway is Still Pending or Not Found, Please Rectify and Retry', []

    return False, valid_seller_po_summary

def validate_and_add_sps_id(request_data, seller_po_summary_objs, misc_dict):
    """
    Validates and adds the summary ID to the request data based on the seller PO summary objects.

    Args:
        request_data (dict): The request data containing the items.
        seller_po_summary_objs (QuerySet): The seller PO summary objects.

    Returns:
        tuple: A tuple containing the error message (if any) and the updated request data.

    """
    items = request_data.get('data', [])
    seller_po_summary_objs_values = []
    seller_po_summary_id_dict = {}
    error_list = []
    error =  False
    eff_rate_in_rtv = misc_dict.get('eff_rate_in_rtv', 'false')
    if seller_po_summary_objs:
        seller_po_summary_objs_values = seller_po_summary_objs.values('grn_number','grn_reference','id', 'sku__sku_code', 'batch_detail__batch_no', 'price', 
                                                                      'batch_detail__mrp','purchase_order__pcf','asn__eff_rate')
        for item in seller_po_summary_objs_values:
            grn_number = item['grn_number'] if item['grn_number'] else item['grn_reference']
            sku_code = item['sku__sku_code']
            batch_no = item['batch_detail__batch_no'] if item['batch_detail__batch_no'] else ''
            price = item['price'] if item['price'] else 0
            pcf = item['purchase_order__pcf']
            if eff_rate_in_rtv == 'true':
                price = item['asn__eff_rate'] if item.get('asn__eff_rate') else 0
            seller_po_summary_id_dict[(grn_number,sku_code, batch_no, price)] = (item['id'],pcf)

    for item in items:
        sku_code = item.get('sku_code', '')
        batch_no = item.get('batch_number') if item.get('batch_number') else ''
        price = item.get('price') if item.get('price') else 0
        if not item.get('summary_id'):
            if (sku_code, batch_no, price) not in seller_po_summary_id_dict:
                error_list.append({'sku_code': item.get('sku_code', ''), 'batch_no': item.get('batch_number'), 'price': item.get('price')})
            else:
                item['summary_id'] = seller_po_summary_id_dict[(item.get('grn_number'),sku_code, batch_no, price)][0]
        pcf = seller_po_summary_id_dict.get((item.get('grn_number'),sku_code, batch_no, price))[1]
        if misc_dict.get('enable_purchase_uom') == 'true':
            item['uom_return_quantity'] = item.get('return_quantity', 0) * pcf

    request_data.update({'items': items})
            
    if error_list:
        error = "RTV Creation Failed, Given SKU,Batch details- " +str(error_list)+ " not found in GRN"

    return error, request_data

def prepare_picklist_details(data_list):
    '''
    Preparing picklist items
    '''
    picklist_data = {}
    for item in data_list:
        if item.get('reserved_quantity', 0) > 0 and item.get('rtv_id'):
            rtv_number = item.get('rtv_number')
            data_dict = {
                'id': item.get('rtv_id'),
                'sku_id': item.get('sku_id'),
                'sku_code': item.get('sku_code', ''),
                'reference_model': 'ReturnToVendor',
                'order_type': 'rtv',
                'original_quantity': item.get('reserved_quantity', 0.0),
                'quantity': item.get('reserved_quantity', 0.0),
                'location': item.get('location_name', ''),
                'batch_number': item.get('batch_number', ''),
                'zone': item.get('zone', '')
            }
            if not picklist_data.get(rtv_number):
                picklist_data[rtv_number] = {
                    'reference_number': item.get('rtv_number'),
                    'reference_type': 'RTV',
                    'items': []
                }
            picklist_data[rtv_number]['items'].append(data_dict)
    picklist_items = list(picklist_data.values())
    return picklist_items

def rtv_picklist_confirmation(request, warehouse, picklist_data):
    '''
    RTV picklist confirmation process
    '''
    try:
        rtv_df, rtv_number = get_rtv_details(warehouse, picklist_data)
        if rtv_df.empty:
            log.info('RTV records are not availble for user%s and picklist data- %s'%(str(warehouse.username), str(picklist_data)))
            return
        update_picklist_confirmation_data(warehouse, rtv_df, picklist_data)
        # try:
        #     rtv_3p_integration(request, warehouse, rtv_number)
        # except Exception as e:
        #     import traceback
        #     log.debug(traceback.format_exc())
        #     log.info(("Webhook failed for %s in rtv and params are %s and error statement is %s") % (str(warehouse.username), str(picklist_data), str(e)))
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info("RTV Picklist confirmation process failed with error- %s" % str(e))

def get_rtv_details(user, picklist_data):
    '''
    Get rtv objects using picklist data for update
    '''
    reference_ids, reference_numbers, rtv_number = [], [], ''
    for picklist_record in picklist_data:
        reference_id = picklist_record.get('reference_id', '')
        rtv_number = reference_number = picklist_record.get('reference_number', '')

        #reference id
        if reference_id:
            reference_id = int(reference_id)
        picklist_record['reference_id'] = reference_id
        if reference_id and reference_id not in reference_ids:
            reference_ids.append(reference_id)
        if reference_number and reference_number not in reference_numbers:
            reference_numbers.append(reference_number)

    rtv_df = rtv_objects(user, reference_ids, reference_numbers)
    return rtv_df, rtv_number

def rtv_objects(user, reference_ids, reference_numbers):
    '''
    Get RTV objects
    '''
    rtv_obs = ReturnToVendor.objects.filter(location__zone__user=user.id, id__in=reference_ids, rtv_number__in=reference_numbers)
    rtv_df = pd.DataFrame(rtv_obs.values())
    rtv_df['object'] = list(rtv_obs)
    return rtv_df

def update_picklist_confirmation_data(warehouse, rtv_df, picklist_data):
    '''
    Update picklist confirmation data
    '''
    wip_stock_details = {}
    for picklist_record in picklist_data:
        reference_id = picklist_record.get('reference_id')
        batch_id = picklist_record.get('batch_id')
        reference_number = picklist_record.get('reference_number')
        quantity = picklist_record.get('quantity', 0)
        if quantity < 0:
            continue

        stock_unique_key = (reference_id, batch_id)
        if not wip_stock_details.get(stock_unique_key):
            wip_stock_details[stock_unique_key] = picklist_record
        else:
            wip_stock_details[stock_unique_key]['quantity'] += quantity

        for index, record_df in rtv_df.loc[(rtv_df['id'] == reference_id) & (rtv_df['rtv_number'] == reference_number)].iterrows():
            min_qty = min(record_df['reserved_quantity'], quantity)

            quantity -= min_qty
            record_df['object'].reserved_quantity -= min_qty
            if quantity <= 0:
                break

        if quantity > 0:
            log.info('Picklist and RTV quantity are not matching for user %s - %s'%(str(warehouse.username), str(picklist_record)))
            return

    update_rtv_objects(rtv_df)
    create_stock_in_wip_location(warehouse, wip_stock_details)

def update_rtv_objects(rtv_df):
    '''
    Update rtv objects
    '''
    if rtv_df['object'].tolist():
        ReturnToVendor.objects.bulk_update_with_rounding(rtv_df['object'].tolist(), ['reserved_quantity'])

def create_stock_in_wip_location(warehouse, picklist_data):
    '''
    Create stock in wip location
    '''
    location_objs = list(LocationMaster.objects.filter(zone__user=warehouse.id, status=1, zone__zone = 'WIPZONE', location = 'WIPLocation').\
                    order_by('fill_sequence'))
    if not location_objs:
        location_objs = create_default_zones(warehouse, 'WIPZONE', 'WIPLocation', 99999, 'non_sellable', 'wip_area')

    stock_detail_list = []
    account_id = warehouse.userprofile.id
    if location_objs:
        location_obj = location_objs[0]
        for picklist_record in picklist_data.values():
            stock_detail_list.append(StockDetail(**{
                'receipt_number': picklist_record.get('reference_id'),
                'receipt_type': 'rtv_picking',
                'sku_id': picklist_record.get('sku_id'),
                'batch_detail_id': picklist_record.get('batch_id'),
                'quantity': picklist_record.get('quantity'),
                'original_quantity': picklist_record.get('quantity'),
                'grn_number': picklist_record.get('reference_number'),
                'status': picklist_record.get('status', 1),
                'location_id': location_obj.id,
                'receipt_date': datetime.datetime.now(),
                'account_id': account_id
            }))
    if stock_detail_list:
        StockDetail.objects.bulk_create_with_rounding(stock_detail_list, batch_size=250)

def get_rtv_stocks(warehouse, reference_ids=[]):
    '''
    Get rtv stocks
    '''
    stock_objects = StockDetail.objects.filter(sku__user=warehouse.id, receipt_number__in=reference_ids, receipt_type__in=['rtv_picking', 'rtv_dispense'])
    stock_df = pd.DataFrame(stock_objects.values('receipt_number', 'receipt_type', 'sku_id', 'grn_number', 'quantity'))
    stock_df['object'] = list(stock_objects)
    return stock_df

def prepare_rtv_confirmation_stock_data(reference_id, rtv_quantity, stock_df):
    if not stock_df.empty:
        for index, stock_record in stock_df.loc[stock_df['receipt_number'] == reference_id].iterrows():
            min_qty = min(stock_record['quantity'], rtv_quantity)

            stock_record['quantity'] -= min_qty
            stock_record['object'].quantity -= min_qty
            rtv_quantity -= min_qty

            if rtv_quantity <= 0:
                break
    return stock_df

def update_rtv_stock_quantity(stock_df):
    '''
    Update rtv stock quantity
    '''
    if stock_df['object'].tolist():
        StockDetail.objects.bulk_update_with_rounding(stock_df['object'].tolist(), ['quantity'])

@get_warehouse
def get_debit_note_print(request, warehouse=''):
    '''
    Get debit note print
    '''
    rtv_number = request.GET.get('rtv_number',"")
    rtv_ref_num= ReturnToVendor.objects.filter(seller_po_summary__purchase_order__open_po__sku__user=warehouse.id,rtv_reference=rtv_number).values_list('rtv_number',flat=True)
    if rtv_ref_num:
        rtv_number = rtv_ref_num[0]
    else:
        return JsonResponse({'message': "No Data Found"}, status=400)
    # Get debit note from master doc
    debit_note_urls = get_debit_note_from_masterdoc(request, warehouse, rtv_number)
    if debit_note_urls:
        return JsonResponse({'debit_note_urls': debit_note_urls}, status=200) # Return the debit note urls
    #If encoded debit note is not present get the data from the database
    show_data_invoice = get_debit_note_data(request, rtv_number, warehouse)
    invoice_format = get_misc_value('DEBIT NOTE', warehouse.id)
    # Get the Invoice Form if not render the default template
    debit_note_data = InvoiceForms.objects.filter(document_type="DEBIT NOTE", invoice_format=invoice_format, status=1).values('output_data')
    if debit_note_data.exists():
        rendered_data = get_rendered_invoice_data(debit_note_data[0].get('output_data'), {"show_data_invoice": [show_data_invoice]})
    else:
        t = loader.get_template('templates/toggle/debit_note.html')
        rendered_data = t.render({'show_data_invoice' : [show_data_invoice]})
    return JsonResponse({"data":rendered_data})

def get_debit_note_from_masterdoc(request,warehouse, rtv_number):
    '''
        Get debit note from master doc
    '''
    rtv_url_object = list(MasterDocs.objects.filter(master_id=rtv_number , user_id=warehouse.id, master_type='debit_note'))
    debit_urls = []
    for rtv_obj in rtv_url_object:
        rtv_doc = rtv_obj.document_url
        if not rtv_doc:
            full_path_url = request.META.get('HTTP_REFERER')#request.build_absolute_uri('/')
            if rtv_obj.uploaded_file:
                if settings.CLOUD_STORAGE:
                    full_path_url = rtv_obj.uploaded_file.url
                else:
                    full_path_url = full_path_url + rtv_obj.uploaded_file.name
                debit_urls.append( full_path_url or '')
        else:
            debit_urls.append(rtv_doc)
    return debit_urls

def save_encoded_pdf_convert(warehouse,reference):
    '''This method is used to prepare and save invoice pdf'''
    try:
        # Assuming you have the normal PDF file named "inv001.pdf"
        # replace / with -
        reference = reference.replace("/", "-")
        normal_pdf_path = f"{reference}{warehouse.id}.pdf"

        # Read the content of the PDF file into memory
        with open(normal_pdf_path, "rb") as f:
            file_content = io.BytesIO(f.read())

        # Create an InMemoryUploadedFile object
        uploaded_file = InMemoryUploadedFile(
            file=file_content,
            field_name=None,  # Field name from form, if applicable
            name=normal_pdf_path,  # Desired filename
            content_type="application/pdf",
            size=len(file_content.getvalue()),
            charset=None  # Character encoding, if applicable
        )
        os.remove(normal_pdf_path)
        return True, uploaded_file
    except Exception as e:
        os.remove(normal_pdf_path)
        log.info('Error uploading PDF file for invoice %s and error statement is %s for %s' % (reference, str(e), str(warehouse.username)))
        return False,''

def write_encoded_data(warehouse,reference,encoded_string):
    '''This method is used to write encoded data to pdf'''
    try:
        reference = reference.replace("/", "-")
        # Decode the base64 encoded string
        decoded_data = base64.b64decode(encoded_string)
        with open(f"{reference}{warehouse.id}.pdf", 'wb') as f: # Write the decoded data to a PDF file
            f.write(decoded_data)
        return True
    except Exception as e:
        log.info('Error decoding or writing PDF file for invoice %s and error statement is %s' % (reference, str(e)))
        return False      

def create_master_docs_dict(warehouse,master_id,master_type,extra_flag,document_url='',uploaded_file='',master_docs_list=[]):
    ''' 
    This method is used to create master docs dict
    '''
    data_dict = {
        'master_id': master_id,
        'master_type': master_type,
        'extra_flag': extra_flag,
        'document_url': document_url,
        'user_id': warehouse.id,
        'account_id': warehouse.userprofile.id
    }
    master_docs_obj= MasterDocs(**data_dict)
    master_docs_obj.uploaded_file = uploaded_file
    master_docs_list.append(master_docs_obj)
    return master_docs_list

def create_master_doc(master_docs_list):
    try:
        if master_docs_list:
            MasterDocs.objects.bulk_create_with_rounding(master_docs_list)
        return True
    except:
        return False


def update_encoded_debit(warehouse,encoded_string='',debit_note_url='',rtv_number='',rtv_reference='',master_docs_list =[]):
    '''
    This method is used to update encoded debit note
    encoded_string : base64 encoded string
    debit_note_url : debit note url
    rtv_number : rtv number
    rtv_reference : rtv reference
    '''
    if encoded_string:
        # Decode the base64 encoded string
        encoded_status = write_encoded_data(warehouse,rtv_number,encoded_string)
        if not encoded_status:
            return "Error decoding or writing PDF file for RTV"+ rtv_number , master_docs_list
    if debit_note_url or encoded_string:
        # Save the encoded PDF file
        encoded_status,uploaded_file= save_encoded_pdf_convert(warehouse, rtv_number)
        # Save the encoded PDF file in MasterDocs
        if encoded_status:
            master_docs_list = create_master_docs_dict(warehouse,rtv_number,'debit_note',rtv_reference,debit_note_url,uploaded_file,master_docs_list)
        else:
            return "Error decoding or writing PDF file for RTV"+ rtv_number,master_docs_list
    return "",master_docs_list