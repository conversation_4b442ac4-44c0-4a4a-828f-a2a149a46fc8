import datetime
import pytz
from dateutil import parser
import traceback
import json

#django imports
from django.db.models import Count, F
from django.core.cache import cache
from django.db import transaction
from django.http import JsonResponse 

from inbound.models import ASNSummary, PurchaseOrder, SKUSupplier, OpenPO
from inventory.models import BatchDetail
from core.models import Form<PERSON>ieldValue, TempJson, MiscDetailOptions, GatePassItem
from django.contrib.contenttypes.models import ContentType

from core_operations.views.common.main import (
    get_user_prefix_incremental, WMSListView, get_warehouse,
    get_user_time_zone, get_local_date_known_timezone, 
    scroll_data, generate_log_message, get_decimal_value, truncate_float,
    get_user_ip, get_multiple_misc_values, get_user_attributes,
    create_or_update_master_attributes, get_extra_attributes
    )
from inventory.views.locator.stock_detail import get_or_create_batch, modify_batch_characters
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from core_operations.views.common.user_attributes import get_sku_type_wise_checklist_details

from .common import get_asn_numbers_list, save_grn_extra_fields, create_file_po_grn_mapping
from .integrations import asn_call_back_3p_integration, send_asn_call_back
from .validation import POGRNValidation, duplicate_invoice_number_check
from core_operations.serializers.main import get_serializer_error_message

from inbound.views.common.common import get_asn_data_with_asn_number
from inbound.views.common.constants import SLASH_DATE_FORMAT, INVALID_PAYLOAD_CONST, DEFAULT_DATETIME_FORMAT
from inbound.serializers.grn import GRNSerializer
from wms_base.models import User, StaffWarehouseMapping, StaffMaster
from core_operations.views.services.approval_service import ApprovalService
from core_operations.views.services.packing_service import PackingService
from inbound.views.purchase_order.common import get_asn_grn_custom_attributes_value

from wms_base.wms_utils import init_logger, APPROVAL_CONFIG_DATA
from wms_base.middleware.current_user import CurrentUserMiddleware
from django.db.models import Min
from django.utils import timezone

from wms_base.sendgrid_mail import send_sendgrid_mail

today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/asn' + today + '.log')
log_err = init_logger('logs/asn.log')


class GetASNSet(WMSListView):
    def get_queryset(self, args, kwargs, warehouse=None, sku_type='FG'):
        return None
    
    def get_header_asn_status(self, asn_status_list):
        asn_status_dict = {
            0 : "Received",
            1 : "In Transit",
            2 : "Partially Received",
            3 : "Cancelled",
            5 : "Draft",
            6 : "Pending For Approval"
        }
        asn_status = "Partially Received"
        unique_statuses = set(asn_status_list)
        if unique_statuses == {3, 5}:
            asn_status = "Draft"
        elif unique_statuses == {0, 3}:
            asn_status = "Received"
        elif unique_statuses == {3, 1}:
            asn_status = "In Transit"
        elif len(set(asn_status_list)) == 1:
            asn_status = asn_status_dict.get(asn_status_list[0]) 
        return asn_status

    def get_sku_wise_asn_data(self, final_data_list):
        asn_data_dict = {}
        asn_status_dict = {}
        asn_number_list = []
        date_key_dict = {'creation_date': 'created_at', 'updation_date': 'updated_at', 'asn_creation_date': 'asn_creation_date'}
        asn_ids = []
        for each_row in final_data_list:
            asn_number_list.append(each_row['asn_number'])
            asn_ids.append(each_row['asn_id'])
            if each_row['asn_number'] in asn_data_dict:
                asn_data_dict[each_row['asn_number']].append(each_row)
                asn_status_dict[each_row['asn_number']].append(each_row.get('asn_status'))
            else:
                asn_data_dict[each_row['asn_number']] = [each_row]
                asn_status_dict[each_row['asn_number']] = [each_row.get('asn_status')]

        custom_attribute_filter_dict = {'user':self.warehouse.id,
                'receipt_no__in' : asn_number_list}
        custom_attributes_dict = get_asn_grn_custom_attributes_value(custom_attribute_filter_dict)
        
        final_data_list = []
        serial_filter = {'filters': {'reference_type': 'asn', 'transact_id__in': asn_ids}}
        serial_mixin = SerialNumberTransactionMixin(self.warehouse, self.warehouse, serial_filter)
        existing_serials = serial_mixin.get_sntd_details()
        existing_serials_dict = {}
        for data in existing_serials.get('data', []):
            existing_serials_dict.setdefault(data['transact_id'], []).extend(data['serial_numbers'])

        line_extra_data = get_extra_attributes(asn_ids, "asn_line_extra_fields")
        for asn_number, asn_data in asn_data_dict.items():
            asn_dict = {}
            header_asn_data = asn_data[0]
            for header_key in self.header_keys:
                asn_dict[header_key] = header_asn_data[header_key]
            
            line_item_data = []
            for sku_data in asn_data:
                line_item_dict = {}
                for sku_key in self.line_level_keys + self.batch_keys:
                    if sku_key in date_key_dict:
                        line_item_dict[date_key_dict[sku_key]] = sku_data[sku_key]
                    else:
                        line_item_dict[sku_key] = sku_data[sku_key]
                line_item_dict['cancelled_by'] = ''
                line_item_dict['cancelled_on'] = ''
                if line_item_dict.get('asn_status') == 3:
                    line_item_dict['cancelled_by'] = line_item_dict.get('updated_by')
                    line_item_dict['cancelled_on'] = line_item_dict.get('updated_at')
                invoice_quantity = sku_data.get('invoice_quantity') or sku_data.get('line_invoice_quantity', 0)
                invoice_free_quantity = sku_data.get('invoice_free_quantity') or sku_data.get('line_invoice_free_quantity', 0)
                line_item_dict['invoice_quantity'] = invoice_quantity
                line_item_dict['invoice_free_quantity'] = invoice_free_quantity
                line_item_dict.pop('line_invoice_quantity', None)
                line_item_dict.pop('line_invoice_free_quantity', None)
                line_item_dict['batch_display_key'] = line_item_dict.get('batch_reference') or line_item_dict.get('batch_no')
                json_data = line_item_dict.get('json_data', {})
                line_item_dict['lpn_numbers'] = json_data.pop('lpns', [])
                line_item_dict['serial_numbers'] = existing_serials_dict.get(line_item_dict['asn_id'], [])
                line_item_dict['line_extra_fields'] = line_extra_data.get(str(line_item_dict['asn_id']), {})
                line_item_dict.update(**line_item_dict['line_extra_fields'])
                line_item_data.append(line_item_dict)
            asn_dict['items'] = line_item_data
            asn_dict['custom_attributes'] = custom_attributes_dict.get(asn_number, {})
            asn_dict['discount_amount'] = sku_data.get('json_data', {}).get('discount_amount', 0)
            asn_dict['asn_status'] = self.get_header_asn_status(asn_status_dict[asn_number])
            asn_dict['warehouse'] = self.warehouse.username
            discount_amount = asn_dict.get('discount_amount') or 0
            cn_amount = asn_dict.get('cn_amount') or 0
            additional_cost = asn_dict.get('additional_cost') or 0
            tcs = asn_dict.get('tcs') or 0
            asn_dict['total_asn_value'] = truncate_float((asn_dict.get('value_with_tax', 0) - discount_amount - cn_amount + additional_cost + tcs), self.price_decimal_limit)
            final_data_list.append(asn_dict)
        return final_data_list

    def frame_date_filters(self, request_data):
        """
        Frame date filters based on the request data.

        Args:
            request_data (dict): The request data containing the date filter values.

        Returns:
            dict: The date filters dictionary.

        """
        date_filters = {}
        try:
            # Parsing the date filters to convert them to the required format
            filter_date = {}
            date_keys = ['from_date', 'to_date', 'updated_at_gte', 'updated_at_lte', 'asn_creation_date']
            for key in date_keys:
                if request_data.get(key):
                    parsed_date = parser.parse(request_data[key])
                    localized_date = pytz.timezone(self.timezone).localize(parsed_date)
                    filter_date[key] = localized_date.astimezone(pytz.UTC)

        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('GET ASN API Date Filters Failed for {} and params are {} and error statement is {}'.format(
                self.request.user.username, request_data, e))

        filter_map = {
            'from_date': 'creation_date__gt',
            'to_date': 'creation_date__lt',
            'updated_at_gte': 'updation_date__gte',
            'updated_at_lte': 'updation_date__lte',
            'asn_creation_date': 'asn_creation_date__gte'
        }
        # Mapping the filter keys to the actual keys in the request data
        for key, filter_key in filter_map.items():
            if key in filter_date:
                date_filters[filter_key] = filter_date[key]
                request_data.pop(key)

        return date_filters

    def frame_status_filters(self, request_data):
        """
        Frame the status filters based on the request data.

        Args:
            request_data (dict): The request data containing the status filter.

        Returns:
            dict: The status filters dictionary.

        """
        status_filters_mapping = {'In Transit': 1, 'Received': 0, 'Cancelled': 3, 'Draft': 5}
        status_filters = {}
        filter_check = False
        # Mapping the status filters to the actual db status values
        status_filter = request_data.get('status', '') or request_data.get('asn_status', '')
        if status_filter and isinstance(status_filter, list):
            status_filter = status_filter[0]
        if status_filter :
            filter_key = status_filter
            if filter_key in status_filters_mapping:
                status_filters['status'] = status_filters_mapping[filter_key]
                filter_check = True
            elif filter_key == 'Open':
                status_filters['status__in'] = [1, 2]
            request_data.pop('status', '')
            request_data.pop('asn_status', '')
        return status_filters, filter_check

    def get(self, *args, **kwargs):
        """
        Handles GET requests to fetch ASN (Advance Shipping Notice) data.

        Retrieves and processes ASN data based on request parameters, applies scrolling pagination,
        and returns a structured response.

        Returns:
            dict: A dictionary containing ASN data, pagination details, status, and error messages.
        """
        error_status = []
        limit = int(self.request.GET.get("limit", 0))
        request_data = self.request.GET.copy()

        # Set user credentials before fetching data.
        self.set_user_credientials()

        # Retrieve ASN data based on warehouse and request parameters.
        final_data_list = self.get_asn_data(self.warehouse, request_data)

        # Apply scrolling pagination to the data.
        page_info = scroll_data(self.request, final_data_list, limit=limit, request_type="GET")

        # Update response with additional details.
        page_info.update(
            {
                "data": final_data_list,
                "message": "Success",
                "status": 200,
                "error": [{"message": error_status}],
            }
        )

        # Add total count of records to pagination info.
        page_info["page_info"]["total_count"] = len(final_data_list)

        return JsonResponse(page_info, status=200)

    def get_asn_data(self, warehouse, request_data):
        limit = int(request_data.get('limit',0))
        offset = int(request_data.get('offset', 0))
        flat_api =  request_data.get('flat', False)
        header = request_data.get('header', False)
        final_data_list = []
        self.warehouse = warehouse
        decimal_limit = get_decimal_value(self.warehouse.id)
        price_decimal_limit = get_decimal_value(self.warehouse.id, 'price')
        self.price_decimal_limit = price_decimal_limit
        self.timezone = get_user_time_zone(self.warehouse)
        date_filters = self.frame_date_filters(request_data)
        status_filters, filter_check = self.frame_status_filters(request_data)
        message, filter_dict, asn_numbers = get_asn_numbers_list(request_data, self.warehouse, date_filters, status_filters)
        if message:
            return JsonResponse({'error_message' : 'Invalid Filters'}, status=400)
        #Filtering ASN based on header level status
        exclude_dict = {}
        exclude_dict['status__in'] = [8]
        if filter_check:
            if request_data.get('status') == 'Partially Received':
                exclude_dict = {'status':2}
            if 'status' in exclude_dict:
                excluded_status = exclude_dict.pop('status')
                excluded_status_list = [excluded_status, 8]
                exclude_dict['status__in'] = excluded_status_list
            filterd_asn_numbers = ASNSummary.objects.filter(
                asn_number__in=asn_numbers, asn_user=self.warehouse.id
                ).exclude(**exclude_dict).values('asn_number').annotate(status_count=Count('status', distinct=True)).filter(status_count=1).values_list('asn_number', flat=True).distinct()
            if request_data.get('status') == 'Partially Received':
                exclude_dict = {'asn_number__in': filterd_asn_numbers}
            else:
                asn_numbers = filterd_asn_numbers

        limit = limit if limit else len(asn_numbers)
        asn_numbers = asn_numbers[offset:limit+offset]

        if not asn_numbers:
            return []

        #Fetching ASN Details
        self.header_keys = [
            'asn_number', 'po_number', 'po_reference',
            'invoice_number', 'invoice_date',
            'supplier_id', 'supplier_name',
            'value_with_tax', 'value_without_tax',
            'supplier_gst_number', 'invoice_value',
            'tcs', 'discount_amount', 'cn_amount',
            'additional_cost', 'po_type',
            'asn_reference'
            ]
        self.line_level_keys = [
            'id', 'asn_id', 'sku_code', 'sku_desc', 'batch_based', 'original_quantity', 'receive_quantity',
            'rejected_quantity', 'accepted_quantity', 'json_data', 'asn_status', 'price', 'po_line_reference',
            'created_by', 'updated_by','creation_date', 'updation_date', 'remarks', 'reason',
            'scheduled_percent', 'scheduled_amount', 'cash_discount_percent', 'cash_discount_amount', 'tax_amt',
            'net_amount', 'margin', 'additional_margin', 'gatekeeper_margin', 'gross_amount', 'tcs_value', 'eff_rate',
            'scanned_qr_lpns', 'po_number', 'po_reference', 'asn_creation_date','po_uom'
            ]
        self.batch_keys = ['batch_no', 'batch_reference', 'manufactured_date', 'expiry_date', 
            'mrp', 'vendor_batch_number', 'retest_date', 'reevaluation_date',
            'inspection_lot_number', 'best_before_date', 'invoice_quantity', 'invoice_free_quantity',
            'line_invoice_quantity', 'line_invoice_free_quantity',
            ]
        date_keys = [
            'manufactured_date', 'expiry_date', 'retest_date', 'reevaluation_date', 'best_before_date',
            'creation_date', 'updation_date', 'asn_creation_date', 'invoice_date'
            ]
        
        values_list = self.header_keys
        if not header:
            values_list = self.header_keys + self.line_level_keys + self.batch_keys
        
        asn_extra_data = {
            'return_type': 'values_list', 
            'value_key_list' : values_list,
            'filters' : filter_dict,
            'exclude_dict': exclude_dict
            }
        
        asn_data_list = get_asn_data_with_asn_number(asn_numbers, self.warehouse, asn_extra_data)
        timezone = get_user_time_zone(self.warehouse)
        quantity_keys = ['invoice_quantity', 'line_invoice_quantity', 'invoice_free_quantity', 'line_invoice_free_quantity', 'original_quantity', 'accepted_quantity']
        price_keys = ['scheduled_percent', 'scheduled_amount', 'cash_discount_percent', 'cash_discount_amount', 'tax_amt',
                      'net_amount', 'margin', 'additional_margin', 'gatekeeper_margin', 'gross_amount', 'tcs_value', 'eff_rate',
                      'price', 'value_without_tax', 'value_with_tax', 'mrp'
                      ]
        if header:
            final_data_list = asn_data_list
        else:
            final_data_list = []
            for each_asn_data in asn_data_list:
                for line_date_key in date_keys:
                    if each_asn_data[line_date_key]:
                        if line_date_key == 'invoice_date':
                            each_asn_data[line_date_key] = get_local_date_known_timezone(
                                timezone, each_asn_data[line_date_key], send_date=True)
                        elif line_date_key in ['creation_date', 'updation_date', 'asn_creation_date']:
                            each_asn_data[line_date_key] = get_local_date_known_timezone(
                                timezone, each_asn_data[line_date_key], send_date=True).strftime(DEFAULT_DATETIME_FORMAT)
                        else:
                            each_asn_data[line_date_key] = get_local_date_known_timezone(
                            timezone, each_asn_data[line_date_key], send_date=True).strftime(SLASH_DATE_FORMAT)

                for quantity_key in quantity_keys:
                    each_asn_data[quantity_key] = truncate_float(each_asn_data[quantity_key], decimal_limit)

                for price_key in price_keys:
                    try:
                        each_asn_data[price_key] = truncate_float(each_asn_data[price_key], price_decimal_limit)
                    except Exception:
                        each_asn_data[price_key] = each_asn_data.get(price_key, 0)
                final_data_list.append(each_asn_data)
            
            if not flat_api:
                final_data_list = self.get_sku_wise_asn_data(final_data_list)
        return final_data_list

def asn_call_back_data(warehouse, request_data):
    """Fetch ASN GET API Data to send asn call back"""
    final_data_list = GetASNSet().get_asn_data(warehouse, request_data)
    return final_data_list

@get_warehouse
def cancel_asn(request, warehouse):
    try:
        asn_number = request.POST['asn_number']
        log.info("Cancel ASN Request Params" + str(request.POST) + " username " + request.user.username)
        message, status, _ = asn_cancellation(asn_number, warehouse, request = request)
        return JsonResponse({"message": message}, status=status)
    except KeyError:
        return JsonResponse({"error": "ASN number is required"}, status=400)
    except Exception as e:
        log.error("Error during ASN cancellation: " + str(e))
        return JsonResponse({"error": "Internal server error"}, status=500)

def get_asn_cancel_reasons(warehouse, misc_type):
    asn_cancel_reasons_dict = dict(MiscDetailOptions.objects.filter(
        misc_detail__user=warehouse.id, misc_detail__misc_type=misc_type, misc_detail__misc_value='true', status=1
        ).values_list('misc_key', 'misc_value'))
    return asn_cancel_reasons_dict

def asn_cancellation(asn_number, warehouse, asn_status = None, asn_id = "", request = None):
    from core_operations.views.integration.integration import webhook_integration_3p

    #ASN Revert is also handled based on the asn_status
    #Cancel_status = 3, Revert_status = 7

    lock_key = f"asn_cancellation_lock_{asn_number}_{warehouse.username}"

    # Check if a cancellation is already in progress
    if cache.get(lock_key):
        return 'ASN cancellation is already in progress', 400, ''
    asn_cancel_reasons_dict = get_asn_cancel_reasons(warehouse,'asn_return_reasons')
    # Acquire lock with a timeout (to auto-release if something goes wrong)
    cache.set(lock_key, True, timeout=60)
    try:
        request_user = CurrentUserMiddleware.get_current_user()
        message = 'NO Open ASN Found to Cancel'
        status = 400
        asn_status = asn_status if asn_status else 3
        filter_dict = {}
        asn_type = ''
        reason = ''
        if request and request.GET:
            reason = request.GET.get('reason', '')
        if asn_id:
            filter_dict['id__in'] = asn_id
            asn_status = 8
        inner_carton_list = []
        sku_codes = []
        asn_orders = ASNSummary.objects.filter(
                asn_user_id=warehouse.id, asn_number = asn_number, **filter_dict)
        if asn_orders.filter(status__in = [2, 0]).exists():
            return 'ASN cannot be cancelled as it is partially received', 400, ''
        
        elif asn_orders.filter(status__in=[1,5,7,6]).exists():
            # Check if ASN is linked to a Gate Pass
            error_message = validate_gate_pass_status_for_asn(warehouse, asn_number)
            if error_message:
                return error_message, 400, ''
            # Process ASN cancellation
            current_timestamp = timezone.now()
            asn_orders = asn_orders.filter(status__in = [1,5,7,6])
            asn_ids = list(asn_orders.values_list('id', flat=True))
            for asn_order in asn_orders:
                purchase_order = asn_order.purchase_order
                asn_quantity = float(purchase_order.saved_quantity) - float(asn_order.quantity)
                purchase_order.saved_quantity= asn_quantity
                asn_free_quantity = asn_order.invoice_free_quantity + asn_order.line_invoice_free_quantity
                asn_free_quantity = max(asn_free_quantity, 0)
                purchase_order_saved_free_quantity = float(purchase_order.saved_free_quantity) - float(asn_free_quantity)
                purchase_order.saved_free_quantity = max(purchase_order_saved_free_quantity, 0)
                purchase_order.save()
                asn_json_data = asn_order.json_data
                if asn_json_data.get('scanned_qr_lpns'):
                    inner_carton_list.extend(asn_json_data.get('scanned_qr_lpns'))
                asn_type = asn_order.asn_type
                sku_codes.append(purchase_order.open_po.sku.sku_code)
            update_fields = {'status': asn_status, 'updation_date': current_timestamp, 'reason': asn_cancel_reasons_dict.get(reason, '')}
            if request_user:
                update_fields['updated_by'] = request_user
            
            asn_orders.update(**update_fields)
            message = 'ASN Cancelled Successfully'
            status = 200
            delete_temp_batch_details(warehouse,  model_ids = asn_ids)
            #cancel grn pending records for GRN
            grn_inspection_objs = TempJson.objects.filter(
                warehouse=warehouse,
                model_name='grn_inspection',
                model_reference=asn_number)
            if grn_inspection_objs.exists():
                grn_inspection_objs.delete()
            if inner_carton_list:
                update_packing_transactional_status(warehouse, asn_number, "asn", inner_carton_list, request)

        #Inventory Callback on ASN Cancellation
        filters = {'sku_codes': sku_codes}
        webhook_integration_3p(warehouse.id, "asn_cancellation", filters)

        update_serial_status(warehouse, asn_number)
        log.info("CancelASN: Warehouse " + warehouse.username + " " +asn_number + " " + message)
        return message, status, asn_type
    
    except Exception as e:
        log.error(f"Error during ASN cancellation: {str(e)}")
        return "ASN Cancellation Failed", 400, ''

    finally:
        # Always release the lock at the end
        cache.delete(lock_key)
        
def validate_gate_pass_status_for_asn(warehouse, asn_number):
    """
        Validates the gate pass status for a given ASN (Advance Shipment Notice) to determine
        whether it can be cancelled based on its associated gate pass records.
        Args:
            warehouse (Warehouse): The warehouse object where the ASN is being processed.
            asn_number (str): The unique identifier of the ASN to validate.
        Returns:
            str: A message indicating why the ASN cannot be cancelled, or None if cancellation is allowed.
    """
    gate_pass_items = GatePassItem.objects.filter(
                gate_pass__warehouse=warehouse.id,
                gate_pass__gate_pass_type='inbound',
                gate_pass__gate_pass_sub_type='material_receipt',
                transaction_id=asn_number
            ).exclude(gate_pass__status=8).exclude(status=2)  # Not cancelled gate pass

    # If gate pass items exist, check their status
    if gate_pass_items.exists():
        # Check if any ASN is marked as "No Show" (GatePassItem status = 1)
        no_show_items = gate_pass_items.filter(status=1)  # not_received = 1
        asn_is_no_show = no_show_items.exists()

        # Get gate passes with restricted statuses
        restricted_gate_passes = gate_pass_items.filter(
            gate_pass__status__in=[0, 1, 3]  # Created, Gate-IN, Unloaded
        )

        # If ASN is not marked as "No Show" and gate pass is in restricted status, prevent cancellation
        if restricted_gate_passes.exists() and not asn_is_no_show:
            gp_statuses = {
                0: "Created",
                1: "Gate-IN",
                3: "Unloaded"
            }
            gp_status_list = [gp_statuses.get(gp.gate_pass.status, "Unknown") for gp in restricted_gate_passes]
            
            return f'ASN cannot be cancelled as it is linked to a Gate Pass with status: {", ".join(gp_status_list)}'

        # If ASN is marked as "No Show" but gate pass is not in Gate-OUT status, prevent cancellation
        gate_out_passes = gate_pass_items.filter(gate_pass__status=2)  # Gate-OUT
        if asn_is_no_show and not gate_out_passes.exists():
            return 'ASN marked as No Show can only be cancelled after Gate Pass is in Gate-OUT status'

def update_serial_status(warehouse, asn_number):
    """Update serial number transaction status after asn cancellation """
    serial_filter = {'filters':{'reference_type': 'asn', 'reference_number': asn_number, 'status': 1}}
    serial_mixin = SerialNumberTransactionMixin(warehouse, warehouse, serial_filter)
    existing_serials = serial_mixin.get_sntd_details()
    items_data = existing_serials.get('data', [])
    if not items_data:
        return
    for each_data in items_data:
        each_data['status'] = 3
        each_data['serial_status'] = 0
    sn_transact_dict = {
        'reference_number': asn_number,
        'reference_type': 'grn',
        'items': existing_serials.get('data')
    }
    SerialNumberTransactionMixin(None, warehouse, sn_transact_dict).create_update_sn_transaction()

def update_packing_transactional_status(warehouse, transaction_number, transaction_type, lpn_numbers, request):
    request_dict = {
        "request_headers": request.headers,
        "request_meta": request.META,
        "request_scheme": request.scheme,
    }
    packing_service_instance = PackingService(request_dict, request.user, warehouse)
    params = {
            "warehouse": warehouse.username,
            "transaction_number": str(transaction_number),
            "transaction_type": transaction_type,
            "lpn_numbers": lpn_numbers
        }
    status = "cancelled"
    packing_details, packing_service_errors = packing_service_instance.update_packing_status(params, status)

class UpdateASN(WMSListView):
    def get_asn_status(self):
        asn_status = 1
        if self.grn_type == "ASNSave":
            asn_status = 5 
        elif self.asn_approval_flow:
            asn_status = 6
        return asn_status
    
    def send_callback(self, status, asn_number):
        try:
            if status and self.grn_type != "ASNSave":
                asn_call_back_3p_integration(self.warehouse, asn_number)
        except Exception as e:
            log.info(generate_log_message("ASNCallbackFailure",
                                            warehouse=self.warehouse.username,
                                            asn_number=asn_number,
                                            error=str(e)))
    
    def get_quantities(self):
        self.grn_quantity = self.each_row.get("quantity", 0)
        self.rejected_quantity = self.each_row.get("return_quantity", 0)
        if not self.rejected_quantity:
            self.rejected_quantity = self.each_row.get("rejected_quantity", 0)
        self.accepted_quantity = self.each_row.get("accepted_quantity", 0)
    
    def update_asn_object(self, asn_obj, asn_batch_obj):
        #Fetching PutZone To Override SKU PutZone
        asn_json_data = asn_obj.json_data if asn_obj.json_data else {}
        asn_batch_obj = asn_batch_obj or None
        if self.each_row.get('put_zone'):
            asn_json_data.update({"put_zone": self.each_row["put_zone"]})
            asn_obj.json_data = asn_json_data
            self.asn_update_keys.append('json_data')

        #Updating Json Data which has No.of Containers too
        if self.each_row.get('json_data'):
            asn_json_data.update(self.each_row.get('json_data'))
            asn_obj.json_data = asn_json_data
            self.asn_update_keys.append('json_data')

        if self.each_row.get('remarks'):
            asn_obj.reason = self.each_row.get('remarks')
            self.asn_update_keys.append('reason')

        batch_dict = self.each_row.get("batch_detail", {})
        if self.each_row.get("batch_based", False) and self.grn_type != "ASNSave":
            batch_dict["receipt_number"] = asn_obj.receipt_number
            
            #Create or Update BatchDetail
            if not asn_batch_obj:
                error_list, asn_batch_obj = get_or_create_batch(
                            self.warehouse, batch_dict, draft=True)
                if error_list:
                    raise ValueError(error_list)
        
        retest_date = self.each_row.get('batch_detail').get('retest_date')
        if retest_date and asn_batch_obj:
            asn_batch_obj.retest_date = retest_date
            self.batch_update_keys.append('retest_date')

        asn_obj.batch_detail= asn_batch_obj
        self.asn_update_keys.append('batch_detail')
    
        asn_obj.tcs_value = self.additional_dict.get('tcs_value', 0) or 0
        self.asn_update_keys.append('tcs_value')


        #Updating Status
        asn_status = asn_obj.status
        if self.each_row.get('status') and asn_status != self.each_row.get('status'):
            asn_obj.status = self.each_row.get('status')
            self.asn_update_keys.append('status')
            if asn_status == 5 and self.each_row.get('status') == 1:
                asn_obj.asn_type = 'PO'
                self.asn_update_keys.append('asn_type')
        return asn_obj, asn_batch_obj
    
    def replace_asn_object(self, asn_obj, po_obj):
        batch_dict = self.each_row.get("batch_detail", {})
        batch_obj= None
        if self.each_row.get("batch_based", False) and self.grn_type != "ASNSave" and self.allow_batch_creation:
            batch_dict["receipt_number"] = asn_obj.receipt_number
            
            #Create or Update BatchDetail
            error_list, batch_obj = get_or_create_batch(
                        self.warehouse, batch_dict, draft=True)
            if error_list:
                raise ValueError(error_list)

        if not self.grn_quantity:
            self.grn_quantity = self.each_row.get("invoice_quantity", 0) if self.each_row.get("invoice_quantity", 0) else self.each_row.get("line_invoice_quantity", 0)

        self.new_asn_quantity =  po_obj.saved_quantity - (asn_obj.quantity - self.grn_quantity)
        self.new_asn_free_quantity = po_obj.saved_free_quantity - (asn_obj.line_invoice_free_quantity - self.each_row.get('line_invoice_free_quantity', 0))

        #Update Po quantities
        po_obj.saved_quantity = self.new_asn_quantity
        po_obj.saved_free_quantity = self.new_asn_free_quantity
        po_obj.save()

        if self.open_po_obj:
            self.open_po_obj.sgst_tax = self.each_row.get('sgst_tax', 0)
            self.open_po_obj.cgst_tax = self.each_row.get('cgst_tax', 0)
            self.open_po_obj.igst_tax = self.each_row.get('igst_tax', 0)
            self.open_po_obj.cess_tax = self.each_row.get('cess_tax', 0)
            self.open_po_obj.utgst_tax = self.each_row.get('utgst_tax', 0)
            self.open_po_obj.apmc_tax = self.each_row.get('apmc_tax', 0)
            self.open_po_update_keys.extend(['sgst_tax', 'cgst_tax', 'igst_tax', 'cess_tax', 'utgst_tax', 'apmc_tax'])
            self.open_po_update_list.append(self.open_po_obj)

        try:
            asn_obj.asn_type = self.grn_type
            asn_obj.quantity= self.grn_quantity
            asn_obj.original_quantity= self.grn_quantity
            asn_obj.accepted_quantity = self.accepted_quantity
            asn_obj.rejected_quantity = self.rejected_quantity
            asn_obj.discount_percent= self.each_row.get("discount_percent", 0)
                
            asn_obj.invoice_number= self.each_row.get("invoice_number", None)
            asn_obj.invoice_date= self.each_row.get("invoice_date", None)
            asn_obj.round_off_total= self.each_row.get("round_off_total", 0)
                
            asn_obj.cess_tax= self.each_row.get("cess_tax", 0)
            asn_obj.price= self.each_row.get("price", 0)
            asn_obj.apmc_tax= self.each_row.get("apmc_tax", 0)
            asn_obj.overall_discount= self.each_row.get("overall_discount", 0)
                
            asn_obj.invoice_value= self.each_row.get("invoice_value", 0)
            asn_obj.invoice_quantity= self.each_row.get("invoice_quantity", 0)
            asn_obj.invoice_free_quantity= self.each_row.get("invoice_free_quantity", 0)
            asn_obj.line_invoice_quantity= self.each_row.get("line_invoice_quantity", 0)
            asn_obj.line_invoice_free_quantity= self.each_row.get("line_invoice_free_quantity", 0)
            asn_obj.tax_percent = self.each_row.get("total_tax", 0)
            asn_obj.gross_amount = self.each_row.get("gross_amt", 0)
            asn_obj.scheduled_percent = self.each_row.get("sd_percent", 0)
            asn_obj.scheduled_amount = self.each_row.get("sd_amt", 0)
            asn_obj.net_gross_amt = self.each_row.get("net_gross_amt", 0)
            asn_obj.cash_discount_percent = self.each_row.get("cd_percent", 0)
            asn_obj.cash_discount_amount = self.each_row.get("cd_amt", 0)
            asn_obj.tax_amt = self.each_row.get("tax_amt", 0)
            asn_obj.taxable_amount =  self.each_row.get("taxable_amount", 0)
            asn_obj.net_amount = self.each_row.get("net_amount", 0)
            asn_obj.eff_rate = self.each_row.get("eff_rate", 0)
            asn_obj.margin = self.each_row.get("margin", 0)
            asn_obj.additional_margin = self.each_row.get("additional_margin", 0)
            asn_obj.tcs_value = self.additional_dict.get('tcs_value', 0) or 0
            
            asn_obj.remarks = self.each_row.get("remarks",'')
            asn_obj.reason = self.each_row.get('po_remarks', '')
            asn_obj.batch_detail= batch_obj
            json_data = self.each_row.get('json_data', {})
            json_data.update(**self.additional_dict)
            asn_obj.json_data = json_data 
            status = self.get_asn_status()
            if self.grn_type == "ASNSave" and self.is_reverted:
                status = 7
            asn_obj.status = status

            asn_obj.value_with_tax = self.value_with_tax
            asn_obj.value_without_tax = self.value_without_tax

            if not self.each_row.get('asn_id'):
                asn_obj.account_id = self.warehouse.userprofile.id
                asn_obj.asn_number = self.asn_number
                asn_obj.asn_reference = self.asn_reference
                asn_obj.asn_user = self.warehouse
                asn_obj.source_warehouse_id = self.each_row.get('warehouse_user',None)
                asn_obj.asn_type = self.grn_type
                asn_obj.purchase_order = po_obj
                asn_obj.save()
            batch_dict = prepare_batch_temp_json_data_for_draft_asn(self.grn_type, batch_dict, asn_obj, self.warehouse, self.allow_batch_creation)
            if batch_dict:
                self.temp_batch_list.append(batch_dict)
            self.asn_update_keys.extend([
                'asn_type', 'quantity', 'original_quantity', 'accepted_quantity',
                'rejected_quantity', 'discount_percent', 'invoice_number', 'invoice_date',
                'round_off_total', 'cess_tax', 'price', 'apmc_tax', 'overall_discount',
                'invoice_value', 'invoice_quantity','invoice_free_quantity',
                'line_invoice_quantity', 'line_invoice_free_quantity',
                'remarks', 'reason', 'batch_detail',
                'json_data', 'status', 'value_with_tax', 'value_without_tax',
                'tax_percent', 'gross_amount', 'scheduled_percent', 'scheduled_amount',
                'net_gross_amt', 'cash_discount_percent', 'cash_discount_amount', 
                'tax_amt', 'taxable_amount', 'net_amount', 'eff_rate', 'additional_margin',
                'margin', 'tcs_value'
                ])
        
        except Exception as e:
            log.error(generate_log_message("ReplaceASNObjectFailure",
                                            warehouse=self.warehouse.username,
                                            data=self.each_row,
                                            error=str(e)))
    
        return asn_obj, batch_obj, po_obj

    def update_asn(self, warehouse, data_list, data, grn_extra_dict={}):
        """Update ASN Details"""
        status =True
        self.grn_type = data.get("grn_type", "")
        self.warehouse = warehouse
        self.asn_number = data.get("asn_number", "")
        self.asn_reference = data.get("asn_reference", self.asn_number)
        self.value_with_tax = grn_extra_dict.get('asn_amount_with_tax', 0)
        self.value_without_tax = grn_extra_dict.get('asn_amount', 0)
        self.approval_data, asn_summary_objects_list = [], []
        self.approval_header_data = {"transaction_type": "ASN", "transaction_number": self.asn_number,
                                     "json_data": {}, "line": []}
        self.is_reverted = data.get('is_reverted', False)
        self.misc_dict = CreateASN().misc_data(warehouse)
        self.asn_approval_flow = self.misc_dict.get('asn_approval', '') == 'true'
        self.allow_batch_creation = self.misc_dict.get('allow_asn_creation_without_batch', 'false') == 'false'
        self.asn_ids, self.temp_batch_list = [], []
        asn_approval_status = 'open'
        try:
            asn_update_list, self.asn_update_keys = [], []
            batch_update_list, self.batch_update_keys = [], []
            self.po_update_list, self.po_update_keys = [], []
            self.open_po_update_list, self.open_po_update_keys = [], []
            self.new_skus_to_create = []
            line_id_list, line_extra_list = [], []
            po_ids = [each_row.get('purchase_order_id') for each_row in data_list]
            po_obj_dict = PurchaseOrder.objects.filter(id__in=po_ids).in_bulk()
            asn_obj = None
            with transaction.atomic('default'):
                for self.each_row in data_list:
                    self.additional_dict = {"discount_amount": data.get('discount_amount', 0), 
                                'additional_cost': data.get('additional_cost', 0),
                                'cn_amount': data.get('cn_amount', 0),
                                "gatekeeper_margin": self.each_row.get("gatekeeper_margin", 0),
                                "margin": self.each_row.get("calculated_margin", 0),
                                "mrp_without_tax": self.each_row.get("mrp_without_tax", 0),
                                "tcs_value": data.get('tcs', 0),
                                "sgst_tax": self.each_row.get("sgst_tax", 0),
                                "cgst_tax": self.each_row.get("cgst_tax", 0),
                                "igst_tax": self.each_row.get("igst_tax", 0),
                                "utgst_tax": self.each_row.get("utgst_tax", 0),
                                "cess_tax": self.each_row.get("cess_tax", 0),
                                }
                    if self.each_row.get('asn_id'):
                        asn_objs = ASNSummary.objects.filter(
                        id=self.each_row.get('asn_id'), status__in=[1, 2, 5, 7])
                        asn_obj = asn_objs[0]
                        asn_batch_obj = asn_obj.batch_detail
                        po_obj= asn_obj.purchase_order
                        self.open_po_obj = po_obj.open_po
                    else:
                        asn_obj = ASNSummary()
                        po_obj = po_obj_dict.get(self.each_row.get('purchase_order_id'))
                        po_obj.refresh_from_db()
                        asn_batch_obj = None
                        self.open_po_obj = None
                    self.get_quantities()
                    if data.get('is_replace'):
                        self.replace_asn_object(asn_obj, po_obj)
                    else:
                        self.update_asn_object(asn_obj, asn_batch_obj)

                    asn_obj.updation_date = datetime.datetime.now()
                    self.asn_update_keys.append('updation_date')
                    asn_update_list.append(asn_obj)
                    self.asn_ids.append(asn_obj.id)

                    if asn_batch_obj:
                        asn_batch_obj.updation_date = datetime.datetime.now()
                        self.batch_update_keys.append('updation_date')
                        batch_update_list.append(asn_batch_obj)
                    line_extra_fields = self.each_row.get('line_extra_fields', {})
                    line_extra_dict = {str(asn_obj.id):line_extra_fields}
                    line_id_list.append(str(asn_obj.id))
                    if line_extra_fields:
                        line_extra_list.append(line_extra_dict)
                #Update ASN Line Extra Fields
                if line_extra_list:
                    create_or_update_master_attributes(line_id_list, 'asn_line_extra_fields', line_extra_list, self.warehouse)
                asn_status = self.get_asn_status()
                if asn_obj and asn_status  == 1:
                    create_dock_schedule_entries(self.warehouse, self.misc_dict, grn_extra_dict, asn_obj)
                update_asn_creation_date(asn_update_list, asn_status)
                if asn_update_list and self.asn_update_keys:
                    ASNSummary.objects.bulk_update_with_rounding(asn_update_list, self.asn_update_keys)
                if batch_update_list and self.batch_update_keys:
                    BatchDetail.objects.bulk_update_with_rounding(batch_update_list, self.batch_update_keys)

                if data.get('extra_fields', {}):
                    save_grn_extra_fields(warehouse, self.asn_number, data.get('extra_fields', {}), 'asn')
                if self.open_po_update_list and self.open_po_update_keys:
                    OpenPO.objects.bulk_update_with_rounding(self.open_po_update_list, self.open_po_update_keys)
                ASNSummary.objects.filter(asn_number=self.asn_number).update(asn_type=self.grn_type)
            if self.grn_type != "ASNSave":
                asn_summary_objects_list = ASNSummary.objects.filter(id__in=self.asn_ids)
                approval_data = CreateASN().prepare_approval_data(asn_summary_objects_list, warehouse, self.asn_approval_flow)
                asn_approval_status = CreateASN().create_approval_entry(approval_data, self.approval_header_data, warehouse, data, self.asn_approval_flow)
                if self.allow_batch_creation:
                    delete_temp_batch_details(self.warehouse, model_references = [self.asn_number])
            else:
                create_or_update_temp_batch_details(self.warehouse, self.temp_batch_list)
        except Exception as e:
            log.error(
                generate_log_message("ASNUpdationFailure", 
                                            warehouse=warehouse.username,
                                            data_list=data_list,
                                            asn_number=self.asn_number,
                                            error=str(e))
                                            )
            status = False
        self.send_callback(status, self.asn_number)
        return status, self.asn_number, asn_approval_status


class CreateASN(WMSListView):
    def get_asn_status(self):
        asn_status = 1
        if self.grn_type == "ASNSave":
            asn_status = 5 
        elif self.asn_approval_flow:
            asn_status = 6
        return asn_status
    
    def update_checklist_values(self, asn_checklist_attrs, po_obj, asn_summary):
        checklist_config = asn_checklist_attrs[self.each_grn['sku_type']]['config_name']                
        form_fields = FormFieldValue.objects.filter(field__attribute_model=checklist_config)
        if form_fields.exists():
            form_field_filters = {
                'content_type' : ContentType.objects.get_for_model(po_obj),
                'object_id' : po_obj.id
                }
            update_dict = {
                'content_type' : ContentType.objects.get_for_model(asn_summary),
                'object_id' : asn_summary.id
            }
            asn_form_fields = form_fields.filter(**form_field_filters)
            if asn_form_fields.exists():
                asn_form_fields.update(**update_dict)
    
    def get_quantities(self):
        self.grn_quantity = self.each_grn.get("quantity", 0)
        self.rejected_quantity = self.each_grn.get("return_quantity", 0)
        if not self.rejected_quantity:
            self.rejected_quantity = self.each_grn.get("rejected_quantity", 0)
        self.accepted_quantity = self.each_grn.get("accepted_quantity", 0)
    
    def generate_asn_number(self, header_details):
        self.grn_type = header_details.get("grn_type", "")
        receipt_number = 1
        if self.grn_type in ['PO', 'ASNSave']:
            grn_prefix_val = 'asn_prefix'
            summary = ASNSummary.objects.filter(
                purchase_order__open_po__sku__user= header_details['user'],
                purchase_order__po_number=header_details['po_number']
                ).order_by('-id')
            if summary:
                receipt_number = int(summary[0].receipt_number) + 1
        
        sku_code = header_details.get("sku_code", "")
        dept_code = ''
        _, _, asn_number, _, _ = \
            get_user_prefix_incremental(self.warehouse, grn_prefix_val, sku_code, dept_code=dept_code,create_default='ASN')
        
        return asn_number, receipt_number

    def create_asn_record(self, asn_details):
        if not self.grn_quantity:
            self.grn_quantity = self.each_grn.get("invoice_quantity", 0) if self.each_grn.get("invoice_quantity", 0) else self.each_grn.get("line_invoice_quantity", 0)
        asn_summary = ASNSummary.objects.create(
                receipt_number= self.receipt_number,
                quantity= self.grn_quantity,
                asn_user_id= self.each_grn.get('user',None),
                asn_type = self.grn_type,
                source_warehouse_id = self.each_grn.get('warehouse_user',None),
                original_quantity= self.grn_quantity,
                accepted_quantity = self.accepted_quantity,
                rejected_quantity = self.rejected_quantity,
                purchase_order_id= self.each_grn.get("purchase_order_id", None),
                discount_percent= self.each_grn.get("discount_percent", 0),
                challan_number= self.each_grn.get("challan_number", None),
                challan_date= self.each_grn.get("challan_date", None),
                invoice_number= self.each_grn.get("invoice_number", None),
                order_status_flag= self.each_grn.get("order_status_flag", None),
                invoice_date= self.each_grn.get("invoice_date", None),
                round_off_total= self.each_grn.get("round_off_total", 0),
                cess_tax= self.each_grn.get("cess_tax", 0),
                price= self.each_grn.get("price", 0),
                apmc_tax= self.each_grn.get("apmc_tax", 0),
                overall_discount= self.each_grn.get("overall_discount", 0),
                asn_number= self.asn_number,
                value_with_tax= asn_details.get("value_with_tax", 0),
                value_without_tax= asn_details.get("value_without_tax", 0),
                invoice_value= self.each_grn.get("invoice_value", 0),
                invoice_quantity= self.each_grn.get("invoice_quantity", 0),
                invoice_free_quantity= self.each_grn.get("invoice_free_quantity", 0),
                line_invoice_quantity= self.each_grn.get("line_invoice_quantity", 0),
                line_invoice_free_quantity= self.each_grn.get("line_invoice_free_quantity", 0),
                tax_percent = self.each_grn.get("total_tax", 0),
                gross_amount = self.each_grn.get("gross_amt", 0),
                scheduled_percent = self.each_grn.get("sd_percent", 0),
                scheduled_amount = self.each_grn.get("sd_amt", 0),
                net_gross_amt = self.each_grn.get("net_gross_amt", 0),
                cash_discount_percent = self.each_grn.get("cd_percent", 0),
                cash_discount_amount = self.each_grn.get("cd_amt", 0),
                tax_amt = self.each_grn.get("tax_amt", 0),
                taxable_amount =  self.each_grn.get("taxable_amount", 0),
                net_amount = self.each_grn.get("net_amount", 0),
                eff_rate = self.each_grn.get("eff_rate", 0),
                margin = self.each_grn.get("margin", 0),
                additional_margin = self.each_grn.get("additional_margin", 0),
                
                credit_status= self.each_grn.get("credit_status", None),
                remarks = self.each_grn.get("remarks",''),
                reason = asn_details.get('reason', ''),
                batch_detail= self.batch_obj,
                json_data = asn_details.get('json_data', {}),
                status = self.get_asn_status(),
                account_id = self.warehouse.userprofile.id,
                tcs_value = asn_details.get('tcs_value', 0),
                asn_reference = self.asn_reference
            )
        
        return asn_summary
    
    def get_supplier_gate_keeper_margin(self, asn_summary_objects_list, warehouse):
        supplier_ids = []
        supplier_sku_margin_dict = {}
        for asn_summary in asn_summary_objects_list:
            supplier_ids = [asn_summary.purchase_order.open_po.supplier_id]
        result = (SKUSupplier.objects
              .filter(supplier__id__in=supplier_ids, supplier__user = warehouse.id)
              .annotate(min_preference=Min('preference'))
              .values('sku__sku_code', 'gatekeeper_margin', 'supplier__supplier_id'))
        for each_row in result:
            supplier_sku_margin_dict[(each_row['supplier__supplier_id'], each_row['sku__sku_code'])] = each_row['gatekeeper_margin']
        return supplier_sku_margin_dict
            
    def calculate_tax_inclusive_value(self, value, tax_percent):
        tax_inclusive_value = value * (1 + tax_percent / 100)
        return tax_inclusive_value

    
    def prepare_approval_data(self, asn_summary_objects_list, warehouse, asn_approval_flow):
        approval_data = []
        if asn_approval_flow:
            supplier_sku_margin_dict = self.get_supplier_gate_keeper_margin(asn_summary_objects_list, warehouse)
            for asn_summary in asn_summary_objects_list:
                po_free_quantity = asn_summary.purchase_order.open_po.free_quantity
                po_quantity = asn_summary.purchase_order.open_po.order_quantity
                invoice_free_quantity = asn_summary.invoice_free_quantity or asn_summary.line_invoice_free_quantity
                invoice_quantity = asn_summary.invoice_quantity or asn_summary.line_invoice_quantity
                expiry_date = asn_summary.batch_detail.expiry_date if asn_summary.batch_detail else None
                sku_minimum_shelf_life = asn_summary.purchase_order.open_po.sku.minimum_shelf_life
                current_date = datetime.datetime.now()
                invoice_mrp = asn_summary.batch_detail.mrp if asn_summary.batch_detail else None
                po_mrp = asn_summary.purchase_order.open_po.mrp
                margin = asn_summary.margin
                sku_code = asn_summary.purchase_order.open_po.sku.sku_code
                supplier_margin = supplier_sku_margin_dict.get((asn_summary.purchase_order.open_po.supplier.supplier_id, sku_code), 0)

                
                approval_dict = {
                    'transaction_id' : asn_summary.id,
                    'item_code' : asn_summary.purchase_order.open_po.sku.sku_code,
                    'item_description' : asn_summary.purchase_order.open_po.sku.sku_desc,
                    "json_data": {"purchase_order": asn_summary.purchase_order.po_number}
                }

                if all([po_free_quantity > 0, po_quantity > 0, invoice_free_quantity > 0, invoice_quantity > 0]):
                    invoice_free_quantity_ratio = (invoice_free_quantity / invoice_quantity)
                    po_free_quantity_ratio = (po_free_quantity / po_quantity)
                    approval_dict.update({
                        "invoice_free_quantity_ratio": invoice_free_quantity_ratio,
                        "po_free_quantity_ratio" : po_free_quantity_ratio
                    })

                if any([po_free_quantity == 0, po_quantity == 0, invoice_free_quantity == 0, invoice_quantity == 0]):
                    approval_dict.update({
                        "invoice_free_quantity": invoice_free_quantity,
                        "po_free_quantity" : po_free_quantity
                    })

                if expiry_date and sku_minimum_shelf_life:
                    sku_remaining_shelf_life = (expiry_date - current_date.replace(tzinfo=datetime.timezone.utc)).days
                    sku_minimum_shelf_life = sku_minimum_shelf_life.days
                    approval_dict.update({
                        'sku_remaining_shelf_life': sku_remaining_shelf_life,
                        'sku_minimum_shelf_life': sku_minimum_shelf_life,
                    })
                if invoice_mrp and invoice_mrp not in [None] and po_mrp:
                    approval_dict.update({
                        'invoice_mrp': invoice_mrp,
                        'po_mrp': po_mrp
                    })

                approval_dict.update({
                    'sku_margin': margin,
                    'gatekeeper_margin': float(supplier_margin)
                })

                approval_data.append(approval_dict)
        return approval_data

    def create_approval_entry(self, approval_data, approval_header_data, warehouse, data, asn_approval_flow):
        self.final_approval_data = {}
        asn_status = ''
        if approval_data and approval_header_data and asn_approval_flow:
            approval_header_data['lines'] = approval_data
            self.final_approval_data = {"data": [approval_header_data]}
            request_data = data.get('request_data')
            request_dict = {
                "request_headers": {
                        "Warehouse": request_data.get("warehouse"),
                        "Authorization": request_data.get('headers').get('Authorization')
                    },
                "request_meta": request_data.get('request_meta'),
                
            }
            packing_service_instance = ApprovalService(request_dict, request_data.get("user"), warehouse)
            final_data, _ = packing_service_instance.create_approval(self.final_approval_data)
            if final_data:
                asn_status = final_data[0].get('status')

        return asn_status

    def misc_data(self, warehouse):
        extra_batch_attributes = get_user_attributes(warehouse, 'extra_batch_attributes')
        self.extra_batch_attributes = list(extra_batch_attributes.values_list('attribute_name', flat=True))
        misc_type_list = ["asn_approval", "dock_scheduling", "allow_asn_creation_without_batch"]
        misc_permission_dict = get_multiple_misc_values(misc_type_list , warehouse.id)
        return misc_permission_dict

    def delete_tempjson_data(self, warehouse):
        """Delete TempJson Data"""
        temp_json_data = TempJson.objects.filter(model_name='PO', model_reference__in=self.po_numbers, warehouse=warehouse)
        if temp_json_data.exists():
            temp_json_data.delete()

    def update_open_po_objects(self, open_po_obj, asn_data):
        if open_po_obj:
            open_po_obj.sgst_tax = asn_data.get('sgst_tax', 0)
            open_po_obj.cgst_tax = asn_data.get('cgst_tax', 0)
            open_po_obj.igst_tax = asn_data.get('igst_tax', 0)
            open_po_obj.cess_tax = asn_data.get('cess_tax', 0)
            open_po_obj.utgst_tax = asn_data.get('utgst_tax', 0)
            open_po_obj.apmc_tax = asn_data.get('apmc_tax', 0)
            open_po_obj.save()

    def prepare_serial_item_list(self, asn_obj, batch_dict):
        s_n_transact_status = 4
        lpns = self.each_grn.get('json_data', {}).get('lpns', [])
        serial_numbers = self.each_grn.get('serial_numbers', [])
        if lpns:
            for lpn_data in lpns:
                serial_numbers = lpn_data.get('serial_numbers', [])
                if serial_numbers:
                    self.sn_item_list.append({
                        'transact_id': asn_obj.id,
                        'transact_type': 'accepted',
                        'serial_numbers': serial_numbers,
                        'sku_code': self.each_grn['sku_code'],
                        'sku_id': asn_obj.purchase_order.open_po.sku.id,
                        'batch_number': batch_dict.get('batch_no', '') or '',
                        'batch_detail_id': asn_obj.batch_detail.id if asn_obj.batch_detail else None,
                        'status': 1,
                        'serial_status': s_n_transact_status,
                        'lpn_number': lpn_data.get('lpn_number', '')
                    })
        else:
            self.sn_item_list.append({
                'transact_id': asn_obj.id,
                'transact_type': 'accepted',
                'serial_numbers': serial_numbers,
                'sku_code': self.each_grn['sku_code'],
                'sku_id': asn_obj.purchase_order.open_po.sku.id,
                'batch_number': batch_dict.get('batch_no', '') or '',
                'batch_detail_id': asn_obj.batch_detail.id if asn_obj.batch_detail else None,
                'status': 1,
                'serial_status': s_n_transact_status,
            })

    def create_serial_number_mapping(self):
        """Serial Number mapping for ASN Creation"""
        self.sn_transact_dict.update({
            'reference_number': self.asn_number,
            'reference_type': 'asn',
            'extra_params': {'serial_creation': True},
            'items': self.sn_item_list
        })

        SerialNumberTransactionMixin(None, self.warehouse, self.sn_transact_dict).create_update_sn_transaction()

    def save_asn(self, warehouse, grn_data_list, data={} ,extra_data={}):
        from core_operations.views.integration.integration import webhook_integration_3p
        self.warehouse = warehouse
        status =True
        #Generating ASN Number
        header_details = grn_data_list[0]
        self.misc_dict = self.misc_data(warehouse)
        self.asn_approval_flow = self.misc_dict.get('asn_approval', '') == 'true'
        self.allow_batch_creation = self.misc_dict.get('allow_asn_creation_without_batch', 'false') == 'false'
        self.request_user = extra_data.get('request_username', '')
        if data.get('asn_number'):
            self.asn_number = data.get('asn_number')
            self.receipt_number = 1
            self.grn_type = header_details.get("grn_type", "")
        else:
            self.asn_number, self.receipt_number = self.generate_asn_number(header_details)
            if self.asn_number == '':
                return self.asn_number, False, 'ASN Prefix Not Available !'

        #save asn number in asn reference field if asn reference is not given
        self.asn_reference = header_details.get('asn_reference') or self.asn_number

        #Fetching SKU Type wise Checklist Details
        sku_types = [each_row.get('sku_type') for each_row in grn_data_list]
        asn_checklist_attrs = get_sku_type_wise_checklist_details(
            sku_types, 'asn_checklist', warehouse, only_config=True)
        
        po_number = ""
        self.po_numbers = set()
        asn_summary_objects_list = []
        self.approval_data = []
        self.lpn_data = []
        self.packing_data = {}
        temp_batch_list = []
        self.sn_transact_dict = {}
        self.sn_item_list = []
        sku_codes = []
        line_extra_list = []
        line_id_list = []
        asn_approval_status = 'open'

        self.approval_header_data = {"transaction_type": "ASN", "transaction_number": self.asn_number,
                                     "json_data": {}, "line": []}
        try:
            with transaction.atomic('default'):
                for self.each_grn in grn_data_list:
                    sku_codes.append(self.each_grn.get('sku_code', ''))
                    json_data = extra_data.get('json_data', {}).copy()
                    req_json_data = self.each_grn.get('json_data', {}) or {}
                    json_data.update(req_json_data)
                    json_data.update({"discount_amount": data.get('discount_amount', 0), 
                                      'additional_cost': data.get('additional_cost', 0),
                                      'cn_amount': data.get('cn_amount', 0),
                                      'sgst_tax': self.each_grn.get('sgst_tax', 0),
                                      'cgst_tax': self.each_grn.get('cgst_tax', 0),
                                      'igst_tax': self.each_grn.get('igst_tax', 0),
                                      'utgst_tax': self.each_grn.get('utgst_tax', 0),
                                      'apmc_tax': self.each_grn.get('apmc_tax', 0),
                                      'cess_tax': self.each_grn.get('cess_tax', 0),
                                      'supplier_sgst': self.each_grn.get('supplier_sgst', 0),
                                      'supplier_cgst': self.each_grn.get('supplier_cgst', 0),
                                      'supplier_igst': self.each_grn.get('supplier_igst', 0),
                                      'supplier_cess': self.each_grn.get('supplier_cess', 0),
                                      'supplier_total_tax': self.each_grn.get('supplier_total_tax', 0)})
                    self.lpns = json_data.get('scanned_qr_lpns', [])
                    self.lpn_type = 'inner carton'
                    if json_data.get('lpns_list', []):
                        self.lpns = json_data['lpns_list']
                        self.lpn_type = ''
                    
                    #Fetching PutZone To Override SKU PutZone
                    if self.each_grn.get('put_zone'):
                        json_data.update({"put_zone": self.each_grn["put_zone"]})
                    json_data.update({"remarks": data.get("remarks", ""),
                                      "gatekeeper_margin": self.each_grn.get("gatekeeper_margin", 0),
                                      "margin": self.each_grn.get("calculated_margin", 0),
                                      "mrp_without_tax": self.each_grn.get("mrp_without_tax", 0)})

                    po_obj = PurchaseOrder.objects.get(id=self.each_grn.get("purchase_order_id"))
                    
                    batch_dict = self.each_grn.get("batch_detail", {})
                    po_number= self.each_grn.get("po_number", "")
                    self.po_numbers.add(po_number)
                    self.get_quantities()
                
                    self.batch_obj= None
                    if self.each_grn.get("batch_based", False) and self.grn_type != 'ASNSave' and self.allow_batch_creation:
                        batch_dict["receipt_number"] = self.receipt_number
                        
                        #Create or Update BatchDetail
                        error_list, self.batch_obj = get_or_create_batch(
                                    warehouse, batch_dict, draft=True, extra_attributes=self.extra_batch_attributes)
                        if error_list:
                            raise ValueError(error_list)
                    reason = self.each_grn.get("discrepency_reason", "")
                    if not reason:
                        reason = self.each_grn.get("po_remarks", "")

                    asn_details = {
                        'reason' : reason,
                        'json_data' : json_data,
                        'value_with_tax': extra_data.get('asn_amount_with_tax', 0),
                        'value_without_tax': extra_data.get('asn_amount', 0),
                        'tcs_value': data.get('tcs', 0)
                        }
                    asn_summary = self.create_asn_record(asn_details)
                    asn_summary_objects_list.append(asn_summary)
                    temp_batch_dict = prepare_batch_temp_json_data_for_draft_asn(self.grn_type, batch_dict, asn_summary, warehouse, self.allow_batch_creation)
                    if temp_batch_dict:
                        temp_batch_list.append(temp_batch_dict)
                    self.prepare_lpn_creation_data(asn_summary)
                    asn_quantity = float(po_obj.saved_quantity)
                    saved_free_qty = float(po_obj.saved_free_quantity)
                    asn_free_qty = self.each_grn['line_invoice_free_quantity'] if self.each_grn.get('line_invoice_free_quantity', 0) else self.each_grn.get('invoice_free_quantity', 0)
                    po_obj.saved_quantity = asn_quantity + self.grn_quantity
                    po_obj.saved_free_quantity = saved_free_qty + asn_free_qty
                    po_obj.save()
                    self.update_open_po_objects(po_obj.open_po, self.each_grn)

                    if asn_checklist_attrs:
                        self.update_checklist_values(asn_checklist_attrs, po_obj, asn_summary)
                    self.prepare_serial_item_list(asn_summary, batch_dict)
                    line_extra_fields = self.each_grn.get('line_extra_fields', {})
                    line_extra_dict = {str(asn_summary.id):line_extra_fields}
                    line_id_list.append(str(asn_summary.id))
                    if line_extra_fields:
                        line_extra_list.append(line_extra_dict)
                save_grn_extra_fields(warehouse, self.asn_number, data.get('extra_fields', {}), 'asn')
                #Save ASN Line Extra Fields
                if line_extra_list:
                    create_or_update_master_attributes(line_id_list, 'asn_line_extra_fields', line_extra_list, warehouse)
            asn_status = self.get_asn_status()
            update_asn_creation_date(asn_summary_objects_list, asn_status)
            self.delete_tempjson_data(warehouse)
            approval_data = self.prepare_approval_data(asn_summary_objects_list, warehouse, self.asn_approval_flow)
            if self.grn_type != 'ASNSave':
                asn_approval_status = self.create_approval_entry(approval_data, self.approval_header_data, warehouse, data, self.asn_approval_flow)
            create_dock_schedule_entries(self.warehouse, self.misc_dict, extra_data, asn_summary)
            if temp_batch_list:
                create_or_update_temp_batch_details(warehouse, temp_batch_list)
            self.create_serial_number_mapping()
        except Exception as e:
            log.info(generate_log_message("ConfirmASNFailure",
                                          warehouse=warehouse.username, 
                                          data=grn_data_list, asn_number=self.asn_number,
                                          error=str(e)
                                          ))
            status =False

        try:
            if status and self.grn_type != "ASNSave":
                # Inventory Callback after ASN creation
                filters = {"sku_codes": sku_codes}
                webhook_integration_3p(warehouse.id, 'asn_creation', filters)

                #ASN Callback after ASN creation
                asn_call_back_3p_integration(self.warehouse, self.asn_number) # asn_number
                self.create_packing(data)
        except Exception as e:
            log.info(generate_log_message("ASNCallbackFailure",
                                          warehouse=self.warehouse.username,
                                          asn_number=self.asn_number,
                                          error=str(e)))    

        return status, self.asn_number, self.receipt_number, po_number, asn_approval_status

    def prepare_lpn_creation_data(self, asn_summary):
        if self.lpns:
            batch_details = {}
            if asn_summary.batch_detail:
                batch_detail = asn_summary.batch_detail
                mfg_date, exp_date = '', ''
                timezone = get_user_time_zone(self.warehouse.id, self.warehouse.id)
                if batch_detail.manufactured_date:
                    mfg_date = get_local_date_known_timezone(
                        timezone, batch_detail.manufactured_date, send_date=True).strftime("%Y-%m-%d")
                if batch_detail.expiry_date:
                    exp_date = get_local_date_known_timezone(
                        timezone, batch_detail.expiry_date, send_date=True).strftime("%Y-%m-%d")
                batch_details = {
                    "batch_number": batch_detail.batch_no,
                    "batch_reference": batch_detail.batch_reference,
                    "manufactured_date": mfg_date,
                    "expiry_date": exp_date,
                    "mrp": batch_detail.mrp
                }

            lpn_item = {
                    "transaction_id": asn_summary.id,
                    "sku_code": asn_summary.purchase_order.open_po.sku.sku_code,
                    "sku_description": asn_summary.purchase_order.open_po.sku.sku_desc,
                    "carton_type": "",
                    "status": "closed",
                    "mrp": asn_summary.batch_detail.mrp if asn_summary.batch_detail else 0,
                    "unit_price": asn_summary.price,
                    "packed_quantity": asn_summary.invoice_quantity,
                    "max_packable_quantity": 0,
                    "batch_details": batch_details,
                    "json_data": {},
                    "unique_id" : asn_summary.id
                }

            for lpn in self.lpns:
                if lpn not in self.packing_data:

                    #update_lpn_item_quantity_before
                    self.packing_data[lpn] = {
                        "lpn_number": lpn,
                        "carton_type": self.lpn_type,
                        "weight": 10,
                        "uom": "KG",
                        "json_data": {
                            "created_by": self.request_user
                        },
                        "items": [lpn_item]
                        }
                else:
                    self.packing_data[lpn]['items'].append(lpn_item)

    def create_packing(self, data):
        if self.packing_data:
            request_data = data.get('request_data')
            request_dict = {
                "request_headers": {
                        "Warehouse": request_data.get("warehouse").username,
                        "Authorization": request_data.get('headers').get('Authorization')
                    },
                "request_meta": request_data.get('request_meta'),    
            }
            packing_dict = {
                "warehouse": self.warehouse.username,
                "user": self.request_user,
                "status": "closed",
                "transaction_number": self.asn_number,
                "transaction_type": "asn",
                "allow_multiple_transactions" : True,
                "source": 'ASN',
                "packing_details": list(self.packing_data.values()),
                "json_data": {}
                }
            log.info(generate_log_message("ASNPackingRequest",
                                          warehouse=self.warehouse.username,
                                          data = packing_dict,
                                          asn_number=self.asn_number))   
            packing_service_instance = PackingService(request_dict, request_data.get("user"), self.warehouse)
            final_data, errors = packing_service_instance.create_packing(packing_dict)
            log.info(generate_log_message("ASNPackingResponse",
                                          warehouse=self.warehouse.username,
                                          data = final_data,
                                          errors= errors,
                                          asn_number=self.asn_number))

class ASNSet(WMSListView):
    def delete(self, *args, **kwargs):
        """ ASN Cancellation """
        request = self.request
        request_data = request.GET
        self.set_user_credientials()
        asn_number = request_data.get('asn_number')
        asn_id = request_data.get('asn_id')
        self.grn_type = request_data.get('grn_type', '')
        self.reason = request_data.get('reason', '')
        asn_id = asn_id.split(',') if asn_id else []
        self.asn_type = ''
        if asn_number:
            message, status, self.asn_type = asn_cancellation(asn_number, self.warehouse, asn_id = asn_id, request = request)
            log.info(message + str(request.POST) + " username " + request.user.username)
            self.send_callback(status, asn_number)
            return JsonResponse({"message": message}, status=status)
        else:
            return JsonResponse({"message": 'ASN Number is Required'}, status=400)

    def send_callback(self, status, asn_number):
        try:
            if status and self.asn_type != "ASNSave":
                asn_call_back_3p_integration(self.warehouse, asn_number, 'asn_cancellation') # asn_number
        except Exception as e:
            log.info(generate_log_message("ASNCallbackFailure",
                                          warehouse=self.warehouse.username,
                                          asn_number=asn_number,
                                          error=str(e)))    
    
    def clear_cache(self, keys):
        for key in keys:
            cache_key = f'{self.warehouse.id}##{key}'
            cache.delete(str(cache_key))
    
    def process_request(self):
        try:
            self.request_details = {
                'user' : self.request.user
                }
            self.request_data = json.loads(self.request.body)
        except Exception:
            try:
                self.request_data = json.loads(self.request.POST.get("data"))
            except Exception:
                self.request_data = ""
        
    def process_request_dates(self):
        for e_row in self.request_data.get("items", []):
            if len(e_row.get("retest_date", ""))==10:
                e_row["retest_date"] = e_row["retest_date"] + " 23:59"
            if len(e_row.get("expiry_date", ""))==10:
                e_row["expiry_date"] = e_row["expiry_date"] + " 23:59"
            if len(e_row.get("manufactured_date", ""))==10:
                e_row["manufactured_date"] =  e_row["manufactured_date"] + " 00:00"
    
    def update_request_data(self):
        if self.grn_type == 'PO':
            self.request_details['request_headers'] = self.request.headers

    def create(self, request, warehouse, data, grn_data_list, grn_extra_dict={}):
        source_from = grn_data_list[0].get("source") if grn_data_list[0].get("source") else "WEB"
        data['request_data'] = {"warehouse": warehouse,
                                        "user": request.user,
                                        "headers": request.headers,
                                        "request_meta": request.META}
        extra_data = {
            "json_data":{
            "source": source_from, "request_username":request.user.username , "request_user_id":request.user.id
            }
        }
        extra_data.update(grn_extra_dict)
        status, asn_number, receipt_number, po_number, asn_approval_status = CreateASN().save_asn(warehouse, grn_data_list, data, extra_data)
        try:
            if asn_number and status:
                create_file_po_grn_mapping(request, warehouse, receipt_number, po_number, asn_number, master_type="ASN_PO_NUMBER")
        except Exception as e:
            log.info(generate_log_message('ASNFileUploadFailure', warehouse=warehouse.username, asn_number=asn_number, error=str(e)))
        return asn_number, status, asn_approval_status
     
    def post(self, *args, **kwargs):
        try:
            self.process_request()
            if not self.request_data:
                return JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
            log.info(generate_log_message("ASNRequest", 
                                          username=self.request.user.username,
                                          request_data = self.request_data,
                                          ip_address=get_user_ip(self.request)))
            self.process_request_dates()
            self.set_user_credientials()
            valid_ser = GRNSerializer(data=self.request_data)
            po_numbers_list, po_ref_list = [], []
            self.response_dict, data = {}, {}
            if valid_ser.is_valid():
                data = valid_ser.validated_data
                self.grn_type = data.get('grn_type', '')
                self.update_request_data()
                validation_type = "asn_creation"
                if data.get('asn_number'):
                    validation_type = "asn_addition"

                validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, grn_extra_dict, _, po_ref_list \
                    = POGRNValidation().validate(data, self.warehouse, is_asn=True, validation_type=validation_type, extra_params=self.request_details)
                log.info(generate_log_message("POASNValidationProcessedPayload",request_payload=data, grn_data_list=grn_data_list))  
                error_message_dict = {}
                if validated_error_dict:
                    status= False
                    error_message_dict = validated_error_dict
                elif po_dict_data:
                    self.grn_number, status, asn_approval_status = self.create(self.request,
                            warehouse=self.warehouse, data=data, grn_data_list=grn_data_list, grn_extra_dict=grn_extra_dict)
                    self.response_dict['asn_number'] = self.grn_number  
                    self.response_dict['asn_status'] = asn_approval_status
                else:
                    status = False
                    error_message_dict= {"header" : ["Something went Wrong"]}
                
                
            else:
                error_dict = valid_ser.errors
                error_message_dict = get_serializer_error_message(self.request_data, error_dict)
                status= False
            
            self.clear_cache(po_numbers_list+po_ref_list)
            if status:
                return JsonResponse(self.response_dict, status=200)
            else:
                error_dict = {'error': [{'message': error_message_dict }]}
                return JsonResponse(error_dict, status=400)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info(generate_log_message("ASNRequestFailed",
                                          username=self.request.user.username,
                                          error=str(e)))
            return  JsonResponse({'error': [{'message': INVALID_PAYLOAD_CONST }]}, status=400)
      
    def asn_updation(self):
        po_numbers_list, po_ref_list = [], []
        asn_approval_status = 'open'
        asn_ids_list,  asn_status = get_draft_asn_status(self.request_data.get('asn_number'), self.warehouse)
        if not self.request_data.get('po_asn_updation', False):
            if 5 not in asn_status and 7 not in asn_status:
                return False, {'header':['This ASN is not available for updation']}, [], []
            payload_asn_ids = get_asn_ids_from_paylaod(self.request_data.get('items'))

            if sorted(asn_ids_list) != sorted(payload_asn_ids):
                return False, {'header':['Please Process Full ASN']}, [], []
            
        lock_key = f"asn_transaction_lock_{self.request_data.get('asn_number')}_{self.warehouse.username}"
        if cache.get(lock_key):
            return False, {'header':['ASN Updation is already in progress']}, [], []
        cache.set(lock_key, True, timeout=180)
        try:
            valid_ser = GRNSerializer(data=self.request_data)
            if valid_ser.is_valid():
                data = valid_ser.validated_data
                self.grn_type = data.get('grn_type', '')
                self.update_request_data()
                validated_error_dict, po_dict_data, grn_data_list, po_numbers_list, \
                    grn_extra_dict, _, po_ref_list = POGRNValidation().validate(data, self.warehouse, validation_type="asn_update", extra_params=self.request_details)

                if validated_error_dict:
                    status = False
                elif po_dict_data:
                    data['request_data'] = {"warehouse": self.warehouse,
                                            "user": self.user,
                                            "headers": self.request.headers,
                                            "request_meta": self.request.META}
                    data['is_reverted'] = self.request_data.get('is_reverted')
                    status, asn_number, asn_approval_status = UpdateASN().update_asn(self.warehouse, grn_data_list, data, grn_extra_dict=grn_extra_dict)
                    if status:
                        response_dict = {'asn_number': asn_number, 'asn_status': asn_approval_status}
                        try:
                            if asn_number and status:
                                create_file_po_grn_mapping(self.request, 
                                    self.warehouse, '', po_numbers_list[0], asn_number, master_type="ASN_PO_NUMBER")
                        except Exception as e:
                            log.info(generate_log_message('ASNFileUploadFailure', 
                                        warehouse=self.warehouse.username, asn_number=asn_number, error=str(e)))

                        return True, response_dict, po_numbers_list, po_ref_list
                return False, validated_error_dict, po_numbers_list, po_ref_list
            else:
                error_dict = valid_ser.errors
                error_message_dict = get_serializer_error_message(self.request_data, error_dict)
                return False, error_message_dict, po_numbers_list, po_ref_list
            
        except Exception as e:
            log.error(f"Error during ASN Updation: {str(e)}")
            return False, {'header':['ASN Updation Failed']}, [], []

        finally:
            # Always release the lock at the end
            cache.delete(lock_key)
    
    def put(self, *args, **kwargs):
        """ASN update """
        self.process_request()    
        self.process_request_dates()
        self.set_user_credientials()
        log.info(generate_log_message("ASNUpdationRequest",
                                      username = self.warehouse.username,
                                      request_data = self.request_data,
                                      ip_address=get_user_ip(self.request)
                                      ))

        # Handle ASN Reference Update separately
        if self.request_data.get('update_type') == 'reference':
            return self.process_asn_reference_update()

        # ASN Draft Update & Draft Confirmation
        status, response_dict, po_numbers_list, po_ref_list = self.asn_updation()
        self.clear_cache(po_numbers_list + po_ref_list)
        if status:
            return JsonResponse(response_dict, status=200)
        else:
            return JsonResponse({'error': [{'message': response_dict}]}, status=400)
    
    def process_asn_reference_update(self):
        """Validate and update ASN reference."""
        self.asn_reference = self.request_data.get('asn_reference','')
        self.invoice_number = self.request_data.get('invoice_number', '')
        self.asn_number = self.request_data.get('asn_number', '')

        #Validate ASN request data
        validation_response = self.validate_asn_details()
        if validation_response['status'] == 400:
            return JsonResponse({'message': validation_response['message']}, status=400)

        #Process Update ASN
        update_response = self.update_asn_reference()
        return JsonResponse({'message': update_response['message']}, status=update_response['status'])

    def validate_asn_details(self):
        """Validate ASN details."""
        #check for asn number for updation
        if not self.asn_number:
            return {'message': 'asn_number is Mandatory', 'status':400}

        #check for asn objects to update
        self.asn_objs = ASNSummary.objects.filter(asn_user=self.warehouse.id, asn_number=self.asn_number)
        if not self.asn_objs.exists():
            return {'message': 'ASN Number Not Found', 'status': 400}

        #check for update fields
        if not self.request_data.get('asn_reference') and not self.request_data.get('invoice_number'):
            return {'message': 'Invalid Request: Either asn_reference or invoice_number is required', 'status': 400}

        #Check for duplicate ASN reference
        if self.asn_reference:
            is_duplicate_reference = ASNSummary.objects.filter(
                asn_user=self.warehouse.id,
                asn_reference=self.asn_reference
            ).exclude(asn_number=self.asn_number).exists()
            if is_duplicate_reference:
                return {'message': 'ASN Reference Already Exists', 'status': 400}

        #Unique PO unique invoice validations
        misc_dict = get_multiple_misc_values(['same_supplier_invoice_check'] , self.warehouse.id)
        if self.invoice_number:
            asn_obj = self.asn_objs[0]
            supplier_id = asn_obj.purchase_order.open_po.supplier.supplier_id
            po_number = [asn_obj.purchase_order.po_number]
            supplier_inv_check, error_message = duplicate_invoice_number_check(
                self.warehouse, self.asn_number, self.invoice_number, supplier_id, po_number, [], misc_dict
            )
            if supplier_inv_check:
                return {'message': error_message, 'status': 400}

        return {'message': 'Success', 'status': 200}

    def update_asn_reference(self):
        """Update ASN references & Invoice Number"""
        try:
            updated_objs = []
            update_fields = []
            for asn_obj in self.asn_objs:
                #when asn_reference is given
                if self.asn_reference:
                    asn_obj.asn_reference = self.asn_reference
                    update_fields.append('asn_reference')
                #when invoice_number is given
                if self.invoice_number:
                    asn_obj.invoice_number = self.invoice_number
                    update_fields.append('invoice_number')

                updated_objs.append(asn_obj)

            if updated_objs:
                ASNSummary.objects.bulk_update_with_rounding(updated_objs, list(set(update_fields)))

            return {'message': 'Updated Successfully', 'status': 200}

        except Exception as e:
            log.error(f"Update ASN failed for {self.request_data}: {e}", exc_info=True)
            return {'message': 'Update Failed', 'status': 400}

    
@get_warehouse
def update_asn_status(request, warehouse:User):
    """
    Update the status of an ASN (Advance Shipping Notice).

    Args:
        request (HttpRequest): The HTTP request object.
        warehouse (User): The warehouse user object.

    Returns:
        JsonResponse: A JSON response indicating success or failure of the status update.

    Raises:
        Exception: If there's an error during the status update process.
    """
    try:
        status_mapping = {'approved':1, 'reverted': 7, 'rejected': 3}
        data = json.loads(request.body)
        asn_ids = data.get('asn_ids')
        asn_status = data.get('status')
        asn_number = data.get('transaction_number')
        transaction_type = data.get('transaction_type')
        if asn_number and transaction_type == 'ASN':
            log.info(generate_log_message("ASNStatusUpdateRequest",
                                            asn_ids= str(asn_ids)))
            asn_objs = ASNSummary.objects.filter(asn_number = asn_number, purchase_order__open_po__sku__user = warehouse.id)
            if not asn_objs.exists():
                return JsonResponse({'error': 'Invalid ASN Numbers'}, status=400)
            status = status_mapping.get(asn_status)
            if status in [3,7]:
                asn_cancellation(asn_number, warehouse, asn_status = status, request = request)
            else:
                asn_objs.update(status=status)
            if status == 1:
                no_of_skus = asn_objs.values('purchase_order__open_po__sku').distinct().count()
                extra_data = {"no_of_skus": no_of_skus}
                misc_dict = get_multiple_misc_values(['dock_scheduling'], warehouse.id)

                #Create Dock Schedule Entries
                create_dock_schedule_entries(warehouse, misc_dict, extra_data, asn_objs[0])

                #Trigger ASN CallBack
                try:
                    send_asn_call_back(warehouse, asn_number, 'asn_creation')
                except Exception as e:
                    log.error(generate_log_message("Exception Raised on ASN CallBack",
                        warehouse_name = warehouse.username, asn_number = asn_number, error=str(e)))
            return JsonResponse({'message': 'ASN Status Updated Successfully'}, status=200)
    except Exception as e:
        log.error(generate_log_message("ASNStatusUpdateFailure",
                                          asn_ids= str(asn_ids),
                                          error = str(e)))
        return JsonResponse({'error': 'Internal server error'}, status=500)

@get_warehouse
def send_approval_email(request, warehouse:User):
    # Extract recipient emails from request data
    data = json.loads(request.body)
    asn_number = data.get('transaction_number', '')
    send_to = data.get('emails')
    if isinstance(send_to, str):
        send_to = [send_to]

    if not send_to:
        return {}

    # Get the current date
    current_date = datetime.datetime.now().date()

    # Construct email subject and body
    subject = 'Approval Request - %s' % asn_number
    body = 'Dear User,\n\nPlease review and approve the request submitted on %s.\n\nThank you!' % current_date

    # Send the email using send_sendgrid_mail
    send_sendgrid_mail('<EMAIL>', [send_to], subject, body)

    return JsonResponse({"message": "Approval email sent successfully"}, status=200)

@get_warehouse
def asn_equation(request, warehouse:User):
    log.info(generate_log_message("ASNEQUATION",
                                          request_user= str(request.user.username),
                                          warehouse = str))
    return JsonResponse({'asn_equations': APPROVAL_CONFIG_DATA})


@get_warehouse
def get_asn_approval_user(request, warehouse:User):
    log.info(generate_log_message("ASNApprovalUser",
                                          request_user= str(request.user.username),
                                          warehouse = str))
    st_whs = list(StaffWarehouseMapping.objects.filter(warehouse=warehouse).values_list('staff_id',flat=True))
    search_params = {}
    search_params['id__in'] = st_whs
    master_data = StaffMaster.objects.select_related("user").exclude(status=2).filter(**search_params)
    approval_emails = [data.user.email for data in master_data]
    return {"approval_email": approval_emails}

def prepare_batch_temp_json_data_for_draft_asn(grn_type, batch_dict, asn_summary, warehouse, allow_batch_creation):
        """Prepare Temp batch json data for Draft ASN"""
        if grn_type == 'ASNSave' or not allow_batch_creation:
            batch_dict = modify_batch_characters(warehouse, batch_dict)
            return {
                'model_id': asn_summary.id,
                'model_name': 'DraftASN',
                'model_reference': asn_summary.asn_number,
                'model_json': batch_dict
            }
        return {}

def create_or_update_temp_batch_details(warehouse, batch_data):

    model_reference, model_name, model_id = set(), set(), set()

    for data in batch_data:
        model_reference.add(data.get('model_reference'))
        model_name.add(data.get('model_name'))
        model_id.add(data.get('model_id'))

    existing_batch_details = {}

    temp_objs = TempJson.objects.filter(
        warehouse = warehouse.id, model_reference__in = model_reference,
        model_name__in = model_name, model_id__in = model_id
    )
    for obj in temp_objs:
        existing_batch_details[(obj.model_reference, obj.model_name, obj.model_id)] = obj

    update_temp_batch_details, create_temp_batch_details = [], []

    for data in batch_data:
        # Format dates
        json_data = data.get('model_json', {})
        date_formats = ['manufactured_date', 'expiry_date', 'retest_date', 'best_before_date', 'reevaluation_date']
        for date in date_formats:
            date_obj = json_data.get(date)
            if date_obj and isinstance(date_obj, datetime.datetime):
                json_data[date] = json_data[date].strftime("%m/%d/%Y")

        json_data = json.dumps(json_data)

        unique_key = (data.get('model_reference'), data.get('model_name'), data.get('model_id'))
        if unique_key in existing_batch_details:
            existing_obj = existing_batch_details.get(unique_key)
            existing_obj.model_json = json_data
            update_temp_batch_details.append(existing_obj)
        else:
            create_temp_batch_details.append(TempJson(
                warehouse = warehouse, account_id = warehouse.userprofile.id,
                model_reference = data.get('model_reference'),
                model_name = data.get('model_name'), model_id = data.get('model_id'),
                model_json = json_data
            ))

    if create_temp_batch_details:
        TempJson.objects.bulk_create(create_temp_batch_details)
    if update_temp_batch_details:
        TempJson.objects.bulk_update(update_temp_batch_details, ['model_json'])

def get_temp_batch_details(warehouse, reference_ids):

    filters = {
        'warehouse': warehouse.id, 'model_id__in': reference_ids, 'model_name': 'DraftASN'
    }
    return_dict = {}
    temp_jsons = list(TempJson.objects.filter(**filters).values('model_id', 'model_json'))
    for batch_data in temp_jsons:
        return_dict[batch_data.get('model_id')] = json.loads(batch_data.get('model_json'))
    return return_dict

def delete_temp_batch_details(warehouse, model_ids = None, model_references = None):
    filters = {
        'warehouse': warehouse.id, 'model_name': 'DraftASN'
    }
    if model_ids:
        filters['model_id__in'] = model_ids
    if model_references:
        filters['model_reference__in'] = model_references
    TempJson.objects.filter(**filters).delete()


def update_asn_creation_date(asn_summary_objects_list, asn_status):
    if asn_status == 1:
        asn_ids = [asn_summary.id for asn_summary in asn_summary_objects_list]
        ASNSummary.objects.filter(id__in=asn_ids).update(asn_creation_date=timezone.now())

def get_draft_asn_status(asn_number, warehouse):
    asn_objects = dict(ASNSummary.objects.filter(asn_number=asn_number, 
                  purchase_order__open_po__sku__user=warehouse.id, 
                  status__in = [5, 7]).values_list('id', 'status'))
                  
    return list(asn_objects.keys()), list(asn_objects.values())


def get_asn_ids_from_paylaod(asn_data):
    asn_ids = []
    for data in asn_data:
        if data.get('asn_id'):
            asn_ids.append(int(data.get('asn_id')))
    return asn_ids

def create_dock_schedule_entries(warehouse, misc_dict, extra_data, asn_summary):
    """Create entries in gate pass item for dock scheduling """
    if misc_dict.get("dock_scheduling", '') =='true' and asn_summary.status == 1:
        invoice_date = ''
        if asn_summary.invoice_date:
            invoice_date = asn_summary.invoice_date.strftime("%Y-%m-%d")
        dock_schedule_dict = {
            'transaction_id': asn_summary.asn_number,
            'warehouse_id': warehouse.id,
            'transaction_type': 'material_receipt',
            'status': 1,
            'account_id': warehouse.userprofile.id,
            'json_data': {
                'supplier_id': asn_summary.purchase_order.open_po.supplier.supplier_id,
                'supplier_name': asn_summary.purchase_order.open_po.supplier.name,
                'no_of_skus': extra_data.get('no_of_skus', 0),
                'total_quantity': extra_data.get('total_quantity', 0),
                'invoice_number': asn_summary.invoice_number,
                'invoice_date': invoice_date
            }
        }
        GatePassItem.objects.create(**dock_schedule_dict)