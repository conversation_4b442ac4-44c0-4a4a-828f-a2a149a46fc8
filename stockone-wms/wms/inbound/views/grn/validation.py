import datetime
import pytz
import copy
import math
import re
import json
from itertools import groupby
from django.db.models import F, Q, Sum, Min
from django.core.cache import cache
from collections import defaultdict
import pandas as pd
from django.http import JsonResponse

#Model Imports
from inventory.models import (
    LocationMaster, SerialNumberMapping, StockDetail, ZoneMaster
    )
from core.models import (
    SKUMaster, MasterDocs, QCConfiguration, GatePassItem
)
from inbound.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SellerPOSummary,
    ASNSummary, SupplierMaster,
    PutawayMapping, SKUSupplier
)
from inventory.models import BatchDetail

from outbound.models import SalesReturnBatchLevel, SalesReturn, SellerOrderSummary

from production.models import JobOrder, JOMaterial
from wms_base.models import User

#Method Imports
from core_operations.views.common.main import (
    WMSListView, get_multiple_misc_values,
    get_misc_value, get_user_attributes, get_user_ip,
    get_company_id, get_decimal_value, truncate_float,
    get_local_date_known_timezone, get_user_time_zone,
    get_extra_attributes
    )
from .combo import fetch_sr_child_skus_batch_data_from_picklist

from inventory.views.serial_numbers.serial_number import SerialNumberMixin

from core_operations.views.common.user_attributes import (
    validate_attributes, get_radio_button_checklist, checklist_pass_validation,
    get_sku_type_wise_checklist_details
    )
from inbound.views.common.fetch_query import get_data_from_custom_params
from inbound.views.common.constants import TRUE_VALUES, FALSE_VALUES
from .constants import GRN_ERROR_MESSAGE, BATCH_ERROR_MESSAGE

from inventory.views.locator.stock_detail import validate_batch_details
from production.views.bom.bom import fetch_combo_skus_relation_data
from core_operations.views.common.main import get_warehouse

from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

from core_operations.views.services.packing_service import LpnManager

from wms_base.wms_utils import init_logger

today = datetime.datetime.now().strftime("%Y%m%d")
log = init_logger('logs/grn_validation' + today + '.log')
log_err = init_logger('logs/grn_validation.log')

LOCK_EXPIRE = 60*10

BATCH_VALIDATE_KEYS = {
     'transact_type', 'batch_no', 'vendor_batch_no', 'expiry_date', 'manufactured_date',
     'tax_percent', 'cess_percent', 'mrp', 'best_before_date', 'weight', 'batch_reference',
     'warehouse_id', 'retest_date', 'reevaluation_date', 'inspection_lot_number', 'sku_id'
}

class InboundBatchValidation:
    def configuration_validations(self, misc_dict):
        if misc_dict.get('allow_unitprice_gt_mrp') in FALSE_VALUES \
                and self.each_row.get('po_price', 0) > self.each_row.get('mrp', 0):
            self.error_message.append(BATCH_ERROR_MESSAGE[104])
        
        if misc_dict.get('allow_future_manufactured_dates') in FALSE_VALUES \
            and self.each_row.get('manufactured_date') and self.each_row.get('manufactured_date').date() > datetime.date.today():
                self.error_message.append(BATCH_ERROR_MESSAGE[105])
        
        if misc_dict.get('allow_past_expiry_dates') in FALSE_VALUES \
            and self.each_row.get('expiry_date') and self.each_row.get('expiry_date').date() < datetime.date.today():
            self.error_message.append(BATCH_ERROR_MESSAGE[106])
    
    def batch_based_keys_validation(self, configured_batch_attributes, configured_non_batch_attributes, incremental_batch):
        required_batch_attributes = []
        if not (self.each_row.get('batch_number') or self.each_row.get('batch_reference') or self.each_row.get('vendor_batch_number') or incremental_batch):
            self.error_message.append(BATCH_ERROR_MESSAGE[103])
        for batch_attribute in configured_batch_attributes:
            batch_attr_value =  self.each_row.get(batch_attribute)
            if batch_attribute == "batch_number" and self.grn_type == "ASNSave":
                continue
            if batch_attr_value in ['', None, 'None'] and batch_attribute not in configured_non_batch_attributes:
                required_batch_attributes.append(batch_attribute.replace('_', ' ').title())

        if required_batch_attributes:
            error_suffix =  BATCH_ERROR_MESSAGE[302]
            if len(required_batch_attributes) == 1:
                error_suffix =  BATCH_ERROR_MESSAGE[301]
            self.error_message.append('%s %s'%(', '.join(required_batch_attributes), error_suffix))

    def non_batch_based_keys_validation(self, batch_attributes):
        for line_item_key in self.each_row:
            if line_item_key in batch_attributes:
                self.error_message.append(BATCH_ERROR_MESSAGE[102])
    
    def validate(self, each_row, batch_based, misc_dict, grn_type):
        self.each_row = each_row
        self.grn_type = grn_type

        configured_batch_attributes, configured_non_batch_attributes = [], []
        #Additional batch details
        self.error_message = []
        batch_attributes = {
            'vendor_batch_number': '', 'best_before_date': None, 'retest_date': None,
            'reevaluation_date': None, 'inspection_lot_number': '', 'mrp': 0, 'batch_number': '',
            'manufactured_date': None, 'expiry_date': None, 'batch_reference' : 0
            }
        additional_batch_details, non_mandatory_batch_attributes = (
            misc_dict.get('additional_batch_attributes'), misc_dict.get('non_mandatory_batch_attributes')
        )
        if additional_batch_details not in ['false', '', None, False]:
            configured_batch_attributes = additional_batch_details.split(',')
        if non_mandatory_batch_attributes not in ['false', '', None, False]:
            configured_non_batch_attributes = non_mandatory_batch_attributes.split(',')
        extra_batch_attributes = misc_dict.get('extra_batch_attributes', {})
        for attr, attr_data in extra_batch_attributes.items():
            if not attr_data.get('is_mandatory'):
                configured_non_batch_attributes.append(attr)
            self.each_row[attr] = self.each_row.get('extra_batch_attributes', {}).get(attr)
            configured_batch_attributes.append(attr)
        
        incremental_batch = False
        if misc_dict.get('incremental_batch_prefix'):
            incremental_batch = True
            configured_non_batch_attributes.append('batch_number')

        if batch_based:
            self.batch_based_keys_validation(configured_batch_attributes, configured_non_batch_attributes, incremental_batch)
            self.configuration_validations(misc_dict)
        else:
            self.non_batch_based_keys_validation(batch_attributes)
        return self.error_message


class POGRNValidation(WMSListView):

    def get_configurations(self):
        #Fetching Configurations
        misc_types = [
            'receive_higher_quantity', 'same_supplier_invoice_check',
            'sku_inv_price_tolerance', 'grn_inv_price_tolerance', 'block_min_shelf_life_stock',
            'po_tolerance', 'additional_batch_attributes', 'non_mandatory_batch_attributes',
            'inbound_packing', 'invoice_level', 'blind_grn', 'enable_invoice_value_in_grn',
            'allow_unitprice_gt_mrp', 'allow_future_manufactured_dates', 'allow_past_expiry_dates',
            'asn_inv_price_tolerance', 'enable_invoice_value_in_asn', 'gate_pass_validation_during_grn',
            'margin_value_validation', 'incremental_batch_prefix', 'po_type_level_packing', 'asn_packing',
            'validate_doc_num_per_fy', 'enable_inbound_staging_lanes', "enable_invoice_value_tolerance",
            "lpn_restriction", "auto_putaway", "allow_asn_creation_without_batch", "allow_partial_grn_in_asn",
            ]
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)
        extra_batch_attributes = get_user_attributes(self.warehouse, 'extra_batch_attributes')
        batch_attributes_dict = {}
        for attr_data in extra_batch_attributes:
            batch_attributes_dict[attr_data["attribute_name"]] = attr_data
        self.misc_dict['extra_batch_attributes'] = batch_attributes_dict
        self.asn_line_extra_fields = get_user_attributes(self.warehouse, 'asn_line_extra_fields')
        self.company_id = get_company_id(self.warehouse)
        self.decimal_limit = int(get_decimal_value(self.warehouse.id))
        self.decimal_limit_price = int(get_decimal_value(self.warehouse.id, 1))
        self.allow_partial_grn = self.misc_dict.get('allow_partial_grn_in_asn', 'false') == 'true'
        self.po_tolerance_pcnt = 0
        if self.misc_dict.get('po_tolerance', '') == 'po_level':
            po_tolerance_pcnt = self.misc_dict.get('receive_higher_quantity', 0)
            if po_tolerance_pcnt:
                try:
                    self.po_tolerance_pcnt = float(po_tolerance_pcnt)
                except ValueError:
                    pass
        
    def get_qc_configuration(self):
        #Fetching QC Configurations to Send to QC
        qc_config_dict = {'SR' : 'after_sr_grn', 'PO' : 'after_grn'}
        self.qc_transaction_type = qc_config_dict.get(self.grn_type)

        #Fetching ALL QC Config
        qc_enabled = False
        all_qc_enabled = False
        qc_config_data = {}
        qc_config = QCConfiguration.objects.filter(
            warehouse_id=self.warehouse.id, transaction_type=self.qc_transaction_type, status=1)
        if qc_config.exists():
            qc_enabled = True
            all_qc_enabled = qc_config[0].all_qc
            qc_config_data = qc_config[0].json_data
        return qc_enabled, all_qc_enabled, qc_config_data
    
    def get_supplier_details(self, supplier_id):
        supplier_obj = None
        supplier_qc = False
        if supplier_id:
            supplier_obj = SupplierMaster.objects.get(supplier_id = supplier_id, user=self.warehouse.id)
            supplier_qc = supplier_obj.qc_eligible
        return supplier_obj, supplier_qc
    
    def get_staging_lane_to_location(self, dock_location):
        to_location_id, error = '', []
        if dock_location:
            location_objs = LocationMaster.objects.filter(location=dock_location, zone__user=self.warehouse.id)

            # Apply additional filters if inbound staging lanes are enabled
            if self.misc_dict.get('enable_inbound_staging_lanes', '').lower() == 'true':
                location_objs = location_objs.filter(zone__segregation='inbound_staging', zone__storage_type='PUT')

            # Check if location exists
            if not location_objs.exists():
                error = [f"{GRN_ERROR_MESSAGE[12]} {dock_location}"]
            else:
                to_location_id = location_objs.first().id  # Fetch the first matching location ID
        return to_location_id, error

    def line_level_cache_check(self, each_row):
        """Header Level cache check """
        po_number = each_row.get('po_number', '')
        po_reference = each_row.get('po_reference', '')
        if po_number:
            cache_po_number = f'{self.warehouse.id}##{po_number}'
            cache_status= cache.add(cache_po_number, "True", timeout=LOCK_EXPIRE)
            if not cache_status and po_number and po_number not in self.po_numbers_list:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[13] + str(po_number)]
            else:
                self.po_numbers_list.append(po_number)

        elif po_reference:
            cache_po_number = f'{self.warehouse.id}##{po_reference}'
            cache_status= cache.add(cache_po_number, "True", timeout=LOCK_EXPIRE)
            if not cache_status and po_reference and po_reference not in self.po_ref_list:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[13] + str(po_reference)]
            else:
                self.po_ref_list.append(po_reference)

    def get_batch_skus(self, sku_code, batch_number, vendor_batch_number, combo_sku_data):
        if batch_number:
            self.batch_numbers_list.append(batch_number)
            if sku_code not in self.skus_with_batches:
                self.skus_with_batches.append(sku_code)
            
        #Fetching Child Batch SKUs 
        if combo_sku_data:
            if not self.combo_sku_check:
                self.combo_sku_check = True
            for each_child in combo_sku_data:
                if each_child.get('sku_code', '') not in self.skus_with_batches:
                    self.skus_with_batches.append(each_child.get('sku_code', ''))
        
        if vendor_batch_number:
            self.vendor_batch_nos_list.append(vendor_batch_number)
    
    def request_data_to_query(self, data):
        self.sku_codes_list, self.put_zones_list = [], []
        self.batch_numbers_list, self.vendor_batch_nos_list, self.skus_with_batches = [''], [''], []
        self.po_numbers_list = []
        
        self.asn_ids, self.free_skus, self.sku_codes, self.total_asn_quantity = [], [], [], 0
        has_unique_key_dict = {}
        self.combo_sku_check = False
                
        for each_line_row in data['items']:
            sku_code = each_line_row['sku_code']
            po_line_reference = each_line_row.get('aux_data', {}).get('po_line_reference')
            has_unique_key = True if each_line_row.get('id') or po_line_reference else False

            #Line Level PO Number Cache Check (1 ASN - Multiple POs case)
            self.line_level_cache_check(each_line_row)
            
            #Frame Unique ID Dict Either ID or SKU with Line Reference
            if sku_code in has_unique_key_dict:
                has_unique_key_dict[sku_code].append(has_unique_key)          
            else:
                self.sku_codes_list.append(sku_code)
                has_unique_key_dict[sku_code] = [has_unique_key]
            
            #Framing PO SKUs and Free SKUs
            if each_line_row.get('free_sku'):
                self.free_skus.append(each_line_row["sku_code"])
            else:
                self.sku_codes.append(each_line_row["sku_code"])

            self.total_asn_quantity += each_line_row.get('invoice_quantity', 0)
            
            #Framing ASN IDs and Putzones
            if each_line_row.get('asn_id'):
                self.asn_ids.append(each_line_row.get('asn_id'))
            self.put_zones_list.append(each_line_row.get('put_zone'))
            
            #Framing Batch SKUs
            self.get_batch_skus(
                sku_code,
                each_line_row.get('batch_number'),
                each_line_row.get('vendor_batch_number'),
                each_line_row.get('combo_sku_data', [])
                )
            for each_data in each_line_row.get('lpns', []):
                if each_data.get('lpn_number'):
                    self.lpn_numbers.append(each_data['lpn_number'])

        return has_unique_key_dict
        
    def get_sku_details_for_free_skus(self, free_skus):
        self.sku_details = {}
        sku_filters = {'sku_code__in' : free_skus, 'user' : self.warehouse.id}
        sku_values = ['id', 'sku_code', 'sku_desc', 'mrp', 'price', 'sku_type', 'qc_check', 'product_type']
        sku_values_dict = {'serial_based' : F('enable_serial_based'), 'put_zone' : F('zone__zone'), 'put_zone_id' : F('zone')}
        sku_objs = SKUMaster.objects.filter(**sku_filters).values(*sku_values, **sku_values_dict)
        for sku_data in sku_objs:
            self.sku_details[sku_data['sku_code']] = sku_data
    
    def get_putzone_details(self):
        self.put_zone_dict = {}
        if self.put_zones_list:
            put_zones = list(ZoneMaster.objects.filter(
                zone__in=self.put_zones_list, user=self.warehouse.id).values('id', 'zone'))
            for each_zone in put_zones:
                self.put_zone_dict[each_zone['zone']] = each_zone['id']

    def get_purchase_order_data(self):
        po_values = []
        if self.grn_type in ['PO', 'ASNSave']:
            self.po_filter_dict = {'open_po__po_name__in' : self.po_ref_list} if self.po_ref_list else {'po_number__in' : self.po_numbers_list}
            if not self.free_skus:
                self.po_filter_dict.update({'open_po__sku__sku_code__in' : self.sku_codes})

            self.po_filter_dict.update({'open_po__sku__user__in' : [self.warehouse.id]})
            values_list = [
                'user', 'id', 'po_number', 'creation_date', 'received_quantity', 'intransit_quantity',
                'order_id','prefix', 'po_type', 'saved_quantity', 'line_reference','pcf',
                'sku_code', 'sku_id', 'sku_desc', 'sku_type', 'sku_mrp', 'sku_buy_price', 'sku_uom',
                'po_reference', 'order_quantity', 'free_quantity', 'sku_category',
                'price', 'mrp', 'supplier_id', 'supplier', 'supplier_address', 'enable_serial_based',
                'supplier_name', 'sku', 'put_zone_id', 'put_zone', 'qc_check', 'unit', 'gstin_number',
                'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax', 'cess_tax', 'pmc_tax', 
                'minimum_shelf_life', 'receipt_tolerance', 'serial_based', 'batch_based',
                'json_data', 'cancelled_quantity', 'received_free_quantity', 'customer_shelf_life']
            
            #Fetch Purchase Order Data
            extra_params = {
                'filters' : self.po_filter_dict, 'value_keys' : values_list, 'return_type' : 'values_list',
                }
            po_values = get_data_from_custom_params(PurchaseOrder,'purchase_order', extra_params)
            if po_values:
                if not self.po_number:
                    self.po_number = po_values[0].get('po_number', '')
                elif not self.po_reference:
                    self.po_reference = po_values[0].get('po_reference', '')
                self.supplier_id = po_values[0].get('supplier', '')
        return po_values

    def frame_asn_id_dict(self):
        self.asn_id_dict = {}
        self.asn_batch_id_dict = {}
        self.po_asn_qty_dict = {}
        if self.asn_number:
            try:
                asn_objects = ASNSummary.objects.filter(asn_number=self.asn_number, asn_user=self.warehouse.id)
                if self.validation_type =='asn_grn_creation' and asn_objects.filter(status__in=[5,7,6]).exists():
                    return False

                if self.asn_ids:
                    self.asn_qty_dict = {}
                    asn_objs = list(asn_objects.filter(id__in=self.asn_ids).values('id', 'quantity', 'batch_detail_id', 'line_invoice_free_quantity', 'invoice_free_quantity', 'json_data', 'purchase_order_id'))
                    for each_asn_obj in asn_objs:
                        received_free_quantity = each_asn_obj.get('json_data', {}).get('received_free_quantity', 0)
                        asn_free_quantity = each_asn_obj.get('line_invoice_free_quantity', 0) or each_asn_obj.get('invoice_free_quantity', 0)
                        self.asn_qty_dict[each_asn_obj['id']] = {'quantity':each_asn_obj['quantity'], 'asn_free_qty': asn_free_quantity, 'received_free_quantity': received_free_quantity, 'json_data': each_asn_obj.get('json_data', {})}
                        self.asn_batch_id_dict[each_asn_obj['id']] = each_asn_obj['batch_detail_id']
                        if each_asn_obj['purchase_order_id'] in self.po_asn_qty_dict:
                            self.po_asn_qty_dict[each_asn_obj['purchase_order_id']]+= each_asn_obj['quantity']
                        else:
                            self.po_asn_qty_dict[each_asn_obj['purchase_order_id']] = each_asn_obj['quantity']

                else:
                    batch_filter = (
                        Q(batch_detail__batch_no__in=self.batch_numbers_list) |
                        Q(batch_detail__vendor_batch_no__in=self.vendor_batch_nos_list) |
                        Q(batch_detail__isnull=True) 
                    )
                    filter_dict = {'asn_number':self.asn_number,
                                   'purchase_order__open_po__sku__sku_code__in':self.sku_codes_list,
                                   'purchase_order__open_po__sku__user':self.warehouse.id
                                   }
                    if self.po_ref_list:
                        filter_dict['purchase_order__open_po__po_name__in'] = self.po_ref_list
                    else:
                        filter_dict['purchase_order__po_number__in'] = self.po_numbers_list
                    asn_objs = asn_objects.filter(
                        batch_filter,
                        **filter_dict
                    ).values_list(
                        'id', 'asn_number',
                        'purchase_order__po_number', 'purchase_order__open_po__sku__sku_code',
                        'batch_detail__batch_no', 'batch_detail__vendor_batch_no', 'batch_detail__mrp',
                        'price', 'quantity', 'batch_detail_id', 'purchase_order_id',
                        'invoice_free_quantity', 'line_invoice_free_quantity', 'json_data'
                    )

                    for id, asn, po_no, sku, batch, vendor_batch_no, mrp, price, quantity, batch_detail_id, po_id, invoice_free_quantity, line_invoice_free_quantity, json_data in list(asn_objs):
                        #unique_asn_frame_key
                        asn_free_quantity = line_invoice_free_quantity or invoice_free_quantity
                        received_free_quantity = json_data.get('received_free_quantity', 0)
                        self.asn_id_dict[(asn,po_no,sku,batch,mrp,price,po_id)] = {'id' : id, 'quantity' : quantity, 'batch_detail_id': batch_detail_id, 'asn_free_qty': asn_free_quantity, 'received_free_quantity': received_free_quantity, 'json_data': json_data}
                        self.asn_id_dict[(asn,po_no,sku,vendor_batch_no,mrp,price, po_id)] = {'id' : id, 'quantity' : quantity, 'batch_detail_id': batch_detail_id, 'asn_free_qty': asn_free_quantity, 'received_free_quantity': received_free_quantity, 'json_data': json_data}
                        if po_id in self.po_asn_qty_dict:
                            self.po_asn_qty_dict[po_id]+= quantity
                        else:
                            self.po_asn_qty_dict[po_id] = quantity  

            except ValueError:
                pass

        return True

    def frame_po_id_dict(self, po_values):
        po_dict_data = {}
        sku_types, po_skus, po_duplicate_skus = [], [], []
        self.po_sku_id_mapping, self.po_sku_lr_mapping = {}, {}
        po_wise_skus = []
        for po_row in po_values:
            unique_sku_key = (po_row["po_reference"],po_row['sku_code']) if (self.grn_with_po_ref or po_row["po_reference"]) else (po_row["po_number"],po_row['sku_code'])
            if unique_sku_key in po_skus:
                po_duplicate_skus.append(unique_sku_key)
            po_skus.append(po_row['sku_code'])
            po_wise_skus.append(po_row['sku_code'])
            sku_types.append(po_row['sku_type'])
            po_dict_data[po_row['id']] = po_row
            if self.grn_with_po_ref:
                unique_lr_frame_key = (po_row["sku_code"], po_row["po_reference"], po_row["line_reference"])
                unique_po_frame_key = (po_row["sku_code"], po_row["po_reference"])
            else:
                unique_lr_frame_key = (po_row["sku_code"], po_row["po_number"], po_row["line_reference"])
                unique_po_frame_key = (po_row["sku_code"], po_row["po_number"])
            self.po_sku_id_mapping[unique_po_frame_key]= po_row['id']
            self.po_sku_lr_mapping[unique_lr_frame_key] = po_row['id']
    
        return po_skus, sku_types, po_dict_data, po_duplicate_skus

    def duplicate_po_record_validation(self, po_duplicate_skus, has_unique_key_dict):
        line_reference_required_skus = []
        for duplicate_sku in po_duplicate_skus:
            if not all(has_unique_key_dict[duplicate_sku[1]]):
                line_reference_required_skus.append(duplicate_sku[1])
        return line_reference_required_skus
    
    def grn_reference_validation(self):
        grn_ref_check = SellerPOSummary.objects.filter(
                grn_reference=self.grn_reference, purchase_order__open_po__sku__user=self.warehouse.id).exists()
        return grn_ref_check

    def asn_reference_validation(self):
        asn_ref_check = ASNSummary.objects.filter(
                asn_reference=self.asn_reference, asn_user=self.warehouse.id).exists()
        return asn_ref_check

    def fetch_po_tolerance(self):
        po_tolerance_type = self.misc_dict.get('po_tolerance', '')
        receive_qty_percent = 0
        if po_tolerance_type == 'po_level':
            receive_qty_percent = self.misc_dict.get('receive_higher_quantity', 0)
            if isinstance(receive_qty_percent, str):
                try:
                    receive_qty_percent = float(receive_qty_percent)
                except ValueError:
                    receive_qty_percent = 0
        return receive_qty_percent

    def check_discrepancy_documents_availability(self, master_id):
        '''
            Check discrepancy document available or not
        '''
        master_doc_objects = MasterDocs.objects.filter(
            user_id=self.warehouse.id, extra_flag='discrepancy', master_id=master_id, master_type='PO_TEMP')
        if master_doc_objects.exists():
            return True
        return False
    
    def get_checklist_attributes(self, sku_types):
        '''
            Validation Checklist for ASN Creation
        '''
        #validate asn discrepancy note available or not
        is_discrepancy_doc_available = self.check_discrepancy_documents_availability(self.po_number)

        #Fetching ASN Checklist Attributes on SKU Types
        self.asn_checklist_attrs = {}
        if not (is_discrepancy_doc_available or self.grn_type == 'ASNSave'):
            self.asn_checklist_attrs = get_sku_type_wise_checklist_details(sku_types, 'asn_checklist', self.warehouse, only_config=True)
                    
    def frame_child_skus_dict(self):
        '''
            Framing Parent wise Child SKU Details and Preparing Data for GRN Creation
        '''
        child_sku_objs_dict, self.combo_details, self.parent_child_dict = {}, {}, {}
        if self.combo_sku_check:
            self.sku_combo_dict, child_sku_ids = fetch_combo_skus_relation_data(self.sku_codes, self.warehouse.id)        
            if not self.sku_combo_dict:
                return child_sku_objs_dict, self.combo_details
            
            for parent, child_data in self.sku_combo_dict.items():
                for each_child_data in child_data:
                    if parent in self.parent_child_dict:
                        self.parent_child_dict[parent].update({
                            each_child_data['child_sku_code'] : each_child_data
                            })
                    else:
                        self.parent_child_dict[parent] = {
                        each_child_data['child_sku_code'] : each_child_data
                        }
                        
            child_sku_objs_dict = SKUMaster.objects.in_bulk(child_sku_ids)
            self.grn_extra_dict['child_sku_objs_dict']  = child_sku_objs_dict
            self.grn_extra_dict['sku_combo_dict'] = self.sku_combo_dict
            self.grn_extra_dict['batch_combo_dict'] = self.combo_details
            
    def get_put_zone_id(self):
        '''
            Fetching Put Zone ID and Zone Restriction for Putaway Suggestions
        '''
        put_zone_id = ''
        zone_restriction = ''
        if self.each_row.get("put_zone"):
            zone_restriction = self.each_row.get("put_zone")
            put_zone_id = self.put_zone_dict.get(self.each_row.get("put_zone")) or ''
        return put_zone_id, zone_restriction
    
    def get_po_id(self, sku_code, line_reference):
        '''
            Fetching Purchase order Internal ID to Create GRN Against that Record,
            with the Unique Keys if PO Id does not exist
        '''
        #Creating GRN Against PO Number or PO Reference
        if line_reference:
            line_reference = str(line_reference)

        if self.grn_with_po_ref:
            po_ref = self.each_row.get("po_reference") or self.po_reference
            unique_po_fetch_key = (sku_code, po_ref)
            unique_lr_fetch_key = (sku_code, po_ref, line_reference)
        else:
            unique_po_fetch_key = (sku_code, self.po_number)
            unique_lr_fetch_key = (sku_code, self.po_number, line_reference)

        po_id = None
        if not self.each_row.get('free_sku'):
            if not self.each_row.get("id", 0):
                if line_reference:
                    po_id = self.po_sku_lr_mapping.get(unique_lr_fetch_key)
                else:
                    po_id = self.po_sku_id_mapping.get(unique_po_fetch_key)
            if not po_id:
                self.error_message.append(GRN_ERROR_MESSAGE[25])
            self.each_row['id'] = po_id
        return po_id

    def validate_child_batch_attributes(self, sku_code):
        '''
            Validating Parent Child Relation and Child Batch Attributes Validation.
        '''
        has_combo_error = False
        for each_combo in self.each_row['combo_sku_data']:
            each_combo = dict(each_combo)
            each_combo['sku_code'] = each_combo.get('sku_code', '')
            if each_combo['sku_code'] not in self.parent_child_dict.get(sku_code, {}):
                has_combo_error = True
                self.key_wise_error_dict[self.index_sku_key + '<<>>' + str(each_combo.get("sku_code"))] = ["Invalid Child SKU"]
            
            #Combo SKU Batch Validation
            batch_error_message = InboundBatchValidation().validate(
                each_combo,
                self.parent_child_dict[sku_code].get(each_combo['sku_code'], {}).get("batch_based"),
                self.misc_dict,
                self.grn_type
                )
            if batch_error_message:
                has_combo_error = True
                self.key_wise_error_dict[self.index_sku_key + '<<>>' + str(each_combo.get("sku_code"))] = batch_error_message
        return has_combo_error
    
    def get_asn_data_with_asn_id(self, mrp):
        if self.asn_id:
            asn_batch_id = self.asn_batch_id_dict.get(self.asn_id)
            asn_quantity = self.asn_qty_dict[self.asn_id]['quantity']
            asn_free_quantity = self.asn_qty_dict[self.asn_id]['asn_free_qty']
            received_free_quantity = self.each_row.get('received_free_quantity', 0)
            asn_json_data = self.asn_qty_dict.get(self.asn_id, {}).get('json_data', {})
        else:
            # try with batch number
            buy_price = self.each_row.get("buy_price")
            if not buy_price:
                buy_price = self.each_row.get("po_price")
            if not self.asn_id:
                unique_asn_fetch_key = (
                    self.asn_number,
                    self.po_number,
                    self.sku_code,
                    self.each_row.get("batch_number"),
                    mrp,
                    buy_price,
                    self.po_id
                )
            self.asn_id = self.asn_id_dict.get(unique_asn_fetch_key, {}).get('id', '')
            
            # try with vendor batch number
            if not self.asn_id:
                unique_asn_fetch_key = (
                    self.asn_number,
                    self.po_number,
                    self.sku_code,
                    self.each_row.get("vendor_batch_number"),
                    mrp,
                    buy_price,
                    self.po_id
                )

            self.asn_id = self.asn_id_dict.get(unique_asn_fetch_key,{}).get('id', '')
            asn_quantity = self.asn_id_dict.get(unique_asn_fetch_key,{}).get('quantity', 0)
            asn_batch_id = self.asn_id_dict.get(unique_asn_fetch_key, {}).get('batch_detail_id', 0)
            asn_free_quantity = self.asn_id_dict.get(unique_asn_fetch_key, {}).get('asn_free_qty', 0)
            received_free_quantity = self.asn_id_dict.get(unique_asn_fetch_key, {}).get('received_free_quantity', 0)
            asn_json_data = self.asn_id_dict.get(unique_asn_fetch_key, {}).get('json_data', {}) or {}
            if self.asn_id:
                self.each_row['asn_id'] = self.asn_id
            elif not self.each_row.get('is_new_sku'):
                #Differentiate VBN and BatchNumber in Error Message
                self.error_message.append('%s %s'%(GRN_ERROR_MESSAGE[201], str(unique_asn_fetch_key))) 

        return asn_quantity, asn_free_quantity, received_free_quantity, asn_batch_id, asn_json_data
    
    def frame_dict_for_po_creation(self, free_sku, price, po_details, put_zone_id):
        sku_dict = self.sku_details.get(free_sku, {})
        open_po_dict = {
            "sku_id":sku_dict.get('id', None),
            "supplier_id": po_details.get("supplier_id", ""),
            "po_name": po_details.get("po_reference", ""),
            "price": price,
            "mrp": self.each_row.get("mrp", 0),
            "status": "Free SKU",
            "order_quantity": self.received_quantity,
            "json_data":{
                "created_from": self.source,
                "po_currency": self.po_currency
                },
            "cess_tax": self.each_row.get("cess_tax", 0),
            "cgst_tax": self.each_row.get("cgst_tax", 0),
            "sgst_tax": self.each_row.get("sgst_tax", 0),
            "igst_tax": self.each_row.get("igst_tax", 0),
            "ship_to":  self.each_row.get("ship_to", ""),
            "terms": po_details.get("terms", ""),
            "delivery_date": po_details.get("delivery_date", None)
        }

        po_data = {
            "st_warehouse_id" : self.source_wh_user,
            "supplier_id": po_details.get("supplier",""),
            "supplier_address": po_details.get("address",""),
            "supplier_owner_email": po_details.get("supplier_owner_mail",""),
            "supplier_name": po_details.get("supplier_name",""),
            "gstin_number": po_details.get("gstin_number",""),
            "sku_desc": sku_dict.get("sku_desc"),
            "sku_mrp": sku_dict.get("sku_mrp"),
            "sku_type" : sku_dict.get("sku_type"),
            "sku_buy_price": sku_dict.get("sku_buy_price"),
            "serial_based": sku_dict.get("serial_based"),
            "qc_check": sku_dict.get("qc_check"),
            "put_zone": self.each_row['put_zone'] if self.each_row.get('put_zone') else sku_dict.get("put_zone"),
            "put_zone_id": put_zone_id if put_zone_id else sku_dict.get("put_zone_id"),
            "po_order_quantity": self.received_quantity,
            "unit": self.each_row.get('measurement_unit', ""),
            "po_date": datetime.datetime.now(),
            "po_number": po_details.get("po_number"),
            "order_id": po_details.get("order_id"),
            "user": self.warehouse.id,
            "po_prefix": po_details.get("prefix"),
            "po_type": po_details.get('po_type', ''),
            "pcf": po_details.get('pcf', 1),
            }
        return open_po_dict, po_data

    def frame_po_dict(self, po_dict, put_zone_id, zone_restriction):
        po_data = {
            'st_warehouse_id' : po_dict.get("warehouse_id",""),
            'supplier_id': po_dict.get("supplier",""),
            "supplier_address": po_dict.get("address",""),
            "supplier_owner_email": po_dict.get("supplier_owner_mail",""),
            "supplier_name": po_dict.get("supplier_name",""),
            "gstin_number": po_dict.get("gstin_number",""),
            
            "unit": po_dict.get("unit", ""),
            "po_number": po_dict.get("po_number"),
            "po_name": po_dict.get("po_reference", ""),
            "po_date": po_dict.get("creation_date"),
            "sku_id": po_dict.get("sku_id"),
            "sku_desc": po_dict.get("sku_desc"),
            "sku_uom": po_dict.get("sku_uom"),
            "sku_mrp": po_dict.get("sku_mrp"),
            "sku_type" : po_dict.get("sku_type"),
            "sku_buy_price": po_dict.get("sku_buy_price"),
            "serial_based": po_dict.get("serial_based"),
            "qc_check": po_dict.get("qc_check"),
            "put_zone_id": put_zone_id if put_zone_id else po_dict.get("put_zone_id"),
            "put_zone": self.each_row['put_zone'] if self.each_row.get('put_zone') else po_dict.get("put_zone"),
            "zone_restriction" : zone_restriction,
            "cgst_tax":po_dict.get("cgst_tax", 0),
            "sgst_tax":po_dict.get("sgst_tax", 0),
            "igst_tax":po_dict.get("igst_tax", 0),
            "po_prefix": po_dict.get("prefix", ''),
            "po_type": po_dict.get('po_type', ''),
            "pcf": po_dict.get('pcf', 1),
            "po_order_quantity": po_dict.get('order_quantity'),
            "po_free_quantity" : po_dict.get('po_free_quantity'),
            }
        return po_data
        
    def quantity_validations(self, key, unique_line_id, check_rec_quantity, po_dict):
        if self.misc_dict.get('invoice_level') != 'Line-level' and po_dict.get('po_type', '').lower() == "stocktransfer":
            if self.each_row.get('received_quantity') and not self.each_row.get('invoice_quantity'):
                self.each_row['invoice_quantity'] = self.each_row.get('received_quantity')
                
        if self.validation_type == "asn_grn_creation" and self.asn_id and (self.asn_quantity+self.asn_free_quantity) < self.received_quantity + self.each_row.get('short_quantity', 0):
            self.error_message.append(GRN_ERROR_MESSAGE[103])

        sku_short_key = (self.sku_code, unique_line_id)
        if self.misc_dict.get('blind_grn', '') == 'true':
            sku_short_key = (self.sku_code, unique_line_id, self.asn_id)
        elif self.asn_id:
            sku_short_key = self.asn_id

        if self.misc_dict.get('invoice_level') == 'Line-level':
            if self.validation_type =='asn_grn_creation':
                if self.allow_partial_grn:
                    if self.aggr_received_quantity[self.asn_id] > (self.asn_quantity+self.asn_free_quantity):
                        self.error_message.append(GRN_ERROR_MESSAGE[104])
                else:
                    if self.aggr_line_invoice_quantity[self.asn_id] > (self.asn_quantity+self.asn_free_quantity):
                        self.error_message.append(GRN_ERROR_MESSAGE[104])
            else:
                if self.aggr_line_invoice_quantity[self.po_id] > (po_dict.get('order_quantity', 0) - po_dict.get('received_quantity') - po_dict.get('saved_quantity', 0)+ self.po_asn_qty) + self.extend_qty and self.grn_type != "ASNSave":
                    self.error_message.append(GRN_ERROR_MESSAGE[104])

            if not self.allow_partial_grn:
                if sku_short_key in self.sku_shortage_dict:
                    self.sku_shortage_dict[sku_short_key] -= self.received_quantity
                else:
                    self.sku_shortage_dict[sku_short_key] = self.each_row.get('invoice_quantity', 0)  - self.received_quantity

        elif self.asn_id and not self.allow_partial_grn:
            if self.asn_id in self.sku_shortage_dict:
                self.sku_shortage_dict[self.asn_id] -= self.received_quantity
            else:
                self.sku_shortage_dict[self.asn_id] = self.asn_quantity - self.received_quantity

        elif self.each_row.get('invoice_quantity', 0):
            if sku_short_key in self.sku_shortage_dict:
                self.sku_shortage_dict[sku_short_key] -= self.received_quantity
            else:
                self.sku_shortage_dict[sku_short_key] = self.each_row.get('invoice_quantity', 0) - self.received_quantity

            if self.each_row.get('invoice_quantity', 0) > (po_dict.get('order_quantity', 0) - po_dict.get('received_quantity') - po_dict.get('saved_quantity', 0)+ self.po_asn_qty) + self.extend_qty + self.asn_free_quantity and self.grn_type != "ASNSave":
                self.error_message.append(GRN_ERROR_MESSAGE[104])
            if self.aggregated_sku_count[key] > self.each_row.get('invoice_quantity', 0):
                self.error_message.append(GRN_ERROR_MESSAGE[105])

        if self.aggregated_sku_count[key] > check_rec_quantity + self.extend_qty + self.asn_free_quantity:
            self.error_message.append(GRN_ERROR_MESSAGE[101])
        elif (self.is_asn or self.asn_id is None) and (self.aggregated_sku_count[key] > check_rec_quantity - po_dict.get('saved_quantity') - po_dict.get('cancelled_quantity', 0)+ self.extend_qty):
            self.error_message.append(GRN_ERROR_MESSAGE[106])

        if self.received_quantity < self.rejected_quantity:
            self.error_message.append(GRN_ERROR_MESSAGE[102])

        if self.accepted_quantity != round(self.received_quantity - (self.rejected_quantity + self.each_row.get("return_quantity", 0)), self.decimal_limit):
            self.error_message.append(GRN_ERROR_MESSAGE[107])

        if self.validation_type =='asn_grn_creation' and self.misc_dict.get('blind_grn', '') == 'true' and not self.approval_grn\
            and (not self.received_quantity or self.accepted_quantity != round(self.received_quantity - (self.rejected_quantity + self.each_row.get("return_quantity", 0)), self.decimal_limit)):
            self.error_message.append(GRN_ERROR_MESSAGE[107])
    
    def asn_checklist_validation(self, po_id, sku_type):
        '''
            Validating, Whether all Radio Button Field Values are True or Not
        '''
        custom_attr_ids = []
        if self.is_asn and self.asn_checklist_attrs:
            #Fetching FormFieldValues
            checklist_config = self.asn_checklist_attrs[sku_type]['config_name']
            checklist_filters = {
                'attribute_model' : checklist_config, 'user' : self.warehouse,
                'status' : 1, 'attribute_type' : 'Radio Buttons'
                }
            custom_attr_ids, form_fields = get_radio_button_checklist(checklist_filters, PurchaseOrder, self.po_filter_dict)

            #ASN Check List Validations
            asn_form_fields = dict(form_fields.filter(object_id=po_id).values_list('field_id', 'value'))
            checklist_error = checklist_pass_validation(custom_attr_ids, asn_form_fields)
            if checklist_error:
                self.error_message.append(checklist_error)
        return custom_attr_ids
    
    def weighing_validation(self, json_data):
        '''
            Weighing Validation with Tolerance
        '''
        if json_data:
            weight_error_msgs = validate_maximum_takeoff_weight(self.warehouse, json_data)
            if weight_error_msgs:
                self.error_message.extend(weight_error_msgs)
 
    def min_shelf_life_validation(self, min_shelf_life_value, po_dict):
        '''
            Calculating Expiry date with the Current Date, Blocking if it's less than Min Shelf Life
        '''
        po_type = po_dict.get('po_type', 'Normal')
        min_shelf_life_exception = False
        min_shelf_life_config = po_type in self.misc_dict.get('block_min_shelf_life_stock', '').split(',')
        check_shelflife = False
        if min_shelf_life_value and min_shelf_life_config:
            check_shelflife = True

        if self.each_row.get('expiry_date'):
            good_to_use_in_days = self.each_row.get('expiry_date').date() - datetime.date.today() - datetime.timedelta(days=1)
            minimum_shelf_life =  min_shelf_life_value / datetime.timedelta(days=1) if min_shelf_life_value else 0
            if minimum_shelf_life and float(minimum_shelf_life) > float(good_to_use_in_days.days):
                if check_shelflife:
                    self.error_message.append(GRN_ERROR_MESSAGE[401])
                min_shelf_life_exception = True
        return min_shelf_life_exception

    def customer_shelf_life_validation(self, customer_shelf_life_value):
        '''
            Validating Customer Shelf Life with the Expiry Date
        '''
        customer_shelf_life_exeption = False
        if customer_shelf_life_value and self.each_row.get('expiry_date'):
            good_to_use_in_days = self.each_row.get('expiry_date').date() - datetime.date.today() - datetime.timedelta(days=1)
            customer_shelf_life =  customer_shelf_life_value / datetime.timedelta(days=1) if customer_shelf_life_value else 0
            if customer_shelf_life and float(customer_shelf_life) > float(good_to_use_in_days.days):
                customer_shelf_life_exeption = True

        return customer_shelf_life_exeption

    def mrp_validation(self, mrp):
        """Validating MRP with Tolerance"""
        mrp_exception = False
        if self.asn_json_data and 'attribute_tolerance' in self.asn_json_data:
            for each_attribute in self.asn_json_data['attribute_tolerance']:
                if each_attribute.get('fieldName', '').lower() == 'mrp':
                    try:
                        min_toelrance = float(each_attribute.get('negativeAbsolute', 0))
                        max_tolerance = float(each_attribute.get('positiveAbsolute', 0))
                    except (TypeError, ValueError):
                        min_toelrance, max_tolerance = 0, 0
                    if mrp < min_toelrance or mrp > max_tolerance:
                        mrp_exception = True
        return mrp_exception

    def sku_inv_price_tolerance_validation(self, price):
        '''
            Validating GRN Invoice Price and Calculated Invoice Price with Tolerance
        '''
        if self.grn_type != "ASNSave":
            sku_inv_tolerance_config = self.misc_dict.get('enable_invoice_value_tolerance', 'false')
            sku_inv_price_tolerance = self.misc_dict.get('sku_inv_price_tolerance', 'null')
            inv_price_tolerance = 0
            if sku_inv_price_tolerance and sku_inv_price_tolerance not in ['false', 'null']:
                inv_price_tolerance = float(sku_inv_price_tolerance)

            # Get the purchase order price
            po_price = self.each_row.get("po_price", 0)

            # Calculate the allowed price range
            min_allowed_price = po_price - inv_price_tolerance
            max_allowed_price = po_price + inv_price_tolerance

            # Check if the price is within the allowed range
            if sku_inv_tolerance_config == 'true':
                if not (min_allowed_price <= price <= max_allowed_price):
                    self.error_message.append(GRN_ERROR_MESSAGE[402])
            elif po_price and price > po_price:
                self.error_message.append(GRN_ERROR_MESSAGE[603])
    
    def get_order_status_flag(self, challan_number, invoice_number):
        '''
            Fethcing Order Status
        '''
        order_status_flag = ""
        if not invoice_number and not challan_number:
            order_status_flag = 'processed_pos'
        elif invoice_number:
            order_status_flag = 'supplier_invoices'
        elif challan_number:
            order_status_flag = 'po_challans'
        return order_status_flag

    def get_credit_status(self, total_grn_value):
        '''
            Fetching Credit Status based on Invoice and Total GRN Value
        '''
        self.credit_status = 0
        if (self.invoice_value - total_grn_value) > 20:
            self.credit_status = 1

    def frame_aggregated_grn_data_dict(self, key, grn_data_dict):
        '''
            Framing GRN Data List for Multiple Cartons 
        '''
        if key in self.aggr_grn_data_dict:
            self.aggr_grn_data_dict[key]['received_quantity'] += self.received_quantity
            self.aggr_grn_data_dict[key]['putaway_quantity'] += self.received_quantity
            self.aggr_grn_data_dict[key]['quantity'] += self.received_quantity
            self.aggr_grn_data_dict[key]['accepted_quantity'] += self.accepted_quantity
            self.aggr_grn_data_dict[key]['rejected_quantity'] += self.rejected_quantity
            self.aggr_grn_data_dict[key]['return_quantity'] += self.return_quantity
            self.aggr_grn_data_dict[key]['round_off_total'] += self.each_row.get("total_amount", 0)
            # Ensure keys exist in json_data and update
            json_data = self.aggr_grn_data_dict[key]['json_data']
            json_data.setdefault('lpns', []).extend(self.each_row.get('lpns', []))
            json_data.setdefault('lpns_list', []).extend(self.each_row.get('lpns_list', []))
            self.aggr_grn_data_dict[key]['serial_numbers'].extend(self.each_row.get('serial_numbers', []))
            self.aggr_grn_data_dict[key]['accepted_serial_numbers'].extend(self.each_row.get('accepted_serial_numbers', []))
            self.aggr_grn_data_dict[key]['rejected_serial_numbers'].extend(self.each_row.get('rejected_serial_numbers', []))
            if self.misc_dict.get('invoice_level') == 'Line-level':
                self.aggr_grn_data_dict[key]['invoice_quantity'] += self.invoice_quantity
                self.aggr_grn_data_dict[key]['line_invoice_quantity'] += self.line_invoice_quantity
                self.aggr_grn_data_dict[key]['line_invoice_free_quantity'] += self.line_invoice_quantity
                self.aggr_grn_data_dict[key]['invoice_free_quantity'] += self.invoice_free_quantity

        else:
            self.aggr_grn_data_dict[key] = grn_data_dict

    def get_sku_tolerance_value(self, sku_receipt_tolerance):
        '''
            Fetching Tolerance Value Based on Configuration
        '''
        receive_qty_percent = self.po_tolerance_pcnt
        if self.misc_dict.get('po_tolerance', '') == 'sku_level':
            receive_qty_percent = sku_receipt_tolerance
        return receive_qty_percent
    
    def get_quantities(self, key):
        self.received_quantity = round(self.each_row.get('received_quantity', self.each_row.get('invoice_quantity', 0)), self.decimal_limit)
        self.accepted_quantity = round(self.each_row.get('accepted_quantity', self.each_row.get('invoice_quantity', 0)), self.decimal_limit)
        self.rejected_quantity = round(self.each_row.get('rejected_quantity', 0), self.decimal_limit)
        self.return_quantity = self.each_row.get('return_quantity', 0)
        if self.misc_dict.get('invoice_level') == 'Line-level':
            self.invoice_quantity, self.invoice_free_quantity = 0, 0
            self.line_invoice_quantity = round(self.each_row.get('invoice_quantity', 0), self.decimal_limit)
            self.line_invoice_free_quantity = round(self.each_row.get('invoice_free_quantity', 0), self.decimal_limit)
            self_id = self.asn_id if self.validation_type =='asn_grn_creation' else self.po_id
            if self.allow_partial_grn:
                if self_id in self.aggr_received_quantity:
                    if not self.approval_grn:
                        self.aggr_received_quantity[self_id] += self.received_quantity
                else:
                    self.aggr_received_quantity[self_id] = self.received_quantity

            if self_id in self.aggr_line_invoice_quantity:
                if not self.approval_grn:
                    self.aggr_line_invoice_quantity[self_id] += self.line_invoice_quantity
                    self.aggr_line_invoice_free_quantity[self_id] += self.line_invoice_free_quantity
            else:
                self.aggr_line_invoice_quantity[self_id] = self.line_invoice_quantity
                self.aggr_line_invoice_free_quantity[self_id] = self.line_invoice_free_quantity
        else:
            self.line_invoice_quantity, self.line_invoice_free_quantity = 0, 0
            self.invoice_quantity = round(self.each_row.get('invoice_quantity', 0), self.decimal_limit)
            self.invoice_free_quantity = round(self.each_row.get('invoice_free_quantity', 0), self.decimal_limit)

        if key in self.aggregated_sku_count:
            self.aggregated_sku_count[key] += self.received_quantity
        else:
            self.aggregated_sku_count[key] = self.received_quantity
        if self.received_quantity <= 0 and self.line_invoice_free_quantity <= 0 and self.invoice_free_quantity <= 0\
                and self.line_invoice_quantity <= 0 and self.invoice_quantity <= 0:
            self.zero_quantity_check= True
    
    def get_price(self):
        '''
            Get Price Value
        '''
        price = 0
        if 'po_price' in self.each_row.keys():
            price = self.each_row.get('po_price')
        else:
            self.error_message.append(GRN_ERROR_MESSAGE[303])
        
        if 'buy_price' in self.each_row.keys():
            price = self.each_row["buy_price"]
        return price
    
    def duplicate_serials_validation(self):
        '''
            Validating Duplicate Serial Numbers on Packing
        '''
        duplicate_serials = set(self.packed_serial_numbers) & set(self.serial_num_list)
        if duplicate_serials:
            self.error_message.append(str(','.join(duplicate_serials))+ GRN_ERROR_MESSAGE[26])

    def check_is_serial_based_flow(self, sku_code, is_serial_sku):
        '''
            Checking Serial Based Flow
        '''
        if not self.serial_based:
            if is_serial_sku or self.sku_details.get(sku_code, {}).get("serial_based"):
                self.serial_based = True
    
    def batch_validation(self, sku_code, batch_based):
        #Combo SKUs Batched Attributes Validation
        has_combo_error = False
        if self.each_row.get('combo_sku_data', []) and self.sku_combo_dict.get(sku_code):
            has_combo_error = self.validate_child_batch_attributes(sku_code)
        else:
            #Batch Attributes Validation
            batch_error_message = InboundBatchValidation().validate(
                self.each_row, batch_based, self.misc_dict, self.grn_type)
            if batch_error_message:
                self.error_message.extend(batch_error_message)
        return has_combo_error
    
    def unique_batch_validation(self, batch_details):
        batch_number = batch_details.get('batch_no')
        sku_id = batch_details.get('sku_id')

        batch_sku_key = (sku_id, batch_number)
        if batch_sku_key in self.unique_batch_dict:
            existed_batch_dict = self.unique_batch_dict[batch_sku_key]
            #Check for Same Batch Attributes
            self.not_equal_keys = [
                key for key in BATCH_VALIDATE_KEYS if key not in batch_details or key not in existed_batch_dict or batch_details.get(key) != existed_batch_dict.get(key)
            ]

        else:
            self.unique_batch_dict[batch_sku_key] = batch_details

        return self.not_equal_keys
                
    def batch_details_validation(self, batch_details):
        batch_update = False
        if self.asn_id:
            batch_update = True
        batch_detail_dict = copy.deepcopy(batch_details)
        validate_dates = True
        if self.grn_type == "ASNSave" or self.validation_type == "asn_update":
            validate_dates = False
            if batch_detail_dict.get('retest_date'):
                batch_detail_dict.pop('retest_date')
            
        error_list, _ = validate_batch_details(
            self.warehouse, batch_detail_dict, batch_update, validate_dates=validate_dates,
            validate_retest_date=False
        )
        
        if error_list:
            multiple_error = error_list[0]
            for batch_key in self.not_equal_keys:
                if batch_key not in error_list[0]:
                    multiple_error += str(batch_key)
            self.error_message.append([multiple_error])
        else:
            if self.not_equal_keys:
                self.error_message.append(
                    "Same Batch Cannot Have Multiple Batch Attributes, Validate %s Keys" % str(', '.join(set(self.not_equal_keys))))

    def validate_combo_quantities(self, sku_combo_data, child_details):
        # Initialize dictionaries to track cumulative accepted and rejected quantities per SKU
        accepted_quantities = {}
        rejected_quantities = {}

        err_message = ''

        bom_child_skus = list(child_details.keys())

        #Extract accepted and rejected quantities for each batch
        for entry in sku_combo_data:

            sku_code = entry['sku_code']

            if sku_code in bom_child_skus:
                bom_child_skus.remove(sku_code)

            #Update cumulative accepted and rejected quantities per SKU
            accepted_quantities.setdefault(sku_code, 0)
            rejected_quantities.setdefault(sku_code, 0)
            
            accepted_quantities[sku_code] = round(accepted_quantities[sku_code] + entry.get('accepted_quantity', 0), self.decimal_limit)
            rejected_quantities[sku_code] = round(rejected_quantities[sku_code] + entry.get('rejected_quantity', 0), self.decimal_limit)

        if bom_child_skus:
            return 'Missing Child SKU Details'
        
        #Validate cumulative quantities against parent ratios
        for sku, accepted_qty in accepted_quantities.items():
            rejected_qty = rejected_quantities[sku]
            
            if child_details.get(sku):
                expected_accepted = round(self.accepted_quantity * (child_details[sku]['quantity']), self.decimal_limit)
                expected_rejected = round(self.rejected_quantity * (child_details[sku]['quantity']), self.decimal_limit)
                
                if accepted_qty != expected_accepted or rejected_qty != expected_rejected:
                    err_message = 'Invalid Quantity Ratios'
                    break

        return err_message
    
    def prepare_child_sku_batch_combo_dict(self, parent_sku, quantity, price, parent_child_dict, combo_sku_data):
        price_check = False
        total_qty, buy_price, total_price = 0, 0, 0
        
        #check why
        for each_data in combo_sku_data:
            total_qty += quantity
            if each_data.get('buy_price', 0):
                total_price += each_data.get('buy_price', 0) * parent_child_dict.get('quantity', 0)
        
        if total_price:
            price_check = True
            if total_price != price:
                self.error_message.append('Child SKU Prices does not match with Parent SKU Price')   
        else:
            buy_price = price / total_qty

        for each_child in combo_sku_data:
            child_sku = each_child['sku_code']
            combo_key = (parent_sku, child_sku)
            child_batch_data = parent_child_dict.get(child_sku, {})
            child_details = {
                'received_quantity': each_child.get('received_quantity', 0),
                'accepted_quantity': each_child.get('accepted_quantity', 0),
                'rejected_quantity': each_child.get('rejected_quantity', 0),
                'parent_sku_code': parent_sku,
                'child_sku_code': child_sku,
                'lpns': each_child.get('lpns', []),
            }

            if child_batch_data['batch_based']:
                mfg_date , expiry_date = None, None
                if each_child.get('manufactured_date', None):
                    mfg_date = each_child['manufactured_date']
                if each_child.get('expiry_date', None):
                    expiry_date = each_child['expiry_date']

                child_details.update({
                        'batch_no': each_child.get('batch_number', ''),
                        'manufactured_date': mfg_date,
                        'expiry_date': expiry_date,
                        'tax_percent': 0,
                        'cess_percent': 0.0,
                        'mrp': each_child.get('mrp', 0),
                        'buy_price': each_child.get('buy_price', 0) if price_check else buy_price,
                        'weight': child_batch_data['child_weight'],
                        'batch_reference': '',
                    })
            if combo_key not in self.combo_details:
                self.combo_details[combo_key] = [child_details]
            else:
                self.combo_details[combo_key].append(child_details)
        
    def frame_combo_batch_data(self, sku_code, price, combo_error_list, unique_line_id):
        short_qty = self.sku_shortage_dict.get((sku_code, unique_line_id)) or 0
        if combo_error_list:
            self.error_message.append(combo_error_list)
        else:
            self.prepare_child_sku_batch_combo_dict(
                self.each_row['sku_code'], self.received_quantity + short_qty , price,
                self.parent_child_dict[self.each_row['sku_code']], self.each_row.get('combo_sku_data', [])
                )

    def get_batch_detail_dict(self, mrp, price, po_dict, manufacturer_name):

        return { 
            "transact_type": "po_loc",
            "batch_no": self.each_row.get("batch_number"),
            "vendor_batch_no" : self.each_row.get("vendor_batch_number", ''),
            "expiry_date": self.each_row.get('expiry_date'),
            "manufactured_date": self.each_row.get("manufactured_date"),
            "tax_percent": self.each_row.get("tax", 0), 
            "cess_percent": self.each_row.get("cess_tax", 0),
            "mrp": mrp,
            "best_before_date" : self.each_row.get("best_before_date"),
            "weight": self.each_row.get("weight", ""),
            "batch_reference": self.each_row.get("batch_reference", ''),
            'warehouse_id': po_dict.get("user"),
            "retest_date": self.each_row.get("retest_date"),
            "reevaluation_date": self.each_row.get("reevaluation_date"),
            "inspection_lot_number":  self.each_row.get("inspection_lot_number",''),
            "sku_id": po_dict.get('sku_id'),
            "json_data": {'manufacturer_name': manufacturer_name, 'supplier_name': po_dict.get("supplier_name","")},
            "extra_attributes": self.each_row.get("extra_batch_attributes", {}),
        }
    
    def get_source_wh_details(self, po_dict):
        source_wh_firstname = po_dict.get("warehouse_first_name") if po_dict.get("warehouse_first_name") else self.warehouse.first_name
        source_wh_username = po_dict.get("warehouse_username") if po_dict.get("warehouse_username") else self.warehouse.username
        return source_wh_firstname, source_wh_username
    
    def get_send_for_qc(self, sku_qc_enabled):
        send_for_qc = False
        if self.qc_enabled:
            send_for_qc = self.send_grn_for_qc or self.all_qc_enabled or self.supplier_qc  or sku_qc_enabled
        return send_for_qc

    def lpn_validation(self, json_data, po_dict):
        """ LPN Validation for ASN GRN Creation """
        lpn_quantity = self.each_row.get('received_quantity', 0) if self.validation_type == 'asn_grn_creation' else self.each_row.get('invoice_quantity', 0) or self.each_row.get('received_quantity', 0)
        row_wise_lpns = []
        po_type = po_dict.get('po_type', 'Normal')
        if lpn_quantity:
            total_packed_quantity = 0
            if self.each_row.get('lpns'):
                json_data.update({'lpns': self.each_row.get('lpns')})
                for lpn_data in self.each_row.get('lpns'):
                    row_wise_lpns.append(lpn_data.get('lpn_number'))
                    total_packed_quantity += lpn_data.get('packed_quantity', 0)
                if total_packed_quantity != lpn_quantity:
                    self.error_message.append(GRN_ERROR_MESSAGE[505])
                if lpn_data.get('lpn_number') in self.lpn_error_dict:
                    self.error_message.extend(self.lpn_error_dict[lpn_data.get('lpn_number')])
                json_data.update({'lpns_list': row_wise_lpns})
                self.each_row['lpns_list'] = row_wise_lpns
                self.prepare_serial_data(po_dict, lpns=self.each_row.get('lpns'))
            elif self.validation_type == 'asn_grn_creation' and (self.misc_dict.get('inbound_packing') in TRUE_VALUES or po_type in self.misc_dict.get('po_type_level_packing', 'false').split(',')):
                self.error_message.append(GRN_ERROR_MESSAGE[304])
            elif self.validation_type in ["asn_update", "asn_creation"] and po_type in self.misc_dict.get('asn_packing', 'false').split(','):
                self.error_message.append(GRN_ERROR_MESSAGE[304])
            else:
                self.prepare_serial_data(po_dict, lpns=None, lpn_quantity=lpn_quantity)

    def validate_lpn_data_with_packing_data(self, extra_params):
        """
        Processes a list of data to extract LPN information,
        validates the LPNs, and creates them using the LpnManager.
        """
        if not (self.lpn_numbers and extra_params):
            return {}
        json_data = {
            'lpn_in_response': True
        }
        request_data = {'json_data': json_data, 'headers': extra_params.get('request_headers', {}), 'request_meta': extra_params.get('request_meta', {})}
        # Initialize LpnManager
        lpn_manager = LpnManager(self.warehouse, request_data, self.warehouse)
        transaction_number = ','.join(self.po_numbers_list)
        allow_multiple_transactions = 'multiple_grn' in self.misc_dict.get('lpn_restriction', '').split(',')
        # Validate and create LPNs
        lpn_errors = []
        self.lpn_numbers = list(set(self.lpn_numbers))
        lpn_errors = lpn_manager.create_and_validate_lpns(self.lpn_numbers, transaction_number, allow_multiple_transactions=allow_multiple_transactions)

        self.lpn_error_dict = self.frame_lpn_wise_error_dict(lpn_errors)

    
    def frame_lpn_wise_error_dict(self, lpn_errors):
        lpn_error_dict = defaultdict(list)

        for error in lpn_errors:
            error = error.strip()
            # Case 1: Error ends with "LPN: <lpn_numbers>"
            if 'LPN:' in error:
                message_part, lpn_part = error.rsplit('LPN:', 1)
                message = message_part.strip()
                lpn_numbers = [lpn.strip() for lpn in lpn_part.split(',') if lpn]

                for lpn in lpn_numbers:
                    if message not in lpn_error_dict[lpn]:
                        lpn_error_dict[lpn].append(f"{lpn}: {message}")

            # Case 2: Handle "Invalid LPN <lpn_number>"
            elif 'invalid lpn' in error.lower():
                parts = error.split('-', 1)
                if len(parts) > 1:
                    message = parts[0].strip()
                    lpn_str = parts[1].strip()
                    # Split the second part (LPNs) by spaces and strip any leading/trailing spaces
                    lpns = [lpn.strip() for lpn in re.split(r'[ ,]+', lpn_str)]

                    # If the message is not already in the dictionary for these LPNs, add it
                    for lpn in lpns:
                        if message not in lpn_error_dict.get(lpn, []):
                            lpn_error_dict.setdefault(lpn, []).append('Invalid LPN Number')
        return lpn_error_dict

    def sku_batch_detail_validation(self, sku_batch_details):
        self.not_equal_keys = []
        if 'batch_no' in sku_batch_details and sku_batch_details['batch_no']:
            #ST ASN Payload contains Multiple Batch Details
            #As we are not sending Batch Number, Not Validating Against Batch Number
            self.unique_batch_validation(sku_batch_details)
        self.batch_details_validation(sku_batch_details)
    
    def purchase_order_id_validation(self, sku_code, line_reference):
        self.po_id = self.each_row.get('id')
        if not self.po_id:
            self.po_id = self.get_po_id(sku_code, line_reference)

        continue_this = False
        if not (self.asn_number or self.po_id) and self.error_message:
            self.key_wise_error_dict[self.index_sku_key] = [self.error_message]
            continue_this = True
        return continue_this
    def get_received_free_quantity(self):
        self.received_free_quantity = 0
        if self.asn_quantity < self.received_quantity:
            self.received_free_quantity = self.received_quantity - self.asn_quantity
            if self.received_free_quantity > self.asn_free_quantity:
                self.received_free_quantity = self.asn_free_quantity
            elif self.received_free_quantity < 0:
                self.received_free_quantity = 0

    def get_asn_free_quantity(self):
        if self.asn_free_quantity and self.received_free_quantity:
            self.asn_free_quantity = self.asn_free_quantity - self.received_free_quantity

    def validate_discont_percent_and_amt(self, discount_percent, discount_amount, discount_type):
        error_dict = {"scd": "Scheme Discount", "cd": "Cash Discount"}
        try:
            if discount_percent:
                discount_percent = float(discount_percent)
                if discount_percent > 100:
                    self.error_flag = True
                    self.error_message.append(f"discount percent cannot exceed 100 for {error_dict.get(discount_type)}")
        except (ValueError, TypeError):
            self.error_flag = True
            self.error_message.append(f"Invalid discount percent for {discount_type}")

        try:
            if discount_amount:
                discount_amount = float(discount_amount)
        except (ValueError, TypeError):
            self.error_flag = True
            self.error_message.append(f"Invalid discount amount for {discount_type}")

        return discount_percent, discount_amount

    def validate_discount(self, discount_percent, discount_amount, discount_type):
        """
        Validates the discount values for both schedule and cash discounts.

        Parameters:
        discount_percent (float): The discount percentage.
        discount_amount (float): The discount amount.
        discount_type (str): The type of discount, e.g., 'scd' for schedule discount or 'cd' for cash discount.
        """
        self.error_flag = False
        decimal_limit = self.decimal_limit_price
        self.net_gross_amount = self.gross_amount = round(self.each_row.get('invoice_quantity', 0) * self.each_row.get('buy_price', 0),decimal_limit)
        discount_percent, discount_amount = self.validate_discont_percent_and_amt(discount_percent, discount_amount, discount_type)

        if self.error_flag:
            return
        
        error_messages = {
            'scd': {'percent_error': GRN_ERROR_MESSAGE[204], 'amount_error': GRN_ERROR_MESSAGE[205], 'mismatch_error': GRN_ERROR_MESSAGE[210]},
            'cd': {'percent_error': GRN_ERROR_MESSAGE[206], 'amount_error': GRN_ERROR_MESSAGE[207], 'mismatch_error': GRN_ERROR_MESSAGE[211]},
        }

        # Validate negative values
        if discount_percent and discount_percent < 0:
            self.error_flag = True
            self.error_message.append(error_messages[discount_type]['percent_error'])

        if discount_amount and discount_amount < 0:
            self.error_flag = True
            self.error_message.append(error_messages[discount_type]['amount_error'])

        # Validate zero and non-zero combinations
        if (discount_percent and discount_percent > 0 and discount_amount == 0) or (discount_percent and discount_percent == 0 and discount_amount > 0):
            self.error_flag = True
            self.error_message.append(error_messages[discount_type]['mismatch_error'])


    def prepare_financial_values_dict(self):
        values_list = ['buy_price', 'invoice_quantity', 'invoice_free_quantity',
                       'scheduled_percent', 'scheduled_amount', 'cash_discount_percent',
                       'cash_discount_amount', 'tax', 'cess_tax', 'mrp']
        self.financial_values_dict = {}
        for value in values_list:
            if self.each_row.get(value):
                try:
                    self.financial_values_dict[value] = float(self.each_row.get(value))
                except Exception:
                    self.error_message.append(f"Invalid {value} value")
            else:
                self.financial_values_dict[value] = self.each_row.get(value, 0)
                
    def tax_fields_validation(self):
        self.tax_fields = {}
        if self.validation_type != 'asn_grn_creation':
            schedule_discount = self.each_row.get('scheduled_percent', 0)
            schedule_amount = self.each_row.get('scheduled_amount', 0)
            cash_discount = self.each_row.get('cash_discount_percent', 0)
            cash_discount_amount = self.each_row.get('cash_discount_amount', 0)
            self.validate_discount(schedule_discount, schedule_amount, "scd")
            self.validate_discount(cash_discount, cash_discount_amount, "cd")
            if getattr(self, 'schedule_discount_error_flag', False) or getattr(self, 'error_flag', False):
                return
            self.prepare_financial_values_dict()
            self.tax_fields = process_tax_fields(self.financial_values_dict, decimal_limit = self.decimal_limit_price,
                                            )
            if self.misc_dict.get('margin_value_validation') and self.misc_dict.get('margin_value_validation') not in ["False", "false"] and self.grn_type != "ASNSave":
                if self.tax_fields.get('margin', 0) and self.each_row['sku_code'] in self.sku_gatekeeper_mapping_dict and truncate_float(self.tax_fields.get('margin', 0), self.decimal_limit_price) < truncate_float(self.sku_gatekeeper_mapping_dict[self.each_row['sku_code']], self.decimal_limit_price):
                        self.error_message.append(GRN_ERROR_MESSAGE[212])
        
    def prepare_grn_inspection_data(self, each_data, po_dict):
        """ Prepare GRN inspection data """
        continue_check = False
        if each_data.get('send_for_inspection', False) and self.asn_number:
            created_on = datetime.datetime.now()
            created_on = get_local_date_known_timezone(self.timezone, created_on,send_date=True).strftime("%d %b, %Y %I:%M %p")
            data_dict = copy.deepcopy(each_data)
            data_dict['submitted_on'] = created_on
            data_dict['submitted_by'] = data_dict.get('aux_data', {}).get('submitted_by', '')
            data_dict['error_type'] = data_dict.get('aux_data', {}).get('error_type', '')
            data_dict['po_reference'] = po_dict.get('po_reference', '')
            data_dict['sku_desc'] = po_dict.get('sku_desc', '')
            data_dict['sku_id'] = po_dict.get('sku_id', '')
            for key in ['manufactured_date', 'expiry_date', 'retest_date']:
                if data_dict.get(key):
                    data_dict[key] = data_dict[key].strftime("%m-%d-%Y")
            if po_dict.get('sku_id', '') in self.grn_inspection_dict:
                self.grn_inspection_dict[po_dict.get('sku_id', '')].append(data_dict)
            else:
                self.grn_inspection_dict[po_dict.get('sku_id', '')] = [data_dict]
            continue_check = True
        return continue_check

    def frame_asn_upload_error_key(self, sku_code):
        if self.source == 'Upload':
            self.index_sku_key = str(self.each_row.get('row_index', 0))+'_'+sku_code

    def get_mrp_based_on_asn_type(self, data, po_dict):
        if po_dict.get('po_type', '').lower() == "stocktransfer":
            mrp = self.each_row.get('mrp', 0)
        else:
            mrp = self.each_row.get('mrp') or po_dict.get('mrp')
        return mrp

    def segregate_lpn_data_by_asn(self, data, lpn_quantities):
        """Segregate lpn data at asn level"""
        for item in data['items']:
            if item.get('send_for_inspection') or not item.get('asn_ids'):
                continue
            accepted_qty = item['accepted_quantity']
            rejected_qty = item['rejected_quantity']

            # Initialize LPN list for this item
            item_lpn_data = {'lpns': []}

            # Iterate over LPN quantities to allocate
            for lpn_number, quantities in lpn_quantities.items():
                # Allocate accepted quantities
                if accepted_qty > 0 and quantities['accepted_quantity'] > 0:
                    allocated_accepted = min(accepted_qty, quantities['accepted_quantity'])
                    item_lpn_data['lpns'].append({
                        'lpn_number': lpn_number,
                        'packed_quantity': allocated_accepted,
                        'quantity_type': 'accepted_quantity'
                    })
                    # Update remaining quantities
                    quantities['accepted_quantity'] -= allocated_accepted
                    accepted_qty -= allocated_accepted

                # Allocate rejected quantities
                if rejected_qty > 0 and quantities['rejected_quantity'] > 0:
                    allocated_rejected = min(rejected_qty, quantities['rejected_quantity'])
                    item_lpn_data['lpns'].append({
                        'lpn_number': lpn_number,
                        'packed_quantity': allocated_rejected,
                        'quantity_type': 'rejected_quantity'
                    })
                    # Update remaining quantities
                    quantities['rejected_quantity'] -= allocated_rejected
                    rejected_qty -= allocated_rejected

            # Add LPN data to the item
            item['lpns'] = item_lpn_data['lpns']

    def segregate_items_by_asn(self, data):
        """ Segregates quantities across ASN IDs
            for accepted and rejected items in case of mutli po asn
        """
        asn_ids = []
        asn_wise_items = {}
        lpn_quantities = {}
        # Filter and collect relevant items and ASN IDs
        items_list = copy.deepcopy(data.get('items', []))
        for each_item in items_list:
            if not each_item.get('send_for_inspection') and each_item.get('asn_ids'):
                asn_ids.extend(each_item['asn_ids'])
                asn_wise_items[each_item['asn_ids'][0]] = each_item
                if each_item.get('asn_id') not in each_item.get('asn_ids'):
                    return {}, 'Invalid Payload'

                for lpn_data in each_item.get('lpns', []):
                    lpn_number = lpn_data.get('lpn_number')
                    if not lpn_number:
                        continue

                    lpn_quantities.setdefault(lpn_number, {'accepted_quantity': 0, 'rejected_quantity': 0})
                    qty_key = 'accepted_quantity' if lpn_data.get('quantity_type') == 'accepted_quantity' else 'rejected_quantity'
                    lpn_quantities[lpn_number][qty_key] += lpn_data.get('packed_quantity', 0)

                data['items'].remove(each_item)

        if not asn_ids:
            return data, ''
        asn_objs = ASNSummary.objects.filter(id__in=asn_ids,asn_user=self.warehouse.id).values(
            'id', 'quantity', 'purchase_order_id','invoice_quantity', 'line_invoice_quantity',
            'invoice_free_quantity', 'line_invoice_free_quantity', 'purchase_order__po_number'
        )
        asn_dict = {}
        for each_asn in asn_objs:
            asn_dict[each_asn['id']] = each_asn
        # Extract the base item (assuming there's at least one item)
        new_items = []
        for base_item in asn_wise_items.values():
            # Get total quantities for accepted and rejected from the base item
            total_accepted = base_item.get('accepted_quantity', 0)
            total_rejected = base_item.get('rejected_quantity', 0)

            relevant_asn_ids = [asn_id for asn_id in asn_dict if asn_id in base_item.get('asn_ids', [])]
            # Calculate the total quantity across all ASN IDs
            for asn_id in relevant_asn_ids:
                asn_data = asn_dict[asn_id]
                asn_qty = asn_data['quantity']
                inv_qty = asn_data['invoice_quantity'] or asn_data['line_invoice_quantity']
                free_qty = asn_data['invoice_free_quantity'] or asn_data['line_invoice_free_quantity']
                asn_qty += free_qty
                po_id  = asn_data['purchase_order_id']
                po_number = asn_data['purchase_order__po_number']
                # Create a copy of the base item for each ASN ID
                item_copy = base_item.copy()
                item_copy['asn_id'] = asn_id  # Assign the current ASN ID
                item_copy['id'] = po_id
                item_copy['po_id'] = po_id
                item_copy['invoice_quantity'] = inv_qty
                item_copy['invoice_free_quantity'] = free_qty + inv_qty
                item_copy['line_invoice_free_quantity'] = asn_data['line_invoice_free_quantity']
                item_copy['po_number'] = po_number

                # Initialize quantities
                item_copy['accepted_quantity'] = 0
                item_copy['rejected_quantity'] = 0

                accepted_alloc = min(total_accepted, asn_qty)
                item_copy['accepted_quantity'] = accepted_alloc
                total_accepted -= accepted_alloc

                # Allocate rejected quantity if applicable
                if total_rejected > 0:
                    available_qty_for_rejection = asn_qty - item_copy['accepted_quantity']
                    rejected_alloc = min(total_rejected, available_qty_for_rejection)
                    item_copy['rejected_quantity'] = rejected_alloc
                    total_rejected -= rejected_alloc

                # Calculate received quantity
                received_quantity = (
                    item_copy['accepted_quantity'] +
                    item_copy['rejected_quantity']
                )
                item_copy['received_quantity'] = received_quantity

                # Append the modified item to the new_items list
                new_items.append(item_copy)

        # Update the data with the new segregated items
        data['items']+=new_items
        self.segregate_lpn_data_by_asn(data, lpn_quantities)

        return data, ''
    
    def validate_asn_tax_with_po_tax(self, sku_code, po_id):
        po_tax_dict = self.po_tax_dict.get((po_id,sku_code), {})
        asn_tax_dict = self.asn_total_tax_dict.get((po_id,sku_code), {})
        if asn_tax_dict and po_tax_dict and (asn_tax_dict != po_tax_dict):
            self.error_message.append(f"PO Tax is already updated once, further update is not allowed")

    def supplier_sku_mapping_data(self):
        self.sku_gatekeeper_mapping_dict = {}
        if self.supplier_id:
            result = (SKUSupplier.objects
              .filter(supplier__supplier_id = self.supplier_id, supplier__user = self.warehouse.id,
                      sku__sku_code__in = self.sku_code_list)
              .annotate(min_preference=Min('preference'))
              .values('sku__sku_code', 'gatekeeper_margin'))
            for each_row in result:
                self.sku_gatekeeper_mapping_dict[each_row['sku__sku_code']] = each_row['gatekeeper_margin']
    
    def update_batch_attributes_with_po_line_fields(self, po_id):
        po_line_extra_fields = self.line_extra_data.get(str(po_id), {})
        defined_batch_attributes = self.misc_dict.get('extra_batch_attributes', {})
        extra_batch_attributes = self.each_row.get("extra_batch_attributes", {})
        for key, value in po_line_extra_fields.items():
            if key not in extra_batch_attributes and key in defined_batch_attributes:
                extra_batch_attributes[key] = value
        self.each_row['extra_batch_attributes'] = extra_batch_attributes
    
    def prepare_serial_data(self, po_dict, lpns=None, lpn_quantity=0):
        enable_serial_based = po_dict.get('enable_serial_based', False)
        serial_numbers = self.each_row.get('serial_numbers', [])
        accepted_quantity = self.each_row.get('accepted_quantity', 0) if 'accepted_quantity' in self.each_row else self.each_row.get('invoice_quantity', 0)
        rejected_quantity = self.each_row.get('rejected_quantity', 0)
        accepted_serial_numbers = self.each_row.get('accepted_serial_numbers', [])
        rejected_serial_numbers = self.each_row.get('rejected_serial_numbers', [])
        existing_serials = set(self.existing_serials_dict.get(self.asn_id, []))
        if lpns:
            for each_lpn in lpns:
                serial_numbers = each_lpn.get('serial_numbers', [])
                if serial_numbers and enable_serial_based:
                    if each_lpn.get('packed_quantity') != len(set(serial_numbers)):
                        self.error_message.append(GRN_ERROR_MESSAGE[29])
                        continue
                    self.serial_data_to_validate.append({
                        'lpn_number': each_lpn.get('lpn_number'),
                        'sku_id': po_dict.get("sku_id"),
                        'batch_number': self.each_row.get('batch_number'),
                        'serial_numbers': serial_numbers
                    })
                elif enable_serial_based:
                    self.error_message.append(GRN_ERROR_MESSAGE[27])
                elif serial_numbers and not enable_serial_based:
                    self.error_message.append(GRN_ERROR_MESSAGE[28])
                if self.validation_type == 'asn_grn_creation':
                    self.asn_id_wise_serial_dict.setdefault(self.asn_id, []).extend(serial_numbers)
                    if not set(serial_numbers).issubset(existing_serials):
                        self.error_message.append(','.join(map(str, set(serial_numbers) - existing_serials)) + " " + GRN_ERROR_MESSAGE[30])

        elif accepted_serial_numbers or rejected_serial_numbers:
            if accepted_serial_numbers and enable_serial_based:
                if accepted_quantity != len(set(accepted_serial_numbers)):
                    self.error_message.append(GRN_ERROR_MESSAGE[29])
                self.serial_data_to_validate.append({
                    'lpn_number': '',
                    'sku_id': po_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': accepted_serial_numbers
                })
            elif enable_serial_based and accepted_quantity:
                self.error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                self.error_message.append(GRN_ERROR_MESSAGE[28])
            if rejected_serial_numbers and enable_serial_based:
                if rejected_quantity != len(set(rejected_serial_numbers)):
                    self.error_message.append(GRN_ERROR_MESSAGE[29])
                self.serial_data_to_validate.append({
                    'lpn_number': '',
                    'sku_id': po_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': rejected_serial_numbers
                })
                serial_numbers = accepted_serial_numbers + rejected_serial_numbers
            elif enable_serial_based and rejected_quantity:
                self.error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                self.error_message.append(GRN_ERROR_MESSAGE[28])
            if self.validation_type == 'asn_grn_creation':
                self.asn_id_wise_serial_dict.setdefault(self.asn_id, []).extend(serial_numbers)
                if not set(serial_numbers).issubset(existing_serials):
                    self.error_message.append(','.join(map(str, set(serial_numbers) - existing_serials)) + " " + GRN_ERROR_MESSAGE[30])

            elif len(set(accepted_serial_numbers)) + len(set(rejected_serial_numbers)) != lpn_quantity:
                self.error_message.append(GRN_ERROR_MESSAGE[29])

        else:
            if serial_numbers and enable_serial_based:
                if lpn_quantity != len(set(serial_numbers)):
                    self.error_message.append(GRN_ERROR_MESSAGE[29])
                self.serial_data_to_validate.append({
                    'lpn_number': '',
                    'sku_id': po_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': serial_numbers
                })
            elif enable_serial_based:
                self.error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                self.error_message.append(GRN_ERROR_MESSAGE[28])

    def get_existing_serials(self):
        """Fetch Existing serial numbers for each asn id """
        if self.validation_type == 'asn_grn_creation':
            serial_filter = {'filters': {'reference_type': 'asn', 'transact_id__in': self.asn_ids, 'status': 1}}
            serial_mixin = SerialNumberTransactionMixin(self.warehouse, self.warehouse, serial_filter)
            existing_serials = serial_mixin.get_sntd_details()
            for data in existing_serials.get('data', []):
                self.existing_serials_dict.setdefault(data['transact_id'], []).extend(data['serial_numbers'])

    def prepare_serial_shortage_dict(self):
        for asn_id, serial_numbers in self.asn_id_wise_serial_dict.items():
            short_serials = set(self.existing_serials_dict.get(asn_id, [])) - set(serial_numbers)
            if short_serials:
                self.short_serials_dict[asn_id] = list(short_serials)

    def check_po_type_level_qc_exceptions(self, min_shelf_life_exception, customer_shelf_life_exception, mrp_exception, po_type, json_data):
        """ Check if the PO type has QC exceptions and set the appropriate flags."""
        send_for_qc = False
        qc_exception_type = ''
        po_type = po_type.lower() if po_type else 'normal'
        if self.qc_enabled and self.qc_config_data and po_type in self.qc_config_data:
            qc_options = self.qc_config_data[po_type]
            if 'expiry_date_exception_min_shelf_life_check' in qc_options and min_shelf_life_exception:
                qc_exception_type = 'expiry_date_exception_min_shelf_life_check'
            if 'expiry_date_exception_customer_shelf_life_check' in qc_options and customer_shelf_life_exception:
                qc_exception_type += ' ,expiry_date_exception_customer_shelf_life_check'
            if 'mrp_exception' in qc_options and mrp_exception:
                qc_exception_type += ' ,mrp_exception'
        if qc_exception_type:
            send_for_qc = True
            json_data['qc_exception_type'] = qc_exception_type
        return send_for_qc

    def line_level_validation(self, data, po_dict_data, po_values):
        self.total_grn_amount = 0
        self.total_grn_amount_with_tax = 0
        self.asn_amount = 0
        self.asn_amount_with_tax = 0
        self.serial_data_to_validate = []
        self.boe_grn_total = 0
        counter = 0
        self.sku_list = []
        self.sku_category_list = []
        self.rejected_reasons = []
        self.approval_grn = data.get('approval_grn', False)
        self.sku_code_list = [item['sku_code'] for item in data['items']]
        self.supplier_id = data.get('supplier_id')
        self.supplier_sku_mapping_data()
        self.line_extra_data = get_extra_attributes(list(po_dict_data.keys()), "po_line_extra_fields")
        self.get_existing_serials()
        for self.each_row in data['items']:
            self.error_message = []
            has_combo_error = ''
            sku_code = self.each_row['sku_code']
            json_data = self.each_row.get("aux_data", {})
            po_id = self.each_row.get("po_id")
            self.asn_id = self.each_row.get("asn_id", None)
            self.index_sku_key = str(counter)+'_'+sku_code
            #frame error message for asn upload
            self.frame_asn_upload_error_key(sku_code)

            put_zone_id, zone_restriction = self.get_put_zone_id()
        
            #Fetching Line Level PO Number (Using for Multiple POs in one ASN Case)
            if self.each_row.get("po_number"):
                self.po_number = self.each_row.get("po_number")

            if self.each_row.get("po_reference"):
                self.po_reference = self.each_row.get("po_reference")
                        
            #Fetching PO Id from the PO SKU ID Mapping Dict
            line_reference = json_data.get("po_line_reference", '')
            
            continue_this = self.purchase_order_id_validation(sku_code, line_reference)
            if continue_this:
                continue
            #Fetching SKU Level PO Data
            po_dict = po_dict_data.get(self.po_id, {})
            self.each_row['sku_id'] = po_dict.get('sku_id')
            #Check for GRN inspection
            contine_check = self.prepare_grn_inspection_data(self.each_row, po_dict)
            if contine_check:
                continue
            mrp = self.get_mrp_based_on_asn_type(data, po_dict)
            batch_based = po_dict.get('batch_based', False)
            self.update_batch_attributes_with_po_line_fields(self.po_id)
            if not (self.misc_dict.get('allow_asn_creation_without_batch', '') == 'true' and (self.is_asn or self.validation_type in ["asn_update", "asn_creation"])):
                has_combo_error = self.batch_validation(sku_code, batch_based)

            self.lpn_validation(json_data, po_dict)

            self.tax_fields_validation()

            if self.po_tax_dict and self.asn_total_tax_dict:
                self.validate_asn_tax_with_po_tax(sku_code, po_id)
            if self.each_row.get('line_extra_fields'):
                error_messages, _ = validate_attributes(self.warehouse, self.each_row['line_extra_fields'], "asn_line_extra_fields", self.asn_line_extra_fields)
                for error_msg in error_messages:
                    self.error_message.append(error_msg)
            self.sku_code = self.each_row['sku_code']

            #Fetching SKU Level PO Data
            po_dict_json_data = po_dict.get('json_data', {})
            manufacturer_name = po_dict_json_data.get('manufacturer_name', '')

            #Fetching ASN Id from SKU Code, Batch, MRP and Price
            self.asn_batch_id = None
            self.asn_free_quantity = 0
            self.asn_quantity = 0
            self.po_asn_qty = self.po_asn_qty_dict.get(self.po_id, 0)
            self.asn_draft_qty = self.asn_draft_qty_dict.get(self.po_id, 0)
            self.asn_json_data = {}
            if self.asn_number:
                self.asn_quantity, self.asn_free_quantity, self.received_free_quantity, self.asn_batch_id, self.asn_json_data = self.get_asn_data_with_asn_id(mrp)
            
            sku_type = po_dict['sku_type']
            custom_attr_ids = self.asn_checklist_validation(self.po_id, sku_type)
            price = self.get_price()
            unique_line_id = self.each_row.get('id') or line_reference
            key = self.po_number, sku_code, unique_line_id, self.each_row.get("batch_number"), price, mrp, self.each_row.get('batch_reference', ''), self.each_row.get('vendor_batch_number', ''), self.each_row.get('rejected_reason', ''), self.asn_id
            self.zero_quantity_check = False
            self.get_asn_free_quantity()
            self.get_quantities(key)
            if self.zero_quantity_check:
                continue

            self.has_grn_quantity = True
            check_rec_quantity = round(po_dict.get('order_quantity', 0) + po_dict.get('free_quantity') + po_dict.get('saved_quantity', 0) - po_dict.get('received_quantity', 0) - self.po_asn_qty, self.decimal_limit)
            receive_qty_percent = self.get_sku_tolerance_value(po_dict.get('receipt_tolerance', 0))

            #Minimum Shelf Life Validation
            min_shelf_life_exception = self.min_shelf_life_validation(po_dict.get('minimum_shelf_life'), po_dict)
            #Customer Shelf Life Validation
            customer_shelf_life_exception = self.customer_shelf_life_validation(po_dict.get('customer_shelf_life'))
            #MRP Validation
            mrp_exception = self.mrp_validation(mrp)
            send_for_qc = self.get_send_for_qc(po_dict['qc_check'])
            if not send_for_qc and not self.is_asn:
                send_for_qc = self.check_po_type_level_qc_exceptions(min_shelf_life_exception, customer_shelf_life_exception, mrp_exception, po_dict.get('po_type', ''), json_data)

            #Quantity Validations
            self.extend_qty = math.ceil((receive_qty_percent/100) * po_dict.get('order_quantity', 0)) or 0
            po_data, open_po_dict = {}, {}
            if self.each_row.get('free_sku'):
                open_po_dict, po_data = self.frame_dict_for_po_creation(sku_code, price, po_values[0], put_zone_id)
            else:
                self.quantity_validations(key, unique_line_id, check_rec_quantity, po_dict)
                po_data = self.frame_po_dict(po_dict, put_zone_id, zone_restriction)
                
            self.weighing_validation(json_data)
            self.sku_inv_price_tolerance_validation(price)
            
            counter+=1
            
            challan_number = data.get("challan_number","")
            invoice_number = data.get("invoice_number", "")
            
            order_status_flag = self.get_order_status_flag(challan_number, invoice_number)
            
            self.check_is_serial_based_flow(sku_code, po_dict.get("serial_based"))
            source_wh_firstname, source_wh_username = self.get_source_wh_details(po_dict)
            
            open_po_json_data = po_dict.get("json_data", {})
            self.po_currency = open_po_json_data.get('po_currency', self.base_currency)

            batch_detail_dict = self.get_batch_detail_dict(mrp, price, po_dict, manufacturer_name)
            sku_batch_details = batch_detail_dict.copy()
            if batch_based and sku_batch_details and self.grn_type != "ASNSave" and self.misc_dict.get('allow_asn_creation_without_batch', '') != 'true':
                self.sku_batch_detail_validation(sku_batch_details)
                            
            if self.combo_sku_check and self.sku_combo_dict.get(self.each_row['sku_code']) and not self.is_asn:
                combo_error_list = self.validate_combo_quantities(
                    self.each_row.get('combo_sku_data', []), self.parent_child_dict.get(sku_code, {}))
                if has_combo_error:
                    continue
                self.frame_combo_batch_data(sku_code, price, combo_error_list, unique_line_id)
            
            grn_data_dict = {}
            self.get_received_free_quantity()
            if key not in self.aggr_grn_data_dict:
                grn_data_dict = {
                    "user" : po_dict.get("user"),
                    "warehouse_user": self.source_wh_user,
                    "warehouse_first_name": source_wh_firstname,
                    "warehouse_username": source_wh_username,

                    'grn_type': self.grn_type,
                    "purchase_order_id": self.po_id,
                    "grn_reference": self.grn_reference,
                    
                    "asn_id": self.asn_id,
                    "asn_batch_id": self.asn_batch_id,
                    "asn_number": self.asn_number,
                    "saved_quantity": self.asn_quantity,
                    "saved_free_quantity": self.asn_free_quantity,
                    
                    "po_product_category": "",
                    
                    "invoice_number": invoice_number,
                    "challan_number": challan_number,
                    "order_status_flag": order_status_flag,
                    "challan_date":  data.get("challan_date",None),
                    "discount_percent": self.each_row.get("discount_percent", 0),
                    "credit_status": self.credit_status,
                    "round_off_total": self.each_row.get("total_amount", 0), #for import grn round_off_total = assessable value

                    "sku_code": self.each_row['sku_code'],
                    "sku_category": po_dict.get('sku_category', ''),
                    "unique_line_id" : unique_line_id,
                    "batch_based" : batch_based,
                    "batch_detail": batch_detail_dict,
                    "sku_attributes" : self.each_row.get("attributes", {}),
                    "status" : self.each_row.get('status'),
                    "carton_number": self.each_row.get("carton_number", ""),

                    "open_po_dict": open_po_dict,
                    "free_sku": self.each_row.get('free_sku', False),
                    
                    "putaway_quantity" : self.received_quantity,
                    "quantity": self.received_quantity,
                    "received_quantity": self.received_quantity,
                    "accepted_quantity" : self.accepted_quantity,
                    "rejected_quantity": self.rejected_quantity,
                    "return_quantity": self.return_quantity,
                    "extend_qty" : self.extend_qty,
                    "invoice_value": self.invoice_value,
                    
                    "invoice_quantity": self.invoice_quantity,
                    "invoice_free_quantity": self.invoice_free_quantity,
                    "line_invoice_quantity": self.line_invoice_quantity,
                    "line_invoice_free_quantity": self.line_invoice_free_quantity,
                    "invoice_date": data.get("invoice_date", None),
                    
                    "po_remarks": data.get("remarks", ""),
                    "rejected_reason": self.each_row.get("rejected_reason", ""),
                    "remarks": self.each_row.get("remarks", ""),
                    "discrepency_reason": self.each_row.get("reason", ""),
                    "json_data": copy.deepcopy(json_data),
                    "replenish_data": self.each_row.get("replenish_data", []),
                    
                    "cess_tax": self.each_row.get("cess_tax", 0),
                    "overall_discount": 0,
                    "tcs_value": data.get("tcs", 0),
                    "price": price,
                    
                    "po_invoice_quantity": data.get("invoice_quantity", 0),
                    "amount" : price * self.received_quantity,
                    
                    "custom_duty_charges": self.each_row.get('custom_duty_charges', 0),
                    "additional_charges": self.each_row.get('additional_charges', 0),
                    "boe_total": self.each_row.get('boe_total', 0),
                    "custom_attr_ids" : custom_attr_ids,
                    
                    "serial_numbers": self.each_row.get('serial_numbers', []),
                    "accepted_serial_numbers": self.each_row.get('accepted_serial_numbers', []),
                    "rejected_serial_numbers": self.each_row.get('rejected_serial_numbers', []),
                                        
                    "send_for_qc" : send_for_qc,
                    "company_id" : self.company_id,
                    "decimal_limit" : self.decimal_limit,
                    "source": self.source,
                    "tax": self.each_row.get("tax", 0),
                    "received_free_quantity": self.received_free_quantity,
                    **self.tax_fields,
                    "gatekeeper_margin": self.each_row.get('gatekeeper_margin', 0),
                    "calculated_margin": self.each_row.get('margin', 0),
                    "asn_reference": data.get('asn_reference'),
                    "line_extra_fields": self.each_row.get("line_extra_fields", {})
                }
                self.update_supplier_taxes(self.each_row, grn_data_dict)
                grn_data_dict.update(po_data)
                grn_data_dict.update({ "sgst_tax" : self.each_row.get('sgst_tax', 0),
                    "cgst_tax" : self.each_row.get('cgst_tax', 0),
                    "igst_tax" : self.each_row.get('igst_tax', 0),
                    "apmc_tax" : self.each_row.get('apmc_tax', 0),
                    "utgst_tax": self.each_row.get('utgst_tax', 0)})
                self.sku_list.append(self.each_row['sku_code'])
                self.sku_category_list.append(po_dict.get('sku_category', ''))
                self.rejected_reasons.append(self.each_row.get('rejected_reason', ''))

            self.asn_amount += self.tax_fields.get('taxable_amount', 0)
            self.asn_amount_with_tax += self.tax_fields.get('net_amount', 0)
            self.total_grn_amount += price * self.line_invoice_quantity
            self.total_grn_amount_with_tax += price * self.line_invoice_quantity * (1 + (self.each_row.get('tax', 0) + self.each_row.get('cess_tax', 0))/ 100)
            self.boe_grn_total += self.each_row.get('boe_total', 0)
            
            self.frame_aggregated_grn_data_dict(key, grn_data_dict)
            
            if self.error_message:
                self.key_wise_error_dict[self.index_sku_key] = [self.error_message]
    
    def update_supplier_taxes(self, each_row, grn_data_dict):
        # Retrieve tax values, defaulting to None if not present
        grn_data_dict['supplier_cgst'] = each_row.get('supplier_cgst')
        grn_data_dict['supplier_sgst'] = each_row.get('supplier_sgst')
        grn_data_dict['supplier_igst'] = each_row.get('supplier_igst')
        grn_data_dict['supplier_cess'] = each_row.get('supplier_cess')
        
        # Collect tax values
        tax_values = [grn_data_dict[tax] for tax in ['supplier_cgst', 'supplier_sgst', 'supplier_igst', 'supplier_cess']]
        
        # Filter out non-numeric values ('', None) and keep only values that can be converted to float
        numeric_values = [float(tax) for tax in tax_values if tax not in (None, '')]
        
        # Set supplier_total_tax based on the conditions
        if numeric_values:  # If there's at least one valid number (0 or greater)
            grn_data_dict['supplier_total_tax'] = sum(numeric_values)
        else:  # If all values were '' or None
            grn_data_dict['supplier_total_tax'] = None

    def frame_grn_extra_dict(self):
        self.grn_extra_dict.update({'grn_inspection_data': self.grn_inspection_dict})
        if self.has_grn_quantity:
            if self.total_grn_amount: 
                self.grn_extra_dict['total_grn_amount'] = self.total_grn_amount
            if self.total_grn_amount_with_tax:
                self.grn_extra_dict['total_grn_amount_with_tax'] = self.total_grn_amount_with_tax
            if self.asn_amount:
                self.grn_extra_dict['asn_amount'] = self.asn_amount
            if self.asn_amount_with_tax:
                self.grn_extra_dict['asn_amount_with_tax'] = self.asn_amount_with_tax
            cn_amount = self.data.get('cn_amount',0)
            tcs = self.data.get('tcs',0)
            discount_amount = self.data.get('discount_amount', 0)
            additional_cost = self.data.get('additional_cost', 0)
            self.grn_extra_dict['asn_total_value_with_tax'] = self.asn_amount_with_tax - cn_amount  - discount_amount + additional_cost + tcs
            self.grn_extra_dict['total_grn_amount_with_tax'] = self.total_grn_amount_with_tax - cn_amount - discount_amount + additional_cost + tcs
    
            self.grn_extra_dict.update({
                'grn_type' : self.grn_type,
                'source' : self.source,
                'po_number' : self.po_number,
                'po_reference': self.po_reference,
                'serial_based' : self.serial_based,
                'aggregated_sku_count' : self.aggregated_sku_count,
                'receive_qty_percent' : self.receive_qty_percent,

                'boe_grn_total' : self.boe_grn_total,
                'po_currency' : self.po_currency,
                'to_location_id' : self.to_location_id,
                'to_location': self.to_location,

                'import_data' : self.import_data,
                'qc_transaction_type' : self.qc_transaction_type,
                'supplier_obj' : self.supplier_obj,
                'sku_shortage_dict' : self.sku_shortage_dict,
                'lpn_numbers' : self.lpn_numbers,
                'sku_strategy_mapping' : self.sku_strategy_mapping,
                'category_strategy_mapping': self.category_strategy_mapping,
                'location_mapping_capacity': self.location_mapping_capacity,
                'mapped_locations': self.mapped_locations,
                'mapped_zones': self.mapped_zones,
                'rej_reason_putaway_mapping': self.rej_reason_putaway_mapping,
                'short_putaway_mapping': self.short_putaway_mapping,
                'short_serials_dict': self.short_serials_dict,
                'existing_serials_dict': self.existing_serials_dict,
                'no_of_skus': len(set(self.sku_codes)),
                'total_quantity': self.total_asn_quantity,

                'auto_putaway': self.misc_dict.get('auto_putaway', '')
            })               
        elif not self.grn_inspection_dict and not self.key_wise_error_dict[self.index_sku_key]:
            self.key_wise_error_dict['header'] = ["No Recieved Quantity Given to Process GRN"]

    def grn_extra_fields_validation(self, grn_extra_fields):
        if self.validation_type == "asn_update" or self.is_asn:
            self.transaction_type = 'asn'
        else:
            self.transaction_type = 'grn'
        extra_fields = {}
        grn_user_attributes = [grn_attr.get('attribute_name') for grn_attr in get_user_attributes(self.warehouse, self.transaction_type)]
        for field_key, value in grn_extra_fields.items():
                if field_key.startswith('attr_'):
                    field_key = field_key.split('attr_')[1]
                if field_key in grn_user_attributes:
                    extra_fields[field_key] = value
        if extra_fields:
            error_message, _ = validate_attributes(self.warehouse, extra_fields, self.transaction_type)
            if error_message:
                self.key_wise_error_dict['header'] = [error_message]
    
    def invoice_price_tolerance_validation(self):
        """Checks and validates the invoice value tolerance for GRN or ASN."""
        if self.transaction_type == 'asn':
            invoice_tolerance_enable_key = 'enable_invoice_value_in_asn'
            invoice_tolerance_value = 'asn_inv_price_tolerance'
            error_message = GRN_ERROR_MESSAGE[9]
            total_amount = self.grn_extra_dict.get('asn_total_value_with_tax', 0)
        else:
            invoice_tolerance_enable_key = 'enable_invoice_value_in_grn'
            invoice_tolerance_value = 'grn_inv_price_tolerance'
            error_message = GRN_ERROR_MESSAGE[11]
            total_amount = self.grn_extra_dict.get('total_grn_amount_with_tax', 0)

        if self.misc_dict.get(invoice_tolerance_enable_key, '') == 'true' and self.grn_type != "ASNSave":
            tolerance_value = self.misc_dict.get(invoice_tolerance_value, 'null')
            inv_price_tolerance = float(tolerance_value) if tolerance_value and tolerance_value not in ['false', 'null'] else 0
            if inv_price_tolerance and abs(self.invoice_value - total_amount) > inv_price_tolerance:
                self.key_wise_error_dict['header'] = [error_message]

    def calculate_grn_amount_with_tax(self, grn_data_list):
        """Calculates the total GRN amount with and without tax."""
        value_with_tax, value_without_tax = 0,0
        short_qty_check = []
        blind_grn = self.misc_dict.get('blind_grn', '') == 'true'
        for data in grn_data_list:
            short_qty_key = (data['sku_code'], data['unique_line_id'])
            if blind_grn:
                short_qty_key = (data['sku_code'], data['unique_line_id'], data['asn_id'])
            elif data.get('asn_id'):
                short_qty_key = data['asn_id']
            if short_qty_key in self.sku_shortage_dict and data['sku_code'] not in short_qty_check:
                if self.sku_shortage_dict[short_qty_key] > 0:
                    data['received_quantity'] += self.sku_shortage_dict[short_qty_key]
                    short_qty_check.append(data['sku_code'])
            amount = data.get('price', 0) * data.get('received_quantity', 0)
            tax_amount = amount * ((data.get('tax', 0) + data.get('cess_tax', 0)) / 100)
            value_with_tax += amount + tax_amount
            value_without_tax += amount
        self.grn_extra_dict['total_grn_amount'] = round(value_without_tax, self.decimal_limit_price)
        self.grn_extra_dict['total_grn_amount_with_tax'] = round(value_with_tax, self.decimal_limit_price)

    def __init__(self):
        self.aggr_grn_data_dict = {}
        self.key_wise_error_dict = {}
        self.grn_extra_dict = {}
        
        self.packed_serial_numbers = []
        self.lpn_numbers = []
        self.has_grn_quantity = False
        self.grn_with_po_ref = False
        self.sku_shortage_dict = {}
        self.aggregated_sku_count = {}
        self.aggr_line_invoice_quantity = {}
        # Stores the aggregated received quantities for each SKU or line item.
        # This is used in conjunction with the `allow_partial_grn` logic to track
        # and validate received quantities when partial GRNs are allowed.
        self.aggr_received_quantity = {}
        self.aggr_line_invoice_free_quantity = {}
        self.serial_based = False
        self.grn_inspection_dict = {}
        self.short_serials_dict = {}
        self.asn_id_wise_serial_dict = {}


    def split_financial_values_validation(self,data):
        items = data.get('items',[])
        sku_batch_unique_sd_amount_dict = {}
        sku_batch_invoice_quantity_sum = {}
        sku_batch_unique_cd_amount_dict = {}
        updated_items = []
        for each_item in items:
            sku_code = each_item.get('sku_code')
            batch_no = each_item.get('batch_number')
            sd_amount = each_item.get('scheduled_amount',0)
            cd_amount = each_item.get('cash_discount_amount',0)
            key = (sku_code,batch_no)
            if sku_code and batch_no:
                if key in sku_batch_invoice_quantity_sum:
                        sku_batch_invoice_quantity_sum[key] += each_item.get('invoice_quantity',0)
                else:
                    sku_batch_invoice_quantity_sum[key] = each_item.get('invoice_quantity',0)
                if sd_amount:
                    if key not in sku_batch_unique_sd_amount_dict:
                        sku_batch_unique_sd_amount_dict[key] = sd_amount
                if cd_amount:
                    if key not in sku_batch_unique_cd_amount_dict:
                        sku_batch_unique_cd_amount_dict[key] = cd_amount

        for each_item in items:
            sku_code = each_item.get('sku_code')
            batch_no = each_item.get('batch_number')
            invoice_quantity = each_item.get('invoice_quantity')
            if (sku_code, batch_no) in sku_batch_invoice_quantity_sum:
                total_invoice_quantity = sku_batch_invoice_quantity_sum.get((sku_code, batch_no))
                if (sku_code, batch_no) in sku_batch_unique_sd_amount_dict:
                    sd_amount = round((float(invoice_quantity)/float(total_invoice_quantity)) * float(each_item.get('scheduled_amount',0)), self.decimal_limit_price)
                    each_item['scheduled_amount'] = sd_amount
                    each_item['scheduled_percent'] = 0
                if (sku_code, batch_no) in sku_batch_unique_cd_amount_dict:
                    cd_amount = round((float(invoice_quantity)/float(total_invoice_quantity)) * float(each_item.get('cash_discount_amount',0)), self.decimal_limit_price)
                    each_item['cash_discount_amount'] = cd_amount
                    each_item['cash_discount_percent'] = 0
            updated_items.append(each_item)

        data['items'] = updated_items
        return data
    
    def get_po_sku_wise_tax_data(self, asn_data, po_dict):
        po_ids_list = [each_item.get('po_id') for each_item in asn_data.get('items',[])]
        asn_data_list = list(ASNSummary.objects.filter(purchase_order__in = po_ids_list, status__in = [0,1,2],
                                                       purchase_order__open_po__sku__user = self.warehouse.id).values_list('purchase_order', flat = True))
        for key,value in po_dict.items():
                self.po_tax_dict[(key, value.get('sku_code'))] = (value.get('sgst_tax',0) + value.get('cgst_tax',0) + value.get('igst_tax',0)
                                          + value.get('cess_tax',0) +  value.get('apmc_tax',0) + value.get('utgst_tax',0))
        for each_item in asn_data.get('items',[]):
            po_id = each_item.get('po_id')
            sku_code = each_item.get('sku_code')
            if po_id in asn_data_list:
                self.asn_total_tax_dict[(po_id,sku_code)] = (each_item.get('sgst_tax',0) + each_item.get('cgst_tax',0) + each_item.get('igst_tax',0) 
                                              + each_item.get('cess_tax',0) + each_item.get('apmc_tax',0) + each_item.get('utgst_tax',0))
    
    def validate_gate_pass_status_for_asn(self):
        gate_pass_asn_numbers = []
        gate_pass_validation_config = self.misc_dict.get('gate_pass_validation', '')
        gate_pass_validation_config = gate_pass_validation_config.split(',') if gate_pass_validation_config else []
        if self.asn_number and 'PO' in gate_pass_validation_config:
            gate_pass_asn_number = list(GatePassItem.objects.exclude(gate_pass__status = 8).exclude(status = 2).filter(
                             gate_pass__warehouse=self.warehouse.id,gate_pass__gate_pass_type='inbound', transaction_id = self.asn_number,
                             gate_pass__gate_pass_sub_type='material_receipt').values('transaction_id','status', 'gate_pass__status'))
            for each_item in gate_pass_asn_number:
                if not (each_item['gate_pass__status'] == 0 and each_item['status'] == 0):
                    gate_pass_asn_numbers.append(each_item['transaction_id'])
            
            if self.asn_number not in gate_pass_asn_numbers:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[601]]

    def get_asn_draft_qty_dict(self):
        #Fetch asn draft qty for each po id
        self.asn_draft_qty_dict = {}
        if self.validation_type == 'asn_grn_creation':
            filter_dict = {'status__in':[5, 6, 7], 'asn_user':self.warehouse.id}
            if self.po_ref_list:
                filter_dict['purchase_order__open_po__po_name__in'] = self.po_ref_list
            else:
                filter_dict['purchase_order__po_number__in'] = self.po_numbers_list
            self.asn_draft_qty_dict = dict(ASNSummary.objects.filter(
               **filter_dict
            ).values('purchase_order_id').annotate(
                total_quantity=Sum('quantity')
            ).values_list('purchase_order_id', 'total_quantity'))
    
    def validate_serial_number_data(self):
        data_list, error = [], False
        if self.serial_data_to_validate:
            serial_mixin = SerialNumberMixin(None, self.warehouse, self.serial_data_to_validate)
            data_list = serial_mixin.validate_create_data()
            error = any([data.get('Status') for data in data_list])
        return data_list, error
    
    def prepare_line_wise_error_message(self, data, serial_data):
        """Frame Serial Error messages"""
        counter = 0
        serial_df = pd.DataFrame(serial_data)
        for each_item in data.get('items',[]):
            sku_code = each_item.get('sku_code')
            sku_id = each_item.get('sku_id')
            counter_key = str(counter)+'_'+sku_code
            #Check SKU batch in serial_df
            serial_df['batch_number'] = serial_df['batch_number'].fillna('')
            batch_number = each_item.get('batch_number') or ''
            serial_df_sku_batch = serial_df[(serial_df['sku_id'] == sku_id) & (serial_df['batch_number'] == batch_number)]
            if not serial_df_sku_batch.empty:
                if serial_df_sku_batch['Status'].any():
                    self.key_wise_error_dict.setdefault(counter_key, [])
                    error_list = []
                    for sublist in serial_df_sku_batch['Status'].values:
                        error_list.extend(sublist)  # because each is a list
                    error_message = ', '.join(error_list)
                    self.key_wise_error_dict[counter_key].append(error_message)

            counter += 1

    def validate_header_level_cache_check(self):
        """Adding cache check at header level po key"""
        if self.po_number:
            cache_po_number = f'{self.warehouse.id}##{self.po_number}'
            cache_status = cache.add(cache_po_number, "True", timeout=LOCK_EXPIRE)
            if not cache_status and self.po_number and self.po_number not in self.po_numbers_list:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[13] + str(self.po_number)]
                return True
            else:
                self.po_numbers_list.append(self.po_number)

        elif self.po_reference:
            cache_po_number = f'{self.warehouse.id}##{self.po_reference}'
            cache_status = cache.add(cache_po_number, "True", timeout=LOCK_EXPIRE)
            if not cache_status and self.po_reference and self.po_reference not in self.po_ref_list:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[13] + str(self.po_reference)]
                return True
            else:
                self.po_ref_list.append(self.po_reference)

        return False

    def validate(self, data, warehouse, is_asn=False, so_st_po=False, validation_type='', extra_params=None):
        """
            Validates the GRN (Goods Receipt Note) data.

            Args:
                data (dict): The GRN data to be validated.
                warehouse (int): The ID of the warehouse.
                is_cancelled (bool, optional): Flag indicating if the GRN is cancelled. Defaults to False.
                is_asn (bool, optional): Flag indicating if the GRN is for ASN (Advance Shipping Notice). Defaults to False.
                so_st_po (bool, optional): Flag indicating if the GRN is for SO (Sales Order) or ST (Stock Transfer) or PO (Purchase Order). Defaults to False.

            Returns:
                tuple: A tuple containing the following:
                    - key_wise_error_dict (dict): A dictionary containing the errors encountered during validation.
                    - request_data (dict): The validated GRN data.
                    - sku_shortage_dict (dict): A dictionary containing the shortage of SKUs.
                    - grn_extra_dict (dict): A dictionary containing extra GRN details.
                    - po_numbers_list (list): A list of PO (Purchase Order) numbers.
            """ 
        self.warehouse = warehouse
        self.is_asn = is_asn
        self.validation_type = validation_type
        self.get_configurations()
        self.grn_type = data.get("grn_type", "PO")
        self.source = data.get("source", "API")
        is_multi_po = data.get("is_multi_po", False)
        self.split_financial_values = data.get('split_financial_values', False)

        self.po_number = data.get('po_number') or ""
        self.po_reference = data.get('po_reference') or ""
        self.grn_reference = data.get('grn_reference', '')
        self.asn_reference = data.get('asn_reference', '')
        self.asn_number = data.get('asn_number', '')
        self.to_location = data.get('to_location', '')
        self.transaction_type = ''
        self.supplier_id = ''

        self.qc_enabled, self.all_qc_enabled, self.qc_config_data = self.get_qc_configuration()
        self.send_grn_for_qc = data.get('send_for_qc', False)  
        self.unique_batch_dict = {}
        self.timezone = get_user_time_zone(self.warehouse)
        self.po_numbers_list = []
        self.po_ref_list = []
        self.po_tax_data = {}
        self.po_tax_dict = {}
        self.asn_total_tax_dict = {}
        self.existing_serials_dict = {}
        self.lpn_error_dict = {}
        self.asn_cache_key = ''
        extra_params = extra_params or {}
        if not data.get("items", {}):
            self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[16]]
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        self.base_currency = self.warehouse.userprofile.base_currency
        
        self.source_wh_user = self.warehouse.id
        if data.get('is_st_asn') and self.grn_type == "ASNSave":
            self.source_wh_user = data.get('supplier_id') 

        self.import_data = {}
        if data.get('aux_data', {}).get('import'):
            self.import_data = data['aux_data'].pop('import')

        #To Location Validations for Inbound Staging Lanes
        self.to_location_id, error = self.get_staging_lane_to_location(self.to_location)
        if error:
            self.key_wise_error_dict['header'] = error
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list
        
        if not (self.validation_type == "asn_update" or self.is_asn) and self.misc_dict.get('blind_grn', '') == 'true':
            data, error_message = self.segregate_items_by_asn(data)
            if error_message:
                self.key_wise_error_dict['header'] = [error_message]
                return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list
        
        #Fetching Required Data to Query Outside of the Loop
        has_unique_key_dict = self.request_data_to_query(data)
        
        if self.key_wise_error_dict.get('header'):
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list
        
        if self.validation_type == 'asn_grn_creation':
            self.validate_gate_pass_status_for_asn()

        #GRN In-Progress Validation
        found_error = self.validate_header_level_cache_check()
        if found_error:
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        if data.get('source', '').lower() not in ['upload', 'web']:
            self.validate_lpn_data_with_packing_data(extra_params)

        #ASN cache check
        if self.validation_type == 'asn_grn_creation':
            asn_cache_lock = f"asn_transaction_lock_{self.asn_number}_{self.warehouse.username}"
            asn_cache_status = cache.add(asn_cache_lock, "True", timeout=LOCK_EXPIRE)
            if not asn_cache_status and not self.asn_cache_key:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[506] + str(self.po_number or self.po_reference)]
                return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list
            else:
                self.asn_cache_key = asn_cache_lock

        if not (self.po_numbers_list or so_st_po or is_multi_po):
            self.grn_with_po_ref = True
            if not (self.po_reference or self.po_ref_list):
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[17]]
                return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list
        elif self.po_ref_list:
            self.grn_with_po_ref = True

        #Fetching details to create po objects for free skus
        self.get_sku_details_for_free_skus(self.free_skus)
        
        #Fetching Putzone Details
        self.get_putzone_details()

        #Fetch ASN draft qty details
        self.get_asn_draft_qty_dict()
        #Framing ASN Details to Fetch the ASN ID, If ASN ID is not Passed
        asn_check = self.frame_asn_id_dict()
        if not asn_check:
            self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[504] + str(self.asn_number)]
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        #Fetching Purchase Order Data For Validation and Data Framing 
        po_values = self.get_purchase_order_data()
        if not po_values:
            self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[10]]

        po_skus, sku_types, po_dict_data, po_duplicate_skus = self.frame_po_id_dict(po_values)

        #Fetching Supplier QC Config
        self.supplier_obj, self.supplier_qc = self.get_supplier_details(self.supplier_id)

        #PO Duplicate Line Reference SKU Validation
        line_reference_required_skus = \
            self.duplicate_po_record_validation(po_duplicate_skus, has_unique_key_dict)
        if line_reference_required_skus and not self.asn_number:
            self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[24] + str(', '.join(line_reference_required_skus))]
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        sku_code_check = set(self.sku_codes) - set(po_skus)
        if sku_code_check:
            tail_message = GRN_ERROR_MESSAGE[21] if len(sku_code_check)==1 else GRN_ERROR_MESSAGE[22]
            self.key_wise_error_dict['header'] = ['SKUCodes: '+', '.join(sku_code_check) + tail_message + self.po_number ]
            return self.key_wise_error_dict, po_dict_data, list(self.aggr_grn_data_dict.values()), self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        #GRN Reference Exists Validation
        if self.grn_reference:
            grn_ref_check = self.grn_reference_validation()
            if grn_ref_check:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[23]]
                return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        if self.asn_reference and self.validation_type == 'asn_creation':
            asn_ref_check = self.asn_reference_validation()
            if asn_ref_check:
                self.key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[507]]
                return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        #Fetching Tolerance Percentage from Configuration
        self.receive_qty_percent = self.fetch_po_tolerance()

        #Same Supplier and same Invoice Restriction
        invoice_number = data.get("invoice_number", "")
        invoice_date = data.get('invoice_date')
        supplier_inv_check, error_message = duplicate_invoice_number_check(warehouse, self.asn_number, invoice_number, self.supplier_id, self.po_numbers_list, self.po_ref_list, self.misc_dict, invoice_date)
        if supplier_inv_check:
            self.key_wise_error_dict['header'] = [error_message]
            return self.key_wise_error_dict, {}, {}, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        self.get_checklist_attributes(sku_types)
        self.invoice_value = data.get("invoice_value", 0)
        self.get_credit_status(data.get("grn_amount", 0))
        
        self.frame_child_skus_dict()
        if self.split_financial_values:
            data = self.split_financial_values_validation(data)
        if self.validation_type in ["asn_update", "asn_creation"]:
            self.po_tax_data = self.get_po_sku_wise_tax_data(data, po_dict_data)
        self.line_level_validation(data, po_dict_data, po_values)

        if self.validation_type != 'asn_grn_creation':
            serial_data, serial_error = self.validate_serial_number_data()
            if serial_error:
                self.prepare_line_wise_error_message(data, serial_data)
                return self.key_wise_error_dict, po_dict_data, list(self.aggr_grn_data_dict.values()), self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list

        #Fetch putaway mapping data for accepted quantities
        self.sku_strategy_mapping, self.category_strategy_mapping, self.location_mapping_capacity,\
            self.mapped_zones, self.mapped_locations = get_putaway_strategy_mappings(self.sku_list, self.sku_category_list, self.warehouse, self.grn_type, po_values[0].get('po_type', ''))

        #Fetch putaway mapping data for each rejected reasons
        self.rej_reason_putaway_mapping, self.short_putaway_mapping = get_rej_reason_putaway_strategy_mappings(self.warehouse, self.grn_type, po_values[0].get('po_type', ''), self.rejected_reasons)

        grn_data_list = list(self.aggr_grn_data_dict.values())

        #Calucalate grn amount with tax and without tax
        if self.misc_dict.get('invoice_level') !='Line-level':
            self.calculate_grn_amount_with_tax(grn_data_list)
        self.data = data
        self.frame_grn_extra_dict()

        #GRN Extra Fields Validation
        self.grn_extra_fields_validation(data.get('extra_fields', {}))

        self.invoice_price_tolerance_validation()

        self.prepare_serial_shortage_dict()

        return self.key_wise_error_dict, po_dict_data, grn_data_list, self.po_numbers_list, self.grn_extra_dict, self.asn_cache_key, self.po_ref_list


class SalesReturnGRNValidation(WMSListView):
    def fetch_qc_configurations(self, grn_type):
        #Fetching QC Configurations to Send to QC
        qc_config_dict = {'SR' : 'after_sr_grn', 'PO' : 'after_grn'}
        self.qc_transaction_type = qc_config_dict.get(grn_type)

        #Fetching ALL QC Config
        qc_enabled = False
        all_qc_enabled = False
        qc_config = QCConfiguration.objects.filter(
            warehouse_id=self.warehouse.id, transaction_type=self.qc_transaction_type, status=1)
        if qc_config.exists():
            qc_enabled = True
            all_qc_enabled = qc_config[0].all_qc
        
        return qc_enabled, all_qc_enabled

    def get_sr_grn_configurations(self):
        misc_types = ['sr_grn_packing', 'gate_pass_validation_during_grn', 'sales_return_check_in', 'additional_batch_attributes', 'non_mandatory_batch_attributes']
        self.misc_dict = get_multiple_misc_values(misc_types, self.warehouse.id)

    def fetch_sales_return_details(self):
        result_values = [
            'sku_code', 'sku_id', 'sku_desc', 'batch_no', 'price', 'mrp', 'sku_category',
            'batch_based', 'buy_price', 'batch_mrp', 'manufactured_date', 'expiry_date',
            'sku_reference', 'image_url', 'sr_quantity', 'rejected_quantity', 'uom',
            'sku_json_data', 'reason', 'show_imei', 'return_id',
            'customer_id', 'customer_name', 'return_reference', 'return_date', 'reference_type', 'return_type',
            'reference_number', 'return_id', 'id', 'user', 'serial_based', 'order_reference',
            'qc_check', 'zone', 'put_zone_id', 'sale_return_id', 'invoice_number', 'credit_note_number',
            'enable_serial_based', 'sos_id'

        ] #removed 'order_json_data' we need to handle separately
    
        search_filters = {
            'sales_return_sku__sales_return__warehouse': self.warehouse.id,
            'sales_return_sku__sales_return__return_id': self.return_id
            }
        q_filters = [{'quantity__gt': 0, 'rejected_quantity__gt':0}]
        exclude_dict = {'sales_return_sku__sales_return__status': 3}
        extra_params = {
            'filters' : search_filters, 'value_keys' : result_values,
            'q_filters' : q_filters, 'return_type' : 'values_dict',
            'distincts' : ['id'], 'excludes': exclude_dict
            }
        return_dict = \
                get_data_from_custom_params(SalesReturnBatchLevel,'sale_return_grn', extra_params)
        return return_dict
        
    def frame_return_dict(self, return_dict):
        return_id_mapping = {}
        for each_key, each_data in return_dict.items():
            uniqu_return_key = each_data['return_id'], each_data['sku_code'], each_data['sr_quantity'], each_data['mrp'], each_data['price'], each_data['batch_no']
            if uniqu_return_key not in return_dict:
                return_id_mapping[uniqu_return_key] = each_key
            self.sku_codes.append(each_data['sku_code'])
            self.sku_categories.append(each_data['sku_category'])
            self.batch_retun_ids.append(each_data['id'])
        return return_id_mapping

    def cache_check(self):
        if self.return_id:
            cache_return_id = f'{self.warehouse.id}##{self.return_id}'
            cache_status = cache.add(cache_return_id, "True", timeout=LOCK_EXPIRE)
            if not cache_status:
                self.error_list.append([GRN_ERROR_MESSAGE[14] + str(self.return_id)])
            else:
                self.return_id_list.append(self.return_id)

    def sku_validation(self, return_skus):
        sku_code_check = set(return_skus) - set(self.sku_codes)
        if sku_code_check:
            tail_message = ' is Not in Return ID: ' if len(sku_code_check)==1 else ' are Not in Return ID: .'
            self.error_list.append(['SKUCodes: '+', '.join(sku_code_check) + tail_message + self.return_id])
    
    def quantity_validations(self, return_qty, grn_key, sku_code, value, sku_shortage_dict):
        error_message = []
        
        received_quantity = value
        if not value:
            value = self.each_row.get('invoice_quantity',0)
        
        if self.each_row.get('id', ''):
            self.aggregated_sku_count[self.each_row['id']] = value
        else:
            self.aggregated_sku_count[self.return_id_mapping[grn_key]] = value

        if 'received_quantity' not in self.each_row:
            received_quantity = self.each_row.get('invoice_quantity',0)
        
        accepted_quantity = self.each_row.get('accepted_quantity')
        if 'accepted_quantity' not in self.each_row:
            accepted_quantity = self.each_row.get('invoice_quantity',0)
        
       
        #We Don't Have Line Reference in SR, But As We have Common Function in PO.
        #Assining Unique Line Id as Empty
        unique_line_id = self.each_row.get('id', '')
        if self.each_row.get('invoice_quantity', 0):
            if (sku_code, unique_line_id) in sku_shortage_dict:
                sku_shortage_dict[(sku_code, unique_line_id)] -= received_quantity
            else:
                sku_shortage_dict[(sku_code, unique_line_id)] = self.each_row.get('invoice_quantity', 0) - received_quantity
            if self.each_row.get('invoice_quantity', 0) > return_qty:
                error_message.append(GRN_ERROR_MESSAGE[104])

        rejected_quantity = self.each_row.get('rejected_quantity',0)
        if self.aggregated_sku_count[self.batch_retun_id] > return_qty:
            error_message.append(GRN_ERROR_MESSAGE[101])
        if received_quantity < rejected_quantity:
            error_message.append(GRN_ERROR_MESSAGE[102])
        if accepted_quantity != received_quantity - rejected_quantity :
            error_message.append(GRN_ERROR_MESSAGE[107])
        
        return error_message, accepted_quantity, rejected_quantity

    def sr_grn_lpn_validation(self, json_data, error_message, sr_dict):
        """ LPN Validation for ASN GRN Creation """
        if self.misc_dict.get('sr_grn_packing') in TRUE_VALUES and self.each_row.get('received_quantity', 0):
            if self.each_row.get('lpns'):
                json_data.update({'lpns': self.each_row.get('lpns')})
                for lpn_data in self.each_row.get('lpns'):
                    self.lpn_numbers.append(lpn_data.get('lpn_number'))
                self.prepare_serial_data(sr_dict, self.each_row.get('lpns', []), error_message)
            else:
                error_message.append(GRN_ERROR_MESSAGE[304])
        else:
            self.prepare_serial_data(sr_dict, None, self.each_row.get('received_quantity', 0), error_message)
        return error_message

    def get_existing_serials(self):
        """Fetch Existing serial numbers for each asn id """
        serial_filter = {'filters': {'reference_type': 'sales_return', 'transact_id__in': self.batch_retun_ids, 'status': 1}}
        serial_mixin = SerialNumberTransactionMixin(self.warehouse, self.warehouse, serial_filter)
        existing_serials = serial_mixin.get_sntd_details()
        for data in existing_serials.get('data', []):
            self.existing_serials_dict.setdefault(data['transact_id'], []).extend(data['serial_numbers'])

    def prepare_serial_data(self, sr_dict, lpns=None, lpn_quantity=0, error_message={}):
        enable_serial_based = sr_dict.get('enable_serial_based', False)
        accepted_quantity = self.each_row.get('accepted_quantity', 0) if 'accepted_quantity' in self.each_row else self.each_row.get('return_quantity', 0)
        rejected_quantity = self.each_row.get('rejected_quantity', 0)
        accepted_serial_numbers = self.each_row.get('accepted_serial_numbers', [])
        rejected_serial_numbers = self.each_row.get('rejected_serial_numbers', [])
        existing_serials = set(self.existing_serials_dict.get(self.batch_retun_id, []))
        serial_numbers = []
        if lpns:
            for each_lpn in lpns:
                serial_numbers = each_lpn.get('serial_numbers', [])
                if serial_numbers and enable_serial_based:
                    if each_lpn.get('packed_quantity') != len(set(serial_numbers)):
                        error_message.append(GRN_ERROR_MESSAGE[29])
                        continue
                    self.serial_data_to_validate.append({
                    'lpn_number': each_lpn.get('lpn_number'),
                    'sku_id': sr_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': serial_numbers
                    })
                elif enable_serial_based:
                    error_message.append(GRN_ERROR_MESSAGE[27])
                elif serial_numbers and not enable_serial_based:
                    error_message.append(GRN_ERROR_MESSAGE[28])
                self.return_id_wise_serial_dict.setdefault(self.batch_retun_id, []).extend(serial_numbers)
            if existing_serials and not set(serial_numbers).issubset(existing_serials):
                self.error_message.append(','.join(map(str, set(serial_numbers) - existing_serials)) + " " + GRN_ERROR_MESSAGE[31])
        elif accepted_serial_numbers or rejected_serial_numbers:
            serial_numbers = accepted_serial_numbers + rejected_serial_numbers
            if accepted_serial_numbers and enable_serial_based:
                if accepted_quantity != len(set(accepted_serial_numbers)):
                    error_message.append(GRN_ERROR_MESSAGE[29])
                self.serial_data_to_validate.append({
                    'lpn_number': '',
                    'sku_id': sr_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': accepted_serial_numbers
                })
            elif enable_serial_based and accepted_quantity:
                error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                error_message.append(GRN_ERROR_MESSAGE[28])
            if rejected_serial_numbers and enable_serial_based:
                if rejected_quantity != len(set(rejected_serial_numbers)):
                    error_message.append(GRN_ERROR_MESSAGE[29])
                self.serial_data_to_validate.append({
                    'lpn_number': '',
                    'sku_id': sr_dict.get("sku_id"),
                    'batch_number': self.each_row.get('batch_number'),
                    'serial_numbers': rejected_serial_numbers
                })
            elif enable_serial_based and rejected_quantity:
                error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                error_message.append(GRN_ERROR_MESSAGE[28])

            self.return_id_wise_serial_dict.setdefault(self.batch_retun_id, []).extend(serial_numbers)
            if len(set(accepted_serial_numbers)) + len(set(rejected_serial_numbers)) != lpn_quantity:
                error_message.append(GRN_ERROR_MESSAGE[29])
            if existing_serials and  not set(serial_numbers).issubset(existing_serials):
                error_message.append(','.join(map(str, set(serial_numbers) - existing_serials)) + " " + GRN_ERROR_MESSAGE[31])
        elif accepted_quantity or rejected_quantity:
            if enable_serial_based:
                error_message.append(GRN_ERROR_MESSAGE[27])
            elif serial_numbers and not enable_serial_based:
                error_message.append(GRN_ERROR_MESSAGE[28])

    def prepare_serial_shortage_dict(self):
        for batch_return_id, serial_numbers in self.return_id_wise_serial_dict.items():
            short_serials = set(self.existing_serials_dict.get(self.batch_retun_id, [])) - set(serial_numbers)
            if short_serials:
                self.short_serials_dict[batch_return_id] = list(short_serials)

    def prepare_line_wise_error_message(self, data, serial_data, key_wise_error_dict):
        """Frame Serial Error messages"""
        counter = 0
        serial_df = pd.DataFrame(serial_data)
        for each_item in data.get('items',[]):
            sku_code = each_item.get('sku_code')
            sku_id = each_item.get('sku_id')
            counter_key = str(counter)+'_'+sku_code
            #Check SKU batch in serial_df
            serial_df['batch_number'] = serial_df['batch_number'].fillna('')
            batch_number = each_item.get('batch_number') or ''
            serial_df_sku_batch = serial_df[(serial_df['sku_id'] == sku_id) & (serial_df['batch_number'] == batch_number)]
            if not serial_df_sku_batch.empty:
                if serial_df_sku_batch['Status'].any():
                    key_wise_error_dict.setdefault(counter_key, [])
                    #string concat
                    error_list = []
                    for sublist in serial_df_sku_batch['Status'].values:
                        error_list.extend(sublist)  # because each is a list
                    error_message = ', '.join(error_list)
                    key_wise_error_dict[counter_key].append(error_message)

            counter += 1

    def validate_serial_number_data(self):
        """Validate serial numbers when serials are not given in sale return"""
        data_list, error = [], False
        if self.serial_data_to_validate and not self.existing_serials_dict:
            serial_mixin = SerialNumberMixin(None, self.warehouse, self.serial_data_to_validate)
            data_list = serial_mixin.validate_create_data()
            error = any([data.get('Status') for data in data_list])
        return data_list, error

    def batch_validation(self, batch_based, error_message):
        """ Validate Batch Attributes for Sales Return GRN """
        #Batch Attributes Validation
        if not batch_based:
            self.each_row.pop('mrp', None)

        batch_error_message = InboundBatchValidation().validate(
            self.each_row, batch_based, self.misc_dict, '')
        if batch_error_message:
            error_message += batch_error_message
        return error_message

    def validate(self, data, warehouse):
        """ SR GRN Validation """
        company_id = get_company_id(warehouse)
        decimal_limit = int(get_decimal_value(warehouse.id))
        grn_type = data.get('grn_type', 'PO')

        grn_data_list  = []
        return_skus, self.sku_codes, self.sku_categories, self.lpn_numbers = [], [], [], []

        key_wise_error_dict, grn_extra_dict = {}, {}
        
        self.warehouse = warehouse
        self.return_id = data.get('return_id', '')
        self.aggregated_sku_count = {}
        self.source = data.get("source", "API")
        sku_shortage_dict = {}
        self.reference_type = ''
        self.order_reference_list = []
        self.sku_combo_dict = {}
        self.return_id_wise_serial_dict = {}
        self.short_serials_dict = {}
        self.batch_retun_ids = []
        self.existing_serials_dict = {}
        counter = 0
        self.error_list = []
        self.return_id_list = []
        self.rejected_reasons = []
        self.serial_data_to_validate = []

        qc_enabled, all_qc_enabled = self.fetch_qc_configurations(grn_type)
        send_grn_for_qc = data.get('send_for_qc', False)
        #fetch configurations 
        self.get_sr_grn_configurations()
        self.validate_on_hold_sales_returns()
        if self.error_list:
            key_wise_error_dict['header'] = self.error_list
            return key_wise_error_dict, {}, [], self.return_id_list, grn_extra_dict
        return_dict = self.fetch_sales_return_details()
        return_dict = self.prepare_sos_details(return_dict)

        return_skus, put_zones_list = get_data_from_items(data['items'])
        put_zone_dict = get_put_zone_dict(put_zones_list, warehouse)

        self.return_id_mapping = self.frame_return_dict(return_dict)
        
        #Header Validations
        self.cache_check()
        self.sku_validation(return_skus)
        if self.error_list:
            key_wise_error_dict['header'] = self.error_list
            return key_wise_error_dict, return_dict, grn_data_list, self.return_id_list, grn_extra_dict
        
        is_valid_return_id = self.validate_gate_pass_status_for_sales_return(key_wise_error_dict)
        if not is_valid_return_id:
            return key_wise_error_dict, return_dict, grn_data_list, self.return_id_list, grn_extra_dict

        self.get_existing_serials()
        if return_dict:
            self.batch_retun_id = None
            for self.each_row in data['items']:
                error_message = []
                sku_code = self.each_row.get('sku_code', '')
                self.index_sku_key = str(counter) + '_' + sku_code
                self.rejected_reasons.append(self.each_row.get("rejected_reason", ""))
                price = self.each_row.get("po_price", 0)
                if self.each_row.get("buy_price", 0):
                    price = self.each_row.get("buy_price", 0)
                mrp =  self.each_row.get('mrp', 0)
                batch_no = self.each_row.get('batch_number', '')
                
                #LPN Validation
                aux_data = self.each_row.get('aux_data', {})
                quantity = self.each_row.get('po_quantity', 0)
                grn_key = self.return_id, sku_code, quantity, mrp, price, batch_no
                
                self.batch_retun_id = self.each_row['id'] if 'id' in self.each_row else self.return_id_mapping[grn_key]
                
                value = self.each_row.get('received_quantity',0)
                sr_dict = return_dict[self.batch_retun_id]
                return_qty = sr_dict.get('sr_quantity', 0)

                error_message, accepted_quantity, rejected_quantity = \
                    self.quantity_validations(return_qty, grn_key, sku_code, value, sku_shortage_dict)
                error_message = self.sr_grn_lpn_validation(aux_data, error_message, sr_dict)
                
                sr_dict = return_dict[self.batch_retun_id]
                error_message = self.batch_validation(sr_dict.get('batch_based', False), error_message)
                #Fetching sales uom quantity from order json data
                sr_json_data = sr_dict.get('order_json_data', {}) or {}
                sale_uom_qty = sr_json_data.get('pack_uom_quantity', 1)
                put_zone_id, zone_restriction = get_put_zone_id(put_zone_dict, self.each_row.get("put_zone"))
                put_zone_id = put_zone_id if put_zone_id else sr_dict["put_zone_id"]
                put_zone = zone_restriction if zone_restriction else sr_dict['zone']
                self.each_row['sku_id'] = sr_dict.get('sku_id')
                grn_data_dict = {
                    "invoice_number": sr_dict["invoice_number"],
                    "source":  self.source,
                    "user" : sr_dict["user"],
                    'grn_type': grn_type,
                    "warehouse_user": 'warehouse_user',
                    "warehouse_first_name": 'warehouse_first_name',
                    "warehouse_username": 'warehouse_username',
                    'challan_number': '',
                    "challan_date": None,
                    "unit": sr_dict.get("unit", ""),
                    "sku_code": sku_code,
                    "sku_category": sr_dict.get("sku_category", ""),
                    "sku_uom": sr_dict["uom"],
                    "sku_id": sr_dict["sku_id"],
                    "serial_based": sr_dict["serial_based"],
                    "batch_based" : sr_dict["batch_based"],
                    "qc_check": sr_dict["qc_check"],
                    "put_zone": put_zone,
                    "put_zone_id": put_zone_id,
                    "zone_restriction" : zone_restriction,
                    "batch_detail": { 
                        "transact_type": "po_loc",
                        "batch_no": batch_no,
                        "expiry_date": self.each_row.get('expiry_date', None),
                        "manufactured_date": self.each_row.get("manufactured_date", None),
                        "tax_percent": self.each_row.get("tax", 0), 
                        "cess_percent": self.each_row.get("cess_tax", 0),
                        "mrp": mrp,
                        "buy_price": price,
                        "weight": self.each_row.get("weight", ""),
                        "batch_reference": self.each_row.get("batch_reference", ""),
                        "sku_id": sr_dict["sku_id"],
                        "json_data" : {}
                    },
                    "putaway_quantity": value,
                    "quantity": value,
                    "accepted_quantity": accepted_quantity,
                    "rejected_quantity": rejected_quantity,
                    "return_quantity": self.each_row.get("return_quantity", 0),
                    "order_status_flag": '',
                    "round_off_total": self.each_row.get("total_amount", 0),
                    "cess_tax": self.each_row.get("cess_tax", 0),
                    "cgst_tax":sr_dict.get("cgst_tax", 0),
                    "sgst_tax":sr_dict.get("sgst_tax", 0),
                    "igst_tax":sr_dict.get("igst_tax", 0),
                    "price": price,
                    "rejected_reason": self.each_row.get("rejected_reason", ""),
                    "remarks": self.each_row.get("remarks", ""),
                    "invoice_quantity": self.each_row.get("invoice_quantity", 0),
                    "amount" : price * value,
                    'batch_return_id': self.batch_retun_id,
                    'sale_return_id': sr_dict.get("sale_return_id", None),
                    'credit_note_no': sr_dict.get('credit_note_number', ''),
                    'po_invoice_quantity': self.each_row.get("invoice_quantity", 0),
                    'company_id' : company_id,
                    'decimal_limit' : decimal_limit,
                    'pcf': sale_uom_qty,
                    'json_data': copy.deepcopy(aux_data),
                    'unique_line_id': self.each_row['id'],
                    'rejected_serial_numbers' : self.each_row.get("rejected_serial_numbers", []),
                    'accepted_serial_numbers' : self.each_row.get("accepted_serial_numbers", [])
                    }
                if qc_enabled:
                    grn_data_dict["send_for_qc"] = send_grn_for_qc or all_qc_enabled or sr_dict['qc_check']
                grn_data_list.append(grn_data_dict)

                if error_message:
                    key_wise_error_dict[self.index_sku_key] = [error_message]

                counter += 1
                self.order_reference_list.append(sr_dict.get('order_reference', ''))

            serial_data, serial_error = self.validate_serial_number_data()
            if serial_error:
                self.prepare_line_wise_error_message(data, serial_data, key_wise_error_dict)
                return key_wise_error_dict, return_dict, grn_data_list, self.return_id_list, grn_extra_dict

            self.reference_type = sr_dict.get('reference_type', '')

            return_type = return_dict.get(self.batch_retun_id, {}).get('return_type', '')
        else:
            key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[19]]

        grn_extra_keys = [
            'aggregated_sku_count', 'to_location_id', 'grn_type', 'return_id',
            'sku_shortage_dict', 'serial_based', 'po_currency', 'qc_transaction_type',
            'reference_type'
            ]

        #Fetch putaway mapping data for accepted quantites
        self.sku_strategy_mapping, self.category_strategy_mapping, self.location_mapping_capacity,\
            self.mapped_zones, self.mapped_locations = get_putaway_strategy_mappings(self.sku_codes, self.sku_categories, self.warehouse, grn_type, return_type)

        #Fetch putaway mapping data for each rejected reasons
        self.rej_reason_putaway_mapping, self.short_putaway_mapping = get_rej_reason_putaway_strategy_mappings(self.warehouse, grn_type, return_type, self.rejected_reasons)
        self.prepare_serial_shortage_dict()
        grn_extra_dict = {
            'qc_transaction_type' : self.qc_transaction_type,
            'sku_strategy_mapping' : self.sku_strategy_mapping,
            'category_strategy_mapping': self.category_strategy_mapping,
            'location_mapping_capacity': self.location_mapping_capacity,
            'mapped_locations': self.mapped_locations,
            'source': self.source,
            'mapped_zones': self.mapped_zones,
            'rej_reason_putaway_mapping': self.rej_reason_putaway_mapping,
            'short_putaway_mapping': self.short_putaway_mapping,
            'short_serials_dict': self.short_serials_dict,
            'existing_serials_dict': self.existing_serials_dict,
            'return_type': return_type
            }

        # Child SKUs putaway data generation, if Combo SKU exists in SR
        if self.reference_type != 'no_document':
            combo_extra_dict = fetch_sr_child_skus_batch_data_from_picklist(self.order_reference_list, self.sku_codes, self.warehouse.id)
            grn_extra_dict.update(combo_extra_dict)
        # Update the dictionary with local variables that exists for grn_extra_keys
        for key in grn_extra_keys:
            if key in locals():
                grn_extra_dict[key] = locals()[key]
        grn_extra_dict['lpn_numbers'] = self.lpn_numbers
        return key_wise_error_dict, return_dict, grn_data_list, self.return_id_list, grn_extra_dict

    def validate_on_hold_sales_returns(self):
        """
        checks if sales return is on hold or not if yes throws error
        """
        if self.misc_dict.get('sales_return_check_in', '') != 'true':
            return
        sales_return = SalesReturn.objects.filter(return_id=self.return_id, warehouse_id=self.warehouse.id, status=4)
        if sales_return.exists():
            self.error_list.append([f"Sales Return {self.return_id} is on hold, please verify sales return to continue with grn"])
    
    def prepare_sos_details(self, return_dict):
        """
        adds sos details in the return dict
        """
        sos_ids = []
        for each_key, each_data in return_dict.items():
            sos_id = each_data.get('sos_id', None)
            if sos_id:
                sos_ids.append(sos_id)
        if sos_ids:
            sos_data = list(SellerOrderSummary.objects.filter(id__in=sos_ids, order_status_flag__in=['customer_invoices', 'delivery_challans']).values('json_data', 'id'))
            sos_data_dict = {}
            for sos in sos_data:
                sos_data_dict[sos['id']] = sos['json_data']
            for each_key, each_data in return_dict.items():
                sos_id = each_data.get('sos_id', None)
                if sos_id and sos_id in sos_data_dict:
                    return_dict[each_key]['order_json_data'] = sos_data_dict[sos_id]
        return return_dict

    def validate_gate_pass_status_for_sales_return(self, key_wise_error_dict):
        """
        validates the sales return gate pass status
        """
        gate_pass_return_ids = []
        gate_pass_validation_config = self.misc_dict.get('gate_pass_validation_during_grn', '')
        gate_pass_validation_config = gate_pass_validation_config.split(',') if gate_pass_validation_config else []
        if self.return_id and 'SR' in gate_pass_validation_config:
            gate_pass_return_id = list(GatePassItem.objects.exclude(gate_pass__status__in = [8, 2]).exclude(status = 2).filter(
                             gate_pass__warehouse=self.warehouse.id,gate_pass__gate_pass_type='inbound', transaction_id = self.return_id,
                             gate_pass__gate_pass_sub_type='returns_receipt').values('transaction_id','status', 'gate_pass__status'))
            for each_item in gate_pass_return_id:
                if not (each_item['gate_pass__status'] == 0 and each_item['status'] == 0):
                    gate_pass_return_ids.append(each_item['transaction_id'])
            
            if self.return_id in gate_pass_return_ids:
                key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[602]]
                return False
        return True


def validate_grn_wms(request, warehouse, myDict):
    return_po_quantity = get_misc_value('return_po_qty', warehouse.id)
    status_msg = ''
    try:
        quantity_dict = {}
        for i in range(0, len(myDict['wms_code'])):
            try:
                rec_quantity = float(myDict["quantity"][i])
            except:
                rec_quantity = 0
            if myDict['wms_code'][i] and rec_quantity:
                sku_master = SKUMaster.objects.filter(wms_code=myDict['wms_code'][i].upper(), user=warehouse.id)
                if not sku_master:
                    if not status_msg:
                        status_msg = 'Invalid WMS Code ' + myDict['wms_code'][i]
                    else:
                        status_msg += ',' + myDict['wms_code'][i]
                if return_po_quantity == 'false':
                    if round(float(myDict['rejected_quantity'][i]), 4) > round(float(myDict['quantity'][i]), 4):
                        status_msg = 'Excess Qty Rejected'
                if not myDict['is_stock_transfer'][0] == 'true':
                    if quantity_dict.get(myDict['wms_code'][i],''):
                        quantity_dict[myDict['wms_code'][i]] += float(myDict['quantity'][i])
                    else:
                        quantity_dict[myDict['wms_code'][i]] = float(myDict['quantity'][i])
                else:
                    quantity_dict[myDict['wms_code'][i]] = float(myDict['quantity'][i])
                rejected_quantity = 0
                if "rejected_quantity" in myDict:
                    if myDict['rejected_quantity'][i]:
                        rejected_quantity = round(float(myDict['rejected_quantity'][i]), 4)
                if rejected_quantity  > round(quantity_dict[myDict['wms_code'][i]], 4):
                    status_msg = 'Excess Qty Rejected'
                returned_quantity = 0
                if "discrepency_quantity" in myDict:
                    if myDict['discrepency_quantity'][i]:
                        returned_quantity = round(float(myDict['discrepency_quantity'][i]), 4)
                if returned_quantity > round(quantity_dict[myDict['wms_code'][i]], 4):
                    status_msg = 'Excess Qty Returned'
                if quantity_dict[myDict['wms_code'][i]] > 0 :
                    datum = PurchaseOrder.objects.get(id=myDict['id'][i])
                    if datum.open_po:
                        if float(datum.open_po.order_quantity - datum.received_quantity) < round(quantity_dict[myDict['wms_code'][i]] , 4):
                            status_msg = 'Please check the excess Qty being recieved'
                    # elif datum.stpurchaseorder_set.filter():
                    #     if float(datum.stpurchaseorder_set.filter().values('open_st__order_quantity')[0]['open_st__order_quantity'] - datum.received_quantity) < round(quantity_dict[myDict['wms_code'][i]], 4):
                    #         status_msg = 'Please check the excess Qty being recieved'
                else:
                    continue
    except Exception as e:
        status_msg = 'Something Went Worng!'
        log_message = (("Grn Validate Error for Username: %s, IpAddress: %s and params are %s and Error: %s") % (
            str(warehouse.username), str(get_user_ip(request)), str(myDict), str(e)))
        log.info(log_message)
    return status_msg

def check_unique_grn_reference(warehouse, grn_reference):
    grn_ref_check = False
    if grn_reference:
        grn_ref_check = SellerPOSummary.objects.filter(
            grn_reference=grn_reference, job_order__product_code__user=warehouse.id
        )
    return grn_ref_check

def get_required_misc_details(warehouse):
    higher_fg_quantity = 0
    misc_types = [
        'additional_batch_attributes', 'non_mandatory_batch_attributes', 'receive_higher_fg_quantity',
        'consume_extra_stock_while_rm_consumption'
    ]
    misc_dict = get_multiple_misc_values(misc_types, warehouse.id)
    configured_batch_attributes, configured_non_batch_attributes = [], []
    #Additional batch details
    additional_batch_details, non_mandatory_batch_attributes, receive_higher_fg_quantity, is_consume_extra_stocks = (
        misc_dict.get('additional_batch_attributes'),
        misc_dict.get('non_mandatory_batch_attributes'),
        misc_dict.get('receive_higher_fg_quantity'),
        misc_dict.get('consume_extra_stock_while_rm_consumption')
    )
    if additional_batch_details not in ['false', '', None, False]:
        configured_batch_attributes = additional_batch_details.split(',')

    if non_mandatory_batch_attributes not in ['false', '', None, False]:
        configured_non_batch_attributes = non_mandatory_batch_attributes.split(',')

    if (
        receive_higher_fg_quantity and receive_higher_fg_quantity not in ['false', '', None, False] and 
        isinstance(receive_higher_fg_quantity, str)
        ):
        higher_fg_quantity = float(receive_higher_fg_quantity)
    
    if is_consume_extra_stocks in ['false', False, '', None, 'null']:
        is_consume_extra_stocks = False
    else:
        is_consume_extra_stocks = True

    return configured_batch_attributes, configured_non_batch_attributes, higher_fg_quantity, is_consume_extra_stocks

def get_data_from_items(data):
    sku_codes, put_zones_list = [], []
    for each_row in data:
        sku_codes.append(each_row['sku_code'])
        if each_row.get('put_zone'):
            put_zones_list.append(each_row['put_zone'])
    return sku_codes, put_zones_list

def get_job_order_details_from_job_code(warehouse, job_code, sku_codes):

    job_filter_dict = {
        'product_code__sku_code__in' : sku_codes,
        'product_code__user__in' : [warehouse.id],
        'job_code' : job_code
        }

    job_order_obj = JobOrder.objects.filter(**job_filter_dict)
    job_order_details = list(job_order_obj.values(
        'id','job_code','jo_reference','received_quantity','status',
        sku_code = F('product_code__sku_code'), sku_desc = F('product_code__sku_desc'),
        ordered_quantity = F('product_quantity'), serial_based = F('product_code__enable_serial_based'),
        sku_id = F('product_code_id'), sku_mrp = F('product_code__mrp'),
        jo_creation_date = F('creation_date'), jo_json_data=F('json_data'),
        sku_price = F('product_code__price'), job_order_type = F('order_type'),
        batch_based = F('product_code__batch_based'),
        sku_uom = F('product_code__measurement_type'),
        put_zone_id = F('product_code__zone_id'),
        put_zone = F('product_code__zone__zone'),
    ))
    fg_skus = [job.get('sku_code') for job in job_order_details]

    #bom quantity details
    bom_quantity_details, jo_dict_data, jo_id_details = {}, {}, {}
    for job in job_order_details:
        bom_quantity_details[job.get('sku_code')] = job.get('jo_json_data',{}).get('bom_quantity_details',{})
        jo_dict_data[job['id']] = job
        key = (job.get('job_code'), job.get('sku_code'))
        jo_id_details[key] = job['id']

    material_qty_details = {}
    jo_material_details = list(job_order_obj.values(
        'id', material_qty = F('jomaterial__material_quantity'),
        material_code = F('jomaterial__material_code__sku_code'))
    )

    for jo in jo_material_details:
        if material_qty_details.get(jo['id']):
            if material_qty_details[jo['id']].get(jo['material_code']):
                material_qty_details[jo['id']][jo['material_code']] += jo['material_qty']
            else:
                material_qty_details[jo['id']][jo['material_code']] = jo['material_qty']
        else:
            material_qty_details[jo['id']] = {jo['material_code']: jo['material_qty']}

    return job_order_details, fg_skus, bom_quantity_details, jo_dict_data, jo_id_details, material_qty_details

def get_picked_and_extra_quantity_details(warehouse, fg_skus, job_code, is_consume_extra_stocks):
    '''
    Returns Picked Quantity and Extra Quantity Details
    '''
    jo_material_details = list(JOMaterial.objects.filter(
        material_code__user = warehouse.id, job_order__product_code__sku_code__in = fg_skus,
        job_order__job_code = job_code
    ).values('id','material_code__sku_code', 'material_code__dispensing_enabled', 'job_order__jo_reference', 'batch_reference', 'batch_no','material_code__batch_based'))

    batch_references, receipt_numbers, material_codes, batch_nos, all_batch_flag = [], [], [], [], True

    #Extracting Batch References, Receipt Numbers and Material Code
    for jo_mat in jo_material_details:
        if jo_mat.get('batch_reference'):
            batch_references.append(jo_mat.get('batch_reference'))
        if jo_mat.get('batch_no'):
            batch_nos.append(jo_mat.get('batch_no'))
        all_batch_flag = all_batch_flag and jo_mat.get('material_code__batch_based')

        receipt_numbers.append(jo_mat.get('id'))
        material_codes.append(jo_mat.get('material_code__sku_code'))

    base_filter_dict = {
        'sku__sku_code__in': material_codes,
        'quantity__gt': 0, 'location__zone__storage_type': 'wip_area',
        'sku__user': warehouse.id,
    }
    query_filter, base_filter_dict = prepare_batch_based_filters(batch_references, batch_nos, all_batch_flag, base_filter_dict)
    
    picked_stock = StockDetail.objects.filter(
        query_filter,**base_filter_dict, receipt_type__in = ['jo_dispense', 'rm_picking'], receipt_number__in = receipt_numbers, 
    )

    picked_stock_details = picked_stock.values(
        'sku__sku_code', 'sku__dispensing_enabled', 'receipt_type'
    ).annotate(Sum('quantity'))

    picked_quantity_details = {}

    for stock in picked_stock_details:
        sku_code, receipt_type = stock['sku__sku_code'], stock['receipt_type']
        is_dispensed, quantity = stock['sku__dispensing_enabled'], stock['quantity__sum']

        if is_dispensed and receipt_type == 'jo_dispense' or not is_dispensed and receipt_type == 'rm_picking':
            if sku_code not in picked_quantity_details:
                picked_quantity_details[sku_code] = quantity
            else:
                picked_quantity_details[sku_code] += quantity

    #Based on Consume Extra Stock While RM Consumption Config
    if is_consume_extra_stocks:
        extra_stock = StockDetail.objects.exclude(
            receipt_type__in = ['rm_picking', 'jo_dispense', 'so_picking', 'so_dispense']
        ).filter(**base_filter_dict)

        extra_stock_quantity_details = dict(extra_stock.values_list('sku__sku_code').annotate(Sum('quantity')))
    else:
        extra_stock, extra_stock_quantity_details = StockDetail.objects.none(), {}

    return (
        picked_stock, extra_stock, picked_quantity_details, extra_stock_quantity_details
    )

def get_jo_id(jo_id_details, each_row, job_code, error_message):
    jo_id = ''
    unique_key = (job_code, each_row.get('sku_code'))
    if jo_id_details.get(unique_key):
        jo_id = jo_id_details.get(unique_key)
    else:
        error_message.append(GRN_ERROR_MESSAGE[501])
    return jo_id, error_message

def validate_configured_batch_attributes(
        each_row, error_message, batch_based, 
        configured_batch_attributes, configured_non_batch_attributes
    ):
    required_batch_attributes = []
    if batch_based:
        if not (each_row.get('batch_number') or each_row.get('batch_reference') or each_row.get('vendor_batch_number')):
            error_message.append(BATCH_ERROR_MESSAGE[103])
        for batch_attribute in configured_batch_attributes:
            if batch_attribute == 'buy_price':
                continue
            batch_attr_value = str(each_row.get(batch_attribute))
            if batch_attr_value in ['', None, 'None'] and batch_attribute not in configured_non_batch_attributes:
                required_batch_attributes.append(batch_attribute.replace('_', ' ').title())

        if required_batch_attributes:
            error_message.append('{} are Mandatory'.format(', '.join(required_batch_attributes)))

    return error_message

def grn_quantity_validation(each_row, jo_dict_data, jo_id, error_message, aggregated_sku_count, key, receive_higher_fg_quantity, consumable_list):
    received_quantity = each_row.get('received_quantity',0)
    rejected_quantity = each_row.get('rejected_quantity',0)
    ordered_quantity, already_received_quantity, check_rec_quantity, extend_qty = 0, 0, 0, 0
    if jo_id:
        ordered_quantity = float(jo_dict_data[jo_id]['ordered_quantity'])
        already_received_quantity = jo_dict_data[jo_id]['received_quantity']
        check_rec_quantity = ordered_quantity - already_received_quantity

        extend_qty = math.ceil((receive_higher_fg_quantity/100) * ordered_quantity) or 0

        if aggregated_sku_count[key] > ordered_quantity + extend_qty:
            error_message.append(GRN_ERROR_MESSAGE[101])

        if received_quantity < rejected_quantity:
            error_message.append(GRN_ERROR_MESSAGE[102])

        if received_quantity < each_row.get('accepted_quantity') + each_row.get('rejected_quantity'):
            error_message.append(GRN_ERROR_MESSAGE[109])

        if already_received_quantity > 0 and received_quantity < check_rec_quantity and consumable_list:
            error_message.append('Cannot Receive Partially for this SKU %s' %  str(each_row.get('sku_code')))

        if not extend_qty and (already_received_quantity + received_quantity > ordered_quantity):
            error_message.append('Received quantity cannot exceed %s for %s. Received Quantity is %s' % (str(ordered_quantity), str(each_row.get('sku_code')), str(already_received_quantity)))
    else:
        error_message.append('Invalid JO Details')

    return error_message, ordered_quantity, already_received_quantity, extend_qty, check_rec_quantity

def get_short_close_status(each_row, is_deplete_stock):
    each_row_deplete_stock = False
    is_short_closed = each_row.get('is_short_closed')
    deplete_stock = each_row.get('deplete_stock')
    if deplete_stock:
        is_deplete_stock = True
        each_row_deplete_stock = True
    is_short_closed = is_short_closed or each_row_deplete_stock

    return is_deplete_stock, is_short_closed, each_row_deplete_stock

def validate_header_details(warehouse, data, key_wise_error_dict, jo_numbers_list):
    job_code = data.get('job_code','')
    jo_reference = data.get('jo_reference', '')

    header_error_list = []
    if not data.get("items", {}):
        header_error_list.append(GRN_ERROR_MESSAGE[16])
    if not (job_code or jo_reference):
        header_error_list.append(GRN_ERROR_MESSAGE[20])

    #GRN Reference Exists Validation
    grn_reference = data.get('grn_reference', '')
    grn_ref_check = check_unique_grn_reference(warehouse, grn_reference)
    if grn_ref_check:
        header_error_list.append(GRN_ERROR_MESSAGE[23])

    job_code, jo_reference, error_status = get_job_code_from_jo_reference(warehouse, jo_reference, job_code)
    if error_status:
        header_error_list.append('Invalid JO Reference')

    if job_code:
        cache_key = jo_reference if jo_reference else job_code
        wh_cache_key = f'{warehouse.id}##{cache_key}'
        cache_status = cache.add(wh_cache_key, "True", timeout=60*2)
        jo_numbers_list.append(cache_key)
        if not cache_status:
            header_error_list.append(GRN_ERROR_MESSAGE[15] + str(cache_key))

    if header_error_list:
        key_wise_error_dict['header'] = header_error_list
    return key_wise_error_dict, jo_numbers_list, job_code

def get_job_code_from_jo_reference(warehouse, jo_reference, job_code):
    filter_dict = {'product_code__user': warehouse.id}
    if job_code:
        filter_dict.update({'job_code': job_code})
    if jo_reference:
        filter_dict.update({'jo_reference': jo_reference})

    jo_obj = JobOrder.objects.exclude(status__in = ['short-closed']).filter(**filter_dict)
    if jo_obj.exists():
        job_code = jo_obj[0].job_code
        jo_reference = jo_obj[0].jo_reference
        return job_code, jo_reference, False
    else:
        return job_code, jo_reference, True

def get_aggregated_sku_count(key, received_quantity, aggregated_sku_count):
    if key in aggregated_sku_count:
        aggregated_sku_count[key] += received_quantity
    else:
        aggregated_sku_count[key] = received_quantity
    return aggregated_sku_count

def validate_received_quantity(received_quantity, fg_count, error_message, sku_code,  extend_qty, receivable_quantity):
    if extend_qty and received_quantity > receivable_quantity:
        if receivable_quantity > fg_count:
            error_message.append(('RM Stock is not availble for %s') % str(sku_code))
    else:
        if received_quantity > fg_count:
            error_message.append(('Recieved quantity cannot exceed %s for %s') % (str(fg_count), str(sku_code)))

    return error_message

def get_serial_based(serial_based, jo_data, each_row):
    if not serial_based and jo_data.get('serial_based',0):
        serial_based = True

    inspection_lot_number = each_row.get("inspection_lot_number") if each_row.get("inspection_lot_number") and each_row.get("inspection_lot_number") not in ['0'] else ''
    return serial_based, inspection_lot_number

def validate_sku_batch_details(warehouse, batch_based, batch_detail_dict, error_message):
    if batch_based:
        batch_detail_dict_ = copy.deepcopy(batch_detail_dict)
        error_list, _ = validate_batch_details(warehouse, batch_detail_dict_, validate_retest_date=False)
        if error_list:
            error_message.append(error_list)
    return error_message

def calculate_fg_count(
        bom_quantity_details, sku_code, picked_quantity_details,
        extra_stock_quantity_details, received_quantity, decimal_limit,
        shortage_data
    ):
    """
    Calculate the count of finished goods (FG) based on the received quantity and other parameters.

    Args:
        bom_quantity_details (dict): A dictionary containing the Bill of Materials (BOM) quantity details.
        sku_code (str): The SKU code for which the FG count is being calculated.
        picked_quantity_details (dict): A dictionary containing the picked quantity details.
        extra_stock_quantity_details (dict): A dictionary containing the extra stock quantity details.
        received_quantity (int): The received quantity of the SKU.
        decimal_limit (int): The decimal limit for rounding the FG count.
        shortage_data (dict): A dictionary to store the shortage data.

    Returns:
        tuple: A tuple containing the FG count and the updated shortage data.
    """
    fg_quantity_list, status, fg_count = [], True, 0
    bom_details_dict = bom_quantity_details.get(sku_code)

    for sku, quantity in bom_details_dict.items():
        if quantity <= 0: continue

        avai_picked_quantity = received_quantity * quantity
        avaialble_quantity = picked_quantity_details.get(sku, 0)

        if sku in picked_quantity_details or extra_stock_quantity_details.get(sku):

            if avaialble_quantity >= avai_picked_quantity:
                picked_quantity = avai_picked_quantity
                reduce_picked_quantity = avai_picked_quantity

            else:
                picked_quantity = avaialble_quantity
                reduce_picked_quantity = avaialble_quantity
                required_quantity = avai_picked_quantity - avaialble_quantity

                if extra_stock_quantity_details.get(sku) and extra_stock_quantity_details.get(sku) > 0:
                    required_avaialble_quantity = extra_stock_quantity_details.get(sku)

                    if required_avaialble_quantity >= required_quantity:
                        picked_quantity += required_quantity
                        extra_reduce_quantity = required_quantity
                    else:
                        picked_quantity += required_avaialble_quantity
                        extra_reduce_quantity = required_avaialble_quantity

                    extra_stock_quantity_details[sku] = extra_stock_quantity_details[sku]- extra_reduce_quantity

            if sku in picked_quantity_details:
                picked_quantity_details[sku] = picked_quantity_details[sku]- reduce_picked_quantity

            update_shortage_data(avai_picked_quantity, picked_quantity, sku, shortage_data)

            max_fgs = round(picked_quantity / quantity, decimal_limit)
            fg_quantity_list.append(max_fgs)
        else:
            update_shortage_data(avai_picked_quantity, 0, sku, shortage_data)
            status = False

    if status and fg_quantity_list:
        fg_count = min(fg_quantity_list)

    return fg_count, shortage_data

def update_shortage_data(avai_picked_quantity, picked_quantity, sku, shortage_data):
    if avai_picked_quantity > picked_quantity:
        short_quantity = avai_picked_quantity - picked_quantity
        if sku in shortage_data:
            shortage_data[sku]['short_quantity'] += round(short_quantity, 3)
        else:
            shortage_data[sku] = {
                'available_quantity': round(picked_quantity, 3), 
                'short_quantity': round(short_quantity, 3),
                'sku_code': sku
            }

def get_aggregated_stock_of_consumable_list(data):
    result = [
        {'material_code': key[0], 'batch_number': key[1], 'total_picked_quantity': value, 'location': key[2]}
        for key, value in {tuple(key): sum(entry['total_picked_quantity'] for entry in group) for key, group in groupby(
            sorted(data, key=lambda x: (x['material_code'], x['batch_number'], x['location'])), key=lambda x: (
                x['material_code'], x['batch_number'], x['location']
            )
        )}.items()
    ]
    return result

def validate_stocks_for_consumption(picked_stock, extra_stock, error_message, consumable_list):
    """
    Validates the availability of stocks for consumption based on the given parameters.

    Args:
        picked_stock (QuerySet): The queryset of picked stocks.
        extra_stock (QuerySet): The queryset of extra stocks.
        error_message (list): The list to store error messages.
        consumable_list (list): The list of consumable stocks to validate.

    Returns:
        list: The updated error message list.
    """
    for stock_dict in consumable_list:
        filter_dict = {'sku__sku_code': stock_dict.get('material_code')}
        if stock_dict.get('location'):
            filter_dict['location__location'] = stock_dict.get('location')
        if stock_dict.get('batch_number'):
            filter_dict['batch_detail__batch_no'] = stock_dict.get('batch_number')

        picked_stock_qty = picked_stock.filter(**filter_dict).aggregate(Sum('quantity')).get('quantity__sum', 0) or 0
        extra_stock_qty = extra_stock.filter(**filter_dict).aggregate(Sum('quantity')).get('quantity__sum', 0) or 0

        if stock_dict.get('total_picked_quantity') > picked_stock_qty + extra_stock_qty:
            error_message.append('Stock is not available for %s' % str(stock_dict))
    return error_message

def calculate_fg_unit_price(sku_unit_price, labour_cost, overhead_cost, received_quantity):
    if (labour_cost or overhead_cost) and received_quantity and sku_unit_price:
        sku_unit_price = sku_unit_price + labour_cost + overhead_cost
        return sku_unit_price
    return round(sku_unit_price, 3)

def get_received_quantity_for_fg_price(
        received_quantity_for_price, received_quantity, receivable_quantity, each_row_deplete_stock
):
    if received_quantity > receivable_quantity:
        received_quantity_for_price = receivable_quantity

    if each_row_deplete_stock:
        received_quantity_for_price = receivable_quantity

    return received_quantity_for_price

def get_put_zone_dict(put_zones_list, warehouse):
    #Fetching Zone Details
    put_zone_dict = {}
    if put_zones_list:
        put_zones = list(ZoneMaster.objects.filter(zone__in=put_zones_list, user=warehouse.id).values('id', 'zone'))
        for each_zone in put_zones:
            put_zone_dict[each_zone['zone']] = each_zone['id']
    return put_zone_dict

def get_put_zone_id(put_zone_dict, put_zone):
    #Fetching Putzone Id
    put_zone_id, zone_restriction = '', ''
    if put_zone:
        zone_restriction = put_zone
        put_zone_id = put_zone_dict.get(put_zone) or ''
    return put_zone_id, zone_restriction

def get_final_putzone_details(each_row_put_zone, jo_data, put_zone_id):
    put_zone = each_row_put_zone if each_row_put_zone else jo_data.get('put_zone', '')
    put_zone_id = put_zone_id if put_zone_id else jo_data.get('put_zone_id', '')
    return put_zone, put_zone_id

def validate_jo_grn(data, warehouse):
    '''
    Validates JO GRN Details.

    Args:
        data (dict): The data containing JO GRN details.
        warehouse (object): The warehouse object.

    Returns:
        tuple: A tuple containing the following:
            - key_wise_error_dict (dict): A dictionary containing the errors encountered during validation.
            - return_data_list (list): A list of dictionaries containing the validated data.
            - sku_codes_list (list): A list of SKU codes.
            - grn_extra_dict (dict): A dictionary containing additional GRN details.
            - is_deplete_stock (bool): Indicates if the stock needs to be depleted.
            - jo_numbers_list (list): A list of JO numbers.
            - shortage_data (dict): A dictionary containing shortage data.
    '''
    log.info(("Validate JO GRN Data %s for Username %s") % (str(data), str(warehouse.username)))

    key_wise_error_dict, aggregated_sku_count, return_data_dict, jo_dict_data, grn_extra_dict = {},{},{},{},{}
    jo_numbers_list, return_data_list, sku_codes_list = [],[],[]

    shortage_data = {}
    #Validate Header Details
    key_wise_error_dict, jo_numbers_list, job_code = validate_header_details(
        warehouse, data, key_wise_error_dict, jo_numbers_list
    )
    if key_wise_error_dict:
        return key_wise_error_dict, return_data_list, sku_codes_list, grn_extra_dict, False, jo_numbers_list, shortage_data

    #Misc Details
    (configured_batch_attributes, configured_non_batch_attributes,
      receive_higher_fg_quantity, is_consume_extra_stocks) = get_required_misc_details(warehouse)

    #sku_codes
    sku_codes, put_zones_list = get_data_from_items(data['items'])
    put_zone_dict = get_put_zone_dict(put_zones_list, warehouse)

    #job order db details
    (job_order_details, fg_skus, 
    bom_quantity_details, jo_dict_data,
    jo_id_details, material_qty_details) = get_job_order_details_from_job_code(warehouse, job_code, sku_codes)

    #Picked Quantity details, Extra Quantity Details
    picked_stock, extra_stock, picked_quantity_details, extra_stock_quantity_details = get_picked_and_extra_quantity_details(
        warehouse, fg_skus, job_code, is_consume_extra_stocks
    )

    log.info(("Extra Quantity %s for Username %s") % (str(extra_stock_quantity_details), str(warehouse.username)))
    log.info(("Picked Quantity %s for Username %s") % (str(picked_quantity_details), str(warehouse.username)))

    #Location Details
    location_ids = dict(LocationMaster.objects.filter(zone__user = warehouse.id).values_list('location', 'id'))

    if not job_order_details:
        key_wise_error_dict['header'] = [GRN_ERROR_MESSAGE[502]]
        return key_wise_error_dict, return_data_list, sku_codes_list, grn_extra_dict, False, jo_numbers_list, shortage_data

    #Configs
    company_id = get_company_id(warehouse)
    decimal_limit = int(get_decimal_value(warehouse.id))

    is_deplete_stock = False
    #validation and dictionary framing
    aggregated_sku_count, material_to_material = {}, False
    for each_row in data['items']:
        error_message, serial_based, fg_count = [], False, 0

        jo_id, error_message = get_jo_id(jo_id_details, each_row, job_code, error_message)

        jo_data = jo_dict_data.get(jo_id, {})
        #consumable Qty
        aux_data = each_row.get('aux_data', {}) or {}
        consumable_list = aux_data.get('consumable_list', [])

        #Batch Based Check
        batch_based = jo_data.get('batch_based', False)

        #JO Material Quantity Details
        jo_material_details = material_qty_details.get(jo_id, {}) or {}

        #Additinal Batch Attributes Validation
        error_message = validate_configured_batch_attributes(
            each_row, error_message, batch_based, configured_batch_attributes,
            configured_non_batch_attributes
        )

        counter = 0
        sku_code = each_row.get('sku_code')

        sku_unit_price = each_row.get('buy_price', 0)
        labour_cost, overhead_cost = each_row.get('labour_cost',0), each_row.get('overhead_cost', 0)

        sku_codes_list.append(sku_code)

        index_sku_key = str(counter)+'_'+sku_code
        key = (job_code, sku_code)

        received_quantity, rejected_quantity = (
            each_row.get('received_quantity',0), each_row.get('rejected_quantity',0)
        )

        if received_quantity <= 0:
            continue

        aggregated_sku_count = get_aggregated_sku_count(key, received_quantity, aggregated_sku_count)

        #GRN Quantity Validation
        error_message, ordered_quantity, already_received_quantity, extend_qty, receivable_quantity = grn_quantity_validation(
            each_row, jo_dict_data, jo_id, error_message, aggregated_sku_count, key, receive_higher_fg_quantity, consumable_list
        )

        #short close check
        is_deplete_stock, is_short_closed, each_row_deplete_stock = get_short_close_status(each_row, is_deplete_stock)

        jo_product_quantity = jo_data.get('ordered_quantity')
        received_quantity_for_price = received_quantity
        received_quantity_for_price = get_received_quantity_for_fg_price(
            received_quantity_for_price, received_quantity, receivable_quantity, each_row_deplete_stock
        )
        #FG Count and FG Price Calculation
        if not error_message:
            if not consumable_list:
                #validating the received quantity based on picked rms
                fg_count, shortage_data = calculate_fg_count(
                    bom_quantity_details, sku_code, picked_quantity_details,
                    extra_stock_quantity_details, received_quantity, decimal_limit,
                    shortage_data
                )
                #validating received quantity
                error_message = validate_received_quantity(
                    received_quantity, fg_count, error_message, sku_code, extend_qty, receivable_quantity
                )

                if not error_message:
                    sku_unit_price = get_rm_sku_batch_details(
                        warehouse, picked_stock, extra_stock, jo_material_details, received_quantity_for_price,
                        jo_product_quantity, received_quantity
                    )

                    sku_unit_price = calculate_fg_unit_price(
                        sku_unit_price, labour_cost, overhead_cost, received_quantity
                    )
            else:
                consumable_list = get_aggregated_stock_of_consumable_list(
                    consumable_list
                )

                error_message = validate_stocks_for_consumption(
                    picked_stock, extra_stock, error_message, consumable_list
                )

                #FG Price Calculation based on Consumed Quantity
                if not error_message:
                    sku_unit_price = calculate_fg_price_for_consumed_data(
                        warehouse, received_quantity, consumable_list
                    )
                    sku_unit_price = calculate_fg_unit_price(
                        sku_unit_price, labour_cost, overhead_cost, received_quantity
                    )
        counter+=1

        serial_based, inspection_lot_number = get_serial_based(serial_based, jo_data, each_row)
        sku_id = jo_data.get("sku_id")

        #Batch Detail Creation Dict
        batch_detail_dict = {
            "transact_type": "job_order",
            "batch_no": each_row.get("batch_number"),
            "batch_reference" : each_row.get("batch_reference", ''),
            "vendor_batch_no" : each_row.get("vendor_batch_number", ''),
            "expiry_date": each_row.get('expiry_date'),
            "manufactured_date": each_row.get("manufactured_date"),
            "mrp": each_row.get("mrp", 0),
            "best_before_date" : each_row.get("best_before_date"),
            "buy_price": sku_unit_price,
            "retest_date": each_row.get("retest_date"),
            "reevaluation_date": each_row.get("reevaluation_date"),
            "inspection_lot_number": inspection_lot_number,
            "sku_id": sku_id,
            "weight": each_row.get('weight', '')
        }

        error_message = validate_sku_batch_details(warehouse, batch_based, batch_detail_dict, error_message)

        #JOGRN Creation Dict
        job_order_details_dict = {
            "job_order_id" : jo_id,
            "quantity": received_quantity,
            "damaged_quantity": rejected_quantity,
            "remarks": each_row.get('remarks', ''),
            "status": 1,
            "default_landed_cost": labour_cost,
            "default_overhead_cost": overhead_cost,
            "grn_reference": data.get('grn_reference', '')
        }

        
        put_zone_id, zone_restriction = get_put_zone_id(put_zone_dict, each_row.get("put_zone"))
        put_zone, put_zone_id = get_final_putzone_details(each_row.get('put_zone'), jo_data, put_zone_id)
        extra_details = {
            "put_zone": put_zone,
            "put_zone_id": put_zone_id,
            "zone_restriction" : zone_restriction,
            "serial_based": jo_data.get('serial_based',0),
            "grn_quantity" : received_quantity_for_price,
            "accepted_quantity": each_row.get('accepted_quantity'),
            "rejected_quantity": each_row.get('rejected_quantity'),
            "job_code": job_code,
            "is_short_closed": is_short_closed,
            "creation_date": jo_data.get('jo_creation_date'),
            "sku_id": sku_id,
            "sku_code": jo_data.get("sku_code"),
            "sku_desc": jo_data.get("sku_desc"),
            "sku_price": jo_data.get('sku_price', 0),
            "quantity": each_row.get('accepted_quantity'),
            "material_to_material": material_to_material,
            "jo_reference": jo_data.get('jo_reference'),
            "batch_based" : batch_based,
            "deplete_stock": each_row_deplete_stock,
            "ordered_quantity": jo_product_quantity,
            "received_quantity": received_quantity,
            "already_received_quantity": already_received_quantity
        }
        if each_row_deplete_stock:
            extra_details.update({
                "deplete_qty": ordered_quantity - already_received_quantity- received_quantity
            })
        #PO Location Creation Dict
        po_location_dict = {
            "sku_id": sku_id,
            "sku_code": jo_data.get("sku_code"),
            "job_order_id": jo_id,
            "grn_quantity": each_row.get('accepted_quantity'),
            "putaway_type": "jo_putaway",
            "sku_mrp": jo_data.get("sku_mrp", 0),
            "sku_buy_price": sku_unit_price,
            "receipt_type": "job_order",
            "decimal_limit": decimal_limit,
            "company_id": company_id,
            "sku_uom": jo_data.get("sku_uom")
        }
        put_location = each_row.get('location', '')
        if put_location and location_ids.get(put_location):
            po_location_dict.update({'location_id': location_ids.get(put_location)})

        return_data_dict = {
            "batch_details": batch_detail_dict,
            "job_order_details": job_order_details_dict,
            'po_location_dict' : po_location_dict,
            "extra_details": extra_details
            }
        return_data_list.append(return_data_dict)
        if error_message:
            key_wise_error_dict[index_sku_key] = [error_message]

    grn_extra_dict = {
        'serial_based' : serial_based,
        'send_grn_for_qc': data.get('send_for_qc', False),
        'consumable_list': consumable_list
        }

    shortage_data = format_shortage_data(shortage_data)

    return (
        key_wise_error_dict, return_data_list, sku_codes_list, grn_extra_dict, 
        is_deplete_stock, jo_numbers_list, shortage_data
    )
 
def format_shortage_data(shortage_data):
    shortage_data_list = []
    for sku, sku_data in shortage_data.items():
        shortage_data_list.append(sku_data)
    return shortage_data_list

def validate_maximum_takeoff_weight(warehouse, weight_json_data):
    '''
    Validating maximum takeoff weight 
    '''
    error_message_list = []
    #get Maximum takeoff weight config
    maximum_take_weight_config = get_misc_value('maximum_takeoff_weight', warehouse.id)
    try:
        maximum_take_weight_config = float(maximum_take_weight_config)
    except:
        maximum_take_weight_config = 0
    # if maximum_take_weight_config in ['false', False, '']:
        # return error_message_list
    #validating weight info
    weight_details = weight_json_data.get('weighing_scale_details', []) or []
    total_gross_weight_claimed, total_gross_weight_accepted, vender_container_numbers = 0, 0, []
    for weight_record in weight_details:
        gross_weight_claimed = weight_record.get('gross_weight_claimed', 0) or 0
        gross_weight_accepted = weight_record.get('gross_weight_observed', 0) or 0
        vender_container_number = weight_record.get('vendor_container_no', 0) or 0

        valid_value, gross_weight_claimed = field_validation(gross_weight_claimed, data_type=float)
        if not valid_value:
            error_message_list.append('vendor container no: %s - "gross_weight_claimed" should be decimal'%vender_container_number)
            continue
        valid_value, gross_weight_accepted = field_validation(gross_weight_accepted, data_type=float)
        if not valid_value:
            error_message_list.append('vendor container no: %s - "gross_weight_accepted" should be decimal'%vender_container_number)
            continue
        total_gross_weight_claimed += gross_weight_claimed
        total_gross_weight_accepted += gross_weight_accepted
        vender_container_numbers.append(vender_container_number)
    if total_gross_weight_claimed > 0:
        takeoff_weight_percentage = ((total_gross_weight_claimed - total_gross_weight_accepted) / total_gross_weight_claimed)*100
        if (takeoff_weight_percentage > maximum_take_weight_config) or (takeoff_weight_percentage < -maximum_take_weight_config):
            error_message_list.append("%s %s" %(GRN_ERROR_MESSAGE[403], str(','.join(vender_container_numbers))))

    return error_message_list

def field_validation(field_value, data_type=None):
    valid_value = True
    try:
        field_value = data_type(field_value)
    except:
        valid_value = False
    return valid_value, field_value

def get_unit_price_details(warehouse, sku_codes, batch_nos):
    '''
    Returns SKU Unit Price Details
    '''
    batch_details = list(BatchDetail.objects.filter(
        warehouse = warehouse.id, sku__sku_code__in = sku_codes, batch_no__in = batch_nos, stockdetail__unit_price__gt = 0
    ).values('sku__sku_code', 'stockdetail__unit_price', 'batch_no'))

    batch_details_dict = {}
    for batch in batch_details:
        unique_key = (batch.get('sku__sku_code'), batch.get('batch_no', ''))
        batch_details_dict[unique_key] = batch.get('stockdetail__unit_price', 0)
    return batch_details_dict

def get_sku_batch_details(warehouse, consumption_details):
    sku_codes, batch_nos = [], []
    for data in consumption_details:
        sku_codes.append(data.get('material_code'))
        if data.get('batch_number'):
            batch_nos.append(data['batch_number'])

    batch_detail_dict = get_unit_price_details(warehouse, sku_codes, batch_nos)
    return batch_detail_dict

def calculate_fg_price(received_quantity, consumption_details, batch_details_dict):
    '''
    Calculate FG Price for Consumed Data

    Parameters:
    received_quantity (int): The total quantity received.
    consumption_details (list): A list of dictionaries containing consumption details.
    batch_details_dict (dict): A dictionary containing batch details as keys and unit prices as values.

    Returns:
    float: The calculated FG price for the consumed data.
    '''
    total_price = 0
    for data in consumption_details:
        sku_code = data.get('material_code')
        batch_no = data.get('batch_number')
        quantity = data.get('total_picked_quantity', 0)
        if sku_code and batch_no:
            unit_price = batch_details_dict.get((sku_code, batch_no), 0)
            total_price += (unit_price * quantity)

    return total_price / received_quantity

def calculate_fg_price_for_consumed_data(warehouse, received_quantity, consumption_details):
    '''
    Calculate FG Price for Consumed Data

    This function calculates the price of finished goods (FG) for the consumed data based on the warehouse, received quantity, and consumption details.

    Parameters:
    - warehouse (str): The name of the warehouse where the consumption is taking place.
    - received_quantity (int): The quantity of goods received.
    - consumption_details (dict): A dictionary containing the details of the consumed goods.

    Returns:
    - fg_price (float): The calculated price of the finished goods.

    '''
    batch_details_dict = get_sku_batch_details(warehouse, consumption_details)

    fg_price = calculate_fg_price(received_quantity, consumption_details, batch_details_dict)
    return fg_price

def get_rm_sku_batch_details(
        warehouse, picked_stock, extra_stock, jo_material_details,
        received_quantity, product_quantity, actual_received_quantity
    ):
    fg_price = 0
    values_list = ['sku__sku_code', 'batch_detail__batch_no', 'quantity']
    picked_batch_details = list(picked_stock.values(*values_list))
    extra_batch_details = list(extra_stock.values(*values_list))

    required_material_details = {}
    for material_code, quantity in jo_material_details.items():
        required_qty = (float(quantity) / float(product_quantity)) * float(received_quantity)
        required_material_details[material_code] = required_qty

    consumble_details = []
    consumble_details = get_consumble_details(
        consumble_details, required_material_details, picked_batch_details
    )
    consumble_details = get_consumble_details(
        consumble_details, required_material_details, extra_batch_details
    )
    fg_price = calculate_fg_price_for_consumed_data(
            warehouse, actual_received_quantity, consumble_details
        )
    return fg_price

def get_consumble_details(consumble_details, required_material_details, rm_batch_details):
    for stock in rm_batch_details:
        sku_code = stock.get('sku__sku_code')

        if required_material_details.get(sku_code, 0) <= 0:
            continue

        required_quantity = required_material_details.get(sku_code)
        available_quantity = stock.get('quantity')
        if available_quantity >= required_quantity:
            consumble_details.append({
                'material_code': sku_code,
                'batch_number': stock.get('batch_detail__batch_no'),
                'total_picked_quantity': float(required_quantity)
            })
            required_material_details[sku_code] = 0
        else:
            consumble_details.append({
                'material_code': sku_code,
                'batch_number': stock.get('batch_detail__batch_no'),
                'total_picked_quantity': float(available_quantity)
            })
            required_material_details[sku_code] = required_material_details[sku_code] - available_quantity

    return consumble_details

def calculate_fg_price_details(warehouse, jo_reference, fg_sku_code, received_quantity):
    jo_mat_obj = JOMaterial.objects.filter(
        job_order__product_code__user = warehouse.id, job_order__jo_reference = jo_reference
    )
    #JOMaterial Details
    jo_material_details = dict(jo_mat_obj.values_list('material_code__sku_code').annotate(Sum('material_quantity')))

    job_code = jo_mat_obj[0].job_order.job_code
    product_quantity = jo_mat_obj[0].job_order.product_quantity

    (picked_stock, extra_stock, 
     picked_quantity_details, extra_stock_quantity_details) = get_picked_and_extra_quantity_details(warehouse, [fg_sku_code], job_code, False)
    
    log.info(("Picked Quantity Details %s, Extra Quantity Details %s") % (str(picked_quantity_details), str(extra_stock_quantity_details)))

    fg_price = get_rm_sku_batch_details(
        warehouse, picked_stock, extra_stock, jo_material_details,
        received_quantity, product_quantity, received_quantity
    )
    return fg_price

def prepare_batch_based_filters(batch_references, batch_nos, all_batch_flag, filter_dict):
    query_filter = Q()
    if all_batch_flag:
        if batch_references:
            filter_dict.update({'batch_detail__batch_reference__in': batch_references})
        if batch_nos:
            filter_dict.update({'batch_detail__batch_no__in': batch_nos})
    else:
        if batch_references and batch_nos:
            query_filter = Q(batch_detail__batch_reference__in=batch_references, batch_detail__batch_no__in=batch_nos)
        elif batch_references:
            query_filter = Q(batch_detail__batch_reference__in=batch_references)
        elif batch_nos:
            query_filter = Q(batch_detail__batch_no__in=batch_nos)
        query_filter = query_filter | Q(batch_detail__isnull=True) if query_filter else Q()
    return query_filter, filter_dict

def calculate_gross_amt(invoice_price, invoice_qty, decimal_limit):
    return truncate_float((invoice_price * invoice_qty), decimal_limit)

def calculate_sd_amt_from_percent(gross_amt, sd_percent, decimal_limit):
    return truncate_float((sd_percent / 100) * gross_amt, decimal_limit)

def calculate_sd_percent_from_amt(gross_amt, sd_amt, decimal_limit):
    if gross_amt == 0:
        return 0  # Avoid division by zero
    return truncate_float((sd_amt / gross_amt) * 100, decimal_limit)

def calculate_net_gross_amt(gross_amt, sd_amt, decimal_limit):
    if sd_amt:   
        return truncate_float((gross_amt - sd_amt), decimal_limit)
    return truncate_float(gross_amt, decimal_limit)

def calculate_cd_amt_from_percent(net_gross_amt, cd_percent, decimal_limit):
    return truncate_float((cd_percent / 100) * net_gross_amt, decimal_limit)

def calculate_cd_percent_from_amt(net_gross_amt, cd_amt, decimal_limit):
    if net_gross_amt == 0:
        return 0  # Avoid division by zero
    return truncate_float((cd_amt / net_gross_amt) * 100, decimal_limit)

def calculate_tax_amt(net_gross_amt, cd_amt, tax_percent, decimal_limit):
    taxable_amt = net_gross_amt
    if cd_amt:
        taxable_amt = truncate_float((net_gross_amt - cd_amt), decimal_limit)
    if taxable_amt == 0:
        return taxable_amt, 0  # Avoid division by zero in tax calculation
    return taxable_amt, truncate_float((tax_percent / 100) * taxable_amt, decimal_limit)

def calculate_net_amount(taxable_amt, tax_amt, decimal_limit):
    return truncate_float((taxable_amt + tax_amt), decimal_limit)

def calculate_effective_rate(taxable_amt, invoice_qty, invoice_free_quantity, decimal_limit):
    total_qty = invoice_qty + invoice_free_quantity
    if total_qty == 0:
        return 0  # Avoid division by zero
    return truncate_float((taxable_amt / total_qty), decimal_limit)

def calculate_margin(mrp, eff_rate, decimal_limit, tax_percent):
    mrp_without_tax = truncate_float((mrp / (1 + (tax_percent / 100))), decimal_limit)
    if mrp_without_tax == 0:
        return 0, mrp_without_tax  # Avoid division by zero
    return truncate_float(((mrp_without_tax - eff_rate) / mrp_without_tax) * 100, decimal_limit), mrp_without_tax

def process_tax_fields(asn_line_level_data, decimal_limit=2):
    """
    Process tax fields based on the given ASN line level data.

    Args:
        asn_line_level_data (dict): A dictionary containing the ASN line level data.
        decimal_limit (int, optional): The decimal limit for rounding calculations. Defaults to 2.

    Returns:
        dict: A dictionary containing the processed tax fields.

    """
    invoice_price = asn_line_level_data.get('buy_price', 0)
    invoice_qty = asn_line_level_data.get('invoice_quantity', 0)
    invoice_free_quantity = asn_line_level_data.get('invoice_free_quantity', 0)
    sd_percent = asn_line_level_data.get('scheduled_percent', 0)
    sd_amt = asn_line_level_data.get('scheduled_amount', 0)
    cd_percent = asn_line_level_data.get('cash_discount_percent', 0)
    cd_amt = asn_line_level_data.get('cash_discount_amount', 0)
    tax_percent = asn_line_level_data.get('tax', 0) + asn_line_level_data.get('cess_tax', 0)
    mrp = asn_line_level_data.get('mrp', 0)

    gross_amt = calculate_gross_amt(invoice_price, invoice_qty, decimal_limit)
    if sd_percent and sd_percent != 0:
        sd_amt = calculate_sd_amt_from_percent(gross_amt, sd_percent, decimal_limit)
    elif sd_amt and sd_amt != 0:
        sd_percent = calculate_sd_percent_from_amt(gross_amt, sd_amt, decimal_limit)

    net_gross_amt = calculate_net_gross_amt(gross_amt, sd_amt, decimal_limit)

    if cd_percent and cd_percent != 0:
        cd_amt = calculate_cd_amt_from_percent(net_gross_amt, cd_percent, decimal_limit)
    elif cd_amt and cd_amt != 0:
        cd_percent = calculate_cd_percent_from_amt(net_gross_amt, cd_amt, decimal_limit)

    taxable_amt, tax_amt = calculate_tax_amt(net_gross_amt, cd_amt, tax_percent, decimal_limit)

    net_amount = calculate_net_amount(taxable_amt, tax_amt, decimal_limit)

    eff_rate = calculate_effective_rate(taxable_amt, invoice_qty, invoice_free_quantity, decimal_limit)

    margin, mrp_without_tax = calculate_margin(mrp, eff_rate, decimal_limit, tax_percent)

    additional_margin = asn_line_level_data.get('gatekeeper_margin', 0) - margin

    return {
        "gross_amt": gross_amt,
        "sd_percent": sd_percent if sd_percent else 0,
        "sd_amt": sd_amt if sd_amt else 0,
        "net_gross_amt": net_gross_amt,
        "cd_percent": cd_percent if cd_percent else 0,
        "cd_amt": cd_amt if cd_amt else 0,
        "tax_amt": tax_amt,
        "taxable_amount": taxable_amt,
        "net_amount": net_amount,
        "eff_rate": eff_rate,
        "margin": margin,
        "mrp_without_tax": mrp_without_tax,
        "additional_margin": additional_margin,
        "total_tax": tax_percent
    }

def get_putaway_strategy_mappings(sku_list, sku_category_list, warehouse, transaction_type, process_type):
    # Initial query to get the necessary data
    q_filter = Q(entity_name__in=sku_list) | Q(entity_name__in=sku_category_list)
    putaway_mapping = PutawayMapping.objects.filter(
        q_filter, warehouse=warehouse, status=1, quantity_type = 'accepted',
        transaction_type = transaction_type, process_type = process_type
    ).order_by('priority').values('entity_name', 'strategy', 'location_id', 'zone_id', 'capacity', 'level')

    # Initializing dictionaries with defaultdict
    sku_strategy_mapping = defaultdict(set)
    category_strategy_mapping = defaultdict(set)
    location_mapping_capacity = defaultdict(int)
    mapped_zones = defaultdict(list)
    mapped_locations = defaultdict(set)
    # Iterating through the fetched data
    for mapping in putaway_mapping:
        entity_name = mapping['entity_name']
        strategy = mapping['strategy']
        location = mapping['location_id']

        # Update strategy mappings
        if mapping['level'] == 'sku_level':
            sku_strategy_mapping[entity_name].add(strategy)
        if mapping['level'] == 'sku_category_level':
            category_strategy_mapping[entity_name].add(strategy)

        mapped_locations[entity_name].add(location)
        if strategy == 'fixed_bin_mapping':
            location_mapping_capacity[(entity_name, location)] += mapping['capacity']
        else:
            mapped_zones[entity_name].append(mapping['zone_id'])

    return sku_strategy_mapping, category_strategy_mapping, location_mapping_capacity, mapped_zones, mapped_locations

def get_rej_reason_putaway_strategy_mappings(warehouse, grn_type, process_type, rejected_reasons):
    """Frame Putaway mapping dict for rejected reasons & rejected quntites"""
    mapping_dict = {}
    putaway_mappings = PutawayMapping.objects.filter(
        warehouse=warehouse, status=1,
        transaction_type = grn_type,
        process_type = process_type,
        reason__in=rejected_reasons,
        quantity_type = 'rejected'
    ).values('strategy', 'reason', 'location', 'zone', 'strategy')

    # Fetch putaway mappings for 'short' quantity type (without reason)
    short_mappings = PutawayMapping.objects.filter(
        warehouse=warehouse, status=1,
        transaction_type=grn_type, process_type=process_type,
        quantity_type='short'
    ).values('strategy', 'location', 'zone')

    #Frame zone dict with first location
    zone_list = [each_row['zone'] for each_row in putaway_mappings if each_row['strategy'] == 'dynamic_bin_top_up']
    zone_list += [each_row['zone'] for each_row in short_mappings if each_row['strategy'] == 'dynamic_bin_top_up']

    zone_loc_dict = dict(LocationMaster.objects.filter(zone_id__in=zone_list, zone__user=warehouse.id).values_list('zone_id', 'id'))

    for each_mapping in putaway_mappings:
        reason = each_mapping['reason']
        strategy = each_mapping['strategy']
        location = each_mapping['location']
        zone = each_mapping['zone']

        if reason not in mapping_dict:
            mapping_dict[reason] = {
                'fixed_bin_mapping': [],
                'dynamic_bin_mapping': []
            }
        #Frame strategy wise locations & zones
        if strategy == 'fixed_bin_mapping':
            mapping_dict[reason]['fixed_bin_mapping'].append(location)
        elif strategy == 'dynamic_bin_top_up':
            mapping_dict[reason]['dynamic_bin_mapping'].append(zone_loc_dict.get(zone))

    short_mapping_dict = {
        'fixed_bin_mapping': '',
        'dynamic_bin_mapping': ''
    }
    for each_mapping in short_mappings:
        strategy = each_mapping['strategy']
        location = each_mapping['location']
        zone = each_mapping['zone']
        if not short_mapping_dict['fixed_bin_mapping'] and strategy == 'fixed_bin_mapping':
            short_mapping_dict['fixed_bin_mapping'] = location
        elif not short_mapping_dict['dynamic_bin_mapping'] and strategy == 'dynamic_bin_top_up':
            short_mapping_dict['dynamic_bin_mapping'] = zone_loc_dict.get(zone)

    return mapping_dict, short_mapping_dict

def get_financial_year_range(invoice_date, timezone):
    """
    Calculate the financial year start and end dates based on the invoice date.

    Args:
        invoice_date (datetime): Date of the invoice
        timezone (str): Timezone of the user/warehouse

    Returns:
        tuple: (financial_year_start, financial_year_end) in UTC
    """
    # Determine financial year based on Indian financial year (April 1 - March 31)
    if invoice_date.month >= 4:
        financial_year_start = datetime.date(invoice_date.year, 4, 1)
        financial_year_end = datetime.date(invoice_date.year + 1, 3, 31)
    else:
        financial_year_start = datetime.date(invoice_date.year - 1, 4, 1)
        financial_year_end = datetime.date(invoice_date.year, 3, 31)

    # Convert to user-defined timezone
    user_timezone = pytz.timezone(timezone)

    # Combine dates with min/max times
    financial_year_start_local = datetime.datetime.combine(financial_year_start, datetime.datetime.min.time())
    financial_year_end_local = datetime.datetime.combine(financial_year_end, datetime.datetime.max.time())

    # Localize and convert to UTC
    financial_year_start_utc = user_timezone.localize(financial_year_start_local).astimezone(pytz.UTC)
    financial_year_end_utc = user_timezone.localize(financial_year_end_local).astimezone(pytz.UTC)

    return financial_year_start_utc, financial_year_end_utc

def get_current_financial_year(timezone):
    """
    Calculate the financial year start and end dates based on the invoice date.

    Args:
        invoice_date (datetime): Date of the invoice
        timezone (str): Timezone of the user/warehouse

    Returns:
        current financial year: (financial_year_start_utc) in UTC
    """
    today = datetime.date.today()
    current_year = today.year
    financial_year_start = datetime.date(current_year if today.month >= 4 else current_year - 1, 4, 1)

    # Convert to user-defined timezone
    user_timezone = pytz.timezone(timezone)
    financial_year_start_local = datetime.datetime.combine(financial_year_start, datetime.datetime.min.time())
    financial_year_start_localized = user_timezone.localize(financial_year_start_local)

    # Convert to UTC
    financial_year_start_utc = financial_year_start_localized.astimezone(pytz.UTC)
    return financial_year_start_utc

def duplicate_invoice_number_check(warehouse, asn_number, invoice_number, supplier_id, po_numbers_list, po_ref_list, misc_dict, invoice_date=None):
    """
    Restrict the same supplier with the same invoice based on configuration.
    """
    supplier_inv_check = misc_dict.get('same_supplier_invoice_check', 'false')
    filter_dict = {
        'purchase_order__open_po__supplier__supplier_id': supplier_id,
        'invoice_number': invoice_number
    }
    #Framing Filter dict unique invoice validation configs
    error_message = GRN_ERROR_MESSAGE[18]
    if supplier_inv_check == 'same_po_same_invoice':
        error_message = GRN_ERROR_MESSAGE[503]
        if po_ref_list:
            filter_dict['purchase_order__open_po__po_name__in'] = po_ref_list
        else:
            filter_dict['purchase_order__po_number__in'] = po_numbers_list
    elif supplier_inv_check != 'same_supplier_same_invoice':
        return False, ''

    # Same invoice - same supplier validation same financial years
    # The same invoice number can exist for the same supplier in different financial years
    if misc_dict.get('validate_doc_num_per_fy', 'false') =='true':
        timezone = get_user_time_zone(warehouse)
        if invoice_date:
            financial_year_start, financial_year_end = get_financial_year_range(
                invoice_date,
                timezone
            )
            # Add financial year date range to filter
            filter_dict['invoice_date__gte'] = financial_year_start
            filter_dict['invoice_date__lte'] = financial_year_end
        else:
            financial_year_start_utc = get_current_financial_year(timezone)
            filter_dict['invoice_date__gte'] = financial_year_start_utc

    # check in same supplier exists within po or supplier in GRN
    grn_inv_check = SellerPOSummary.objects.filter(
        **filter_dict,
        sku__user=warehouse.id
    ).exclude(asn__asn_number=asn_number).exclude(status = 9)

    # check in same supplier exists within po or supplier in ASN
    asn_inv_check = ASNSummary.objects.filter(
        **filter_dict,
        asn_user=warehouse.id
    ).exclude(asn_number=asn_number).exclude(status__in = [3, 8])

    same_supplier_inv_check = grn_inv_check.exists() or asn_inv_check.exists()

    return same_supplier_inv_check, error_message

@get_warehouse
def validate_lpn_sku_batch(request, warehouse: User):
    """
    API to validate whether LPNs have the same SKU and same batch or not.
    """

    try:
        request_data = json.loads(request.body)
        lpn_number = request_data.get('lpn_number', [])
        sku_details = request_data.get('sku_details', {})
        allow_multi_sku = request_data.get('allow_multi_sku', True)
        allow_multi_batch = request_data.get('allow_multi_batch', True)

        if not lpn_number or not sku_details:
            return JsonResponse({"error": "LPNs and SKU details are required."}, status=400)

        filter_dict = {
            'sku__user': warehouse.id,
            'lpn_number': lpn_number,
            'quantity__gt': 0,
        }

        lpn_skus = StockDetail.objects.filter(**filter_dict).values('sku__sku_code', 'batch_detail__batch_no').distinct()

        if not allow_multi_sku:
            # Collect unique sku_codes from lpn_skus
            unique_skus = {entry['sku__sku_code'] for entry in lpn_skus}
            # Check if there's more than one unique sku_code or if the single sku_code doesn't match the provided one
            if unique_skus and sku_details.get('sku_code') not in unique_skus:
                return JsonResponse({"error": "Multiple SKUs are not allowed to add into this LPN."}, status=400)

        if not allow_multi_batch:
            # Collect unique (sku_code, batch_no) pairs from lpn_skus
            unique_sku_batches = {(entry['sku__sku_code'], entry['batch_detail__batch_no']) for entry in lpn_skus}
            unique_skus = {entry[0] for entry in unique_sku_batches}
            # The expected combination from the provided sku_details
            expected_sku_batch = (sku_details.get('sku_code'), sku_details.get('batch_no'))

            # Check if there's more than one unique pair or if the single pair doesn't match the expected one
            if sku_details.get('sku_code') in unique_skus and expected_sku_batch not in unique_sku_batches:
                return JsonResponse({"error": "Multiple batches are not allowed to add into this LPN."}, status=400)

        return JsonResponse({"message": "Success"}, status=200)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.error('LPN SKU Batch Validation failed for %s and params are %s and error statement is %s' % (
        str(request.user.username), str(request.body), str(e)))
        return JsonResponse({"error": "Internal Server Error"}, status=500)
