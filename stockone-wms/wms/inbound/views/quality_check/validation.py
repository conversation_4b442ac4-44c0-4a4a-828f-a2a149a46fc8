from django.core.cache import cache

from quality_control.models import QualityControl

LOCK_EXPIRE = 60*10

def frame_qc_data_from_request(request_data, requested_user, qc_dict, failed_list, failed_status):
    """ Prepare QC confirmation dict """
    qc_data = []
    for data in request_data:
        if not data:
            continue
        qc = qc_dict[data['id']]
        if not data['approved_quantity']:
            data['approved_quantity'] = 0
        if not data['rejected_quantity']:
            data['rejected_quantity'] = 0
        if not data['short_quantity']:
            data['short_quantity'] = 0
        if not data.get('remarks'):
            data['remarks'] = ''
        if qc['total_quantity'] < qc['approved_quantity'] + qc['rejected_quantity'] + qc['short_quantity']+ data['approved_quantity'] + data['rejected_quantity'] + data['short_quantity']:
            failed_status = True
            failed_list['error'] = ['QC quantity more than Total Quantity for id:' + str(data['id'])]
        if len(data['remarks']) > 128:
            failed_status = True
            failed_list['error'] = ['Reasons should be less than 128 characters']

        if len(data.get('comments', '')) > 128:
            failed_status = True
            failed_list['error'] = ['Remarks should be less than 128 characters']

        elif not failed_status:
            qc['approved_quantity'] += data['approved_quantity']
            qc['rejected_quantity'] += data['rejected_quantity']
            qc['short_quantity'] += data['short_quantity']
            qc['remarks'] = data['remarks']
            if qc['total_quantity'] == qc['approved_quantity'] + qc['rejected_quantity'] + qc['short_quantity']:
                qc['status'] = 2
            if qc['json_data']:
                qc['json_data'].update({'qc_done_by': requested_user.username})
            else:
                qc['json_data'] = {'qc_done_by': requested_user.username}
            qc_data.append(qc)

    return qc_data, failed_list, failed_status

def validate_quality_check(request_data, requested_user):
    failed_list = {'error':[]}
    failed_status = False
    qc_ids = []
    sps_ids = []
    for qc in request_data:
        if qc:
            sps_ids.append(qc['transaction_id'])

        # Caching qc_id
        cache_key = 'QC-' + str(qc['id'])
        cache_status = cache.add(cache_key, "True", timeout=LOCK_EXPIRE)
        if not cache_status and qc['id'] not in qc_ids:
            failed_status = True
            for cache_id_key in qc_ids:
                cache_key = 'QC-' + str(cache_id_key)
                cache.delete(cache_key)
            failed_list['error'] = ['For this SKU, QC is in-progress, please try again! SKU Code: '+qc['sku_code']]
            return failed_status, failed_list, [], '', {}, [], []
        else:
            qc_ids.append(qc['id'])

    instances = list(QualityControl.objects.filter(id__in=qc_ids).values())
    qc_dict = {}
    for instance in instances:
        qc_dict[instance['id']] = instance
    qc_type = instance['transaction_type']
    qc_data, failed_list, failed_status = frame_qc_data_from_request(request_data, requested_user, qc_dict, failed_list, failed_status)

    return failed_status, failed_list, qc_data, qc_type, qc_dict, qc_ids, sps_ids