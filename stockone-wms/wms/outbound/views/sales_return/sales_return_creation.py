#package imports
import pandas as pd
import numpy as np
import pytz
from datetime import datetime
import traceback
from collections import defaultdict
import itertools

#django imports
from django.db.models import Q, F, Sum
from django.db import transaction
from django.core.cache import cache

#wms base imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

#core models imports
from core.models import SKUMaster, MiscDetailOptions

#core operations imports
from core_operations.views.common.main import (
    get_user_prefix_incremental, get_multiple_misc_value_split, get_multiple_misc_values
)
from core_operations.views.integration.integration import webhook_integration_3p

#inventory imports
from inventory.views.locator.stock_detail import (
    get_or_create_batch, validate_batch_details
)

#outbound models imports
from outbound.models import (
    SellerOrderSummary, CustomerMaster, SalesReturn,
    SalesReturnLineLevel, SalesReturnBatchLevel
)

#sales return imports
from .constants import (
    SALES_RETURN_UNIQUE_FIELDS, ERRORS, DOCUMENT_TYPE_KEY_MAPPING,
    SALES_RETURN_DATE_FORAMT
)
from .grn import auto_sales_return_grn

from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from inventory.views.serial_numbers.serial_number import SerialNumberMixin

log = init_logger('logs/common_sales_return.log')
CACHE_EXPIRE = 5 * 60

class SalesReturnCreationMixin:
    def __init__(self, user: User, warehouse: User, request_data: list = None, extra_params:dict = {}):
        self.user = user
        self.warehouse = warehouse
        self.timezone = self.warehouse.userprofile.timezone or 'Asia/Calcutta'

        self.request_data = request_data or []
        self.extra_params = extra_params or {}
        self.internal_request = self.extra_params.get('internal_request', False)

        self.batch_size = 500

    def sales_return_process(self):
        '''
        Start sales return process
        '''
        try:
            self.errors = {}
            self.return_ids, self.new_batch_creation_list = [], []
            self.is_serialized_sku_map, self.sku_serial_map, self.batch_mapping = {}, defaultdict(list), {}
            self.sku_serial_transaction_map = defaultdict(list)
            self.add_cache()
            if self.errors:
                self.delete_cache()
                return {'errors': self.errors}
            self.get_sales_return_configurations()
            self.validating_request_data()
            if self.errors:
                self.delete_cache()
                return {'errors': self.errors}

            self.fetch_reference_details()

            self.validating_reference_details()
            if self.errors:
                self.delete_cache()
                return {'errors': self.errors}

            self.prepare_data_and_create_sales_return_data()

            if self.errors:
                self.delete_cache()
                return {'errors': self.errors}
            
            self.sales_return_callback()

            auto_sales_return_grn.apply_async(args=[self.user.id, self.warehouse.id, self.return_ids, self.extra_params])
            # auto_sales_return_grn(self.user.id, self.warehouse.id, self.return_ids, self.extra_params)
            self.delete_cache()
            return {
                'errors': self.errors,
                'return_ids': self.return_ids
            }

        except Exception as e:
            self.delete_cache()
            log.debug(traceback.format_exc())
            log.info("Sales Return creation failed with error- %s" % str(e))
            return {'errors': {'': ['Sales Return creation Failed!']}}
    

    def add_cache(self):
        """
        adds cache for the salesreturn creation
        """
        
        self.cache_keys = []
        def _add_cache_entry(unique_id):
            """Helper method to add cache and handle errors."""
            if unique_id in self.cache_keys:
                return True  # Already cached, skip
            if not cache.add(unique_id, "True", timeout=CACHE_EXPIRE):
                self.errors.setdefault('', []).append(ERRORS['sales_return_in_progress'])
                return False
            self.cache_keys.append(unique_id)
            return True
    
    
        for sr_record in self.request_data:
            return_reference = sr_record.get('return_reference', '')
            if return_reference:
                if not _add_cache_entry(f"sales_return_creation_{self.warehouse.username}_{return_reference}"):
                    return

            document_type = sr_record.get('document_type', '')
            customer_reference = sr_record.get('customer_reference', '')
            items = sr_record.get('items', [])
            for item in items:
                sku_code = item.get('sku_code', '')
                order_reference = item.get('order_reference', '')
                invoice_number = item.get('reference_number', '')
                unique_id = f"sales_return_creation_{self.warehouse.username}_{document_type}_{customer_reference}_{order_reference}_{invoice_number}_{sku_code}"
                if not _add_cache_entry(unique_id):
                    return

    def delete_cache(self):
        """
        deletes cache for the salesreturn creation
        """
        for key in self.cache_keys:
            cache.delete(key)

    def get_sales_return_configurations(self):
        """
        Retrieves the sales return configurations for the current warehouse.
        """
        misc_dict = get_multiple_misc_value_split(['return_types'], self.warehouse.id)
        if misc_dict.get('return_types', []):
            misc_dict['return_types'].extend(['RTO', 'CIR'])
        else:
            misc_dict['return_types'] = ['RTO', 'CIR']

        configs = get_multiple_misc_values(['sku_serialisation', 'auto_grn_for_salesreturn', 'sales_return_check_in'], self.warehouse.id)
        
        self.sku_serialization = configs.get('sku_serialisation', 'false')
        self.auto_sr_grn = configs.get('auto_grn_for_salesreturn', 'false')
        self.sales_return_check_in = configs.get('sales_return_check_in', 'false')

        self.config_return_types = misc_dict.get('return_types', [])
        self.config_reasons = list(MiscDetailOptions.objects
            .filter(misc_detail__misc_type='sales_return_reasons', misc_detail__user=self.warehouse.id)
            .values_list('misc_value', flat=True)
        )

    def assign_error_message(self, indexes: list = [], error_messages: list = []):
        for index in indexes:
            self.errors.setdefault(index, []).extend(error_messages)

    def validating_request_data(self):
        '''
        1. Validating mandatory fields
        2. Get unique details
        '''
        self.invoice_references, self.order_references, self.sku_codes = [], [], []
        self.customer_references, self.return_references, self.combo_skus = [], [], []
        self.batch_references, self.batch_nos = [], []
        self.request_serial_numbers = []

        unique_values_mapping = {
            'reference_number': self.invoice_references,
            'order_reference': self.order_references,
            'sku_code': self.sku_codes
        }

        for index, record in enumerate(self.request_data):
            items = record.get('items', [])
            document_type = record.get('document_type', '')
            customer_reference = record.get('customer_reference', '')
            return_type = record.get('return_type', '')

            indexes = record.get('indexes', [''])

            if not items:
                self.assign_error_message(indexes, [ERRORS['items_missing']])
            if not document_type or document_type not in ['order_reference', 'invoice_number', 'no_document']:
                self.assign_error_message(indexes, [ERRORS['document_missing']])
            if document_type in ['no_document'] and not customer_reference:
                self.assign_error_message(indexes, [ERRORS['document_reference_missing']['no_document']])
            if return_type not in self.config_return_types:
                self.assign_error_message(indexes, [ERRORS['invalid_return_type']])

            return_reference = record.get('return_reference', '')
            if return_reference and return_reference not in self.return_references:
                self.return_references.append(return_reference)

            if customer_reference and customer_reference not in self.customer_references:
                self.customer_references.append(customer_reference)
            for item in items:
                item_index = [item.get('index', '')]
                serial_numbers = item.get('serial_numbers', {})
                accepted_serials = serial_numbers.get('accepted', [])
                rejected_serials = serial_numbers.get('rejected', [])
                self.request_serial_numbers.extend(accepted_serials)
                self.request_serial_numbers.extend(rejected_serials)
                for key in unique_values_mapping.keys():
                    if document_type == 'no_document' and key in ['reference_number', 'order_reference']:
                        continue
                    value = item.get(key, '')
                    value_list = unique_values_mapping.get(key)
                    if value not in [None, 'null', '']:
                        value_list.append(value)

                if document_type in ['no_document']:
                    self.get_batch_details_from_request_data(item)

                self.validate_mandatory_fields(item_index, record, document_type, item)

    def get_batch_details_from_request_data(self, item: dict):
        '''
        Get batch details from request data for validation
        '''
        batch_reference = item.get('batch_reference', '')
        if batch_reference and batch_reference not in self.batch_references:
            self.batch_references.append(batch_reference)

        batch_number = item.get('batch_no', '')
        if batch_number and batch_number not in self.batch_nos:
            self.batch_nos.append(batch_number)

    def validate_mandatory_fields(self, item_index, record, document_type, item):
        '''
        Validating Mandatory fields
        '''
        mandatory_fields_errors = []

        reason = item.get('reason', '')
        sku_code = item.get('sku_code', '')
        return_quantity = item.get('return_quantity', '')
        rejected_quantity = item.get('rejected_quantity', 0) or 0
        short_quantity = item.get('short_quantity', 0) or 0

        if not reason:mandatory_fields_errors.append(ERRORS['reason_missing'])
        if not self.internal_request and reason and reason not in self.config_reasons:
            mandatory_fields_errors.append(ERRORS['invalid_reason'])
        if not sku_code:mandatory_fields_errors.append(ERRORS['sku_missing'])
        if return_quantity in [None, '', 'null', 0]:
            mandatory_fields_errors.append(ERRORS['return_quantity_missing'])
        if return_quantity and rejected_quantity and rejected_quantity > return_quantity:
            mandatory_fields_errors.append(ERRORS['invalid_rejected_quantity'])

        if short_quantity:
            max_short_quantity = return_quantity - rejected_quantity
            if short_quantity > max_short_quantity:
                mandatory_fields_errors.append(ERRORS['invalid_short_quantity'])

        if document_type and document_type not in ['no_document']:
            document_value_key = DOCUMENT_TYPE_KEY_MAPPING.get(document_type, '')
            record['sales_return_reference_number'] = item.get(document_value_key, '')
            if not record['sales_return_reference_number']:
                mandatory_fields_errors.append(ERRORS['document_reference_missing'][document_type])

        if item.get('combo_details', {}):
            self.combo_skus.append(sku_code)

        if mandatory_fields_errors:
            self.assign_error_message(item_index, mandatory_fields_errors)

    def fetch_reference_details(self):
        '''
        Fetch existing details for validation
        '''
        if self.invoice_references or self.order_references:
            self.fetch_invoice_data()
            self.prepare_reference_serial_numbers()

        self.fetch_sales_return_data()
        self.fetch_sku_master()
        self.fetch_customer_master()
        self.fetch_combo_sku_members_details()
    
    def prepare_reference_serial_numbers(self):
        """
        prepares transaction id serial number mapping
        """
        
        if self.request_serial_numbers:
            filters = {"reference_type": "invoice", "serial_number__in": self.request_serial_numbers, 'status': 1}
            serial_number_transaction = SerialNumberTransactionMixin(self.user, self.warehouse, {'filters': filters, 'values': ['sku_code', 'serial_number', 'reference_number', 'transact_id']})
            serial_number_transaction.get_existing_sntd()
            available_serial_numbers = serial_number_transaction.sntd_objects_list
            for sntd in available_serial_numbers:
                self.sku_serial_transaction_map[sntd['transact_id']].append(sntd['serial_number'])


    def fetch_invoice_data(self):
        '''
        Fetch invoice details process
        '''
        self.prepare_filter_for_invoice_data()
        self.invoice_data_from_sos()

    def prepare_filter_for_invoice_data(self):
        '''
        Preparing invoice data filters
        '''
        self.invoice_data_base_filter = {
            "order__user": self.warehouse.id,
            "order__sku__sku_code__in": self.sku_codes,
            "order_status_flag__in": ["customer_invoices", "delivery_challans"],
        }

        self.invoice_data_filter_list= Q()
        if self.invoice_references:
            self.invoice_data_filter_list |= Q(invoice_reference__in = self.invoice_references)
        if self.order_references:
            self.invoice_data_filter_list |= Q(order__order_reference__in = self.order_references)

    def invoice_data_from_sos(self):
        '''
        Fetch invoice details
        '''
        self.sos_df = pd.DataFrame(SellerOrderSummary.objects.filter(self.invoice_data_filter_list, **self.invoice_data_base_filter).values(
            'id', 'invoice_reference', 'quantity', 'full_invoice_number', 'challan_number', order_reference=F('order__order_reference'), original_order_id=F('order__original_order_id'),
            sku_id=F('order__sku_id'), sku_code=F('order__sku__sku_code'), pick_sku_id=F('picklist__sku_id'), pick_sku_code=F('picklist__sku__sku_code'), order_type=F('order__order_type'),
            batch_no=F('picklist__stock__batch_detail__batch_no'), batch_reference=F('picklist__stock__batch_detail__batch_reference'), batch_detail_id=F('picklist__stock__batch_detail_id'),
            manufactured_date=F('picklist__stock__batch_detail__manufactured_date'), expiry_date=F('picklist__stock__batch_detail__expiry_date'), order_line_reference=F('order__line_reference'),
            mrp=F('order__mrp'), unit_price=F('order__unit_price'), cgst=F('order__customerordersummary__cgst_tax'), sgst=F('order__customerordersummary__sgst_tax'),
            igst=F('order__customerordersummary__igst_tax'), cess=F('order__customerordersummary__cess_tax'), utgst=F('order__customerordersummary__utgst_tax'), pack_uom_quantity = F('order__json_data__pack_uom_quantity'),
            pack_uom_id = F('order__json_data__pack_id'),
            consignee=F('order__customerordersummary__consignee'), customer_id=F('order__customer_id')))
        
        if self.sos_df.empty:
            default_columns = [
                'id', 'invoice_reference', 'quantity', 'full_invoice_number', 'challan_number',
                'order_reference', 'original_order_id', 'sku_id', 'sku_code', 'pick_sku_id',
                'pick_sku_code', 'order_type', 'batch_no', 'batch_reference', 'batch_detail_id',
                'manufactured_date', 'expiry_date', 'order_line_reference', 'mrp', 'unit_price',
                'cgst', 'sgst', 'igst', 'cess', 'utgst', 'pack_uom_quantity', 'pack_uom_id',
                'consignee', 'customer_id', 'sales_return_quantity'
            ]
            self.sos_df = pd.DataFrame(columns=default_columns)
        else:
            sos_ids = self.sos_df['id'].unique().tolist()
            if sos_ids:
                sales_return_quantity_objs = SalesReturnBatchLevel.objects.filter(
                    sales_return_sku__sales_return__warehouse=self.warehouse.id, sellerordersummary_id__in=sos_ids
                ).values('sellerordersummary_id').annotate(sales_return_quantity=Sum('original_return_quantity'))
            
            sales_return_quantity_dict = {
                row['sellerordersummary_id']: row['sales_return_quantity']
                for row in sales_return_quantity_objs
            }
            for index, sos in self.sos_df.iterrows():
                self.sos_df.loc[index, 'sales_return_quantity'] = sales_return_quantity_dict.get(sos['id'], 0)

        self.sos_df['sales_return_quantity'] = self.sos_df['sales_return_quantity'].fillna(0)
        self.sos_df.replace({np.nan: None}, inplace=True)
        for index, sos_record in self.sos_df.iterrows():
            self.sos_df.loc[index, 'available_quantity'] = self.sos_df.loc[index, 'validate_quantity'] = sos_record['quantity'] - sos_record['sales_return_quantity']

        self.formatting_sos_datetime_fields()

    def formatting_sos_datetime_fields(self):
        '''
        Converting Manufactured date and Expiry date datetime format to string format for validation
        Datetime Format should be '%Y-%m-%d'
        '''
        self.sos_df['manufactured_date'] = pd.to_datetime(self.sos_df['manufactured_date'])
        self.sos_df['expiry_date'] = pd.to_datetime(self.sos_df['expiry_date'])

        try:
            self.sos_df['manufactured_date_local'] = self.sos_df['manufactured_date'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)
        except Exception:
            self.sos_df['manufactured_date_local'] = self.sos_df['manufactured_date'].dt.tz_convert('UTC').dt.tz_convert(self.timezone)

        try:
            self.sos_df['expiry_date_local'] = self.sos_df['expiry_date'].dt.tz_localize('UTC').dt.tz_convert(self.timezone)
        except Exception:
            self.sos_df['expiry_date_local'] = self.sos_df['expiry_date'].dt.tz_convert('UTC').dt.tz_convert(self.timezone)

        self.sos_df['manufactured_date_local_str'] = self.sos_df['manufactured_date_local'].dt.strftime(SALES_RETURN_DATE_FORAMT)
        self.sos_df['expiry_date_local_str'] = self.sos_df['expiry_date_local'].dt.strftime(SALES_RETURN_DATE_FORAMT)

    def fetch_sales_return_data(self):
        '''
        Fetch sales return details
        '''
        self.duplicate_sales_return_references = list(SalesReturn.objects.filter(warehouse_id=self.warehouse.id, return_reference__in=self.return_references).values_list('return_reference', flat=True).distinct())

    def fetch_sku_master(self):
        '''
        Fetch sku master details
        '''
        self.sku_df = pd.DataFrame(SKUMaster.objects.filter(user=self.warehouse.id, sku_code__in=self.sku_codes).values())
        for index, sku_record in self.sku_df.iterrows():
            self.is_serialized_sku_map[sku_record['sku_code']] = sku_record['enable_serial_based']

    def fetch_customer_master(self):
        '''
        Fetch customer master details
        '''
        sos_customer_ids = []
        if hasattr(self, 'sos_df') and not self.sos_df.empty:
            sos_customer_ids = self.sos_df['customer_id'].unique().tolist()
        self.customer_df = pd.DataFrame(CustomerMaster.objects.filter(Q(customer_reference__in=self.customer_references) | Q(customer_id__in=sos_customer_ids), user=self.warehouse.id).values())

    def fetch_combo_sku_members_details(self):
        '''
        Needs to implement
        '''
        pass

    def validating_reference_details(self):
        '''
        Document Type:
        invoice_number/order reference: Validating invoice details
        no_document: Validating sku and batch details
        '''
        for sr_record in self.request_data:
            document_type = sr_record.get('document_type', '')
            customer_reference = sr_record.get('customer_reference', '')

            self.sr_errors = []
            indexes = sr_record.get('indexes', [''])

            self.is_valid_return_reference(sr_record)

            if self.customer_df.empty:
                self.sr_errors.append(ERRORS['invalid_customer'])
            elif customer_reference:
                self.is_valid_customer(sr_record)

            sr_record['return_date'] = self.get_return_date(sr_record)
            if self.sr_errors:
                self.assign_error_message(indexes, self.sr_errors)
                return

            items = sr_record.get('items', [])
            for sr_item_data in items:
                self.sr_item_list = []
                item_index = [sr_item_data.get('index', '')]

                if document_type in ['no_document']:
                    self.validate_sku_batch_details(sr_item_data)
                else:
                    self.is_valid_reference_number(sr_item_data)
                    self.is_valid_order_reference(sr_item_data)
                    self.validate_return_item_and_quantity(sr_record, sr_item_data)
                
                # in case of serial numbers
                self.validate_serial_quantity_and_duplicates(sr_item_data)

                if self.sr_item_list:
                    self.assign_error_message(item_index, self.sr_item_list)
    
    def validate_serial_quantity_and_duplicates(self, sr_item_data: dict):
        """
        checks if the serial numbers count and duplicates
        """
        serial_numbers = sr_item_data.get('serial_numbers', {}) or {}

        accepted = serial_numbers.get('accepted', [])
        rejected = serial_numbers.get('rejected', [])
        return_qty = sr_item_data.get('return_quantity', 0)
        rejected_qty = sr_item_data.get('rejected_quantity', 0)
        if self.is_serialized_sku_map.get(sr_item_data.get('sku_code')) and self.auto_sr_grn == 'true' and self.sku_serialization == 'true':
            if len(accepted) + len(rejected) != return_qty:
                self.sr_item_list.append(ERRORS['serial_number_is_mandatory']%sr_item_data.get('sku_code', ''))
        if not accepted and not rejected:
            return
        if len(accepted) + len(rejected) != return_qty:
            self.sr_item_list.append(ERRORS['serial_number_count_mismatch']%sr_item_data.get('sku_code', ''))
        
        elif rejected_qty and len(rejected) != rejected_qty:
            self.sr_item_list.append(ERRORS['serial_number_count_mismatch']%sr_item_data.get('sku_code', ''))
        
        elif return_qty - rejected_qty != len(accepted):
            self.sr_item_list.append(ERRORS['serial_number_count_mismatch']%sr_item_data.get('sku_code', ''))
        
        elif len(set(accepted)) + len(set(rejected)) != len(accepted) + len(rejected):
            self.sr_item_list.append(ERRORS['duplicate_serial']%sr_item_data.get('sku_code', ''))
        
        if sr_item_data.get('sku_code') not in self.sku_serial_map:
            self.sku_serial_map[sr_item_data.get('sku_code')] = []


    def validate_sku_batch_details(self, sr_item_data: dict):
        '''
        Validating sku and batch detail for no document
        '''
        sku_code = sr_item_data.get('sku_code', '')
        if not sku_code:
            self.sr_item_list.append(ERRORS['invalid_sku_code']%'')
            return
        
        if not hasattr(self, 'sku_df') or self.sku_df is None or self.sku_df.empty:
            self.sr_item_list.append(ERRORS['invalid_sku_code']%sku_code)
            return

        sku_details_df = self.sku_df[self.sku_df['sku_code'] == sku_code]
        if sku_details_df.empty:
            self.sr_item_list.append(ERRORS['invalid_sku_code']%sku_code)
            return

        sku_data = sku_details_df.to_dict(orient='records')[0]

        sr_item_data['sku_id'] = sku_data['id']
        sr_item_data['sku_product_type'] = sku_data.get('product_type', '')
        if sku_data['batch_based'] == 1:
            self.validate_no_document_batch_details(sr_item_data)

    def validate_no_document_batch_details(self, sr_item_data: dict):
        '''
        Validating batch details for no_documents
        '''
        batch_reference = sr_item_data.get('batch_reference', '')
        batch_no = sr_item_data.get('batch_no', '')
        sku_code = sr_item_data.get('sku_code', '')
        if not(batch_reference or batch_no):
            self.sr_item_list.append(ERRORS['batch_mandatory']%sku_code)
            return

        self.prepare_data_for_batch_detail_get_or_creation(sr_item_data)

    def is_valid_return_reference(self, sr_record: dict):
        '''
        Check given return reference valid or not
        '''
        return_reference = sr_record.get('return_reference')
        if return_reference and return_reference in self.duplicate_sales_return_references:
            self.sr_errors.append(ERRORS['duplicate_return_reference'])

    def is_valid_customer(self, sr_record: dict):
        '''
        1. Check valid customer or not
        2. Get customer master internal id
        3. Get tax type
        
        '''
        customer_reference = sr_record.get('customer_reference', '')
        customer_details = self.customer_df[self.customer_df['customer_reference'] == customer_reference]
        if customer_details.empty:
            self.sr_errors.append(ERRORS['invalid_customer'])
            return
    
        customer_detail_dict = customer_details.to_dict(orient='records')[0]
        sr_record['customer_id'] = customer_detail_dict.get('id', '')
        if customer_detail_dict['tax_type'] == 'inter_state':
            sr_record['tax_type'] = 1
        else:
            sr_record['tax_type'] = 0

    def is_valid_reference_number(self, sr_item_data: dict):
        '''
        Check valid invoice reference/delivery challan
        '''
        reference_number = sr_item_data .get('reference_number', '')
        if reference_number and self.sos_df[(self.sos_df['invoice_reference'] == reference_number)].empty:
            self.sr_item_list.append(ERRORS['invalid_reference_number']%reference_number)

    def is_valid_order_reference(self, sr_item_data: dict):
        '''
        check valid order reference or not
        '''
        order_reference = sr_item_data.get('order_reference', '')
        if order_reference and self.sos_df[(self.sos_df['order_reference'] == order_reference)].empty:
            self.sr_item_list.append(ERRORS['invalid_order_reference']%order_reference)

    def validate_return_item_and_quantity(self, sr_record:dict, sr_item_data: dict):
        '''
        Validating return quanity with invoice quantity
        '''
        return_quantity = sr_item_data.get('return_quantity', 0)
        df_conditions = self.get_dataframe_conditions(sr_item_data)
        sos_filter_df = self.sos_df[eval('&'.join(df_conditions))]

        if sos_filter_df.empty:
            self.sr_item_list.append(ERRORS['data_not_found'])
            return

        for index, sos_record in sos_filter_df.iterrows():
            if not sr_record.get('customer_reference', ''):
                customer_id = sos_record.get('customer_id', '')
                self.get_reference_customer_details(sr_record, customer_id)

            sos_id = sos_record.get('id')
            sr_item_data['sku_id'] = sos_record.get('sku_id')

            validate_quantity = sos_record.get('validate_quantity')
            assigned_qty = min(return_quantity, validate_quantity)

            self.sos_df.loc[self.sos_df['id'] == sos_id, 'validate_quantity'] -= assigned_qty
            return_quantity -= assigned_qty

            if return_quantity <= 0:
                break

        if return_quantity > 0:
            self.sr_item_list.append(ERRORS['excess_quantity'])

    def get_reference_customer_details(self, sr_record, customer_id):
        customer_details = self.customer_df[self.customer_df['customer_id'] == customer_id]

        customer_detail_dict = customer_details.to_dict(orient='records')[0]
        sr_record.update({
            "customer_id": customer_detail_dict.get('id', ''),
            "customer_reference": customer_detail_dict.get('customer_reference', ''),
        })

    def get_dataframe_conditions(self, sr_item_data: dict, data_frame_name: str = 'self.sos_df', fields: dict = SALES_RETURN_UNIQUE_FIELDS):
        '''
        Preparing dataframe filters
        '''
        DATA_FRAME_NAME = data_frame_name
        data_frame_filter = {}

        for field_name, mapped_name in fields.items():
            field_value = sr_item_data.get(field_name, '')
            if field_value:
                if field_name in ['batch_id', 'mrp', 'unit_price']:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mapped_name}']"] = f"== {field_value}"
                else:
                    data_frame_filter[f"{DATA_FRAME_NAME}['{mapped_name}']"] = f"== '{field_value}'"

        conditions = [f"({column} {condition})" % {'column': column, 'condition': condition} for column, condition in data_frame_filter.items()]
        return conditions

    def prepare_data_for_batch_detail_get_or_creation(self, sr_item_data: dict):
        '''
        Prepare data for create new batch details
        To be remove: once the bulk batch creation is done
        '''
        batch_details = {
            "transact_type": "sales_return",
            "batch_no": sr_item_data.get("batch_no", ""),
            "batch_reference": sr_item_data.get("batch_reference", ""),
            "mrp": sr_item_data.get("mrp", 0),
            "sku_id": sr_item_data.get("sku_id", 0),
            "weight": str(sr_item_data.get("weight", "")),
            "buy_price": sr_item_data.get("unit_price", 0),
            "manufactured_date": sr_item_data.get("manufactured_date", None) or None,
            "expiry_date": sr_item_data.get("expiry_date", None) or None,
        }

        self.convert_batch_details_datetime_format(batch_details, sr_item_data)
        error_list, batch_id = validate_batch_details(self.warehouse, batch_details)
        self.batch_mapping[(sr_item_data.get('sku_id'), sr_item_data.get('batch_no', ''), sr_item_data.get('batch_reference', ''))] = batch_details
        if error_list:
            for error in error_list:
                error = error + f" for Sku {sr_item_data.get('sku_code', '')} and Batch {sr_item_data.get('batch_no', '') or sr_item_data.get('batch_reference', '')}"
                self.sr_item_list.append(error)
            return
        elif batch_id:
            sr_item_data['batch_detail_id'] = batch_id
        else:
            self.new_batch_creation_list.append(batch_details)

    def convert_batch_details_datetime_format(self, batch_details, sr_item_data, keys = ['expiry_date', 'manufactured_date']):
        '''
        Convert datetime format
        To be remove: once the bulk batch creation is done
        '''
        for key in keys:
            if sr_item_data.get(key):
                try:
                    batch_details[key] = datetime.strptime( sr_item_data[key], SALES_RETURN_DATE_FORAMT)
                    if key == 'expiry_date':
                        batch_details[key] = batch_details[key].replace(hour=23, minute=59)
                    ist_timezone = pytz.timezone(self.timezone)
                    batch_details[key] = ist_timezone.localize(batch_details[key])
                except Exception:
                    self.sr_item_list.append(f"Invalid date format of '{key}'")

    def prepare_data_and_create_sales_return_data(self):
        '''
        Preparing data for SalesReturn, SalesReturnLineLevel and SalesReturnBatchLevel model
        '''
        self.return_ids, self.sales_return_data, self.sales_return_line_level_data, self.sales_return_batch_level_data, self.new_batch_id_mapping = [], [], {}, {}, {}
        self.account_id = self.warehouse.userprofile.id
        self.reference_number_serial_map = {}
        self.sr_batch_serial_numbers_map = {}

        # Create new batch details
        for new_batch_details in self.new_batch_creation_list:
            new_batch_key = (new_batch_details.get('sku_id'), new_batch_details.get('batch_no', ''), new_batch_details.get('batch_reference', ''))
            if new_batch_key in self.new_batch_id_mapping:
                continue
            error_list, batch_obj = get_or_create_batch(self.warehouse, new_batch_details, draft=True)
            if error_list:
                self.errors.setdefault('', []).extend(error_list)
                return
            else:
                self.new_batch_id_mapping[new_batch_key] = batch_obj.id

        for sr_record in self.request_data:
            return_id = self.prepare_data_for_sales_return_header_level_data(sr_record)
            document_type = sr_record.get('document_type', '')
            items = sr_record.get('items', [])
            for sr_item_data in items:
                if document_type in ['no_document']:
                    sku_level_unique_key = self.prepare_data_for_sales_return_item_level_data(return_id, sr_item_data, {})
                    self.prepare_data_for_no_document_sales_return_batch_level_data(sku_level_unique_key, sr_item_data)
                else:
                    self.prepare_data_for_sales_return_batch_level_data(return_id, sr_item_data)
            if document_type in ['no_document']:
                self.validate_and_prepare_no_document_serial_numbers()
        

        self.validate_serial_numbers()
        if self.errors:
            return

        if self.sales_return_data:
            self.create_sales_return_data()
    

    def validate_and_prepare_no_document_serial_numbers(self):
        """
        checks whether the given serial number for sku is available for sales return
        """

        serial_numbers, invalid_serials = [], []
        for sku, serials in self.sku_serial_map.items():
            serial_numbers.extend(serials)
        if not serial_numbers:
            return
        filters = {"serial_number__in": serial_numbers, "status": 0}
        values = ['sku_id', 'serial_number', 'sku__sku_code']
        sn_obj = SerialNumberMixin(self.user, self.warehouse, {'filters': filters, 'values': values})
        sn_obj.get_serial_numbers()
        valid_serials = sn_obj.final_dict['data']
        valid_serial_sku_map = defaultdict(list)
        for serial in valid_serials:
            valid_serial_sku_map[serial.get('sku__sku_code')].append(serial.get('serial_number'))
        
        not_present_sku_serial_map = {}
        errors = {}
        for sku, serials in self.sku_serial_map.items():
            valid_serials = valid_serial_sku_map.get(sku, [])
            if not set(serials).issubset(set(valid_serials)):
                invalid_serials = list(set(serials) - set(valid_serials))
                # try creating invalid serials if failed throw error
                not_present_sku_serial_map[sku] = invalid_serials
                errors.setdefault('', []).append(ERRORS['invalid_serial_number']%(invalid_serials, sku, ''))
        serial_number_payload = self.prepare_serial_number_payload(not_present_sku_serial_map)
        if not serial_number_payload.get('items'):
            return
        serial_number_mixin_objs = SerialNumberMixin(self.user, self.warehouse, serial_number_payload)
        self.final_response = serial_number_mixin_objs.create_serial_numbers()
        if self.final_response.get('errors'):
            response_str = ",".join(self.final_response.get('errors', []))
            invalid_serial_numbers = list(not_present_sku_serial_map.values())
            indexes = []
            flattened_list = list(itertools.chain.from_iterable(invalid_serial_numbers))
            serial_number_index_map = self.extra_params.get('serial_number_index_map', {})
            for serial_number in flattened_list:
                if serial_number in response_str:
                    indexes.append(serial_number_index_map.get(serial_number, ''))
            self.assign_error_message(indexes, ["Invalid Serial Number"])
            errors = self.final_response.get('errors', [])
            self.errors.setdefault('', []).extend(errors)


    def prepare_serial_number_payload(self, not_present_sku_serial_map):
        """
        return serial number creation payload with invactive status
        """
        items = []
        for sku, serials in not_present_sku_serial_map.items():
            sku_id = self.sku_df[self.sku_df['sku_code'] == sku].iloc[0]['id']
            items.append({
                "sku_id": sku_id,
                "location_id": "",
                "batch_detail_id": "",
                "lpn_number": "",
                "stock_id": "",
                "status": 0,
                "json_data": {},
                "serial_numbers": serials
            })
        return {"items": items}

    def validate_serial_numbers(self):
        """
        validates the serial numbers available for the given reference numbers
        """
        if self.sku_serialization != 'true' or not self.reference_number_serial_map:
            return

        filters = {"reference_type": "invoice", "reference_number__in": list(self.reference_number_serial_map.keys()), 'status': 1}
        serial_number_transaction = SerialNumberTransactionMixin(self.user, self.warehouse, {'filters': filters, 'values': ['sku_code', 'serial_number', 'reference_number']})
        serial_number_transaction.get_existing_sntd()
        available_serial_numbers = serial_number_transaction.sntd_objects_list
        available_ref_serial_number_map = {}
        for serial_number in available_serial_numbers:
            available_ref_serial_number_map.setdefault(serial_number.get('reference_number'), {}).setdefault(serial_number.get('sku_code'), set()).add(serial_number.get('serial_number'))
        # validate serial numbers
        indexes = []
        for ref, sku_serials in self.reference_number_serial_map.items():
            for sku_code, serials in sku_serials.items():
                available_serials = available_ref_serial_number_map.get(ref, {}).get(sku_code, set())
                if not serials.issubset(available_serials):
                    invalid_serials = list(set(serials) - set(available_serials))
                    self.errors.setdefault('', []).append(ERRORS['invalid_serial_number']%(invalid_serials, sku_code, ref))
                    serial_number_index_map = self.extra_params.get('serial_number_index_map', {})
                    for serial_number in invalid_serials:
                        indexes.append(serial_number_index_map.get(serial_number, ''))
        self.assign_error_message(indexes, ["Invalid Serial Number"])


    def prepare_data_for_sales_return_header_level_data(self, sr_record: dict):
        '''
        Preparing data for SalesReturn model
        '''
        return_id_value, return_id_prefix, return_id, check_return_id_prefix, inc_status = get_user_prefix_incremental(self.warehouse, 'sales_return', "", create_default="SR")

        sales_return_status = 4 if self.sales_return_check_in == 'true' else 1
        self.sales_return_data.append(SalesReturn(**{
            "warehouse_id": self.warehouse.id,
            "account_id": self.account_id,
            "customer_id": sr_record.get('customer_id', ''),
            "return_id": return_id,
            "return_reference": sr_record.get('return_reference', ''),
            "document_type": sr_record.get('document_type', ''),
            "return_type": sr_record.get('return_type', ''),
            "return_date": sr_record.get('return_date', ''),
            "reference_number": sr_record.get('sales_return_reference_number', ''),
            "json_data": {
                "created_by": self.user.username,
                "source": sr_record.get('source', 'API'),
            },
            "status": sales_return_status
        }))
        return return_id

    def get_return_date(self, sr_record: dict):
        '''
        Fetch and formatting the return date
        '''
        return_date = sr_record.get('return_date', '')
        if return_date:
            try:
                return_date = datetime.strptime(return_date, SALES_RETURN_DATE_FORAMT)
            except Exception:
                return_date = ''
                self.sr_errors.append(ERRORS['invalid_return_date'])
        if not return_date:
            return_date = datetime.now()
        return return_date

    def prepare_data_for_sales_return_item_level_data(self, return_id: str, sr_item_data: dict, sos_record: dict):
        '''
        Preparing data for SalesReturnLineLevel
        '''
        sku_id = sos_record.get('sku_id', '') if sos_record.get('sku_id', '') else self.sku_df[self.sku_df['sku_code'] == sr_item_data.get('sku_code', '')].iloc[0]['id']
        reference_number = sos_record.get('invoice_reference', '')
        order_reference = sos_record.get('order_reference', '')
        line_reference = sr_item_data.get('line_reference', '')
        sku_level_unique_key = (return_id, sku_id, reference_number, order_reference, line_reference)

        if not self.sales_return_line_level_data.get(return_id):
            self.sales_return_line_level_data[return_id] = {}
        
        serial_numbers = sr_item_data.get('serial_numbers', {})
        accepted_serials = serial_numbers.get('accepted', [])
        rejected_serials = serial_numbers.get('rejected', [])
        self.sku_serial_map[sr_item_data.get('sku_code', '')].extend(accepted_serials)
        self.sku_serial_map[sr_item_data.get('sku_code', '')].extend(rejected_serials)

        if not self.sales_return_line_level_data[return_id].get(sku_level_unique_key):
            self.sales_return_line_level_data[return_id][sku_level_unique_key] = {
                "account_id": self.account_id,
                "sales_return_id": return_id,
                "sku_id": sku_id,
                "reference_number": reference_number,
                "order_reference": order_reference,
                "line_reference": sr_item_data.get("line_reference", ""),
                "json_data": {
                    "order_type": sos_record.get('order_type', ''),
                    "order_line_reference": sr_item_data.get('order_line_reference', ''),
                    "aux_data" : sr_item_data.get('aux_data',{})
                },
                "status": 1
            }

            if sos_record.get('pack_uom_quantity', ''):
                self.sales_return_line_level_data[return_id][sku_level_unique_key]['json_data']['pack_uom_quantity'] = sos_record.get('pack_uom_quantity', 0)
                self.sales_return_line_level_data[return_id][sku_level_unique_key]['json_data']['pack_uom_id'] = sos_record.get('pack_uom_id', 0)

        return sku_level_unique_key

    def prepare_data_for_sales_return_batch_level_data(self, return_id: str, sr_item_data: dict):
        '''
        Preparing data for SalesReturnBatchLevel
        '''
        return_quantity = sr_item_data.get('return_quantity', 0) or 0
        rejected_quantity = sr_item_data.get('rejected_quantity', 0) or 0
        short_quantity = sr_item_data.get('short_quantity', 0) or 0
        reason = sr_item_data.get('reason', '')

        df_conditions = self.get_dataframe_conditions(sr_item_data)
        sos_filter_df = self.sos_df[eval('&'.join(df_conditions))]

        for index, sos_record in sos_filter_df.iterrows():
            if return_quantity <= 0:
                continue
            sos_id = sos_record.get('id', '')
            request_serial_numbers = sr_item_data.get('serial_numbers', {})
            accepted = request_serial_numbers.get('accepted', [])
            rejected = request_serial_numbers.get('rejected', [])
            serials = accepted + rejected
            sr_serial_numbers = set()
            sr_accepted_serials = set()
            sr_rejected_serials = set()
            if serials:

                sos_serials = self.sku_serial_transaction_map.get(int(sos_id), [])
                sr_serial_numbers = set(sos_serials).intersection(set(serials))
                sr_accepted_serials = set(sos_serials).intersection(set(accepted))
                sr_rejected_serials = set(sos_serials).intersection(set(rejected))
                if not sr_serial_numbers:
                    continue
                available_quantity = len(sr_serial_numbers)
            else:
                available_quantity = sos_record.get('available_quantity', 0)
            assigned_qty = min(return_quantity, available_quantity)
            damaged_availble_qty = min(assigned_qty, rejected_quantity)
            item_short_qty = 0

            self.sos_df.loc[self.sos_df['id'] == sos_id, 'available_quantity'] -= assigned_qty
            return_quantity -= assigned_qty
            rejected_quantity -= damaged_availble_qty

            if short_quantity > 0:
                original_accept_qty = assigned_qty - damaged_availble_qty
                if original_accept_qty > 0:
                    item_short_qty = min(original_accept_qty, short_quantity)
                    short_quantity -= item_short_qty

            accepted_qty = assigned_qty - damaged_availble_qty - item_short_qty
            quantity = assigned_qty - item_short_qty

            sku_level_unique_key = self.prepare_data_for_sales_return_item_level_data(return_id, sr_item_data, sos_record)

            if not self.sales_return_batch_level_data.get(sku_level_unique_key):
                self.sales_return_batch_level_data[sku_level_unique_key] = {}
            
            reference_number = sos_record.get('invoice_reference', '')
            
            serial_numbers = {"accepted": list(sr_accepted_serials), "rejected": list(sr_rejected_serials)}
            if reference_number not in self.reference_number_serial_map:
                self.reference_number_serial_map[reference_number] = {}
            
            sku_code = sr_item_data.get('sku_code', '')
            if sku_code not in self.reference_number_serial_map[reference_number]:
                self.reference_number_serial_map[reference_number][sku_code] = set()
            if sr_serial_numbers:
                self.reference_number_serial_map[reference_number][sku_code].update(sr_serial_numbers)
            batch_detail_id = sos_record.get('batch_detail_id', '')
            sos_id = sos_record.get('id', '')
            batch_unique_key = (batch_detail_id, sos_id, reason)
            serial_unique_key = (sku_level_unique_key[0], sku_level_unique_key[1], sku_level_unique_key[2], sku_level_unique_key[3], sku_level_unique_key[4], batch_unique_key[0], batch_unique_key[1], batch_unique_key[2])
            if batch_unique_key not in self.sales_return_batch_level_data[sku_level_unique_key]:
                self.sr_batch_serial_numbers_map[serial_unique_key] = serial_numbers
                self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key] = {
                    "account_id": self.account_id,
                    "sellerordersummary_id": sos_record.get('id', ''),
                    "batch_detail_id": sos_record.get('batch_detail_id', ''),
                    "original_return_quantity": assigned_qty,
                    "quantity": quantity,
                    "rejected_quantity": damaged_availble_qty,
                    "cancelled_quantity": 0,
                    "accepted_quantity": accepted_qty,
                    "reason": reason,
                    "mrp": sos_record.get('mrp', 0),
                    "unit_price": sos_record.get('unit_price', 0),
                    "status": 1,
                    "json_data": {
                        "currency": sr_item_data.get('currency', 'INR'),
                        "cgst": sos_record.get("cgst", 0),
                        "sgst": sos_record.get("sgst", 0),
                        "igst": sos_record.get("igst", 0),
                        "cess": sos_record.get("cess", 0),
                        "consignee": sos_record.get("consignee", 0),
                        "total_tax": sos_record.get("cgst", 0) + sos_record.get("sgst", 0) + sos_record.get("igst", 0) + sos_record.get("cess", 0),
                        "return_id": return_id,
                    }
                }
            else:
                self.sr_batch_serial_numbers_map[serial_unique_key]['accepted'].extend(serial_numbers.get('accepted', []))
                self.sr_batch_serial_numbers_map[serial_unique_key]['rejected'].extend(serial_numbers.get('rejected', []))
                self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['original_return_quantity'] += assigned_qty
                self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['quantity'] += quantity
                self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['rejected_quantity'] += damaged_availble_qty
                self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['accepted_quantity'] += accepted_qty
                

    def prepare_data_for_no_document_sales_return_batch_level_data(self, sku_level_unique_key: tuple, sr_item_data: dict):
        if not self.sales_return_batch_level_data.get(sku_level_unique_key):
            self.sales_return_batch_level_data[sku_level_unique_key] = {}

        # Assigning batch_detail_id of new batch
        sku_id = sku_level_unique_key[1]
        return_id = sku_level_unique_key[0]
        if not sr_item_data.get('batch_detail_id', ''):
            batch_detail = self.batch_mapping.get((sku_id, sr_item_data.get('batch_no', ''), sr_item_data.get('batch_reference', '')), {})
            new_batch_key = (sku_id, batch_detail.get('batch_no', ''), batch_detail.get('batch_reference', ''))
            sr_item_data['batch_detail_id'] = self.new_batch_id_mapping.get(new_batch_key)

        return_quantity = sr_item_data.get('return_quantity', 0)
        short_quantity = sr_item_data.get('short_quantity', 0) or 0
        rejected_quantity = sr_item_data.get('rejected_quantity', 0) or 0
        accepted_quantity = return_quantity - rejected_quantity - short_quantity
        item_quantity = return_quantity - short_quantity

        json_data = {"currency": sr_item_data.get('currency', "INR")}

        tax_details = self.get_tax_details_from_request_data(sr_item_data)
        if not tax_details:
            tax_details = self.get_tax_details_for_no_document(sr_item_data)
        json_data.update(tax_details)
        serial_numbers = sr_item_data.get('serial_numbers', {})
        if not isinstance(serial_numbers, dict) or 'accepted' not in serial_numbers:
            serial_numbers = {"accepted": [], "rejected": []}

        batch_detail_id = sr_item_data.get('batch_detail_id', '')
        reason = sr_item_data.get('reason', '')
        batch_unique_key = (batch_detail_id, reason)
        serial_unique_key = (sku_level_unique_key[0], sku_level_unique_key[1], sku_level_unique_key[2], sku_level_unique_key[3], sku_level_unique_key[4], batch_unique_key[0], '', batch_unique_key[1])
        if batch_unique_key not in self.sales_return_batch_level_data[sku_level_unique_key]:
            self.sr_batch_serial_numbers_map[serial_unique_key] = serial_numbers
            self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key] = {
                "account_id": self.account_id,
                "batch_detail_id": sr_item_data.get('batch_detail_id', ''),
                "original_return_quantity": return_quantity,
                "quantity": item_quantity,
                "rejected_quantity": rejected_quantity,
                "cancelled_quantity": 0,
                "accepted_quantity": accepted_quantity,
                "reason": sr_item_data.get('reason', ''),
                "mrp": sr_item_data.get('mrp', 0),
                "unit_price": sr_item_data.get('unit_price', 0),
                "status": 1,
                "json_data": json_data
            }
        else:
            self.sr_batch_serial_numbers_map[serial_unique_key]['accepted'].extend(serial_numbers.get('accepted', []))
            self.sr_batch_serial_numbers_map[serial_unique_key]['rejected'].extend(serial_numbers.get('rejected', []))
            self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['original_return_quantity'] += return_quantity
            self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['quantity'] += item_quantity
            self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['rejected_quantity'] += rejected_quantity
            self.sales_return_batch_level_data[sku_level_unique_key][batch_unique_key]['accepted_quantity'] += accepted_quantity


    def get_tax_details_for_no_document(self, sr_item_data):
        '''
        Get tax details for no document from Tax Details
        '''
        tax_details = {}

        product_type = sr_item_data.get('product_type')
        if product_type:
            tax_details = {}

        return tax_details

    def get_tax_details_from_request_data(self, sr_item_data: dict = {}):
        '''
        Get tax details for no document from request data
        '''
        tax_detail_dict = {}

        tax_details = sr_item_data.get('taxes', {})
        if tax_details:
            cgst = tax_details.get('cgst', {}).get('percentage', 0) or 0
            sgst = tax_details.get('sgst', {}).get('percentage', 0) or 0
            igst = tax_details.get('igst', {}).get('percentage', 0) or 0
            cess = tax_details.get('cess', {}).get('percentage', 0) or 0

            tax_detail_dict = {
                "cgst": cgst,
                "sgst": sgst,
                "igst": igst,
                "cess": cess,
                "total_tax": cgst + sgst + igst + cess
            }

        return tax_detail_dict

    def create_sales_return_data(self):
        '''
        Create sales return details
        '''
        with transaction.atomic('default'):
            self.create_sales_return_header_level_data()
            self.create_sales_return_line_level_data()
            self.create_sales_return_batch_level_data()

    def create_sales_return_header_level_data(self):
        '''
        Creating SalesReturn details
        '''
        self.return_id_internal_id_details, self.internal_id_return_id_details = {}, {}
        sales_return_objects = SalesReturn.objects.bulk_create(self.sales_return_data, batch_size=self.batch_size)
        for sales_return_object in sales_return_objects:
            self.return_id_internal_id_details[sales_return_object.return_id] = sales_return_object.id
            self.internal_id_return_id_details[sales_return_object.id] = sales_return_object.return_id
            self.return_ids.append(sales_return_object.return_id)

    def create_sales_return_line_level_data(self):
        '''
        Creating SalesReturnLineLevel details
        '''
        line_level_objects, self.line_level_id_details = [], {}

        for return_id, line_level_details in self.sales_return_line_level_data.items():
            sales_return_id = self.return_id_internal_id_details.get(return_id, '')
            for line_level_dict in line_level_details.values():
                line_level_dict['sales_return_id'] = sales_return_id
                line_level_objects.append(SalesReturnLineLevel(**line_level_dict))

        if line_level_objects:
            line_level_objects_details = SalesReturnLineLevel.objects.bulk_create(line_level_objects, batch_size=self.batch_size)
            for line_level_object in line_level_objects_details:
                return_id = self.internal_id_return_id_details.get(line_level_object.sales_return_id, '')
                sku_level_unique_key = (return_id, line_level_object.sku_id, line_level_object.reference_number, line_level_object.order_reference, line_level_object.line_reference)
                self.line_level_id_details[sku_level_unique_key] = line_level_object.id

    def create_sales_return_batch_level_data(self):
        '''
        Creating SalesReturnBatchLevel details
        '''
        batch_level_objects = []
        unique_key_map = {}
        for sku_level_unique_key, batch_level_details in self.sales_return_batch_level_data.items():
            line_level_id = self.line_level_id_details.get(sku_level_unique_key, '')
            unique_key_map[line_level_id] = sku_level_unique_key
            for batch_unique_key, batch_level_dict in batch_level_details.items():
                batch_level_dict['sales_return_sku_id'] = line_level_id
                batch_level_objects.append(SalesReturnBatchLevel(**batch_level_dict))

        if batch_level_objects:
            sales_return_batches = SalesReturnBatchLevel.objects.bulk_create(batch_level_objects, batch_size=self.batch_size)
            # inactivate the old serial numbers and create new serial numbers
            inactivate_payload = []
            for reference, sku_serials in self.reference_number_serial_map.items():
                payload = {
                        "reference_number": reference,
                        "reference_type": "invoice",
                        "items": []
                    }
                for sku_code, serials in sku_serials.items():
                    item = {
                                "transact_type": "",
                                "serial_numbers": list(serials),
                                "sku_code": sku_code,
                                "batch_number": "",
                                "location": "",
                                "zone": "",
                                "status": 0,
                                "json_data": {},
                                "serial_status": 0
                            }
                    payload['items'].append(item)
                inactivate_payload.append(payload)

            for payload in inactivate_payload:
                serial_transaction_objs = SerialNumberTransactionMixin(self.user, self.warehouse, payload)
                serial_transaction_objs.create_update_sn_transaction()

            final_data = {}
            for sr_batch in sales_return_batches:
                sr_batch_id = sr_batch.id
                sku_level_unique_key = unique_key_map.get(sr_batch.sales_return_sku_id, ('', '', '', ''))
                sos_id = sr_batch.sellerordersummary_id or ''
                return_id = sku_level_unique_key[0]
                batch_unique_key = (sr_batch.batch_detail_id, sos_id, sr_batch.reason)
                serial_numbers = self.sr_batch_serial_numbers_map.get((sku_level_unique_key[0], sku_level_unique_key[1], sku_level_unique_key[2], sku_level_unique_key[3], sku_level_unique_key[4], batch_unique_key[0], batch_unique_key[1], batch_unique_key[2]), {})

                if not serial_numbers:
                    continue
                accepted_serials = serial_numbers.get('accepted', [])
                rejected_serials = serial_numbers.get('rejected', [])
                if not final_data.get(return_id):
                    final_data[return_id] = {
                        "reference_number": return_id,
                        "reference_type": "sales_return",
                        "items": [],
                        "extra_params": {
                            "sales_return_created_by": self.user.username
                        }
                    }
                batch_number = sr_batch.batch_detail.batch_no if sr_batch.batch_detail else ""
                accepted_items = self.get_serial_transaction_data(sr_batch_id, accepted_serials, batch_number, sr_batch.sales_return_sku.sku.sku_code)
                rejected_items = self.get_serial_transaction_data(sr_batch_id, rejected_serials, batch_number, sr_batch.sales_return_sku.sku.sku_code)
                if accepted_serials:
                    accepted_items['transact_type'] = "accepted"
                    final_data[return_id]['items'].append(accepted_items)
                if rejected_serials:
                    rejected_items['transact_type'] = "rejected"
                    final_data[return_id]['items'].append(rejected_items)
            
            for payload in final_data.values():
                serial_transaction_objs = SerialNumberTransactionMixin(self.user, self.warehouse, payload)
                serial_transaction_objs.create_update_sn_transaction()
    
    def get_serial_transaction_data(self, id, serials, batch_number, sku):
        '''
        Get serial transaction data
        '''
        return {
                "transact_id": id,
                "transact_type": "",
                "serial_numbers": list(serials),
                "sku_code": sku,
                "batch_number": batch_number,
                "location": "",
                "zone": "",
                "status": 1,
                "json_data": {},
                "serial_status": 4
            }
        
                

    def sales_return_callback(self):
        '''
        Callback for sales return
        '''
        try:
            for return_id in self.return_ids:
                filters = {
                    "return_id" : return_id,
                }
                webhook_integration_3p(self.warehouse.id, 'sales_return', filters)
        except:
            log.debug(traceback.format_exc())
