#package imports
from datetime import datetime
from copy import deepcopy
from collections import defaultdict
import pandas as pd
import traceback

#django imports
from django.db.utils import IntegrityError
from django.test.client import RequestFactory
from django.db import transaction
from django.utils import timezone

#core operations imports
from core.models import (
    SKUMaster, MiscDetailOptions, MiscDetail
)
from core_operations.views.common.main import (
    get_misc_value, update_batch_error_message,
    get_incremental, get_financial_year, save_sku_stats, update_jsondata,
    update_filled_capacity, get_multiple_misc_values, get_misc_options_list,
    get_sequence_data, prepare_prefix_details, get_local_date_known_timezone
)
from core_operations.views.integration.integration import webhook_integration_3p
from core_operations.views.services.packing_service import PackingService

#wms imports
from wms_base.wms_utils import BACKFLUSH_JO_TYPES, init_logger
from wms_base.models import User

#lms imports
from lms.models import TaskMaster

#inventory imports
from inventory.models import StockDetail
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#outbound imports
from outbound.models import (
    OrderDetail, Order, SellerOrderSummary, PaymentSummary, OrderCharges, CustomerOrderSummary,
    Picklist, NetworkMaster, OrderFields, PaymentInfo, PriceMaster, OrderTypeZoneMapping, OrderType,
    StagingInfo
)

#outbound imports
from outbound.views.picklist.validation import get_picklist_number
from outbound.views.picklist.picklist_task import prepare_task_data

from outbound.views.invoice.main import generate_invoice
from outbound.views.invoice.delivery_challan import generate_delivery_challan_func
from outbound.views.orders.drop_ship_orders import prep_st_po_data, create_st_po

from outbound.views.invoice.invoice_data import get_customer_invoice_tab_func
from outbound.views.invoice.create_invoice import CreateInvoiceMixin

from outbound.views.invoice.create_invoice import insert_invoice_data


from .drop_ship_orders import dropship_po_data_prep, st_so_po_data
from .constants import SKU_NAME_FIELDS_MAPPING


log = init_logger('logs/order_helpers.log')

direct_dispatch_const = 'Direct-Dispatch Orders'

def get_dest_network(dest_loc, warehouse):
    nw_obj_ = None
    network_type = get_misc_value('network_type', warehouse.id)
    if network_type == 'warehouse-partner':
        network_type_filter = 'wh-partner'
    else:
        network_type_filter = 'chief-partner'
    nw_obj = NetworkMaster.objects.filter(dest_location_code__username=dest_loc, status=1, network_type = network_type_filter)
    if nw_obj:
        if network_type == 'warehouse-partner':
            nw_obj_ = nw_obj
        else:
            nw_obj_ = NetworkMaster.objects.filter(dest_location_code_id=nw_obj[0].source_location_code_id, source_location_code__id=warehouse.id, status=1, network_type = 'wh-chief')
        if nw_obj_:
            nw_obj_ = nw_obj_[0]
    return nw_obj_

def check_create_payment_info(order_id, payment_data, warehouse, payment_info_dict, payment_summary_dict):
    payment_id = get_incremental(warehouse, "payment_summary", 1)
    payment_mode = payment_data['payment_info']['payment_mode']
    payment_date = payment_data['payment_info']['payment_date']
    transaction_id = payment_data['payment_info']['transaction_id']
    paid_amount = payment_data['payment_info']['paid_amount'] or 0
    method_of_payment = payment_data['payment_info']['method_of_payment']
    payment_dict = {'method_of_payment':method_of_payment, 'payment_date':payment_date,
                    'paid_amount':paid_amount, 'payment_mode':payment_mode,'transaction_id':transaction_id,
                    'aux_info':payment_data['payment_info']['aux_info'], 'account': warehouse.userprofile}

    payment_info_dict[order_id] = payment_dict
    payment_summary_dict[order_id] = {"payment_id": payment_id, "payment_info": "", "account": warehouse.userprofile}

def check_create_charges(order_id, charge_dict, warehouse):
    charges = OrderCharges(**charge_dict)
    return charges

def save_extra_field_options_fun(warehouse, field, options_list):
    options_string = ",".join(options_list)
    misc_obj = MiscDetail.objects.filter(user=warehouse.id,misc_type='extra_order_fields')
    if misc_obj.exists():
        misc_obj = misc_obj[0]
        misc_options = MiscDetailOptions.objects.filter(misc_detail= misc_obj,misc_key = field)
        if misc_options.exists():
            misc_options =  misc_options[0]
            misc_options.misc_value = options_string
            misc_options.save()
        else:
            MiscDetailOptions.objects.create(misc_detail= misc_obj,misc_key = field,misc_value = options_string, account=warehouse.userprofile)
        message = "Success"
        return message
    else:
        message = "Please Enter Extra Fields"
        return message

def create_extra_fields_for_order(created_order_id, extra_order_fields, warehouse, order_extra_fields_dict=None):
    try:
        order_field_objs = []
        options_dict = {}
        if order_extra_fields_dict is None:
            order_extra_fields_dict = {}
        misc_options =list(MiscDetailOptions.objects.filter(misc_detail__user=warehouse.id, misc_detail__misc_type='extra_order_fields').values('misc_key','misc_value'))
        for option in misc_options :
            options_dict[option.get('misc_key','')]  =  option.get('misc_value','').split(",")
        for key, value in extra_order_fields.items():
            if value:
                order_field_objs.append(OrderFields(**{'user': warehouse.id, 'original_order_id': created_order_id,
                                                       'name': key, 'value': str(value)[:255], 'account': warehouse.userprofile}))

                if key in list(options_dict.keys()):
                    if value not in options_dict.get(key,[]):
                        options_dict[key].append(value)
                        try:
                            save_extra_field_options_fun(warehouse,key,options_dict.get(key,[]))
                        except Exception as e:
                            import traceback
                            log.debug(traceback.format_exc())
                            log.info('Save Extra Field Options failed for %s and params are %s and error statement is %s' % (
                            str(warehouse.username), str(extra_order_fields), str(e)))

        if order_field_objs and created_order_id not in order_extra_fields_dict:
            OrderFields.objects.bulk_create_with_rounding(order_field_objs)
            order_extra_fields_dict[created_order_id] = True
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Create order extra fields failed for %s and params are %s and error statement is %s' % (
        str(warehouse.username), str(extra_order_fields), str(e)))

def manual_pick_orders_data(manual_pick_orders_dict,order_line_dict,order_reference_list,user):
    '''
    Function to Prepare data for manual pick orders
    '''
    try:
        direct_dispatch_dict,dispatch_objs_dict = {},{}
        for dispatch_key,dispatch_value in manual_pick_orders_dict.items():
            if dispatch_key in order_line_dict:
                direct_dispatch_dict.setdefault(order_line_dict[dispatch_key],dispatch_value)
        dispatch_order_ids = list(direct_dispatch_dict.keys())
        dispatch_objs = OrderDetail.objects.filter(id__in=dispatch_order_ids,user=user.id)
        for each_dispatch_obj in dispatch_objs:
            dispatch_objs_dict[each_dispatch_obj.id] = each_dispatch_obj

        if len(manual_pick_orders_dict.keys()):
            for dispatch_key,dispatch_value in direct_dispatch_dict.items():
                dispatch_value['order_instance'] = dispatch_objs_dict[dispatch_key]

        #manual pick batch and location
        manual_pick_orders(user,order_reference_list,manual_pick_orders_dict=manual_pick_orders_dict)

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Direct Dispatch Order Upload failed for %s and params are %s and error statement is %s' % (
        str(user.username), str(manual_pick_orders_dict), str(e)))

def manual_pick_orders(user,order_reference_list,manual_pick_orders_dict={}):
    '''
    Function to manually pick orders from specified location and batch
    '''
    log.info("Manual Pick Started %s and for references %s " % (datetime.now(),str(order_reference_list)))
    creation_date=datetime.now()
    sku_stocks = StockDetail.objects.select_related('sku', 'location'). \
        filter(sku__user=user.id, quantity__gt=0)
    picklist_number = ''
    order_ref_picklist = {}
    for order_ref in list(set(order_reference_list)):
        picklist_number = int(get_picklist_number(user)) + 1
        order_ref_picklist[order_ref] = picklist_number
    if not picklist_number:
        picklist_number = int(get_picklist_number(user)) + 1
    financial_year = get_financial_year(datetime.now())
    task_master_data = []
    dchallan = {
        'inv': [],
        'dc': []
    }
    sku_zones_dict={}
    auto_skus=set()
    misc_types = ['auto_generate_einvoice']
    misc_dict = get_multiple_misc_values(misc_types, user.id)
    auto_generate_einvoice = misc_dict.get('auto_generate_einvoice') in ['true', True]
    picklist_priority= get_misc_value('picklist_priority', user.id)
    for order_id, orders in manual_pick_orders_dict.items():
        dispatch = False
        order = orders['order_instance']
        stock_pick_qty = order.json_data.get('stock_details',{})
        for stock_id,pick_quantity in stock_pick_qty.items():
            stock_filter = {"id": stock_id}
            order_stocks = sku_stocks.filter(**stock_filter)
            for stock in order_stocks:
                picked_quantity = 0
                picked_quantity = pick_quantity
                #direct dispatch
                if orders.get('direct_dispatch') == True:
                    stock.quantity = float(stock.quantity) - picked_quantity
                    stock.save()

                #direct dispatch
                if orders.get('direct_dispatch') == True:
                    dispatch =True
                    reserved_qty = 0
                    pick_qty = picked_quantity
                    remarks = direct_dispatch_const
                    status = 'picked'
                    #delivery challan check
                    auto_skus.add(stock.sku.wms_code)
                    sku_zones_dict.setdefault(stock.location.zone_id,[])
                    sku_zones_dict[stock.location.zone_id].append(stock.sku.wms_code)

                    #delivery challan or invoice formation
                    dchallan, transact_type = prepare_dispatch_or_delivery_challan_data(orders, order, dchallan)
                else:
                    #manual pick orders
                    reserved_qty = picked_quantity
                    pick_qty  = 0
                    remarks = 'Auto-pick Orders'
                    status = 'open'

                picklist_json_data = {'picklist_original_quantity':picked_quantity}

                #picklist entry creation
                new_picklist = Picklist.objects.create(picklist_number=picklist_number, reserved_quantity=reserved_qty,
                                                        picked_quantity=pick_qty, sku_id= stock.sku_id,
                                                        user_id=user.id,location=stock.location,
                                                        remarks=remarks, status=status,
                                                        creation_date=creation_date,
                                                        order_id=order.id, stock_id=stock.id,json_data=picklist_json_data,account=user.userprofile)
                if not dispatch :
                    task_master_data = prepare_task_data(new_picklist, new_picklist.order, picklist_priority, task_master_data,picklist_number=picklist_number)
                elif dispatch:
                    #sellerordersummary create and update order
                    create_sellerordersummary_and_update_order(user, stock,new_picklist, order, pick_qty, transact_type, creation_date, financial_year)

    #task master creation
    create_task_master_data(user,picklist_number,task_master_data)

    #trigger webhook
    prepare_webhook_integration(user, sku_zones_dict, auto_skus)

    log.info("%s" % dchallan)
    if len(dchallan['inv']):
        generate_invoice(dchallan['inv'], user)
    if len(dchallan['dc']):
        generate_delivery_challan_func(dchallan['dc'], user)

    try:
        #saved invoiced data
        order_ids = [*dchallan['inv'],*dchallan['dc']]
        extra_params = {'auto_generate_einvoice': auto_generate_einvoice}
        invoice_references_list = list(SellerOrderSummary.objects.filter(order__order_id__in=order_ids, order__user=user.id, order_status_flag__in=['customer_invoices', 'delivery_challans']).values_list('invoice_reference', flat=True))
        insert_invoice_data.apply_async(args = [user.username, invoice_references_list, None, extra_params])
    except Exception as e:
        log.info(f"saved invoice data creation failed with error {e}")

    return 'Order Created and Picklist Generated Successfully'

def prepare_dispatch_or_delivery_challan_data(orders, order, dchallan):
    '''Prepare Dispatch or Delivery Challan Data'''
    if orders.get('delivery_challan') == True:
        transact_type = 'picklist'
        if order.order_id not in dchallan['dc']:
            dchallan['dc'].append(order.order_id)

    elif orders.get('delivery_challan') == False:
        transact_type = 'dc_picklist'
        if order.order_id not in dchallan['inv']:
            dchallan['inv'].append(order.order_id)

    return dchallan, transact_type

def create_sellerordersummary_and_update_order(user, stock,new_picklist, order, pick_qty, transact_type, creation_date, financial_year):
    '''Create SellerOrderSummary and Update Order'''
    sos_json_data = {
        'mrp': stock.batch_detail.mrp
    } if stock.batch_detail else {}

    save_sku_stats(user, stock.sku_id, new_picklist.id, transact_type, pick_qty, stock)

    #sellerordersummary entry creation for direct dispatch
    SellerOrderSummary.objects.create(picklist_id=new_picklist.id,
                                                quantity=pick_qty, financial_year=financial_year,
                                                order_id=order.id, creation_date=creation_date,json_data=sos_json_data,account=user.userprofile)
    order.status = 2
    if order.json_data:
        order.json_data.update({'direct_dispatch':True})
    else:
        order.json_data = {'direct_dispatch':True}
    order.save()

def create_task_master_data(user,picklist_number,task_master_data):
    '''Create TaskMaster Data'''
    if task_master_data:
        try:
            TaskMaster.objects.bulk_create(task_master_data)
            log.info('Tasks created successfully for  %s  and picklist is %s ' % (str(user.username),str(picklist_number)))
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('Task Master creation failed %s and picklist is %s and error statement is %s' % (str(user.username), str(picklist_number), str(e)))

def prepare_webhook_integration(user, sku_zones_dict, auto_skus):
    '''Prepare Webhook Integration'''
    if auto_skus:
        auto_skus=list(auto_skus)
        try:
            log_message = (("Direct Dispatch order Inventory Changes Webhook for user %s, params %s") % (str(user.username),str(auto_skus)))
            log.info(log_message)
            filters = {
                "sku_codes": auto_skus,
                "zones_data": sku_zones_dict
            }
            webhook_integration_3p(user.id, 'picklist_confirmation', filters)
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info(("Webhook failed for %s in Direct Dispatch order and params are %s and error statement is %s") % (str(user.username), str(auto_skus), str(e)))

def get_direct_dispatch_order_filter(data, order):
    '''
    Get direct dispatch order filters
    '''
    if data.get('stock_ids',{}):
        return {"id__in": data.get('stock_ids')}
    stock_filter = {"sku_id": order.sku_id, "quantity__gt": 0}
    if data.get("location"):
        stock_filter["location__location"]= data["location"]
    if data.get("batch_number"):
        stock_filter["batch_detail__batch_no"] = data.get("batch_number")
    if data.get("lpn_number"):
        stock_filter["lpn_number"] = data.get("lpn_number")
    return stock_filter

def get_invoice_dc_order_ids(orders, order, dchallan):
    '''
    Get invoice and dc order ids
    '''
    transact_type = 'picklist'
    if orders.get('delivery_challan', False) and order.order_id not in dchallan['dc']:
        dchallan['dc'].append(order.order_id)
        transact_type = 'dc_picklist'
    elif order.order_id not in dchallan['inv']:
        dchallan['inv'].append(order.order_id)
    return transact_type, dchallan

def check_and_update_duplicate_picklist(picklist_number, order_id, stock_id, picked_quantity):
    picklist_objects = Picklist.objects.filter(picklist_number=picklist_number, order_id=order_id, stock_id=stock_id, status='dispatched')
    if picklist_objects:
        pick_qty = picklist_objects[0].picked_quantity + picked_quantity
        picklist_objects[0].picked_quantity = pick_qty
        picklist_objects[0].save()
        SellerOrderSummary.objects.filter(picklist_id=picklist_objects[0].id, order_status_flag='processed_orders').update(quantity=pick_qty)
        return True
    return False

def update_direct_dispatch_stock(stock, needed_quantity):
    picked_quantity = 0
    if float(stock.quantity) <= needed_quantity:
        picked_quantity = stock.quantity
        stock.quantity = 0
    else:
        picked_quantity = needed_quantity
        stock.quantity = float(stock.quantity) - picked_quantity
    if stock.quantity < 0:
        stock.quantity = 0
    stock.save()
    return picked_quantity

def create_serial_transaction_details(serial_transaction_details, picklist_serial_nos, picklist_number, serial_numbers, order_data, unique_key):
    """
    Create serial transaction details for a given picklist number.

    Args:
        picklist_number (str): The picklist number.
        serial_numbers (list): List of serial numbers.
        order_data (dict): Data related to the order.
        unique_key (str): Unique key for the item.

    Returns:
        None
    """
    if not serial_transaction_details.get(picklist_number):
        serial_transaction_details[picklist_number] = {
            'reference_number': picklist_number,
            'reference_type': 'so_picking',
            'items': {}
        }

    if not picklist_serial_nos.get(unique_key):
        picklist_serial_nos[unique_key] = []

    if unique_key not in serial_transaction_details[picklist_number]['items']:
        serial_transaction_details[picklist_number]['items'][unique_key] = {
            'serial_numbers': [],
            'sku_code': order_data.get('sku_code', ''),
            'batch_number': order_data.get('batch_number', ''),
            'location': order_data.get('location', ''),
            'zone': order_data.get('zone', ''),
            'status': 1,
            'lpn_number': order_data.get('lpn_number', ''),
            'serial_status': 0
        }

    for serial_number in serial_numbers:
        if serial_number not in serial_transaction_details[picklist_number]['items'][unique_key]['serial_numbers']:
            serial_transaction_details[picklist_number]['items'][unique_key]['serial_numbers'].append(serial_number)
        if serial_number not in picklist_serial_nos[unique_key]:
            picklist_serial_nos[unique_key].append(serial_number)

    return serial_transaction_details

def serial_transaction_id_mapping(serial_transaction_details, picklist_number, unique_key, picklist_id):
    """
    Maps the picklist ID to the corresponding item in the serial transaction details.

    Args:
        picklist_number (int): The picklist number.
        unique_key (str): The unique key of the item.
        picklist_id (int): The ID of the picklist.

    Returns:
        None
    """
    if not serial_transaction_details.get(picklist_number):
        return

    if serial_transaction_details[picklist_number].get('items', {}).get(unique_key):
        serial_transaction_details[picklist_number]['items'][unique_key]['transact_id'] = picklist_id

def create_serial_transaction(user, warehouse, serial_transaction_details):
    """
    Creates a serial transaction based on the serial transaction details.

    Returns:
        None
    """
    sntd_request_data = []
    for _picklist_number, picklist_data in serial_transaction_details.items():
        picklist_data['items'] = list(picklist_data['items'].values())
        sntd_request_data.append(picklist_data)

    sntd_mixin_objs = SerialNumberTransactionMixin(user, warehouse, sntd_request_data, False)
    final_response = sntd_mixin_objs.create_update_sn_transaction()

def prepare_packing_data(packing_details, stock, packing_unique_key, data, picklist_number, pack_uom_qty, pick_type):
    """
    Prepares the packing data for a given stock and packing unique key. 
    """
    date_fields = ['manufactured_date', 'expiry_date']
    if not pick_type == 'pack_while_pick':
        return packing_details
    if not data.get('lpn_number', ''):
        return packing_details
    if packing_unique_key not in packing_details:
        packing_details[packing_unique_key] = {
            'sku_code': data.get('sku_code', ''),
            'sku_description': stock.sku.sku_desc,
            'mrp': stock.batch_detail.mrp if stock.batch_detail else 0,
            
            'json_data': {
                'packed_lpn': True,
                'pack_uom_quantity': pack_uom_qty
            },
            "packed_quantity": data.get('quantity', 0),
        }
        batch_details = {}
        if stock.batch_detail:
            batch_details = {
                "batch_number": stock.batch_detail.batch_no,
            }
            for field in date_fields:
                if getattr(stock.batch_detail, field):
                    batch_details[field] = getattr(stock.batch_detail, field).strftime('%Y-%m-%d')
            
        packing_details[packing_unique_key]['batch_details'] = batch_details
    else:
        packing_details[packing_unique_key]['packed_quantity'] += data.get('quantity', 0)
    return packing_details

def prepare_and_create_packing(warehouse, user, packing_header_data, packing_details, extra_params):
    """
    Prepares and creates packing data for a given warehouse and user.
    """
    try:
        packing_data = {}
        for key, value in packing_details.items():
            lpn_number = key[0]
            if lpn_number not in packing_data:
                packing_data[lpn_number] = {
                    "lpn_number": lpn_number,
                    "items": [value],
                    "weight": 0,
                    "uom": "",
                    "json_data": {
                        "packed_lpn": True
                    }
                }
            else:
                packing_data[lpn_number]['items'].append(value)
                
        request_dict = {
            "request_headers": {
                "Warehouse": warehouse.username,
                "Authorization": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_AUTHORIZATION', '') or '',
            },
            "request_meta": extra_params.get('json_data', {}).get('request_meta', {}),
            "request_scheme": extra_params.get('json_data', {}).get('request_scheme', '')
        }
        packing_header_data['packing_details'] = list(packing_data.values())
        packing_service_instance = PackingService(request_dict, user, warehouse)
        packing_details_response, packing_service_errors = packing_service_instance.create_packing(packing_header_data)
        log.info(f"Packing service response: {str(packing_details_response)}")
        log.info(f"Packing service errors: {str(packing_service_errors)}")
        
    except Exception as e:
        log.debug(traceback.format_exc())
        log.info(f"Failed to call packing service: {str(e)}")

def direct_dispatch_orders(warehouse, user , direct_dispatch_data, creation_date=datetime.now(), extra_params=None):
    """
    Creates direct dispatch orders based on the provided parameters.

    Args:
        warehouse: The warehouse object.
        user: The user object.
        dispatch_orders: The dispatch orders to be created.
        creation_date: The date of creation for the dispatch orders.
        extra_params: Additional parameters for the dispatch orders.

    Returns:
        None
    """
    if extra_params is None:
        extra_params = {}
    log.info(f"Direct Dispatch Order for {warehouse.username} with data {direct_dispatch_data} and extra params {extra_params}")
    invoice_extra_attributes = direct_dispatch_data.get('invoice_extra_attributes', {}) or {}
    dispatch_orders = direct_dispatch_data.get('dispatch_data', {}) or {}
    sku_stocks = StockDetail.objects.select_related('sku', 'location'). \
        filter(sku__user=warehouse.id, quantity__gt=0, status=1)
    picklist_number = ''
    packing_header_data = {
        'user': user.username,
        'warehouse': warehouse.username,
        'transaction_type': 'so_packing',
        'allow_multiple_transactions': True,
        'json_data': {
            'created_by': user.username
        },
        'packing_details': [],
        'status': 'closed'
    }
    packing_details = {}
    mod_locations = []
    dchallan = {
        'inv': [],
        'dc': []
    }
    order_type, order_picklist_map = '', {}
    new_picklist_objs, new_sos_objs, picklist_objects, serial_transaction_details, picklist_serial_nos, staging_stock_data = [], defaultdict(list), [], {}, {}, {}
    try:
        picked_time = get_local_date_known_timezone(warehouse.userprofile.timezone, timezone.now(), send_date=True).strftime('%Y-%m-%d %H:%M:%S')
        with transaction.atomic():
            for order_id, orders in dispatch_orders.items():
                order = orders['order_instance']
                order_type = order.order_type
                total_quantity = 0
                if not order_picklist_map.get(order.order_reference):
                    order_picklist_map[order.order_reference] = int(get_picklist_number(warehouse)) + 1
                picklist_number = order_picklist_map[order.order_reference]
                for data in orders.get('data', []):
                    stock_filter = get_direct_dispatch_order_filter(data, order)
                    order_stocks = sku_stocks.filter(**stock_filter)
                    lpn_number = data.get('lpn_number', '') or ''
                    order_json_data = order.json_data or {}
                    pack_uom_qty = order_json_data.get('pack_uom_quantity', 1) or 1
                    pick_type = data.get('pick_type', 'default') or 'default'
                    needed_quantity = float(data['pick_quantity'])
                    picklist_quantity = float(data['quantity'])
                    total_quantity += picklist_quantity
                    _transact_type, dchallan = get_invoice_dc_order_ids(orders, order, dchallan)
                    financial_year = get_financial_year(datetime.now())
                    for stock in order_stocks:
                        picked_quantity = update_direct_dispatch_stock(stock, needed_quantity)

                        mod_locations.append(stock.location.location)
                        is_duplicate = check_and_update_duplicate_picklist(picklist_number, order_id, stock.id, picked_quantity)
                        if is_duplicate:
                            needed_quantity -= picked_quantity
                            if needed_quantity <= 0:
                                break
                            continue
                        unique_key = (picklist_number, stock.id, order.id, 'OrderDetail')
                        packing_unique_key = (lpn_number, picklist_number, stock.id)
                        json_data = {
                            'generated_by' : user.username,
                            'picker' : user.username,
                            'confirmation_time' : picked_time,
                        }
                        new_picklist = Picklist(picklist_number=picklist_number,
                                                            reserved_quantity=0,
                                                            picked_quantity=picked_quantity,
                                                            sku_id= stock.sku_id,
                                                            user_id=warehouse.id,
                                                            remarks=direct_dispatch_const,
                                                            status='dispatched',
                                                            creation_date=creation_date,
                                                            order_id=order.id,
                                                            stock_id=stock.id,
                                                            reference_number = order.order_reference,
                                                            reference_id = order.id,
                                                            reference_model = 'OrderDetail',
                                                            picklist_quantity = picklist_quantity,
                                                            pick_type = pick_type,
                                                            json_data=json_data,
                                                            account=warehouse.userprofile)
                        new_picklist_objs.append(new_picklist)
                        create_serial_transaction_details(serial_transaction_details, picklist_serial_nos, picklist_number, data.get('serial_numbers', []), data, unique_key)
                        packing_details = prepare_packing_data(packing_details, stock, packing_unique_key, data, picklist_number, pack_uom_qty, pick_type)
                        json_data = {
                            'pack_uom_quantity': pack_uom_qty,
                        }
                        sos_obj = SellerOrderSummary(quantity=picked_quantity, financial_year=financial_year,
                                                    order_id=order.id, creation_date=creation_date, lpn_number=data.get('lpn_number', ''), account=warehouse.userprofile, json_data=json_data)
                        new_sos_objs[unique_key].append(sos_obj)
                        needed_quantity -= picked_quantity
                        if needed_quantity <= 0:
                            break

                order.quantity -= total_quantity
                if order.quantity == 0:
                    order.status = 2
                order.save()
                update_jsondata(order,{'direct_dispatch':True})
            picklist_objects_map = {}
            if new_picklist_objs:
                picklist_objects = Picklist.objects.bulk_create_with_rounding(new_picklist_objs, batch_size=500)
                picklist_ids = []
                
                for picklist_obj in picklist_objects:
                    unique_key = (picklist_obj.picklist_number, picklist_obj.stock_id, picklist_obj.reference_id, picklist_obj.reference_model)
                    picklist_objects_map[unique_key] = picklist_obj.id
                    picklist_ids.append(picklist_obj.id)
                staging_stock_data, staging_info_data = prepare_staging_data(warehouse, picklist_ids, picklist_serial_nos, order_type)

            sos_objects_to_create, invoice_creation_payload  = [], []
            for key, obj_data in new_sos_objs.items():
                picklist_id = picklist_objects_map.get(key)
                for obj in obj_data:
                    obj.picklist_id = picklist_id
                    sos_objects_to_create.append(obj)
            if sos_objects_to_create:
                sos_objects = SellerOrderSummary.objects.bulk_create_with_rounding(sos_objects_to_create, batch_size=500)

            for object in sos_objects:
                picklist_number = object.picklist.picklist_number
                packing_key = (object.lpn_number, picklist_number, object.picklist.stock_id)
                invoice_serial_numbers = []
                if serial_transaction_details:
                    stock_id = object.picklist.stock_id
                    reference_id = int(object.picklist.reference_id)
                    reference_model = object.picklist.reference_model
                    serial_key = (picklist_number, stock_id, reference_id, reference_model)
                    serial_transaction_id_mapping(serial_transaction_details, picklist_number, serial_key, object.id)
                    invoice_serial_numbers = picklist_serial_nos.get(serial_key, [])
                staging_stock_data[object.picklist_id].receipt_number = object.id
                
                payload = {
                    'transaction_id': object.id,
                    'quantity': object.quantity,
                    'lpn_number': object.lpn_number,
                    'pack_uom_quantity': object.json_data.get('pack_uom_quantity', 1) or 1,
                    'serial_numbers': invoice_serial_numbers
                }
                if invoice_extra_attributes:
                    payload['custom_attributes'] = invoice_extra_attributes
                
                if packing_details.get(packing_key, {}):
                    packing_details[packing_key]['transaction_id'] = object.id
                invoice_creation_payload.append(payload)
            
            StockDetail.objects.bulk_create_with_rounding(list(staging_stock_data.values()), batch_size=500)
            
            staging_info_list = []
            for staging_data in staging_info_data:
                staging_info_list.append(StagingInfo(**staging_data))
            StagingInfo.objects.bulk_create_with_rounding(staging_info_list)
            
            if serial_transaction_details:
                create_serial_transaction(user, warehouse, serial_transaction_details)
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Direct Dispatch Order Upload failed for %s and params are %s and error statement is %s' % (
        str(user.username), str(direct_dispatch_data), str(e)))
        return 'Failed to Direct Dispatch Orders'
        
    if packing_details:
        prepare_and_create_packing(warehouse, user, packing_header_data, packing_details, extra_params)

    invoice_request = RequestFactory
    invoice_request.user = warehouse
    invoice_request.headers = {
        "Warehouse": warehouse.username,
        "Authorization": extra_params.get('json_data', {}).get('request_meta', {}).get('HTTP_AUTHORIZATION', '') or '',
    }
    invoice_request.META = extra_params.get('json_data', {}).get('request_meta', {})
    invoice_request.FILES = {}

    #check for order status flag
    log.info('Invoice Creation Payload %s' % invoice_creation_payload)
    create_invoice_objs = CreateInvoiceMixin(invoice_request, invoice_creation_payload, user, warehouse, {})
    invoice_result = create_invoice_objs.generate_invoice()
    log.info('Invoice Creation Result %s' % invoice_result)

    if mod_locations:
        update_filled_capacity(list(set(mod_locations)), warehouse.id)

    return 'Order Created and Dispatched Successfully'

def prepare_staging_data(warehouse, picklist_ids, serial_numbers_data, order_type):
    """
    Prepare staging stock and Drop LPN data for the given picklist IDs.
    Args:
        warehouse (User): The warehouse user.
        picklist_ids (list): List of picklist IDs.
        serial_numbers_data (dict): Dictionary containing serial numbers data.
        order_type (str): The order type.

    Returns:
        tuple: A tuple containing staging stock data and staging info data.
    """
    from outbound.views.picklist.picklist_confirmation import get_zone_from_ordertype
    staging_stock_data, staging_info_data = {}, defaultdict(list)
    
    location_objs, _ = get_zone_from_ordertype(warehouse, order_type, 'PRE_INVOICE', [], {})
    location = location_objs[0] if location_objs else None
    if not location:
        raise Exception('Staging Location not found')
    pick_data = Picklist.objects.select_related('stock').filter(id__in=picklist_ids).\
        only('id', 'picklist_number', 'picked_quantity', 'reference_number', 'reference_id', 'reference_model', 'sku_id', 
             'stock__batch_detail_id', 'stock_id', 'stock__status', 'stock__lpn_number', 'account_id')
    
    for picklist_object in pick_data:
        serial_key = (picklist_object.picklist_number, picklist_object.stock_id, int(picklist_object.reference_id), picklist_object.reference_model)
        serial_numbers = serial_numbers_data.get(serial_key, [])
        
        stock_data = {
            'receipt_type': 'so_picking',
            'sku_id': picklist_object.sku_id,
            'batch_detail_id': picklist_object.stock.batch_detail_id,
            'quantity': picklist_object.picked_quantity,
            'original_quantity': picklist_object.picked_quantity,
            'grn_number': picklist_object.reference_number,
            'status': picklist_object.stock.status,
            'location_id': location.id,
            'receipt_date' : datetime.now(),
            'account_id' : picklist_object.account_id,
            'transact_number': picklist_object.picklist_number,
            'json_data': {
                'picklist_id': str(picklist_object.id),
                'serial_numbers' : list(serial_numbers)
            },
            'lpn_number': picklist_object.stock.lpn_number
        }
        staging_stock_data[picklist_object.id] = StockDetail(**stock_data)
        
        if picklist_object.stock.lpn_number:
            unique_key = (picklist_object.picklist_number, picklist_object.stock.lpn_number, picklist_object.reference_number)
            staging_info_data[unique_key] = {
                'picklist_number': picklist_object.picklist_number,
                'carton_no': picklist_object.stock.lpn_number,
                'order_reference': picklist_object.reference_number,
                'location_id': location.id,
                'account_id' : picklist_object.account_id,
                'user_id' : warehouse.id,
                'segregation': 'outbound_staging',
                'invoice_number': '',
            }
    return staging_stock_data, list(staging_info_data.values())

def create_asn(warehouse, request_user, order_references):
    """
    create asn for given stock transfer invoice number
    """
    request_user = User.objects.get(id=request_user)
    warehouse = User.objects.get(id=warehouse)
    request = RequestFactory
    request.user = request_user
    request.warehouse = warehouse
    invoice_refs = set()
    sos_details = SellerOrderSummary.objects.filter(order__user=warehouse.id, order__order_reference__in=order_references, order_status_flag__in=['customer_invoices', 'delivery_challans']).values('invoice_number', 'challan_number', 'financial_year', 'order__marketplace', 'invoice_reference', 'order__order_type', 'order_status_flag').distinct()
    for sos in sos_details:
        is_challan = False
        dc_check = False
        inv_ref = sos.get('invoice_reference')
        if sos.get('challan_number'):
            is_challan = True
            inv_ref = sos.get('challan_number')
        data_dict = {
            'seller_summary_id': ['%s::%s' % (sos.get('invoice_number'), sos.get('financial_year'))],
            'delivery_challan': is_challan,
            'Marketplace': sos.get('order__marketplace', ''),
            'source': 'APP'
        }
        if sos.get('order__order_type', '').lower() != 'stocktransfer':
            continue
        if sos.get('order_status_flag').lower() == 'delivery_challans':
            dc_check = True
        if inv_ref not in invoice_refs:
            errors_list, invoice_data = get_customer_invoice_tab_func(request, warehouse, data_dict)
            invoice_obj = CreateInvoiceMixin(request, {}, request_user, warehouse, {})
            invoice_obj.asn_creation(request, warehouse, inv_ref, invoice_data, serial_numbers={}, dc_check=dc_check)
            invoice_refs.add(inv_ref)


def get_update_order_obj(order, order_det_dict, original_order_id, line_reference, order_detail_dict):
    """
    Retrieve the order object for updating based on the provided parameters.

    Args:
        order (dict): The order dictionary.
        order_det_dict (dict): The order detail dictionary.
        original_order_id (int): The original order ID.
        line_reference (str): The line reference.
        order_detail_dict (dict): The dictionary containing order details.

    Returns:
        object: The order object for updating.
    """
    if not order.get('order_detail_obj', None):
        fetch_order_key = (original_order_id, order_det_dict['order_id'], order_det_dict['order_code'], order_det_dict['sku_id'], order_det_dict['mrp'], order_det_dict['unit_price'], order_det_dict['user'], line_reference)
        order_obj = order_detail_dict.get(fetch_order_key, None)
    else:
        order_obj = [order.get('order_detail_obj', None)]
    return order_obj


def webhook_call(warehouse, original_order_ids, unique_sku_codes_list, async_call):
    if original_order_ids and unique_sku_codes_list:
        filters = {
            "sku_codes": unique_sku_codes_list,
            "original_order_ids" : list(original_order_ids),
        }
        if not async_call:
            webhook_integration_3p(warehouse.id, 'order_creation', filters)


def update_order_dicts(orders, warehouse: User, payment_info='', charges=[], extra_order_fields={}, header_data={}, order_type='', async_call=False, user=User, request_data=None, sales_uom='false'):
    '''Function to prepare and create orders'''
    order_ids, original_order_ids = set(), []
    line_references, original_order_ids, unique_order_ids, order_codes, sku_ids, mrps, unit_prices, users, sku_codes = set(), set(), set(), set(), set(), set(), set(), set(), set()
    order_references_mapping, dispatch_orders, failed_status, user_dropship_orders, dropship_skus, user_st_orders = {}, {}, {}, {}, {}, {}
    for order_key, order in orders.items():
        if not order.get('order_details', {}):
            continue

        order_det_dict = order['order_details']
        line_references.add(order_det_dict.get('json_data', {}).get('line_reference', '') or '')
        original_order_ids.add(order_det_dict['original_order_id'])
        unique_order_ids.add(order_det_dict['order_id'])
        order_codes.add(order_det_dict['order_code'])
        sku_ids.add(order_det_dict['sku_id'])
        mrps.add(order_det_dict['mrp'])
        unit_prices.add(order_det_dict['unit_price'])
        users.add(order_det_dict['user'])
        sku_code = order_det_dict.get('sku_code', '')
        sku_codes.add(sku_code)

    order_detail_dict = prepare_unique_order_dict(line_references, original_order_ids, unique_order_ids, order_codes, sku_ids, mrps, unit_prices, users)
    new_orders = []
    customer_order_summaries = {}
    extra_order_fields = extra_order_fields or {}
    order_fields = []
    payment_info_dict = {}
    payment_summary_dict = {}
    charges_dict = {}

    #order classification dict framing for dropship orders
    order_classification_dict = get_order_type_classification(warehouse, [order_type])
    for key in ['quantity', 'open_quantity']:
        if not header_data.get(key):
            header_data[key] = 0

    #iterate orders dict and create order
    for order_key, order in orders.items():
        continue_loop = False
        if not order.get('order_details', {}):
            continue

        #initialize order detail creation dict
        customer_code = ''
        order_det_dict = order['order_details']
        order_type = order_det_dict.get('order_type','')
        order_reference_key = order_det_dict.get('order_reference','')
        original_order_id = order_det_dict.get('original_order_id', '')
        line_reference = order_det_dict.get('json_data', {}).get('line_reference', '') or ''
        sku_code = order_det_dict.get('sku_code', '')

        #prepare unique values
        original_order_ids, order_references_mapping = prepare_unique_order_values(order_reference_key, order_references_mapping, original_order_id, original_order_ids, order_type)

        #fetch existing order detail dict
        order_obj = get_update_order_obj(order, order_det_dict, original_order_id, line_reference, order_detail_dict)

        #check and update if order detail exists
        order_detail, customer_code, continue_loop, ord_obj =  create_or_update_order_detail(order, order_obj, order_det_dict, warehouse, original_order_ids, failed_status, customer_code, continue_loop= False)
        if continue_loop:
            continue
        if ord_obj:
            new_orders.append(ord_obj)

        #header table details
        header_data['quantity'] += ord_obj.original_quantity
        header_data['open_quantity'] += ord_obj.original_quantity

        #create customer order summary
        if order.get('order_summary_dict', {}) and not order_obj:
            cos_obj = CustomerOrderSummary(**order['order_summary_dict'])
            line_ref = order_det_dict.get('json_data', {}).get('line_reference', '') or ''
            customer_order_summaries[(original_order_id, sku_code, line_ref)] = cos_obj

        #create order fields
        if order.get('payment_status',''):
            order_fields.append(OrderFields(**{'user': warehouse.id, 'original_order_id': original_order_id,'name': 'payment_status', 'value':order['payment_status'], 'account':warehouse.userprofile}))
            # OrderFields.objects.create(**{'user': warehouse.id, 'original_order_id': original_order_id,'name': 'payment_status', 'value':order['payment_status'], 'account':warehouse.userprofile})

        #frame unique order_ids
        order_ids.add(original_order_id)

        #create payment and order charges
        create_payment_and_order_charges(warehouse, charges, payment_info, original_order_id, continue_loop, payment_info_dict, payment_summary_dict, charges_dict)


        #create direct dispatch orders
        dispatch_orders, jo_auto_pick_orders, jo_auto_pick_skus, jo_auto_pick_sku_codes = prepare_and_create_direct_dispatch_orders(order_det_dict, order_detail, dispatch_orders)

        #create dropship orders
        dropship_skus, user_dropship_orders = prepare_dropship_orders_data(warehouse, order, order_det_dict, user_dropship_orders, dropship_skus, customer_code, order_classification_dict)

        #prepare stocktransfer po data
        user_st_orders, st_po_dict = prepare_stocktransfer_purchase_order_data(warehouse, order_det_dict, user_st_orders, order, sales_uom, customer_code)

    order_ids = list(order_ids)
    order_ids_list = deepcopy(order_ids)
    if order_ids and len(order_ids) == 1:
        order_ids = order_ids[0]

    #create header level data
    try:
        if new_orders and header_data:
            Order.objects.create(**header_data)
    except IntegrityError as e:
        import traceback
        log.debug(traceback.format_exc())
        if 'ORDER_order_reference_status_warehouse_id' in str(e):
            return {'status': 0, 'message': ['Duplicate Order Details'] }
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Create order header data failed for %s and params are %s and error statement is %s' % (str(warehouse.username), str(header_data), str(e)))

    #create orderdetail and customer order summary
    if new_orders:
        OrderDetail.objects.bulk_create_with_rounding(new_orders, batch_size=500)
    order_objs = OrderDetail.objects.filter(original_order_id__in=original_order_ids, user=warehouse.id)
    orders_df = pd.DataFrame(list(order_objs.values('id','original_order_id', 'sku_code', 'line_reference', 'order_reference', 'order_type').order_by('id')))
    order_type_refs_dict = defaultdict(set)
    for order in orders_df.itertuples():
        order_type_refs_dict[order.order_type].add(order.order_reference)

    create_customer_order_summary_objs(customer_order_summaries, orders_df)

    for original_order_id in order_ids_list:
        for attribute_name, attribute_value in extra_order_fields.get((str(original_order_id),str(warehouse.id)), {}).items():
            order_fields.append(OrderFields(**{'user': warehouse.id, 'original_order_id': original_order_id,'name': attribute_name, 'value':attribute_value, 'account':warehouse.userprofile}))

    #create order fields
    if order_fields:
        OrderFields.objects.bulk_create_with_rounding(order_fields, batch_size=500)

    create_payment_obj(payment_info_dict, payment_summary_dict, orders_df)

    create_order_charges_obj(charges_dict)

    #create direct dispatch orders
    to_update_dispatch_orders = {}
    prepare_direct_dispatch_data(dispatch_orders, orders_df, to_update_dispatch_orders)


    if len(dispatch_orders.keys()):
        if not request_data:
            extra_params = request_data
        else:
            extra_params = {
                'json_data': {
                    'request_meta': request_data.META
                }
            }
        direct_dispatch_data = {
            'dispatch_data': to_update_dispatch_orders
        }
        message = direct_dispatch_orders(warehouse=warehouse, direct_dispatch_data=direct_dispatch_data, user=user, extra_params=extra_params)
        log.info("Dispatch Order MSG %s" % str(message))

    unique_sku_codes_list = list(sku_codes)
    webhook_call(warehouse, original_order_ids, unique_sku_codes_list, async_call)

    create_dropship_orders(warehouse, user_dropship_orders, dropship_skus)

    #create po for stocktransfer orders
    create_po_for_stocktransfer_orders(warehouse, user_st_orders)

    #allocate order stock
    allocate_order_stock(user, warehouse, order_type_refs_dict)

    status = {'status': 1, 'message': [{'order_id':order_ids, 'order_mapping':order_references_mapping,'status': 'Success'}] }
    return status

def prepare_direct_dispatch_data(dispatch_orders, orders_df, to_update_dispatch_orders):
    if dispatch_orders:
        for (order_id, sku_code, line_ref), value in dispatch_orders.items():
            order = orders_df.loc[(orders_df['original_order_id'] == order_id) & (orders_df['sku_code'] == sku_code) & (orders_df['line_reference'] == line_ref)]['id']
            # order['order_instance'] = order[0]
            to_update_dispatch_orders[order[0]] = value

def create_customer_order_summary_objs(customer_order_summaries, orders_df):
    cos_objs = []
    for (order_id, sku, line_ref), value in customer_order_summaries.items():
        orders = orders_df[(orders_df['original_order_id'] == order_id) & (orders_df['sku_code'] == sku) & (orders_df['line_reference'] == str(line_ref))]
        for order in orders['id']:
            #
            value.order_id = order
        cos_objs.append(value)
    CustomerOrderSummary.objects.bulk_create_with_rounding(cos_objs, batch_size=500)


def create_order_charges_obj(charges_dict):
    if charges_dict:
        order_charges = []
        for order_id, charges_list in charges_dict.items():
            order_charges.extend(charges_list)
        OrderCharges.objects.bulk_create_with_rounding(order_charges, batch_size=500)

def create_payment_obj(payment_info_dict, payment_summary_dict, orders_df):
    if payment_info_dict:
        for order_id, payment_info1 in payment_info_dict.items():
            payment_obj = PaymentInfo.objects.create(**payment_info1)
            payment_summary = payment_summary_dict.get(order_id)
            payment_summary['payment_info'] = payment_obj
            order_id = orders_df.loc[orders_df['original_order_id'] == order_id]['id'][0]
            payment_summary['order_id'] = order_id
            PaymentSummary.objects.create(**payment_summary)


def prepare_unique_order_dict(line_references, original_order_ids, order_ids, order_codes, sku_ids, mrps, unit_prices, users):
    '''Function to prepare unique order lists'''
    order_detail_dict = {}
    order_detail_obj = OrderDetail.objects.filter(original_order_id__in=list(original_order_ids),
                                                  order_id__in=list(order_ids),
                                                  order_code__in=list(order_codes),
                                                  sku_id__in=list(sku_ids),
                                                  mrp__in=list(mrps),
                                                  unit_price__in=list(unit_prices),
                                                  user__in=list(users),
                                                  line_reference__in=list(line_references))

    for order_detail in order_detail_obj:
        order_key = (order_detail.original_order_id,order_detail.order_id,order_detail.order_code,order_detail.sku_id,order_detail.mrp,order_detail.unit_price,order_detail.user,order_detail.line_reference)
        if order_key not in order_detail_dict:
            order_detail_dict[order_key] = order_detail

    return order_detail_dict

def prepare_unique_skumaster_dict(sku_ids):
    '''Function to prepare unique skumaster dict'''
    sku_master_dict = {}
    sku_master_obj = SKUMaster.objects.filter(id__in=sku_ids)
    for sku_master in sku_master_obj:
        sku_master_dict[sku_master.id] = sku_master

    return sku_master_dict

def prepare_unique_order_values(order_reference_key, order_references_mapping, original_order_id, original_order_ids, order_type):
    '''Function to prepare unique order values'''
    #order references mapping for response
    if str(order_reference_key) not in order_references_mapping:
        order_references_mapping[str(order_reference_key)] = str(original_order_id)

    #prepare unique original_order_ids
    if original_order_id not in original_order_ids:
        if order_type not in BACKFLUSH_JO_TYPES:
            original_order_ids.append(original_order_id)

    return original_order_ids, order_references_mapping

def create_or_update_order_detail(order, order_obj, order_det_dict, warehouse, original_order_ids, failed_status, customer_code, continue_loop = False):
    '''Function to create or update order detail'''
    order_detail = None
    if order_obj:
        order_obj = order_obj[0]
        order_obj.quantity = float(order_obj.quantity) + float(order_det_dict.get('quantity', 0))
        order_obj.original_quantity = float(order_obj.original_quantity) + float(order_det_dict.get('quantity', 0))
        order_obj.invoice_amount = float(order_obj.invoice_amount) + float(order_det_dict.get('invoice_amount', 0))
        order_obj.save()
        order_detail = order_obj

        return order_detail, customer_code, continue_loop, order_detail
    else:
        #update order detail
        if 'customer_reference' in order['order_details'].keys():
            order['order_details'].pop('customer_reference')
            customer_code = order['order_details'].pop('customer_code', '')
        try:
            order['order_details']['original_quantity'] = order['order_details']['quantity']
            ord_obj = OrderDetail(**order['order_details'])
            return order_detail, customer_code, continue_loop, ord_obj

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info(("Update order dicts failed for %s in order creation params are %s and error statement is %s") % (str(warehouse.username), str(original_order_ids), str(e)))
            error_message = 'Order ID Already Exists ' + str(order['order_details']['original_order_id'])
            update_batch_error_message(failed_status, 5024, error_message, order['order_details']['order_reference'], order_reference=order['order_details']['order_reference'])
            continue_loop = True
            return order_detail, customer_code, continue_loop, order_detail

def create_payment_and_order_charges(warehouse, charges, payment_info, original_order_id, continue_loop, payment_info_dict, payment_summary_dict, charges_dict):
    '''Function to create payment and order charges'''
    if payment_info and payment_info.get(original_order_id) and not payment_summary_dict.get(original_order_id):
        payment_data = payment_info[original_order_id]
        check_create_payment_info(original_order_id, payment_data, warehouse, payment_info_dict, payment_summary_dict)
    if charges:
        for charge in charges:
            if charge.get(original_order_id):
                charges_dict[original_order_id] = []
                charges_list = charge[original_order_id]['charge_info']
                for charge_dict in charges_list:
                    charge_dict['user'] = warehouse
                    charge_dict['account_id'] = warehouse.userprofile.id
                    charges_dict[original_order_id].append(check_create_charges(original_order_id, charge_dict, warehouse))
            else:
                continue_loop = True

    return continue_loop

def create_order_extra_fields(order_fields, original_order_id, warehouse, order_extra_fields_dict):
    '''Function to create extra fields for order'''
    if order_fields and order_fields.get((str(original_order_id),str(warehouse.id)),{}):
        exta_fields_dicts = order_fields.get((str(original_order_id),str(warehouse.id)),{})
        if exta_fields_dicts:
            try:
                create_extra_fields_for_order(original_order_id,exta_fields_dicts , warehouse, order_extra_fields_dict)
                del order_fields[(str(original_order_id),str(warehouse.id))]
            except Exception:
                pass

def prepare_and_create_direct_dispatch_orders(order_det_dict, order_detail, dispatch_orders):
    '''Function to prepare and create direct dispatch orders'''
    jo_auto_pick_orders, jo_auto_pick_skus, jo_auto_pick_sku_codes = [], [], []
    if order_det_dict.get('json_data',{}).get('direct_dispatching', '').lower() == 'true' and order_det_dict.get('order_type') not in ['Non Standard JO', 'Standard JO']:
        original_order_id = order_det_dict.get('original_order_id', '')
        sku_code = order_det_dict.get('sku_code', '')
        line_reference = order_det_dict.get('line_reference', '')
        dispatch_orders[(original_order_id, sku_code, line_reference)] = {
                'order_instance': order_detail,
                'data': [{'quantity': float(order_det_dict.get('quantity', 0)),
                          'location': order_det_dict.get('json_data',{}).get('location', ''),
                          'serials': ''}],
                'delivery_challan': True if order_det_dict.get('json_data',{}).get('delivery_challan', '').lower() == 'true' else False
                }

    return dispatch_orders, jo_auto_pick_orders, jo_auto_pick_skus, jo_auto_pick_sku_codes

def prepare_dropship_orders_data(warehouse, order, order_det_dict, user_dropship_orders, dropship_skus, customer_code, order_classification_dict):
    '''Function to prepare and create dropship orders'''
    drop_po_dict = {}
    order_reference = order_det_dict.get('order_reference','')
    sku_id = order_det_dict.get('sku_id','')
    sku_code = order_det_dict.get('sku_code', '')
    # Fetch order classification based on order type
    order_classification = order_classification_dict.get(order_det_dict.get('order_type', ''), '')

    if order_classification == 'DROPSHIP' and not order_det_dict.get('json_data',{}).get('drop_ship_po_reference', ''):
        try:
            if warehouse in user_dropship_orders:
                drop_po_dict = user_dropship_orders[warehouse]
                if str(order_reference) in dropship_skus[warehouse]:
                    dropship_skus[warehouse][str(order_reference)].append(sku_id)
                else:
                    dropship_skus[warehouse][str(order_reference)]=[sku_id]
            else:
                drop_po_dict = {}
                dropship_skus[warehouse] = {str(order_reference):[sku_id]}
            drop_po_key = (str(warehouse.username),str(order_det_dict.get('customer_id','')),customer_code,str(order_det_dict.get('order_reference','')))
            if drop_po_key in drop_po_dict:
                drop_po_item_data = st_so_po_data(order_det_dict,order.get('order_summary_dict',{}),drop_po_dict[drop_po_key],sku_code=sku_code,drop_ship=True)
            else:
                drop_po_item_data = st_so_po_data(order_det_dict,order.get('order_summary_dict',{}),{},sku_code=sku_code,drop_ship=True)
            drop_po_dict[drop_po_key] = drop_po_item_data
            user_dropship_orders[warehouse] = drop_po_dict
        except Exception:
            pass

    return dropship_skus, user_dropship_orders

def prepare_stocktransfer_purchase_order_data(warehouse, order_det_dict, user_st_orders, order, sales_uom, customer_code):
    '''Function to prepare stocktransfer purchase order data'''
    st_po_dict = {}
    sku_code = order_det_dict.get('sku_code', '')
    if order_det_dict.get('order_type','').lower() == 'stocktransfer' and order_det_dict.get('json_data', {}).get('create_st_po', False):
        try:
            st_po_dict = user_st_orders.setdefault(warehouse, {})
            st_po_key = (str(warehouse.username),str(order_det_dict.get('customer_id','')),customer_code,str(order_det_dict.get('order_reference','')))
            st_po_data = st_po_dict.setdefault(st_po_key, {})
            if not st_po_data:
                # PO header data preparation
                st_po_dict = prep_st_po_data(st_po_dict, warehouse, {},{})

            # PO Item data preparation
            st_po_item_data = st_so_po_data(order_det_dict, order.get('order_summary_dict',{}), st_po_dict[st_po_key], sku_code=sku_code, sales_uom=sales_uom)
            st_po_dict[st_po_key] = st_po_item_data
            user_st_orders[warehouse] = st_po_dict
        except Exception:
            pass

    return user_st_orders, st_po_dict

def create_dropship_orders(warehouse, user_dropship_orders, dropship_skus):
    '''Function to create dropship and jo auto pick orders'''
    if user_dropship_orders:
        try:
            dropship_po_data_prep(warehouse,user_dropship_orders,dropship_skus)
        except Exception:
            pass

def create_po_for_stocktransfer_orders(warehouse, user_st_orders):
    '''Function to create po for stocktransfer orders'''
    for each_user, st_po_dict in user_st_orders.items():
        for st_po_key, st_po_data in st_po_dict.items():
            try:
                po_response = create_st_po(st_po_data, each_user, st_po_key[2], dest_user_flag=True)
                log.info('stocktransfer po creation for %s and params are %s po responseis %s' % (
                    str(warehouse.username), str(st_po_data), str(po_response)))
            except Exception as e:
                import traceback
                log.debug('stocktransfer po exception %s' %traceback.format_exc())
                log.info('stocktransfer po creation failed %s and params are %s and error statement is %s' % (
                    str(warehouse.username), str(st_po_data), str(e)))

def allocate_order_stock(user, warehouse, order_type_refs_dict):
    try:
        stock_allocate = get_multiple_misc_values(['stock_allocate'], warehouse.id).get('stock_allocate', 'false')
        misc_options = get_misc_options_list(['auto_generate_picklist', 'auto_allocate_sale_order'], warehouse)
        auto_generate_picklist_types = misc_options.get('auto_generate_picklist', [])
        auto_allocate_sale_order_types = misc_options.get('auto_allocate_sale_order', [])
        if stock_allocate =='true':
            allocate_orders = list()
            for order_type, order_refs in order_type_refs_dict.items():
                if order_type in auto_generate_picklist_types or 'all' in auto_generate_picklist_types:
                    continue
                if order_type.lower() in auto_allocate_sale_order_types or 'all' in auto_allocate_sale_order_types:
                    allocate_orders.extend(order_refs)
            if not allocate_orders:
                return
            request_data = {
                'order_references' : allocate_orders,
                'allocation_type' : 'fifo'
            }
            from outbound.views.picklist.main import SOPicklistSet
            message, status = SOPicklistSet().prepare_and_generate_picklist_process(request_data, warehouse.id, user.id)
            log.info('Auto Allocate for orders %s, Message %s' % (allocate_orders, message))
    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info(("Webhook failed/shipsy for %s in order creation params are %s and error statement is %s") % (str(warehouse.username), str(list(order_type_refs_dict.values())), str(e)))

def update_order_excel(data_list, warehouse):
    for data in data_list:
        OrderDetail.objects.filter(order_reference=data.get('order_reference'), user=warehouse.id).update(network_work_id=data['network_work'].id)

def get_order_slot_response(data=''):
    response = ''
    try:
        if data:
            slot_data = data.split(' ')
            if len(slot_data) == 2 and slot_data[1]:
                slot_inner_data = slot_data[1].split(':')
                if int(slot_inner_data[0]) > 12:
                    response = "%s:%s %s"% (int(slot_inner_data[0]) - 12, slot_inner_data[1], 'PM')
                elif int(slot_inner_data[0]) < 12:
                    response = "%s:%s %s"% (slot_inner_data[0], slot_inner_data[1], 'AM')
                elif int(slot_inner_data[0]) == 12:
                    check_type = 'PM'
                    if slot_inner_data[1]:
                        if int(slot_inner_data[1]) > 0:
                            check_type = 'PM'
                    elif slot_inner_data[2]:
                        if int(slot_inner_data[1]) > 0:
                            check_type = 'PM'
                    response = "%s:%s %s"% (slot_inner_data[0], slot_inner_data[1], check_type)
        return response
    except Exception as e:
        return ''

def get_order_id(user_id, prefix='MN', prefix_data = {}, naming_series=False, is_pos=False):
    '''
    Get Order incremnet value
    '''
    order_ref, count = '', 0
    user = User.objects.get(id=user_id)
    if naming_series:
        count = get_incremental(user, 'so_prefix')
        prefix = prefix_data.get('prefix', 'MN')
        order_ref, _ = prepare_prefix_details(prefix_data, '', count, prefix)
    else:
        count = get_incremental(user, 'so')
        order_ref = prefix + str(count)
    return order_ref, count

def get_order_prefix(userId):
    user = User.objects.get(id=userId)
    naming_series_data = get_sequence_data(user, {'type_name':'so_prefix'})
    prefix, prefix_data = 'MN', {}
    naming_series = False
    if naming_series_data:
        prefix_data = naming_series_data[0]
        prefix = prefix_data.get('prefix', 'MN')
        naming_series = True
    else:
        data = get_misc_value('order_prefix', userId)
        if data != 'false' and data:
            prefix = data
        prefix_data = {'prefix': prefix}
    return prefix_data, naming_series, prefix

def save_order_type(warehouse, order_types, misc_type):
    try:
        if order_types:
            # Fetch existing order types
            existing_order_types = list(OrderType.objects.filter(warehouse=warehouse).values_list('order_type', flat=True).distinct())
            new_order_types = list(set(order_types) - set(existing_order_types))
            order_type_insert_objs = []
            for order_type in new_order_types:
                order_type_insert_objs.append(OrderType(warehouse=warehouse, order_type=order_type, account_id=warehouse.userprofile.id))
            # Bulk create new order types
            if order_type_insert_objs:
                OrderType.objects.bulk_create(order_type_insert_objs)

            order_types = ','.join(order_types)
            selected_obj = MiscDetail.objects.filter(user=warehouse.id,misc_type = misc_type)
            if selected_obj:
                if misc_type == 'order_type_list' :
                    if order_types in selected_obj[0].misc_value.split(','):
                        return 'success'
                    else:
                        order_types = selected_obj[0].misc_value+','+order_types
                selected_obj.update(misc_value=order_types)
            else:
                MiscDetail.objects.create(user=warehouse.id, misc_type = misc_type, misc_value=order_types, account=warehouse.userprofile)
        return 'success'
    except:
        return True

def get_order_sku_price(customer_master, data, user, taxes_data=[], order_type_mapping=None, order_price_type = ''):
    price_bands_list = []
    order_type_price_mapping = False
    currency_code = 'INR'
    sku_code = data['sku_code']
    customer_price_name = get_misc_value('calculate_customer_price', user.id)
    is_sellingprice = False
    price = data['cost_price']
    if customer_price_name == 'price':
        price = data['price']
        is_sellingprice = True
    discount = 0
    if customer_master:
        customer_obj = customer_master[0]
        if order_price_type:
            price_type = order_price_type
        else:
            price_type = customer_obj.price_type
        if order_type_mapping and not order_price_type:
            if order_type_mapping.price_type:
                price_master_objs = PriceMaster.objects.filter(price_id=order_type_mapping.price_type, sku__sku_code=sku_code, user=user.id)
                if price_master_objs.exists():
                    pricemaster = price_master_objs[0]
                    price = pricemaster.price
                    discount = pricemaster.discount
                    currency_code = pricemaster.currency_code
                    if not pricemaster.mrp_required:
                        data['mrp'] = price
                    order_type_price_mapping = True
                    if discount:
                        price = price  * float(1 - float(discount) / 100)
                    elif customer_obj.discount_percentage:
                        discount = customer_obj.discount_percentage
                        price = price  * float(1 - float(discount) / 100)

        if is_sellingprice and customer_obj.discount_percentage and not order_type_price_mapping:
            discount = customer_obj.discount_percentage
            sale_price_gst = data['mrp'] * float(1 - float(discount) / 100)
            if taxes_data:
                tax = taxes_data[0]
                tax_percentage = tax['cgst_tax'] + tax['sgst_tax'] + tax['igst_tax'] + tax['cess_tax']
                price = sale_price_gst / (1 + tax_percentage/100)
            else:
                price = sale_price_gst
        price_master_objs = PriceMaster.objects.filter(price_id=price_type, sku__sku_code=sku_code,
                                                       user=user.id).values()
        if price_master_objs:
            price_bands_list = []
            for i in price_master_objs:
                price_band_map = {'price': i['price'], 'discount': i['discount']}
                price_bands_list.append(price_band_map)
            pricemaster = price_master_objs[0]
            price = pricemaster['price']
            discount = pricemaster['discount']
            currency_code = pricemaster['currency_code']
            if not pricemaster['mrp_required']:
                data['mrp'] = price
            if discount:
                price = price  * float(1 - float(discount) / 100)
            elif customer_obj.discount_percentage:
                discount = customer_obj.discount_percentage
                price = price  * float(1 - float(discount) / 100)
        elif price_type:
            attr_mapping = deepcopy(SKU_NAME_FIELDS_MAPPING)
            for attr_key, attr_val in attr_mapping.items():
                attribute_val = data[attr_val] if data[attr_val] else ''
                price_master_objs = PriceMaster.objects.filter(user=user.id, price_id=price_type,
                                                                    attribute_type=attr_key,
                                                                    attribute_value=attribute_val).values()
                if price_master_objs.exists():
                    pricemaster = price_master_objs[0]
                    price = pricemaster['price']
                    discount = pricemaster['discount']
                    currency_code = pricemaster['currency_code']
                    if not pricemaster['mrp_required']:
                        data['mrp'] = price
                    if discount:
                        price = price  * float(1 - float(discount) / 100)
                    elif customer_obj.discount_percentage:
                        discount = customer_obj.discount_percentage
                        price = price  * float(1 - float(discount) / 100)
                    price_bands_list = [{'price': price, 'discount': discount}]
                    break

    if currency_code != 'INR':
        taxes_data = []

    return price, discount, currency_code, price_bands_list

def get_mode_of_transport(user):
    mode_of_transport = get_misc_value('mode_of_transport', user.id)
    if mode_of_transport:
        mode_of_transport = mode_of_transport.split(',')
    return mode_of_transport

def get_inco_terms(user):
    inco_term_str = get_misc_value('inco_terms', user.id)
    inco_terms = []
    if inco_term_str != "false":
        inco_terms = inco_term_str.split(',')
    return inco_terms

def get_terms_of_payment(user):
    terms_of_payment_str = get_misc_value('terms_of_payment', user.id)
    terms_of_payment = []
    if terms_of_payment_str != "false":
        terms_of_payment = terms_of_payment_str.split(',')
    return terms_of_payment

def has_decimal(number):
    return '.' in str(number) and str(number).split('.')[1].strip('0') != ''

def get_order_type_classification(user, order_types):
    """
    Get the order type classification for the given user and order types.

    Args:
        user (User): The user for whom to retrieve the order type classification.
        order_types (list): A list of order types.

    Returns:
        dict: A dictionary mapping order types to their corresponding order classification.
    """
    order_classification_dict = dict(OrderTypeZoneMapping.objects
        .filter(order_type__in=order_types, status=1, user=user)
        .values_list('order_type','order_classification')
        .distinct()
    )
    return order_classification_dict


def prepare_approval_data_for_so_creation(warehouse_id, order_reference):
    """
    Prepare approval data for SO creation.
    Args:
        order_reference (str): The order reference to retrieve data for.
        order_type (str): The order type.
        warehouse_id (int): The ID of the warehouse/user.
    Returns:
        list: List of dicts containing approval data for each line item.
    """
    if not order_reference:
        return []

    order_objs = OrderDetail.objects.filter(
        order_reference=order_reference,
        user=warehouse_id,
        status=1
    ).values(
        'id',
        'original_order_id',
        'sku__sku_code',
        'sku__sku_desc',
        'quantity',
        'unit_price',
        'order_reference'
    )

    return [
        {
            'transaction_id': order['id'],
            'item_code': order['sku__sku_code'],
            'item_description': order['sku__sku_desc'],
            'json_data': {"sale_order_number": order['original_order_id']},
            'unit_price': order['unit_price'] * order['quantity'],
            'order_reference': order['order_reference'],
        }
        for order in order_objs
    ]
