from collections import defaultdict
import pandas as pd
from datetime import datetime

from django.db.models import F, Sum
from django.db import transaction

from wms_base.models import User
from wms_base.wms_utils import init_logger

from inventory.models import StockDetail
from inbound.models import POLocation, ASNSummary, POHeader
from wms.celery import app as celery_app
from inbound.views.putaway.confirmation import create_putaway_task_for_multiple
from inbound.views.grn.create_grn import create_grn_lpn_transactions
from core_operations.views.common.validation import update_order_header_status
from inbound.views.purchase_order.validation import validate_po_updation
from inbound.views.purchase_order.update_po import update_po
from outbound.views.invoice.helpers import get_destination_user
from core_operations.views.common.main import get_misc_value

from lms.models import TaskMaster

from outbound.models import (
    OrderDetail, Picklist, SellerOrderSummary, SalesReturnLineLevel,
    ShipmentInvoice, ShipmentInvoiceItems, OrderShipment, OrderShipmentItems,
    PickAndPassStrategy, StockAllocation
)

log = init_logger('logs/delete_orders.log')


def update_sku_shipment_details(invoice_skus_dict, shipment_invoice_id_dict, shipment_invoice_records, shipment_invoice_items_id_dict, shipment_invoice_items_records, shipment_items_sku_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments):
    """
    this method is used to update the shipment details of sku
    """
    for invoice, skus in invoice_skus_dict.items():
        shipment_invoice_index = shipment_invoice_id_dict.get(invoice)
        shipment_invoice_id = shipment_invoice_records[shipment_invoice_index].id
        for (sku, line_reference) in skus:
            #updating shipmentinvoice, shipmentinvoice items
            if (shipment_invoice_id, sku, line_reference) in shipment_invoice_items_id_dict:
                index = shipment_invoice_items_id_dict[(shipment_invoice_id, sku, line_reference)]
                shipment_invoice_items_records[index].status = 7
                shipment_invoice_records[shipment_invoice_index].reserved_quantity -= shipment_invoice_items_records[index].reserved_quantity
                if shipment_invoice_records[shipment_invoice_index].reserved_quantity <= 0:
                    shipment_invoice_records[shipment_invoice_index].reserved_quantity = 0
                    shipment_invoice_records[shipment_invoice_index].status = 7
                shipment_invoice_items_records[index].reserved_quantity = 0
            #updating ordershipment, ordershipmentitems
            if (shipment_invoice_id, sku, line_reference) in shipment_items_sku_dict:
                index = shipment_items_sku_dict[(shipment_invoice_id, sku, line_reference)]

                if order_shipment_items_records[index].order_shipment.id in shipment_id_index_dict:
                    order_shipment_index = shipment_id_index_dict[order_shipment_items_records[index].order_shipment.id]
                    order_shipments[order_shipment_index].total_quantity -= order_shipment_items_records[index].quantity
                    if order_shipments[order_shipment_index].total_quantity <= 0:
                        order_shipments[order_shipment_index].status = 7

                order_shipment_items_records[index].quantity = 0


def update_packed_shipment_records(lpn_number, sku, line_reference, quantity, shipment_items_lpn_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments):
    if (lpn_number, sku, line_reference) in shipment_items_lpn_dict:
        index = shipment_items_lpn_dict[(lpn_number, sku, line_reference)]
        order_shipment_items_records[index].quantity -= quantity
        if order_shipment_items_records[index].order_shipment.id in shipment_id_index_dict:
            order_shipment_index = shipment_id_index_dict[order_shipment_items_records[index].order_shipment.id]
            order_shipments[order_shipment_index].total_quantity -= quantity
            if order_shipments[order_shipment_index].total_quantity <= 0:
                order_shipments[order_shipment_index].status = 7

def update_packed_shipment_details(lpn_sku_dict, shipment_invoice_id_dict, lpn_shipment_invoice_item_dict, shipment_invoice_items_records, shipment_invoice_records, shipment_items_lpn_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments):
    for invoice, skus in lpn_sku_dict.items():
        shipment_invoice_index = shipment_invoice_id_dict.get(invoice)
        for (lpn_number, sku, line_reference, quantity) in skus:
            if lpn_number in lpn_shipment_invoice_item_dict:
                index = lpn_shipment_invoice_item_dict[lpn_number]

                for item in shipment_invoice_items_records[index].json_data.get('items', []):
                    if item.get('sku_code') == sku and item.get('line_reference') == line_reference:
                        item['quantity'] -= quantity
                        shipment_invoice_records[shipment_invoice_index].reserved_quantity -= quantity
                        if shipment_invoice_records[shipment_invoice_index].reserved_quantity <= 0:
                            shipment_invoice_records[shipment_invoice_index].reserved_quantity = 0
                            shipment_invoice_records[shipment_invoice_index].status = 7
                        shipment_invoice_items_records[index].reserved_quantity -= quantity
                        if shipment_invoice_items_records[index].reserved_quantity <= 0:
                            shipment_invoice_items_records[index].reserved_quantity = 0
                            shipment_invoice_items_records[index].status = 7
            update_packed_shipment_records(lpn_number, sku, line_reference, quantity, shipment_items_lpn_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments)
            
@celery_app.task
def delete_shipment(warehouse_id, invoice_skus_dict: dict, lpn_sku_dict: dict):
    '''
    Change status entries in shipmentInvoice and shipmentInvoiceItems  and ordershipment records

     Args:
            warehouse_id: warehouse id.
            invoice_skus_dict: dict with invoice as key and a list of skus for that invoice to delete.
            lpn_sku_dict: dict with invoice as key and lpn, sku as a list for that invoice
        Returns:
            None
    '''
    sku_invoices = list(invoice_skus_dict.keys())
    lpn_invoices = list(lpn_sku_dict.keys())
    order_invoices = sku_invoices + lpn_invoices
    filter_data = {'user_id': warehouse_id, 'reference_number__in': order_invoices}
    shipment_invoice_records = list(ShipmentInvoice.objects.filter(**filter_data))
    shipment_invoice_id_dict = {}
    for index, shipment_invoice in enumerate(shipment_invoice_records):
        shipment_invoice_id_dict[shipment_invoice.reference_number] = index
    
    shipment_invoice_items_records = list(ShipmentInvoiceItems.objects.filter(shipment_invoice__in=shipment_invoice_records).select_related('shipment_invoice', 'sku'))
    #frame shipment_invoice_id, sku record for easy access
    shipment_invoice_items_id_dict = {}
    lpn_shipment_invoice_item_dict = {}
    lpn_invoice_items_dict = {}
    for index, shipment_invoice_item in enumerate(shipment_invoice_items_records):
        if not shipment_invoice_item.package_reference:
            shipment_invoice_items_id_dict[(shipment_invoice_item.shipment_invoice.id, shipment_invoice_item.sku.sku_code, shipment_invoice_item.line_reference)] = index
        else:
            lpn_shipment_invoice_item_dict[shipment_invoice_item.package_reference] = index
            for item in shipment_invoice_item.json_data.get('items', []):
                lpn_invoice_items_dict[(shipment_invoice_item.id, item.get('sku_code', ''), item.get('line_reference', ''))] = index

    shipment_items_sku_dict = {}
    shipment_items_lpn_dict = {}
    shipment_id_index_dict = {}
    order_shipment_items_records = OrderShipmentItems.objects.filter(shipment_invoice__in=shipment_invoice_records)
    order_shipments = list(OrderShipment.objects.filter(id__in=order_shipment_items_records.values_list('order_shipment__id', flat=True)).annotate(total_quantity = Sum('ordershipmentitems__quantity')))
    order_shipment_items_records = list(order_shipment_items_records)
    for i, order_shipment_item in enumerate(order_shipment_items_records):
        if not order_shipment_item.package_reference:
            shipment_items_sku_dict[(order_shipment_item.shipment_invoice.id, order_shipment_item.sku.sku_code, order_shipment_item.line_reference)] = i
        elif order_shipment_item.package_reference:
            for item in order_shipment_item.json_data.get('items', []):
                shipment_items_lpn_dict[(order_shipment_item.package_reference, item.get('sku_code', ''), item.get('line_reference', ''))] = i

    for i, order_shipment in enumerate(order_shipments):
        shipment_id_index_dict[order_shipment.id] = i

    #update shipment invoice and shipment invoice items records, ordershipmentitems, ordershipment for normal skus
    update_sku_shipment_details(invoice_skus_dict, shipment_invoice_id_dict, shipment_invoice_records, shipment_invoice_items_id_dict, shipment_invoice_items_records, shipment_items_sku_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments)

    #update lpn skus
    update_packed_shipment_details(lpn_sku_dict, shipment_invoice_id_dict, lpn_shipment_invoice_item_dict, shipment_invoice_items_records, shipment_invoice_records, shipment_items_lpn_dict, order_shipment_items_records, shipment_id_index_dict, order_shipments)
    
    with transaction.atomic('default'):
        ShipmentInvoice.objects.bulk_update_with_rounding(shipment_invoice_records, ['reserved_quantity', 'status'])
        ShipmentInvoiceItems.objects.bulk_update_with_rounding(shipment_invoice_items_records, ['reserved_quantity', 'status'])
        OrderShipment.objects.bulk_update(order_shipments, ['status'])
        OrderShipmentItems.objects.bulk_update_with_rounding(order_shipment_items_records, ['quantity'])


class DeleteOrdersMixin:

    def __init__(self, warehouse: User, request_data: list = None, request = None):
        self.warehouse = warehouse
        self.request_data = request_data or []
        self.request = request
        self.st_po_payload = []
        self.dest_user = None

        self.batch_size = 500

    def delete_orders(self):
        """
        this methodi is used to delete orders
        """
        log.info(f'Deleting orders for warehouse {self.warehouse.id} with payload {str(self.request_data)}')
        try:
            self.errors, self.request_order_references, self.order_records_to_delete, self.order_detail_df, self.order_ref_items_dict = [], set(), pd.DataFrame(), pd.DataFrame(), {}
            self.pull_to_locate_records, self.order_invoices = [], defaultdict(list)
            self.orders_unique_keys, self.sos_df, self.picklist_df, self.stock_df = set(), None, None, None
            self.reason = ''
            self.lpn_sku_dict = defaultdict(list)
            self.order_records_to_update, self.sos_df, self.stock_df, self.allocation_df = pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
            
            self.suspend_cancel_orders = get_misc_value('suspend_cancel_orders', self.warehouse.id)
            self.suspend_cancel_orders = True if self.suspend_cancel_orders == 'true' else False
            self.replace = False

            self.get_request_unique_data_and_validate_data()
            if self.errors:
                return {'errors': self.errors}

            # self.get_order_references_and_order_records()
            self.get_db_order_details()
            if self.order_detail_df.empty:
                return {'errors': ['No orders found to delete.']}
            self.orders_deleted, self.cancelled_sku_codes = set(), set()
            self.validate_orders_data()
            if self.errors:
                return {'errors': self.errors}
            self.get_orders_to_delete()
            if self.order_records_to_delete.empty:
                return {'errors': ['No orders found to delete.']}
            self.get_allocation_records()
            self.prepare_and_update_order_details()
            self.prepare_and_update_picklist_details()
            if not self.suspend_cancel_orders:
                self.prepare_and_update_sos_details()
                self.prepare_and_update_picked_stock()
            
            if self.errors:
                return {'errors': self.errors}

            self.update_deleted_order_records()
            if self.errors:
                return {'errors': self.errors}
            # delete shipment records
            if self.order_invoices or self.lpn_sku_dict:
                log.info(f'Deleting shipment records for warehouse {self.warehouse.id} with invoices {str(self.order_invoices)}')
                delete_shipment.apply_async(args=[self.warehouse.id, dict(self.order_invoices), dict(self.lpn_sku_dict)])
            return {'orders_updated': self.orders_deleted, 'skus_updated' : self.cancelled_sku_codes}
        
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.error(f'Error in delete_orders: {e}')
            return {'errors': ['Order updation failed!.']}
        
    def get_request_unique_data_and_validate_data(self):
        """
        this method is used to get unique order references and validate the request data
        """
        for order in self.request_data:
            order_reference = order.get('order_reference', '')
            if not order_reference:
                self.errors.append('Order reference is mandatory.')
            if order_reference in self.request_order_references:
                self.errors.append(f'Duplicate order reference found {order_reference}.')
            self.reason = order.get('reason', '')
            self.request_order_references.add(order_reference)
            for item in order.get('items', []):
                sku = item.get('sku')
                line_reference = str(item.get('aux_data', {}).get('line_reference', ''))
                mrp = item.get('mrp', 0)
                sale_price = item.get('sale_price', 0)
                unique_key = (order_reference, sku, line_reference)
                if unique_key in self.orders_unique_keys:
                    self.errors.append(f'Duplicate items found for order {order_reference} with sku {sku} and line reference {line_reference} and mrp {mrp} and sale price {sale_price}.')
                self.orders_unique_keys.add(unique_key)


    def get_db_order_details(self):
        """
        get order details from db
        """
        order_records = OrderDetail.objects.select_related('sku', 'customer_identifier').filter(user=self.warehouse.id, order_reference__in=self.request_order_references)
            # .exclude(status=3)
        order_records_list = list(order_records)
        self.order_detail_df = pd.DataFrame(order_records.values())
        self.order_detail_df.fillna('', inplace=True)
        for obj in order_records_list:
            self.order_detail_df.loc[self.order_detail_df['id'] == obj.id, 'sku_code_'] = obj.sku.sku_code
            if obj.customer_identifier_id:
                self.order_detail_df.loc[self.order_detail_df['id'] == obj.id, 'customer_code'] = obj.customer_identifier.customer_code
            self.order_detail_df.loc[self.order_detail_df['id'] == obj.id, 'object'] = obj
        self.order_records_to_delete = pd.DataFrame(columns=self.order_detail_df.columns)

    def validate_orders_data(self):
        """
        validates the actual order details and request data order details for order deletion
        """
        #validate order data
        for order in self.request_data:
            order_reference = order.get('order_reference')
            order_records = self.order_detail_df[self.order_detail_df['order_reference'] == order_reference]
            # validate stock transfer order
            self.validate_st_order(order_reference, order_records, order)
            if order.get('replace', False) in [True, 'true', 'True']:
                self.replace = True
                continue
            if order_records.empty:
                self.errors.append(f'Order {order_reference} not found.')
            duplicate_items = set()
            for items in order.get('items', []):
                sku = items.get('sku')
                quantity = items.get('quantity')
                line_reference = str(items.get('aux_data', {}).get('line_reference', ''))
                reason = items.get('reason', '')
                remarks = items.get('remarks', '')
                unique_key = (order_reference, sku, line_reference)
                if unique_key in duplicate_items:
                    self.errors.append(f'Duplicate items found for order {order_reference} with sku {sku} and line reference {line_reference}.')
                duplicate_items.add(unique_key)
                order_record = order_records[(order_records['sku_code'] == sku) & (order_records['line_reference'] == line_reference)]
                if not order_record.empty:
                    self.order_detail_df.loc[order_record.index, 'cancel_quantity'] = items.get('quantity', 0) or 0
                    self.order_detail_df.loc[order_record.index, 'remarks'] = remarks
                    self.order_detail_df.loc[order_record.index, 'reason'] = reason
                else:
                    self.errors.append(f'Order {order_reference} does not have item {sku}. with line reference {items.get("line_reference")} with mrp {items.get("mrp")} and sale price {items.get("sale_price")}.')
                    continue
                if int(order_record['status'].values[0]) == 3 and order_record['quantity'].values[0] == 0:
                    self.errors.append(f'Order {order_reference} with item {sku} is already cancelled.')
                elif quantity > order_record['original_quantity'].sum() - order_record['cancelled_quantity'].sum() - order_record['suspended_quantity'].sum():
                    self.errors.append(f'Order {order_reference} with item {sku} has insufficient quantity to cancel. Available quantity is {order_record["original_quantity"].sum() - order_record["cancelled_quantity"].sum() - order_record['suspended_quantity'].sum()}.')
                elif int(order_record['status'].values[0]) == 5 and quantity != order_record['original_quantity'].sum() - order_record['cancelled_quantity'].sum():
                    self.errors.append(f'Partial cancellation is not allowed for order {order_reference} with item {sku}.')
                elif self.suspend_cancel_orders and int(order_record['status'].values[0]) == 5:
                    self.errors.append(f'Order {order_reference} with item {sku} is already invoiced. Cannot cancel.')
            #if shipment exists then partial cancel is not allowed
            self.validate_shipment_data(order_reference, order_records, duplicate_items)


        #validate sales return data
        self.validate_sales_return_data()

        #validate manifest data
        self.validate_manifest_data()

    def validate_st_order(self, order_reference, order_records, request_data):
        """
        Validates stock transfer order for deletion
        """
        
        if order_records.empty:
            return
        update_po = request_data.get('update_po', 'true') or 'true'
        if update_po.lower() == 'false':
            return {}
        # Check if this is a stock transfer order with create_st_po flag
        json_data = order_records.iloc[0].get('json_data', {}) or {}
        customer_code = order_records.iloc[0].get('customer_code', '') or ''
        order_type = order_records.iloc[0].get('order_type', '').lower()

        if order_type.lower() != 'stocktransfer':
            return

        is_po_created = json_data.get('create_st_po', True)
        if not is_po_created:
            return

        # Get destination user and prepare payload
        self.dest_user = get_destination_user(customer_code)
        if not self.dest_user:
            return {}
        po_obj = POHeader.objects.filter(warehouse_id=self.dest_user.id, po_reference=order_reference)
        if not po_obj.exists():
            return {}
        st_payload = self.prepare_st_po_payload(order_reference, request_data, order_records)

        # Validate PO updation
        failed_status, _ = validate_po_updation(st_payload, self.dest_user, True)
        if failed_status:
            self.errors.extend(failed_status)
        else:
            self.st_po_payload.append(st_payload)

    def prepare_st_po_payload(self, order_reference, request_data, order_records):
        """
        Prepare stock transfer PO payload for cancellation
        """
        st_payload = {
            'po_reference': order_reference,
            'supplier_id': self.warehouse.username,
            'po_type': "STOCKTRANSFER",
            'status': "update",
            'reason': "SO Updated",
            'update_order': False, 
            "items": []
        }
        if request_data.get('replace', False) in [True, 'true', 'True']:
            st_payload['replace'] = True
        if not request_data.get('items', []):
            st_payload['status'] = "cancel"
            return st_payload
        for item in request_data.get('items',[]):
            sku_code = item.get('sku', '')
            aux_data = item.get('aux_data', {}) or {}
            line_reference = str(aux_data.get('line_reference', ''))
            order_record = order_records[(order_records['sku_code'] == sku_code) & (order_records['line_reference'] == line_reference) & (order_records['mrp'] == item.get('mrp', 0)) & (order_records['unit_price'] == item.get('sale_price', 0))]
            quantity = item.get('quantity', 0.0) or 0.0
            if not order_record.empty:
                existing_quantity = float(order_record['original_quantity'].sum() - order_record['cancelled_quantity'].sum())
                quantity = (existing_quantity - quantity, min(existing_quantity, quantity))[existing_quantity - quantity < 0]
            item_dict = {
                "sku": item.get('sku', ''),
                "order_quantity": quantity,
                "price": item.get('sale_price', 0.0) or 0.0,
                "mrp": item.get('mrp', 0.0) or 0.0,
                "remarks": item.get('remarks', ''),
                "sgst_tax": item.get('sgst_tax', 0.0) or 0.0,
                "cgst_tax": item.get('cgst_tax', 0.0) or 0.0,
                "igst_tax": item.get('igst_tax', 0.0) or 0.0,
                "aux_data": {}
            }
            if item.get('line_reference'):
                item_dict['aux_data']['line_reference'] = item.get('line_reference')
            elif aux_data.get('line_reference'):
                item_dict['aux_data']['line_reference'] = aux_data.get('line_reference')
            st_payload['items'].append(item_dict)

        # For cancel status, we don't need to add items
        return st_payload


    def validate_shipment_data(self, order_reference, order_records, duplicate_items):
        if len(order_records) != len(duplicate_items):
                shipment_invoice_ids = list(ShipmentInvoice.objects.filter(user=self.warehouse.id, order_reference=order_reference).values_list('id', flat=True))
                if shipment_invoice_ids:
                    shipment_records = OrderShipmentItems.objects.filter(shipment_invoice__in=shipment_invoice_ids)
                    if shipment_records.exists():
                        self.errors.append(f'Shipment exists for the given order reference {order_reference}. Partial cancellation is not allowed.')


    def validate_sales_return_data(self):
        """
        validates the sales return data if any records present for that order order cancel is not allowed
        """
        sales_return_filter = {'sku__user': self.warehouse.id, 'order_reference__in': self.request_order_references}
        sales_return_objs = SalesReturnLineLevel.objects.filter(**sales_return_filter).values_list('order_reference', flat=True)
        if sales_return_objs.exists():
            self.errors.append(f'Sales Return exists for the given order references {list(sales_return_objs)}.')

    def validate_manifest_data(self):
        """
        validate manifest details for the given order references
        if manifest is created for order reference then order cancel is not allowed
        """
        shipment_invoice_filters = {'user__id': self.warehouse.id, 'order_reference__in': self.request_order_references}
        shipment_invoice_ids = list(ShipmentInvoice.objects.filter(**shipment_invoice_filters).values_list('id', flat=True))
        #get ordershipment_ids with status 2 (manifest created)
        manifest_order_references = OrderShipmentItems.objects.filter(shipment_invoice__in=shipment_invoice_ids, order_shipment__status=2).values_list('shipment_invoice__order_reference', flat=True)
        if manifest_order_references.exists():
            self.errors.append(f'Order Cancellation is not allowed for Manifest generated Orders {list(manifest_order_references)}.')


    def get_orders_to_delete(self):
        """
        return orderdetail records to delete
        """
        replace_orders, present_orders = set(), []
        for order in self.request_data:
            replace = order.get('replace', False)
            #if replace is true, get missing order_record items from the order and add to delete list.
            if replace in [True, 'true', 'True']:
                replace_orders.add(order.get('order_reference'))
            else:
                present_orders.append(order)
            #if replace is false, get given order_records items and add to delete list.
        self.get_all_records_for_order(replace_orders)
        self.get_present_order_records(present_orders)

    def get_all_records_for_order(self, order_refs):
        """
        gets all records for a order reference
        """
        for order_ref in order_refs:
            order_records = self.order_detail_df[self.order_detail_df['order_reference'] == order_ref]
            for index, order_record in order_records.iterrows():
                self.order_records_to_delete = pd.concat([self.order_records_to_delete, order_record.to_frame().T], ignore_index=True)


    def get_present_order_records(self, orders):
        """
        gets the order records that is present in request data
        """
        for order in orders:
            order_reference = order.get('order_reference')
            order_records = self.order_detail_df[self.order_detail_df['order_reference'] == order_reference]
            for index, order_record in order_records.iterrows():
                order_item_level_unique_key = (order_reference, order_record['sku_code'], order_record['line_reference'])
                if order_item_level_unique_key in self.orders_unique_keys:
                    self.order_records_to_delete = pd.concat([self.order_records_to_delete, order_record.to_frame().T], ignore_index=True)
                    self.orders_unique_keys.remove(order_item_level_unique_key)
    
    def get_allocation_records(self):
        """
        gets all allocation records for the order records to delete
        """
        ids = self.order_records_to_delete['id'].unique()
        allocation_records = StockAllocation.objects.filter(reference_model='OrderDetail', reference_id__in=ids, warehouse=self.warehouse.id, status=1)
        self.allocation_df = pd.DataFrame(allocation_records.values())
        self.allocation_df.fillna('', inplace=True)
        for allocation in allocation_records:
            self.allocation_df.loc[self.allocation_df['id'] == allocation.id, 'object'] = allocation


    def prepare_and_update_order_details(self):
        """
        Prepares and updates order and allocation details for orders marked for deletion.
        This method iterates over the DataFrame `self.order_records_to_delete`, updating the cancelled and open quantities
        for each order and its corresponding allocation (if any). It adjusts the `cancel_quantity`, `quantity`, and `status`
        fields for both the order and allocation objects based on the cancellation logic. If all quantities are cancelled,
        the status is set to 3 (cancelled). It also updates the `json_data` field with a cancellation reason if provided,
        and tracks deleted orders and cancelled SKU codes.
        Attributes Used:
            - self.order_records_to_delete: DataFrame containing orders to be deleted.
            - self.allocation_df: DataFrame containing allocation objects.
            - self.suspend_cancel_orders: Boolean flag for additional cancellation logic.
            - self.reason: Optional cancellation reason.
            - self.orders_deleted: Set tracking deleted order references.
            - self.cancelled_sku_codes: Set tracking cancelled SKU codes.
        """
        for index, order_record in self.order_records_to_delete.iterrows():
            cancel_quantity = order_record.get('cancel_quantity', 0) or order_record.original_quantity or 0
            cancelled_quantity = cancel_quantity + order_record.cancelled_quantity
            order_record['object'].cancelled_quantity = cancelled_quantity
            open_quantity = order_record.original_quantity - cancelled_quantity
            
            open_order_quantity = max(0, order_record['object'].quantity - cancel_quantity)
            
            allocation_obj = self.allocation_df.loc[self.allocation_df['reference_id'] == str(order_record.id), 'object'].iloc[0] if not self.allocation_df.empty else None
            open_allocated_quantity = max(0, allocation_obj.quantity - cancel_quantity) if allocation_obj else 0
            
            if order_record['object'].quantity != 0:
                self.order_records_to_delete.at[index, 'cancel_quantity'] = max(0, cancel_quantity - order_record['object'].quantity)
                order_record['object'].quantity = open_order_quantity 
            if allocation_obj and allocation_obj.quantity != 0:
                self.order_records_to_delete.at[index, 'cancel_quantity'] = max(0, cancel_quantity - allocation_obj.quantity)
                allocation_obj.quantity = open_allocated_quantity
                
            if open_quantity == 0:
                order_record['object'].status = 3
            if allocation_obj and open_allocated_quantity == 0:
                allocation_obj.status = 3
                
            self.order_records_to_delete.at[index, 'quantity'] = order_record['object'].quantity
            if self.suspend_cancel_orders and not open_order_quantity and not open_allocated_quantity and cancelled_quantity != order_record.original_quantity:
                order_record['object'].cancelled_quantity = cancelled_quantity - self.order_records_to_delete.at[index, 'cancel_quantity']
            if self.reason or order_record.get('reason','') or order_record.get('remarks', ''):
                json_data = order_record['object'].json_data or {}
                json_data['reason'] = self.reason or order_record.reason
                json_data['remarks'] = order_record.remarks or ''
                order_record['object'].json_data = json_data
            self.orders_deleted.add(order_record['order_reference'])
            self.cancelled_sku_codes.add(order_record['sku_code_'])
    
    def prepare_and_update_picklist_details(self):
        """
        Prepares and updates picklist details for orders marked for deletion or suspension.
        This method processes picklist objects associated with the orders to be deleted or suspended.
        It updates the picklist and order records based on the cancellation or suspension logic:
          - Cancels or suspends picklists depending on the `suspend_cancel_orders` flag.
          - Adjusts reserved, picked, cancelled, and suspended quantities for both picklists and orders.
          - Updates the picklist DataFrame (`self.picklist_df`) to reflect changes.
          - Tracks picklist numbers and IDs that have been cancelled.
          - Prepares pull-to-locate records for cancelled quantities.
        """
        ids = self.order_records_to_delete['id'].unique()
        cancel_quantity_map = {}
        if 'cancel_quantity' in self.order_records_to_delete.columns:
            cancel_quantity_map = dict(zip(self.order_records_to_delete['id'], self.order_records_to_delete['cancel_quantity']))
        filter_data = {'user_id': self.warehouse.id, 'order__id__in': ids}
        picklist_objs = Picklist.objects.filter(**filter_data).exclude(status='cancelled').order_by('status')
        picklist_list = list(picklist_objs)
        self.picklist_df = pd.DataFrame(picklist_objs.values())
        self.picklist_df.fillna('', inplace=True)
        self.picklist_numbers, self.cancelled_picklist_ids = set(), set()
        for picklist in picklist_list:
            cancelled_quantity = 0
            if self.suspend_cancel_orders:
                remaining_cancel = cancel_quantity_map.get(picklist.order_id, 0) or 0
                if self.replace:
                    remaining_cancel = picklist.order.original_quantity or 0
                if remaining_cancel <= 0:
                    self.picklist_df = self.picklist_df[self.picklist_df['id'] != picklist.id]
                    continue
                order_obj = self.order_records_to_delete.loc[self.order_records_to_delete['id'] == picklist.order_id, 'object'].iloc[0]
                if picklist.status == 'open':
                    cancel_from_open = min(picklist.reserved_quantity, remaining_cancel)
                    picklist.reserved_quantity -= cancel_from_open
                    picklist.cancelled_quantity += cancel_from_open
                    remaining_cancel -= cancel_from_open
                    if picklist.reserved_quantity == 0 and picklist.picked_quantity == 0:
                        picklist.status = 'cancelled'
                    cancel_quantity_map[picklist.order_id] = remaining_cancel
                    cancel_from_open += picklist.order.cancelled_quantity
                    self.order_records_to_delete.loc[self.order_records_to_delete['id'] == picklist.order_id, 'cancelled_quantity'] = cancel_from_open
                    order_obj.cancelled_quantity = cancel_from_open
                else:
                    suspended_qty = 0
                    suspend_from_picked = min(picklist.picked_quantity, remaining_cancel)
                    suspended_qty += suspend_from_picked
                    remaining_cancel -= suspend_from_picked
                    if suspended_qty > 0:
                        self.order_records_to_delete.loc[self.order_records_to_delete['id'] == picklist.order_id, 'suspended_quantity'] += suspend_from_picked
                        order_obj.suspended_quantity += suspend_from_picked
                        order_obj.status = 0
                        self.picklist_df = self.picklist_df[self.picklist_df['id'] != picklist.id]
                        continue
                                
            else:
                cancelled_quantity = picklist.picked_quantity - picklist.cancelled_quantity
                picklist.cancelled_quantity += cancel_quantity_map.get(picklist.order_id, 0) or 0
                cancel_quantity = cancel_quantity_map.get(picklist.order_id, 0) or picklist.order.original_quantity or 0
                picklist.reserved_quantity  = max(0, picklist.reserved_quantity - cancel_quantity or 0)
                if picklist.reserved_quantity == 0:
                    picklist.status = 'cancelled'
            self.picklist_df.loc[self.picklist_df['id'] == picklist.id, 'object'] = picklist
            self.picklist_numbers.add(picklist.picklist_number)
            self.cancelled_picklist_ids.add(picklist.id)

            if cancelled_quantity > 0:
                self.prepare_pull_to_locate_records(picklist, cancelled_quantity)

        self.picklist_numbers = list(self.picklist_numbers)
        self.cancelled_picklist_ids = list(self.cancelled_picklist_ids)
        
    def validate_asn_creation(self, invoice_numbers):
        '''
        Validate if ASN already generated for this order
        '''
        
        if self.dest_user and invoice_numbers:
            asn_filters = {
                'source_warehouse_id': self.dest_user.id,
                'invoice_number__in': invoice_numbers,
            }
            asn_objs = ASNSummary.objects.filter(**asn_filters).exclude(status__in=[3, 8])
            if asn_objs.exists():
                return True
        return False
        
    def prepare_and_update_sos_details(self):
       
        ids = self.order_records_to_delete['id'].unique()
        filter_data = {'order__user': self.warehouse.id, 'order_id__in': ids, 'order_status_flag__in': ['customer_invoices', 'delivery_challans', 'processed_orders']}
        sos_objs = SellerOrderSummary.objects.filter(**filter_data)
        self.sos_df = pd.DataFrame(sos_objs.values('id', 'order_status_flag', 'quantity', 'order_id', 'invoice_reference', 'lpn_number', order_reference=F('order__order_reference'), sku_code=F('order__sku__sku_code'), line_reference=F('order__line_reference')))
        for sos in sos_objs:
            self.sos_df.loc[self.sos_df['id'] == sos.id, 'object'] = sos
        self.sos_df.fillna('', inplace=True)
        if not self.sos_df.empty:
            for index, sos_data in self.sos_df.iterrows():
                if sos_data['invoice_reference'] and not sos_data['lpn_number']:
                    self.order_invoices[sos_data['invoice_reference']].append((sos_data['sku_code'], sos_data['line_reference']))
                elif sos_data['lpn_number']:
                    self.lpn_sku_dict[sos_data['invoice_reference']].append((sos_data['lpn_number'], sos_data['sku_code'], sos_data['line_reference'], sos_data['quantity']))
                sos_data['object'].order_status_flag = 'cancelled'
            invoice_numbers = self.sos_df['invoice_reference'].unique().tolist()
            open_asns = self.validate_asn_creation(invoice_numbers)
            if open_asns:
                self.errors.append(f'ASN already generated for the order references {invoice_numbers}.')

    def prepare_and_update_picked_stock(self):

        """
        prepares stock data for deletion
        """
        sos_ids = []
        if not self.sos_df.empty:
            sos_ids = self.sos_df['id'].unique()

        #get stock_data for deletion
        filter_data = {'sku__user': self.warehouse.id, 'receipt_number__in': sos_ids, 'receipt_type__in': ['so_picking', 'so_dispense'], 'location__zone__segregation': 'outbound_staging', 'quantity__gt': 0}
        stock_objs = StockDetail.objects.filter(**filter_data)
        self.stock_df = pd.DataFrame(stock_objs.values())
        for stock in stock_objs:
            stock.quantity = 0
            self.stock_df.loc[self.stock_df['id'] == stock.id, 'object'] = stock

    def get_picklist_and_seller_order_summary(self):
        """
        prepares picklist and seller order summary and stock data for updation
        """
        cancelled_order_ids = []
        ids = self.order_records_to_delete['id'].unique()
        for index, order_record in self.order_records_to_delete.iterrows():
            cancelled_order_ids.append(order_record['id'])
            order_record['object'].cancelled_quantity = order_record['object'].original_quantity
            order_record['object'].quantity = 0
            order_record['object'].status = 3
            order_record['quantity'] = 0
            self.orders_deleted.add(order_record['order_reference'])
        
        if cancelled_order_ids:
            #picklist data
            filter_data = {'user_id': self.warehouse.id, 'order__id__in': ids}
            picklist_objs = Picklist.objects.filter(**filter_data)
            self.picklist_df = pd.DataFrame(picklist_objs.values())
            self.picklist_df['object'] = picklist_objs

            #seller order summary data
            filter_data = {'order__user': self.warehouse.id, 'order_id__in': ids, 'order_status_flag__in': ['processed_orders', 'customer_invoices', 'delivery_challans']}
            sos_objs = SellerOrderSummary.objects.filter(**filter_data)
            self.sos_df = pd.DataFrame(sos_objs.values('id', 'order_status_flag', 'quantity', 'order_id', 'invoice_reference', 'lpn_number', order_reference=F('order__order_reference'), sku_code=F('order__sku__sku_code'), line_reference=F('order__line_reference')))
            self.sos_df['object'] = list(sos_objs)
            sos_ids = []
            self.sos_df.fillna('', inplace=True)
            if not self.sos_df.empty:
                sos_ids = self.sos_df['id'].unique()

            #get stock_data for deletion
            filter_data = {'sku__user': self.warehouse.id, 'receipt_number__in': sos_ids, 'receipt_type__in': ['so_picking', 'so_dispense'], 'location__zone__zone__in': ['WIPZONE', 'PREINVOICEZONE'], 'quantity__gt': 0}
            stock_objs = StockDetail.objects.filter(**filter_data)
            self.stock_df = pd.DataFrame(stock_objs.values())
            self.stock_df['object'] = list(stock_objs)


    def prepare_pull_to_locate_records(self, picklist_record, cancelled_quantity):
        """
        frames pull to locate records and appends to pull_to_locate_records
        """
        po_location_dict = {
            'picklist_id' : picklist_record.id,
            'quantity': cancelled_quantity,
            'original_quantity':cancelled_quantity,
            'status': 1,
            'location_id': picklist_record.location_id,
            'batch_detail_id': picklist_record.stock.batch_detail_id,
            'sku_id':picklist_record.sku_id,
            'putaway_type':'cancelled_picklist',
            'account_id': self.warehouse.userprofile.id,
            'json_data': {'cancelled_type': 'Order Update Cancelled Picklist', 'request_user': self.request.user.username}
        }
        self.pull_to_locate_records.append(POLocation(**po_location_dict))

    def update_deleted_order_records(self):
        """
        saves the updated order details to db
        """
        try:
            with transaction.atomic('default'):
                self.save_order_details()
                self.save_allocation_details()
                self.save_picklist_data()
                self.save_sos_data()
                self.update_picked_stock()
                self.create_po_locations()
                self.update_lms_task_status()
                self.update_pick_and_pass_tasks()
                self.update_st_po()

        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info("Order Updation Process failed with error- %s" % str(e))
            self.errors.append('Order Updation Failed!')

    def update_st_po(self):
        """
        Update stock transfer PO
        """
        if self.st_po_payload and self.dest_user:
            log.info(f"Updating stock transfer PO with payload: {self.st_po_payload}")
            try:
                status = update_po(self.st_po_payload, [], self.dest_user, self.request.user.username)
                log.info(f"Stock transfer PO updated with status: {status}")
                if status != 'Success':
                    self.errors.append(f'Stock transfer PO update failed with status: {status}')
            except Exception as e:
                import traceback
                log.debug(traceback.format_exc())
                log.error(f"Error updating stock transfer PO: {e}")
                self.errors.append(f'Stock transfer PO update failed: {str(e)}')

    def save_order_details(self):
        """
        saves the order details to db
        """
        if self.order_detail_df['object'].tolist():
            OrderDetail.objects.bulk_update_with_rounding(self.order_records_to_delete['object'].tolist(), ['quantity', 'cancelled_quantity', 'suspended_quantity', 'status', 'updation_date', 'json_data'])
            update_order_header_status(self.warehouse, self.request_order_references)
    
    def save_allocation_details(self):
        """
        saves the allocation details to db
        """
        if not self.allocation_df.empty:
            StockAllocation.objects.bulk_update_with_rounding(self.allocation_df['object'].tolist(), ['quantity', 'status', 'updation_date'])
    
    def save_picklist_data(self):
        """
        saves the picklist data to db
        """
        if not self.picklist_df.empty:
            Picklist.objects.bulk_update_with_rounding(self.picklist_df['object'].tolist(), ['reserved_quantity', 'cancelled_quantity', 'status', 'updation_date'])
    
    def save_sos_data(self):
        """
        saves the sos data to db
        """
        if not self.sos_df.empty:
            SellerOrderSummary.objects.bulk_update_with_rounding(self.sos_df['object'].tolist(), ['order_status_flag', 'updation_date'])
    
    def update_picked_stock(self):
        """
        updates the stock data to db
        """
        if not self.stock_df.empty:
            StockDetail.objects.bulk_update_with_rounding(self.stock_df['object'].tolist(), ['quantity', 'updation_date'])
    
    def create_po_locations(self):
        """
        creates po locations for cancelled picklist
        """
        if self.pull_to_locate_records:
            po_loc_objs = POLocation.objects.bulk_create(self.pull_to_locate_records)
            create_putaway_task_for_multiple(po_loc_objs)
            pol_ids = [po_loc_obj.id for po_loc_obj in po_loc_objs]
            extra_params = {'request_headers': self.request.headers, 'request_user': self.request.user, 'request_meta': self.request.META, 'headers': self.request.headers}
            create_grn_lpn_transactions(self.request.user.id, self.warehouse.id, extra_params = extra_params, pol_ids=pol_ids)

    def update_lms_task_status(self):
        '''
        Update lms task status
        '''
        if self.cancelled_picklist_ids:
            TaskMaster.objects.filter(task_ref_id__in=self.cancelled_picklist_ids).update(status=1)

    def update_pick_and_pass_tasks(self):
        """
        Update the status of pick and pass tasks based on the open subzones.
        """
        picklist_numbers = list(Picklist.objects.filter(picklist_number__in=self.picklist_numbers, user_id=self.warehouse.id, status='open').values_list('picklist_number', flat=True).distinct())
        picklist_numbers = [str(picklist_number) for picklist_number in picklist_numbers if picklist_number]
        PickAndPassStrategy.objects.filter(warehouse_id=self.warehouse.id, reference_number__in=self.picklist_numbers).exclude(reference_number__in=picklist_numbers).update(status='cancelled')
