#package imports
import pandas as pd
import re
import numpy as np
from collections import defaultdict
import json

from decimal import Decimal
from datetime import datetime
from typing import Optional

#django imports
from django.db.models import F, Sum, Case, When, IntegerField, Q, Value, CharField
from django.db import transaction

#wms imports
from wms_base.models import User
from wms_base.wms_utils import init_logger

#core operations imports
from core_operations.views.common.main import (
    get_multiple_misc_values, get_decimal_value, truncate_float
)

#inventory imports
from inventory.models import (
    StockDetail, CycleCount, SerialNumber
)
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin

#lms imports
from lms.models import TaskMaster

#core imports
from core.models import UserAttributes

#outbound imports
from outbound.models import (
    OrderDetail, Picklist, SellerOrderSummary, PriceMaster
)
from outbound.views.picklist.validation import get_picklist_number
from .helpers import direct_dispatch_orders
from outbound.views.picklist.constants import BACKFLUSH_JO_TYPES, SHORT_PICK
from outbound.views.picklist.helpers import get_reserved_stock_data, get_picklist_tolerance_cal_extra_quantity, get_pack_sizes_for_sku
from outbound.views.masters.pricingmaster.pricing_master import PriceApplicationPriority
from outbound.views.orders.utils import validate_transaction_lock

log = init_logger('logs/order_fulfill.log')

ORDER_FULFILL_FORM = {
    "Order Reference*": "order_reference",
    "SKU Code*": "sku_code",
    "Line Reference": "line_reference",
    "Location*": "location",
    "Batch Number": "batch_number",
    "Quantity*": "quantity",
    "Serial Number": "serial_number",
    "LPN Number": "lpn_number",
    "Create Invoice (Y/N)": "create_invoice",
    "Picklist Strategy" : "picklist_strategy",
}

ORDER_UPLOAD_MANDATORY_FIELDS = ["Order Reference*", "SKU Code*", "Location*", "Quantity*"]

PICKLIST_STRATEGY_MAPPING = {
    'Single Order Picklist' : 'default',
    'LPN Based Picklist' : 'pack_while_pick',
}


def get_invoice_extra_attributes(warehouse: User):
    """
    Retrieve mandatory and optional extra attributes for invoices based on the given warehouse.
    Args:
        warehouse (User): The warehouse user object for which the invoice attributes are to be fetched.
    Returns:
        tuple[set, set]: A tuple containing two sets:
            - mandatory_invoice_attributes (set): A set of attribute names that are mandatory.
            - invoice_attributes (set): A set of all attribute names (mandatory and optional).
    """
    
    
    mandatory_invoice_attributes, invoice_attributes = [], []
    user_attr_filter = {
        'user_id': warehouse.id,
        'attribute_model': 'invoice',
        'usage': 'extra-attrs',
        'status': 1
    }
    invoice_attrs_data = list(UserAttributes.objects.filter(**user_attr_filter).values('attribute_name', 'is_mandatory').order_by('id'))

    for invoice_attr in invoice_attrs_data:
        invoice_attributes.append(invoice_attr.get('attribute_name'))
        if invoice_attr.get('is_mandatory'):
            mandatory_invoice_attributes.append(invoice_attr.get('attribute_name'))
            
    return mandatory_invoice_attributes, invoice_attributes

def order_fulfill_form(warehouse: User, extra_params={}):
    '''
    Order Fullfil Form
    '''
    log.info("Request for download order form from %s with extra params %s" % (str(warehouse.username), str(extra_params)))
    fulfill_headers = list(ORDER_FULFILL_FORM.keys())
    _, invoice_attributes = get_invoice_extra_attributes(warehouse)
    if invoice_attributes:
        fulfill_headers.extend(invoice_attributes)
    return fulfill_headers

def order_fulfill_upload(request_user: User, warehouse: User, request_data: dict, extra_params: dict = {}):
    try:
        error_status, request_data = OrderFulfillmentUpload(request_user, warehouse, request_data, extra_params).order_fulfillment_process()
        if error_status:
            return request_data
        return 'success'

    except Exception as e:
        import traceback
        log.debug(traceback.format_exc())
        log.info('Order Fulfill Upload failed for %s and params are %s and error statement is %s' % (
        str(request_user.username), str(request_data), str(e)))
        for order_data in request_data:
            order_data['errors'].append('Order Fulfill Upload Failed!')
        return request_data

class InvoiceCreationValidation:
    def __init__(self):
        # Dictionary to track LPN numbers and their associated order references
        self.lpn_to_order_ref = {}

    def validate_lpn_uniqueness(self, lpn_number, order_reference, each_row):
        """
        Validate that an LPN number is used with only one order reference.

        Args:
            lpn_number (str): The LPN number to validate
            order_reference (str): The order reference associated with the LPN number
            each_row (dict): The row data to add errors to

        Returns:
            bool: True if validation fails, False otherwise
        """
        if not self.full_lpn_invoice_at_order_fulfill:
            return False

        if not lpn_number:
            each_row['errors'].append('LPN Number is mandatory for invoice')
            return True

        # Validate LPN number uniqueness across order references
        if lpn_number in self.lpn_to_order_ref:
            # If LPN already exists but for a different order reference, add error
            if self.lpn_to_order_ref[lpn_number] != order_reference:
                each_row['errors'].append(f"LPN Number {lpn_number} is already used for Order Reference {self.lpn_to_order_ref[lpn_number]}")
                return True
        else:
            # Store the LPN and its associated order reference
            self.lpn_to_order_ref[lpn_number] = order_reference

        return False
    
    def validate_invoiced_lpns(self, row_record, each_row, is_create_invoice=False):
        """
        Validates that LPN numbers are used consistently with the same invoice creation status.
        
        This function ensures that an LPN number is not used for order references with
        different invoice creation statuses. It tracks which LPNs are marked for invoicing and
        which are not, preventing inconsistent usage across upload rows.
        
        Args:
            row_record (dict): The record data containing the LPN number and other order details
            each_row (dict): The row data to add validation errors to
            is_create_invoice (bool, optional): Whether this LPN should be invoiced. Defaults to False.
            
        Returns:
            None: Updates the errors field in each_row if validation fails
        """
        
        if not row_record.get('lpn_number'):
            return
        
        lpn_number = row_record.get('lpn_number')
        
        if not self.full_lpn_invoice_at_order_fulfill:
            return
        
        if (lpn_number in self.invoiced_lpns and not is_create_invoice) or (lpn_number in self.non_invoiced_lpns and is_create_invoice):
            each_row['errors'].append(f"LPN Number {lpn_number} is already used for Order Reference with different invoice creation status")
            self.errors = True
            return
        
        if is_create_invoice:
            self.invoiced_lpns.add(lpn_number)
        else:
            self.non_invoiced_lpns.add(lpn_number)
            
    
    def validate_full_lpn_number_picking(self):
        """
        Validate that the quantity for an LPN number is equal to the total quantity for that LPN number.

        Args:
            row_record (dict): The row data to validate

        Returns:
            None
        """
        if not self.full_lpn_invoice_at_order_fulfill:
            return True

        is_validated = True
        for lpn_number, given_lpn_quantity in self.lpn_quantity_dict.items():
            lpn_indexes = self.lpn_number_index.get(lpn_number, []) or []
            available_lpn_quantity = self.stock_df[(self.stock_df['lpn_number'] == lpn_number)]['quantity'].sum()
            if given_lpn_quantity != available_lpn_quantity:
                for index in lpn_indexes:
                    self.request_data[index]['errors'].append(f"LPN Number {lpn_number} quantity is not equal to total quantity for that LPN number")
                    is_validated = False
        return is_validated
        
    def validate_invoice_sku_limit(self, row_record, each_row, is_create_invoice=False):
        """
        Validates the SKU limit for invoice creation.
        This method checks if the number of unique SKUs added to an invoice exceeds
        the allowed limit. If the limit is exceeded, an error message is appended
        to the `errors` list of the current row, and the `errors` flag is set to True.
        Args:
            row_record (dict): A dictionary containing details of the current row, 
                including the SKU code.
            each_row (dict): A dictionary representing the current row being processed, 
                where errors can be appended.
            is_create_invoice (bool, optional): A flag indicating whether invoice creation 
                is being attempted. Defaults to False.
        Returns:
            None
        """
        
        if not self.invoice_sku_limit_check or not is_create_invoice: 
            return

        sku_code = row_record.get('sku_code', '')
        if sku_code:
            self.unique_skus.add(sku_code)
        
        if len(self.unique_skus) > int(self.no_skus_for_invoice):
            each_row['errors'].append(f"SKU limit exceeded for invoice creation")
            self.errors = True
            
    def validate_price_details(self, row_record):
        '''
        Validate price details
        '''
        
        if not self.price_application_at_invoice:
            return
        
        sku_code = row_record.get('sku_code', '')
        location = row_record.get('location', '')
        batch_number = row_record.get('batch_number', '')
        sku_category = self.sku_category_map.get(sku_code, '')
        if sku_code not in self.priced_skus and (sku_category and sku_category not in self.priced_sku_categories):
            self.request_data[row_record['row_index']]['errors'].append(f"Price list not available for SKU Code {sku_code}")
            self.errors = True
            return
        
        key = (sku_code, location, batch_number)
        batch_info = self.sku_loc_batch_to_batch_info.get(key, {})
        batch_wac = batch_info.get('batch_wac')
        batch_mrp = batch_info.get('batch_mrp')
        if batch_wac in [None, '', 'null']:
            self.request_data[row_record['row_index']]['errors'].append(f"Cost Price not available for SKU Code {sku_code}")
        elif batch_mrp is not None and batch_wac > batch_mrp:
            self.request_data[row_record['row_index']]['errors'].append(f"Cost Price should be less than MRP for SKU Code {sku_code}")
            
    def validate_invoice_extra_attributes(self, row_record, each_row, is_create_invoice=False):
        '''
        Validate invoice extra attributes
        '''

        if not is_create_invoice:
            return
        
        if self.mandatory_invoice_attributes and not set(self.mandatory_invoice_attributes).issubset(set(row_record.keys())):
            each_row['errors'].append(f"Mandatory User attributes are configured for invoice creation")
            
        for attr in self.invoice_user_attributes:
            attr_value = row_record.get(attr, '') or ''
            existing_attr_value = self.invoice_extra_fields.get(attr, '') or ''
            if attr_value:
                if attr in self.invoice_extra_fields and existing_attr_value != attr_value:
                    each_row['errors'].append(f"Multiple values found for attribute {attr}")
                    self.errors = True
                else:
                    self.invoice_extra_fields[attr] = attr_value
            elif attr in self.mandatory_invoice_attributes:
                each_row['errors'].append(f"Mandatory User attribute {attr} is missing")
                self.errors = True
        
        
class OrderFulfillmentUpload(InvoiceCreationValidation):
    def __init__(self, request_user: User, warehouse: User, request_data: dict, extra_params: Optional[dict] = None):
        # Call parent class's __init__ method
        super().__init__()

        self.user = request_user
        self.warehouse = warehouse
        self.request_data = request_data
        self.extra_params = extra_params or {}

        self.userprofile_obj = self.warehouse.userprofile
        self.account_id = self.userprofile_obj.id
        self.timezone = self.userprofile_obj.timezone or 'Asia/Kolkata'
        self.get_decimal_limit()

        self.batch_size = 100

    def get_decimal_limit(self):
        '''
        Get decimal limit from config
        '''
        self.decimal_limit = None
        config_decimal_limit = get_decimal_value(self.warehouse.id)
        if config_decimal_limit and isinstance(config_decimal_limit, str) and config_decimal_limit.isdigit():
            self.decimal_limit = int(config_decimal_limit)

    def order_fulfillment_process(self):
        '''
        Order Fulfillment Process
        '''
        self.errors = False
        self.get_order_fulfill_required_configs()
        self.get_order_fulfill_request_data()
        if self.errors:
            return self.errors, self.request_data

        self.get_existing_details()
        if self.errors:
            return self.errors, self.request_data

        self.direct_dispatch_details, self.picklist_details, self.open_picklist_order_ids, self.open_order_skus = {}, {}, [], []
        self.updated_order_ids, self.updated_picklist_ids = [], []
        self.validate_and_prepare_order_fulfill_details()
        if self.errors:
            return self.errors, self.request_data

        self.create_or_update_order_fulfill_details()
        return self.errors, self.request_data

    def get_order_fulfill_request_data(self):
        '''
        1. Formatting order fulfill form data
        2. Preparing Unique values from request data
        '''
        self.formatted_request_data, self.stock_ids = [], set()
        self.lpn_to_order_ref, self.lpn_number_index, self.invoiced_lpns, self.non_invoiced_lpns, self.invoice_extra_fields = {}, defaultdict(list), set(), set(), {}
        self.unique_skus = set()
        self.lpn_quantity_dict, self.lpn_order_quantity_dict, self.lpn_sku_mapping, self.lpn_serial_dict = defaultdict(int), defaultdict(int), {}, defaultdict(set)
        self.request_key_unique_values = {
            "sku_code": set(),
            "location": set(),
            "order_reference": set(),
            "batch_number": set(),
            "serial_number": set(),
        }
        self.unique_serial_numbers = set()
        self.mandatory_invoice_attributes, self.invoice_user_attributes = get_invoice_extra_attributes(self.warehouse)
        self.invoice_sku_limit_check = False
        if self.no_skus_for_invoice.isdigit() and int(self.no_skus_for_invoice) > 0:
            self.invoice_sku_limit_check = True

        for _index, each_row in enumerate(self.request_data):
            each_row['errors'] = []

            row_record = {ORDER_FULFILL_FORM.get(key, key): value for key, value in each_row.items()}
            create_invoice = row_record.get('create_invoice', 'N') or 'N'
            is_create_invoice = False
            if create_invoice in ['Y', 'y', True]:
                is_create_invoice = True
            
            row_record['create_invoice'] = is_create_invoice
                
            if row_record.get('quantity'):
                try:
                    row_record['quantity'] = float(row_record.get('quantity'))
                except Exception:
                    self.errors = True
                    each_row['errors'].append('Quantity should be decimal!')

            for key, value in self.request_key_unique_values.items():
                if row_record.get(key):
                    value.add(row_record.get(key))

            for mandatory_key in ORDER_UPLOAD_MANDATORY_FIELDS:
                if mandatory_key == 'Location*':
                    if not each_row.get('Serial Number') and not each_row.get(mandatory_key):
                        self.errors = True
                        each_row['errors'].append(f"{mandatory_key} is Mandatory for Order Fulfill Upload")
                elif not each_row.get(mandatory_key):
                    self.errors = True
                    each_row['errors'].append(f"{mandatory_key} is Mandatory for Order Fulfill Upload")

            if row_record.get('serial_number'):
                if row_record.get('quantity') != 1:
                    self.errors = True
                    each_row['errors'].append('Serial Number is allowed only for Quantity 1')
                unique_sn = (row_record.get('serial_number'), row_record.get('sku_code'))
                if unique_sn in self.unique_serial_numbers:
                    self.errors = True
                    each_row['errors'].append('Duplicate Serial Number')
                else:
                    self.unique_serial_numbers.add(unique_sn)

            picklist_strategy = row_record.get('picklist_strategy') or 'Single Order Picklist'
            lpn_validation = False
            if picklist_strategy not in PICKLIST_STRATEGY_MAPPING:
                self.errors = True
                each_row['errors'].append(f"Picklist Strategy should be one of {', '.join(PICKLIST_STRATEGY_MAPPING)}")
            else:
                row_record['picklist_strategy'] = PICKLIST_STRATEGY_MAPPING[picklist_strategy]
                if row_record['picklist_strategy'] == 'pack_while_pick':
                    lpn_validation = True
                    
            if row_record.get('lpn_number'):
                lpn_number = row_record.get('lpn_number')
                order_reference = row_record.get('order_reference')
                if is_create_invoice:
                    self.lpn_quantity_dict[lpn_number] += row_record.get('quantity', 0)
                    self.lpn_sku_mapping[lpn_number] = row_record.get('sku_code', '')
                    if row_record.get('serial_number'):
                        self.lpn_serial_dict[lpn_number].add(row_record['serial_number'])
                self.lpn_number_index[lpn_number].append(_index)
                # Validate LPN number uniqueness across order references using the method from InvoiceCreationValidation
                validation_failed = (False, self.validate_lpn_uniqueness(lpn_number, order_reference, each_row))[lpn_validation==True]
                if validation_failed:
                    self.errors = True
            elif lpn_validation and is_create_invoice:
                self.errors = True
                each_row['errors'].append('LPN Number is mandatory for LPN Based Picking with Auto Invoice')
         
            self.validate_invoiced_lpns(row_record, each_row, is_create_invoice)
            
            self.validate_invoice_sku_limit(row_record, each_row, is_create_invoice)
            
            self.validate_invoice_extra_attributes(row_record, each_row, is_create_invoice)

            self.formatted_request_data.append(row_record)
        
        self.sku_pack_details = {}
        if self.pack_size_tolerance and self.lpn_sku_mapping:
            self.sku_pack_details = get_pack_sizes_for_sku(self.warehouse, list(self.lpn_sku_mapping.values()))

    def get_existing_details(self):
        '''
        Get existing details
        1. Get order details
        2. Get Picklist details
        3. Get stock Details
        4. Get Customer Details
        '''
        self.get_order_details()
        if self.errors:
            return
        self.get_price_details()
        self.get_serial_number_details()
        # self.get_existing_sos_data()
        self.get_stock_details()
        

    def get_order_details(self):
        '''
        Get order details
        '''
        self.order_references = self.request_key_unique_values.get('order_reference', [])
        order_objects = OrderDetail.objects.filter(user=self.warehouse.id, order_reference__in=self.order_references)
        self.order_detail_df = pd.DataFrame(order_objects.values(
            'id', 'order_reference', 'quantity', 'original_quantity', 'cancelled_quantity', 'order_type', 'customer_id',
            'trip_id', 'original_order_id', 'line_reference', sku_code_=F('sku__sku_code'), sku_category=F('sku__sku_category')))
        self.unique_order_types = self.order_detail_df.get("order_type", pd.Series(dtype="object")).unique().tolist()
        # get the list of unique customer_ids from the order_detail_df
        self.customer_ids = list(self.order_detail_df.get("customer_id", pd.Series(dtype="object")).unique().tolist())
        if len(self.customer_ids) > 1:
            self.errors = True
            self.request_data[0].setdefault('errors', []).append('Multiple Customers are not allowed in a single upload')
            return
        if len(self.unique_order_types) > 1:
            self.errors = True
            self.request_data[0].setdefault('errors', []).append('Multiple Order Types are not allowed in a single upload')
            return
        self.order_type = self.unique_order_types[0] if self.unique_order_types else ''
        order_objects = list(order_objects)
        self.sku_category_map = {}
        for obj in order_objects:
            self.order_detail_df.loc[self.order_detail_df['id'] == obj.id, 'object'] = obj
        if not self.order_detail_df.empty:
            self.order_detail_df['available_quantity'] = self.order_detail_df['quantity']
            self.order_detail_df['sku_category'] = self.order_detail_df['sku_category'].fillna('')
            self.sku_category_map = self.order_detail_df.set_index('sku_code_')['sku_category'].to_dict()
            
    def get_price_details(self):

        self.priced_sku_codes, self.priced_sku_categories = set(), set()
        
        if not self.price_application_at_invoice or not self.customer_ids:
            return
        
        price_type_data = {
            'customer_ids': self.customer_ids,
            'order_types': self.unique_order_types
        }
        self.price_type = PriceApplicationPriority(self.warehouse, price_type_data).get_price_type()

        if not self.price_type:
            self.request_data[0].setdefault('errors', []).append('Price Type is Mandatory for Invoice Generation, Please configure Price Type in Customer Master or Order Type Configuration!')
            self.errors = True
            return
        
        price_filters = {
            'user': self.warehouse.id,
            'price_id': self.price_type,
            'status': True
        }
        price_master_objects = PriceMaster.objects.filter(**price_filters).values('sku_category', sku_code=F('sku__sku_code'))
        self.priced_skus = set([price_master.get('sku_code') for price_master in price_master_objects if price_master.get('sku_code')])
        self.priced_sku_categories = set([price_master.get('sku_category') for price_master in price_master_objects if price_master.get('sku_category')])
        
    def get_stock_details(self):
        '''
        Get stock details
        '''
        sku_codes = self.request_key_unique_values['sku_code']
        locations = self.request_key_unique_values['location']
        self.cycle_count_stock = set()
        self.cycle_count_data(sku_codes)
        stock_filters = Q(
            sku__user=self.warehouse.id,
            sku__sku_code__in=sku_codes,
            quantity__gt=0,
            status=1
        )
        if locations:
            stock_filters &= Q(location__location__in=locations)

        # to handle both serial and no serial skus
        final_stock_filter = stock_filters | Q(id__in=self.stock_ids)

        stock_objects = StockDetail.objects.filter(final_stock_filter).exclude(location__zone__segregation__in=['outbound_staging', 'inbound_staging'])
        self.stock_df = pd.DataFrame(stock_objects.values('id', 'quantity', 'status', 'sku_id', 'location_id', 'lpn_number',
            batch_id = F('batch_detail_id'), sku_code=F('sku__sku_code'), location_=F('location__location'),
            batch_number=F('batch_detail__batch_no'), batch_reference=F('batch_detail__batch_reference'), manufactured_date=F('batch_detail__manufactured_date'),
            expiry_date=F('batch_detail__expiry_date'), batch_based=F('sku__batch_based'), serial_based=F('sku__enable_serial_based'), batch_mrp=F('batch_detail__mrp'),
            batch_json_data=F('batch_detail__json_data')))

        for stock in self.stock_df.itertuples():
            batch_id = 0 if pd.isna(stock.batch_id) else stock.batch_id
            lpn_number = '' if pd.isna(stock.lpn_number) else stock.lpn_number
            stock_key = (stock.sku_id, stock.location_id, batch_id, lpn_number)
            if stock_key in self.cycle_count_stock:
                self.stock_df.drop(stock.Index, inplace=True)

        if not self.stock_df.empty:
            stock_objects = list(stock_objects)
            for obj in stock_objects:
                self.stock_df.loc[self.stock_df['id'] == obj.id, 'object'] = obj

            self.stock_df['batch_number'] = self.stock_df['batch_number'].fillna('')
            self.get_stock_reserved_quantity_details()

            # Create a dictionary with (sku_code, location, batch) as key and batch_wac, batch_mrp as values
            self.sku_loc_batch_to_batch_info = {}
            for _, row in self.stock_df.iterrows():
                batch_no = row.get('batch_number', '') or ''
                key = (row['sku_code'], row['location_'], batch_no)
                batch_json = row.get('batch_json_data', {}) or {}
                batch_wac = None
                if batch_json:
                    try:
                        if isinstance(batch_json, str):
                            batch_json = json.loads(batch_json)
                        batch_wac = batch_json.get('batch_wac')
                    except Exception:
                        batch_wac = None
                self.sku_loc_batch_to_batch_info[key] = {
                    'batch_wac': batch_wac,
                    'batch_mrp': row.get('batch_mrp', 0) or 0
                }

    def get_serial_number_details(self):
        """
        Retrieves the details of serial numbers associated with the order.

        Returns:
            None
        """
        serial_numbers = self.request_key_unique_values.get('serial_number', [])
        self.serial_number_stock_id_map = {}
        self.serial_df = pd.DataFrame()
        if serial_numbers:
            sn_objects = SerialNumber.objects.filter(warehouse_id=self.warehouse.id, serial_number__in=serial_numbers, status=1)
            self.serial_df = pd.DataFrame(sn_objects.values('id', 'serial_number', 'lpn_number', 'stock_id', sku_code=F('sku__sku_code'), location_=F('location__location'), batch_no=F('batch_detail__batch_no')))
            for sn_obj in sn_objects:
                self.serial_df.loc[self.serial_df['id'] == sn_obj.id, 'object'] = sn_obj
                self.serial_number_stock_id_map[sn_obj.serial_number] = sn_obj.stock_id
            if not self.serial_df.empty:
                self.stock_ids = set(self.serial_df['stock_id'].tolist())
                
    def get_existing_sos_data(self):
        
        self.existing_sos_quantity, self.invoice_picked_quantity = defaultdict(int), defaultdict(int)
        picklist_ids = set()
        sos_filters = {
            'order__user': self.warehouse.id,
            'order_status_flag__in': ['customer_invoices','delivery_challans'],
            'quantity__gt': 0,
            'order__order_reference__in': self.order_references
        }
        values = ['quantity', 'order__order_reference', 'order__customer_id', 'order__id']
        existing_invoices = SellerOrderSummary.objects.filter(**sos_filters).only(*values)
        for sos in existing_invoices:
            quantity = sos.quantity
            if quantity:
                self.existing_sos_quantity[sos.order_id] += quantity
            elif sos.picklist_id not in picklist_ids:
                self.invoice_picked_quantity[sos.order_id] += quantity
                picklist_ids.add(sos.picklist_id)

    def cycle_count_data(self, sku_codes):
        '''
        Exclude cycle count location stocks
        '''
        restrict_stock_cycle_count_creation_types,  restrict_stock_cycle_count_pending_approval_types = set(), set()

        cycle_type_run_type_mapping = {
            'short_cycle_count': [SHORT_PICK],
            'scheduled_cycle_count': ['scheduled', 'Scheduled'],
            'unscheduled_cycle_count': ['unscheduled', 'Unscheduled', 'UnScheduled'],
            'audit_cycle_count': ['Audit'],
            'adhoc_cycle_count': ['adhoc'],
        }

        # By default, exclude short pick cycle count records if short close is enabled
        self.restrict_stock_cycle_count_creation_types = self.switch_values.get('restrict_picklist_on_cycle_count_creation_options', '').split(',')
        self.restrict_stock_cycle_count_pending_approval_types = self.switch_values.get('restrict_picklist_on_cycle_count_pending_approval_options', '').split(',')

        cycle_type_run_type_mapping = {
            'short_cycle_count': ['short pick'],
            'scheduled_cycle_count': ['scheduled', 'Scheduled'],
            'unscheduled_cycle_count': ['unscheduled', 'Unscheduled', 'UnScheduled'],
            'audit_cycle_count': ['Audit'],
            'adhoc_cycle_count': ['adhoc'],
        }

        # Get cycle count run types based on the restrict options
        for cycle_type in self.restrict_stock_cycle_count_creation_types:
            if cycle_type not in cycle_type_run_type_mapping:
                continue
            restrict_stock_cycle_count_creation_types.update(cycle_type_run_type_mapping[cycle_type])
        
        for cycle_type in self.restrict_stock_cycle_count_pending_approval_types:
            if cycle_type not in cycle_type_run_type_mapping:
                continue
            restrict_stock_cycle_count_pending_approval_types.update(cycle_type_run_type_mapping[cycle_type])

        if not (restrict_stock_cycle_count_creation_types or restrict_stock_cycle_count_pending_approval_types):
            return

        # Fetch cycle count records based on run type filters
        if restrict_stock_cycle_count_creation_types:
            self.cycle_count_stock = set(CycleCount.objects
                .filter(run_type__in= restrict_stock_cycle_count_creation_types, sku__sku_code__in=sku_codes, status__in= [1,2])
                .values_list('sku_id', 'location_id')
                .annotate(
                    batch_id=Case(When(batch_detail__isnull=False,then=F('batch_detail_id')), default=0, output_field=IntegerField()),
                    lpn_number=Case(When(lpn_number__isnull=False, then=F('lpn_number')), default=Value(''), output_field=CharField())
                )
                .distinct()
            )
        
        if restrict_stock_cycle_count_pending_approval_types:
            self.cycle_count_stock.update(set(CycleCount.objects
                .filter(run_type__in= restrict_stock_cycle_count_pending_approval_types, sku__sku_code__in=sku_codes, status=2)
                .values_list('sku_id', 'location_id')
                .annotate(
                    batch_id=Case(When(batch_detail__isnull=False,then=F('batch_detail_id')), default=0, output_field=IntegerField()),
                    lpn_number=Case(When(lpn_number__isnull=False, then=F('lpn_number')), default=Value(''), output_field=CharField())
                )
                .distinct()
            ))

    def get_stock_reserved_quantity_details(self):
        '''
        Calculate the reserved quantity for available stock
        '''
        stock_ids = []
        if 'id' in self.stock_df.columns:
            stock_ids = list(self.stock_df['id'])
        reserved = get_reserved_stock_data(self.warehouse.id, stock_ids)
        if reserved:
            # Convert dictionary to DataFrame with stock_id as a column
            reserved_df = pd.DataFrame.from_dict(reserved, orient='index').reset_index()
            reserved_df.columns = ['stock_id', 'reserved_qty']
            
            # Merge with stock_df on id = stock_id
            self.stock_df = self.stock_df.merge(
                reserved_df, left_on='id', right_on='stock_id', how='left')
        else:
            self.stock_df['reserved_qty'] = 0
        if not self.stock_df.empty:
            self.stock_df['quantity'] = self.stock_df['quantity'].fillna(0)
            self.stock_df['reserved_qty'] = self.stock_df['reserved_qty'].fillna(0)
            for index, stock_record in self.stock_df.iterrows():
                self.stock_df.loc[index, 'available_quantity'] = float(Decimal(str(stock_record['quantity'])) - Decimal(str(stock_record['reserved_qty'])))
                if self.decimal_limit:
                    self.stock_df.loc[index, 'available_quantity'] = truncate_float(self.stock_df.loc[index, 'available_quantity'], self.decimal_limit)

    def get_order_fulfill_required_configs(self):
        '''
        Get required configurations
        '''
        misc_types = [
            'stock_allocate', 'mandate_tripid_for_picklist',
            'restrict_picklist_on_cycle_count_creation_options', 'restrict_picklist_on_cycle_count_pending_approval_options',
            'full_lpn_invoice_at_order_fulfill', 'price_application', 'sku_limit_for_invoice', 'order_expiration_date',
            'order_hold_options', 'restrict_expired_orders', 'picklist_tolerance_type'
        ]
        self.switch_values = get_multiple_misc_values(misc_types, self.warehouse.id)
        self.stock_allocate, self.is_trip_id_mandatory, self.price_application_at_invoice = True, False, False
        self.no_skus_for_invoice = ''
        self.full_lpn_invoice_at_order_fulfill = False
        if self.switch_values.get('stock_allocate') in [None, 'false', False, '']:
            self.stock_allocate = False
        if self.switch_values.get('mandate_tripid_for_picklist', '') == 'true':
            self.is_trip_id_mandatory = True
        if self.switch_values.get('full_lpn_invoice_at_order_fulfill', '') == 'true':
            self.full_lpn_invoice_at_order_fulfill = True
        if self.switch_values.get('price_application', '') == 'at_invoice_creation':
            self.price_application_at_invoice = True
        if self.switch_values.get('sku_limit_for_invoice', ''):
            self.no_skus_for_invoice = self.switch_values.get('sku_limit_for_invoice', '')
        self.pack_size_tolerance = False
        if self.switch_values.get('picklist_tolerance_type', '') == 'pack_size_tolarance' and self.full_lpn_invoice_at_order_fulfill:
            self.pack_size_tolerance = True

    def validate_and_prepare_order_fulfill_details(self):
        '''
        Validate and prepare order fulfill details
        '''
        no_of_rows = len(self.formatted_request_data)
        
        if self.pack_size_tolerance and self.lpn_sku_mapping:
            self.validate_lpn_quantity(no_of_rows)
            if self.errors:
                return
        
        restricted_orders_dict = validate_transaction_lock(self.warehouse, list(self.request_key_unique_values.get('order_reference', [])), extra_params=self.switch_values, transaction='picklist')
        
        for row_index in range(0, no_of_rows, 1):
            row_record = self.formatted_request_data[row_index]
            row_record['row_index'] = row_index
            
            order_reference = row_record.get('order_reference', '')

            
            if order_reference in restricted_orders_dict:
                self.request_data[row_record['row_index']]['errors'].append(restricted_orders_dict.get(order_reference,''))
                self.errors = True
                continue

            self.validate_order_reference(row_record)
            self.validate_price_details(row_record)
            if self.request_data[row_index]['errors']:
                self.errors= True
                continue

            self.validate_order_quantity(row_record)
            self.validate_stock_quantity(row_record)
            if self.request_data[row_index]['errors']:
                self.errors = True
                continue

            self.prepare_data_for_picklist_and_invoice(row_record)
    
    def validate_lpn_quantity(self, no_of_rows):
        '''
        Prepare LPN wise order data
        '''
        order_unique_lines = set()
        for row_index in range(0, no_of_rows, 1):
            row_record = self.formatted_request_data[row_index]
            order_reference = row_record.get('order_reference', '')
            sku_code = row_record.get('sku_code', '')
            line_reference = row_record.get('line_reference', '')
            lpn_number = row_record.get('lpn_number', '')
            create_invoice = row_record.get('create_invoice', 'N')
            quantity = row_record.get('quantity', 0)
            location = row_record.get('location', '')
            is_create_invoice = False
            if create_invoice in ['Y', 'y', True]:
                is_create_invoice = True
            if not (lpn_number and is_create_invoice):
                continue
            
            # Base conditions
            conditions = [
                self.order_detail_df['order_reference'] == order_reference,
                self.order_detail_df['sku_code_'] == sku_code
            ]

            # Retrieve line_reference and add condition if it's valid
            line_reference = row_record.get('line_reference', '')
            if line_reference:
                conditions.append(self.order_detail_df['line_reference'] == line_reference)

            # Apply all conditions in a single query
            order_filter_df = self.order_detail_df.loc[np.logical_and.reduce(conditions)]
            order_quantity = order_filter_df['quantity'].sum()
            if order_quantity > quantity:
                self.request_data[row_index]['errors'].append(f"Quantity should not be given less than order quantity for LPN Based picklist and auto invoice. LPN Number - {lpn_number}")
                self.errors = True
                continue
            
            stock_conditions = [
                self.stock_df['lpn_number'] == lpn_number,
                self.stock_df['sku_code'] == sku_code,
                self.stock_df['location_'] == location
            ]
            if row_record.get('batch_number'):
                stock_conditions.append(self.stock_df['batch_number'] == row_record.get('batch_number'))
            available_lpn_quantity = self.stock_df[np.logical_and.reduce(stock_conditions)]['quantity'].sum()
            given_lpn_quantity = self.lpn_quantity_dict[lpn_number]
            lpn_indexes = self.lpn_number_index.get(lpn_number, []) or []
            if given_lpn_quantity != available_lpn_quantity:
                for index in lpn_indexes:
                    self.request_data[index]['errors'].append(f"LPN Number {lpn_number} quantity is not equal to total quantity for that LPN number")
                self.errors = True
                continue
                
            order_unique_key = (order_reference, sku_code, line_reference)
            if order_unique_key in order_unique_lines:
                continue
            order_unique_lines.add(order_unique_key)
            self.lpn_order_quantity_dict[lpn_number] += order_quantity
        
        if not self.lpn_order_quantity_dict:
            return
        for lpn_number, quantity in self.lpn_quantity_dict.items():
            available_quantity = self.lpn_order_quantity_dict[lpn_number]
            if not available_quantity:
                continue
            if self.decimal_limit:
                quantity = truncate_float(quantity, self.decimal_limit)
                available_quantity = truncate_float(available_quantity, self.decimal_limit)
            if quantity == available_quantity:
                continue
            sku_code = self.lpn_sku_mapping[lpn_number]
            max_quantity = get_picklist_tolerance_cal_extra_quantity(available_quantity, sku_code, 'pack_size_tolarance', 0, self.sku_pack_details)
            if quantity != max_quantity:
                for index in self.lpn_number_index[lpn_number]:
                    self.request_data[index]['errors'].append(f"Given Quantity is not equal to Tolerance quantity")
                self.errors = True
    
    def validate_order_reference(self, row_record):
        '''
        Validate order reference
        '''
        order_reference = row_record.get('order_reference', '')
        if self.order_detail_df.empty:
            self.request_data[row_record['row_index']]['errors'].append(f'No Order found for Order reference {str(order_reference)}')
            return
        order_data = self.order_detail_df.loc[self.order_detail_df['order_reference']==order_reference]
        if order_data.empty:
            self.request_data[row_record['row_index']]['errors'].append(f'No Order found for Order reference {str(order_reference)}')
        elif self.is_trip_id_mandatory and not order_data['trip_id'].iat[0]:
            self.request_data[row_record['row_index']]['errors'].append(f'Trip ID is mandatory for Generating Picklist for Order reference {str(order_reference)}')

        if row_record.get('serial_number') and not self.serial_df.empty and (not row_record.get('location') or not row_record.get('batch_number') or not row_record.get('lpn_number')):
            serial_df = self.serial_df[(self.serial_df['sku_code'] == row_record['sku_code']) & (self.serial_df['serial_number'] == row_record['serial_number'])]
            if serial_df.empty:
                return
            row_record['location'] = serial_df['location_'].iat[0]
            row_record['batch_number'] = serial_df['batch_no'].iat[0]
            row_record['lpn_number'] = serial_df['lpn_number'].iat[0]


    def validate_order_quantity(self, row_record):
        '''
        Validate open order quantity
        '''
        order_reference = row_record.get('order_reference', '')
        sku_code = row_record.get('sku_code', '')
        quantity = row_record.get('quantity', 0)
        line_reference = row_record.get('line_reference', '')
        lpn_number = row_record.get('lpn_number', '')

        if quantity <= 0:
            self.request_data[row_record['row_index']]['errors'].append('Quantity should be more than zero!')
            return

        # Base conditions
        conditions = [
            self.order_detail_df['order_reference'] == order_reference,
            self.order_detail_df['sku_code_'] == sku_code
        ]

        # Retrieve line_reference and add condition if it's valid
        line_reference = row_record.get('line_reference', '')
        if line_reference:
            conditions.append(self.order_detail_df['line_reference'] == line_reference)

        # Apply all conditions in a single query
        order_filter_df = self.order_detail_df.loc[np.logical_and.reduce(conditions)]

        if order_filter_df.empty:
            self.request_data[row_record['row_index']]['errors'].append(f'SKU Code {sku_code} and Line Reference {line_reference} not found for this order as {order_reference}')
        
        if self.lpn_order_quantity_dict.get(lpn_number):
            return

        for index, row_data in order_filter_df.iterrows():
            available_quantity =  row_data.get('available_quantity', 0)
            min_qty = min(available_quantity, quantity)

            quantity -= min_qty
            available_quantity -= min_qty
            if self.decimal_limit:
                quantity = truncate_float(quantity, self.decimal_limit)
                available_quantity = truncate_float(available_quantity, self.decimal_limit)

            self.order_detail_df.loc[self.order_detail_df['id'] == row_data.get('id'), 'available_quantity'] = available_quantity
            if quantity == 0:
                break

        if quantity > 0:
            self.request_data[row_record['row_index']]['errors'].append('Quantity passed is more than open order quantity')

    def validate_stock_quantity(self, row_record):
        '''
        Validate stock available quantity
        '''
        quantity = row_record.get('quantity', 0)
        sku_code = row_record.get('sku_code', '')
        batch_number = row_record.get('batch_number', '')
        location = row_record.get('location', '')
        lpn_number = row_record.get('lpn_number', '') or ''

        if self.stock_df.empty:
            self.request_data[row_record['row_index']]['errors'].append(f'Stock not found for SKU Code - {sku_code}, Batch Number - {batch_number}, Location - {location}')
            return
        
        avbl_stock_condition = (self.stock_df['sku_code'] == sku_code) & (self.stock_df['location_'] == location)
        if batch_number:
            avbl_stock_condition = avbl_stock_condition & (self.stock_df['batch_number'] == batch_number)
        if lpn_number:
            avbl_stock_condition = avbl_stock_condition & (self.stock_df['lpn_number'] == lpn_number)

        available_stock = self.stock_df[avbl_stock_condition]

        if available_stock.empty:
            error_message = f'Stock not available for SKU Code {sku_code}, Location {location}'
            if batch_number:
                error_message += f', Batch Number {batch_number}'
            self.request_data[row_record['row_index']]['errors'].append(error_message)
            return
        elif self.stock_df[(self.stock_df['sku_code'] == sku_code)]['batch_based'].all()  and not (batch_number or row_record.get('serial_number')):
            self.request_data[row_record['row_index']]['errors'].append(f'Batch Number is mandatory for batch based SKU Code - {sku_code}')
            return
        
        if lpn_number and self.stock_df[(self.stock_df['sku_code'] == sku_code) & (self.stock_df['lpn_number'] == lpn_number)].empty:
            self.request_data[row_record['row_index']]['errors'].append(f'LPN Number {lpn_number} not found for SKU Code {sku_code}')
            return
        

        if self.stock_df[(self.stock_df['sku_code'] == sku_code)]['serial_based'].all() and not row_record.get('serial_number'):
            self.request_data[row_record['row_index']]['errors'].append(f'Serial Number is mandatory for serial based SKU Code - {sku_code}')
            return
        

        if row_record.get('serial_number'):
            if self.serial_df.empty:
                self.request_data[row_record['row_index']]['errors'].append(f'Serial Number {row_record["serial_number"]} not found')
            elif row_record.get('lpn_number') and self.serial_df[(self.serial_df['serial_number'] == row_record['serial_number']) & (self.serial_df['lpn_number'] == row_record['lpn_number'])].empty:
                self.request_data[row_record['row_index']]['errors'].append(f'Serial Number {row_record["serial_number"]} with LPN Number {row_record["lpn_number"]} not found')
                return
            elif self.serial_df[(self.serial_df['serial_number'] == row_record['serial_number'])].empty:
                self.request_data[row_record['row_index']]['errors'].append(f'Serial Number {row_record["serial_number"]} not found')
                return
            else:
                available_stock = available_stock[available_stock['id'].isin(self.serial_df['stock_id'])]
                if available_stock.empty:
                    self.request_data[row_record['row_index']]['errors'].append(f'Given serial number is not available in stock')
                else:
                    row_record['stock_ids'] = available_stock['id'].tolist()

        for _index, stock_record in available_stock.iterrows():
            min_qty = min(stock_record['available_quantity'], quantity)
            if self.decimal_limit:
                min_qty = truncate_float(min_qty, self.decimal_limit)
            quantity -= min_qty
            total_available_stock = stock_record['available_quantity'] - min_qty
            self.stock_df.loc[self.stock_df['id'] == stock_record['id'], 'available_quantity'] = total_available_stock

        if quantity > 0:
            self.request_data[row_record['row_index']]['errors'].append(f'Stock not found for SKU Code - {sku_code}, Batch Number - {batch_number}, Location - {location}, LPN Number - {lpn_number}')

    def prepare_data_for_picklist_and_invoice(self, row_record):
        '''
        Prepare data for picklist and invoice
        '''
        create_invoice = row_record.get('create_invoice', 'N')

        is_create_invoice = False
        if create_invoice in ['Y', 'y', True]:
            is_create_invoice = True

        order_reference = row_record.get('order_reference', '')
        sku_code = row_record.get('sku_code', '')
        quantity = row_record.get('quantity', '')
        lpn_number = row_record.get('lpn_number', '')

        conditions = (self.order_detail_df['order_reference'] == order_reference) & (self.order_detail_df['sku_code_'] == sku_code)
        line_reference = row_record.get('line_reference')
        if line_reference:
            conditions &= (self.order_detail_df['line_reference'] == line_reference)
        order_filter_df = self.order_detail_df.loc[conditions]
        for index, row_data in order_filter_df.iterrows():
            available_quantity =  row_data.get('quantity', 0)
            order_quantity = row_data.get('quantity', 0)
            if available_quantity <= 0:
                continue

            min_qty = min(available_quantity, quantity)
            quantity -= min_qty
            available_quantity -= min_qty
            if self.decimal_limit:
                quantity = truncate_float(quantity, self.decimal_limit)
                available_quantity = truncate_float(available_quantity, self.decimal_limit)
            self.order_detail_df.loc[self.order_detail_df['id'] == row_data.get('id'), 'quantity'] = available_quantity
            pick_qty = min_qty
            serial_numbers = []
            if self.pack_size_tolerance and self.lpn_order_quantity_dict[lpn_number]:
                if order_quantity == self.lpn_order_quantity_dict[lpn_number]:
                    #When Last line of that lpn is iterated, we need to pass the extra quantity/serials as picked items for tolerance
                    pick_qty = self.lpn_quantity_dict[lpn_number]
                    serial_numbers = list(self.lpn_serial_dict[lpn_number])
                self.lpn_order_quantity_dict[lpn_number] -= pick_qty
                self.lpn_quantity_dict[lpn_number] -= pick_qty
            else:
                serial_numbers = [row_record['serial_number']] if row_record.get('serial_number') else []
            if is_create_invoice:
                self.direct_dispatch_details =  self.prepare_data_picklist_dispense(row_record, row_data, min_qty, pick_qty, serial_numbers, self.direct_dispatch_details)
            else:
                self.picklist_details = self.prepare_data_picklist_dispense(row_record, row_data, min_qty, pick_qty, serial_numbers, self.picklist_details)
                self.open_picklist_order_ids.append(row_data['id'])
                self.open_order_skus.append(row_data['sku_code_'])

            if quantity == 0:
                break

    def prepare_data_picklist_dispense(self, row_record, order_data, min_qty, pick_qty, serial_numbers, data_dict):
        '''
        Prepare direct dispatch details
        '''
        order_id = order_data['id']
        if not data_dict.get(order_id):
            data_dict[order_id] = {
                'order_instance': order_data['object'],
                'order_reference': row_record.get('order_reference', ''),
                'delivery_challan': False,
                'pick_type' : row_record.get('picklist_strategy', 'default'),
                'data': {}
            }
        unique_item_wise_key = (row_record.get('sku_code', ''), row_record.get('location', ''), row_record.get('batch_number', ''), row_record.get('picklist_strategy', 'default'), row_record.get('lpn_number', ''))
        if not data_dict.get(order_id, {}).get('data',{}).get(unique_item_wise_key):
            data_dict[order_id]['data'][unique_item_wise_key] = {
                'quantity': 0,
                'pick_quantity': 0,
                'location': row_record.get('location', ''),
                'batch_number': row_record.get('batch_number', ''),
                'sku_code': row_record.get('sku_code', ''),
                'lpn_number': row_record.get('lpn_number', ''),
                'pick_type' : row_record.get('picklist_strategy', 'default'),
                'stock_ids': set(row_record.get('stock_ids', [])),
                'serial_numbers': []
            }
        data_dict[order_id]['data'][unique_item_wise_key]['quantity'] += min_qty
        data_dict[order_id]['data'][unique_item_wise_key]['pick_quantity'] += pick_qty

        stock_ids = set(row_record.get('stock_ids', []))
        if serial_numbers:
            data_dict[order_id]['data'][unique_item_wise_key]['serial_numbers'].extend(serial_numbers)
        if stock_ids:
            data_dict[order_id]['data'][unique_item_wise_key]['stock_ids'].update(stock_ids)
        return data_dict

    def create_or_update_order_fulfill_details(self):
        '''
        1. Create direct dispatch orders
        2. Create picklist
        '''
        if self.direct_dispatch_details:
            for order_id, orders in self.direct_dispatch_details.items():
                orders['data'] = list(orders['data'].values())
            direct_dispatch_data = {
                'invoice_extra_attributes': self.invoice_extra_fields,
                'dispatch_data': self.direct_dispatch_details,
            }
            message = direct_dispatch_orders(warehouse=self.warehouse, user = self.user, direct_dispatch_data=direct_dispatch_data, extra_params=self.extra_params)
            log.info("Dispatch Dispatch Order Response %s" % str(message))
        if self.picklist_details:
            self.create_picklist()
            self.create_and_update_order_picklist_details()

    def create_picklist(self):
        '''
        Create picklist details
        '''
        self.stock_df = pd.DataFrame(StockDetail.objects.prefetch_related('sku', 'location', 'batch_detail').filter(
            sku__user=self.warehouse.id, quantity__gt=0, status=1, sku__sku_code__in=self.open_order_skus).values('id', 'sku_id', 'location_id', 'quantity', expiry_date=F('batch_detail__expiry_date'), mrp=F(
            'batch_detail__mrp'), batch_number=F('batch_detail__batch_no'), batch_reference=F('batch_detail__batch_reference'), batch_id=F('batch_detail'),
            location_name=F('location__location'), zone=F('location__zone__zone'), sku_code=F('sku__sku_code')))

        if not self.stock_df.empty:
            self.get_stock_reserved_quantity_details()
            self.stock_df['batch_number'] = self.stock_df['batch_number'].fillna('')

        self.final_picklist_records_dict, self.picklist_number_details, self.serial_transaction_details = {}, {}, {}

        for order_id, order_details in self.picklist_details.items():
            for order_fulfill_record in order_details['data'].values():
                order_reference = order_details.get('order_reference', '')
                if not self.picklist_number_details.get(order_reference):
                    self.picklist_number_details[order_reference] = int(get_picklist_number(self.warehouse)) + 1
                self.prepare_picklist_dict(order_fulfill_record, order_details['order_instance'])

    def prepare_picklist_dict(self, order_data, order_obj):
        '''
        preparing picklist detail dict
        '''
        open_order_quantity = order_data.get('quantity', 0)
        pick_type = order_data.get('pick_type', 'default')
        serial_numbers = order_data.get('serial_numbers', [])
        conditions = [
            self.stock_df['sku_code']==order_data['sku_code'],
            self.stock_df['location_name']==order_data['location'],
            self.stock_df['batch_number']==order_data['batch_number'],
            self.stock_df['available_quantity'] > 0
        ]

        if serial_numbers:
            stock_ids = []
            for serial_number in serial_numbers:
                stock_id = self.serial_number_stock_id_map.get(serial_number)
                if stock_id:
                    stock_ids.append(stock_id)
            conditions = [self.stock_df['id'].isin(stock_ids), self.stock_df['available_quantity'] > 0]

        for index, stock_item_record in self.stock_df.loc[np.logical_and.reduce(conditions)].iterrows():
            min_qty = min(open_order_quantity, stock_item_record['available_quantity'])

            if not self.picklist_number_details.get(order_obj.order_reference):
                self.picklist_number_details[order_obj.order_reference] = int(get_picklist_number(self.warehouse)) + 1
            picklist_number = self.picklist_number_details[order_obj.order_reference]

            unique_key = (picklist_number, order_obj.id, stock_item_record['id'])
            if not self.final_picklist_records_dict.get(unique_key):
                self.final_picklist_records_dict[unique_key] = {
                    "picklist_number": picklist_number,
                    "reserved_quantity": min_qty,
                    "picked_quantity": 0,
                    "sku_id": stock_item_record['sku_id'],
                    "user_id": self.warehouse.id,
                    "remarks": "",
                    "status": 'open',
                    "order_id": order_obj.id,
                    "stock_id": stock_item_record['id'],
                    "reference_number": order_obj.order_reference,
                    "reference_id": order_obj.id,
                    "reference_model": "OrderDetail",
                    "location_id": stock_item_record['location_id'],
                    "picklist_quantity": min_qty,
                    "account_id": self.account_id,
                    "json_data" : {
                        "created_from" : "upload",
                        "generated_by" : self.user.username
                    },
                    "order_type" : order_obj.order_type,
                    'pick_type': pick_type
                }
            else:
                self.final_picklist_records_dict[unique_key]['reserved_quantity'] += min_qty
                self.final_picklist_records_dict[unique_key]['picklist_quantity'] += min_qty

            open_order_quantity -= min_qty
            self.stock_df.loc[self.stock_df['id'] == stock_item_record['id'], 'available_quantity'] = stock_item_record['available_quantity'] - min_qty

            if self.decimal_limit:
                self.final_picklist_records_dict[unique_key]['reserved_quantity'] = truncate_float(self.final_picklist_records_dict[unique_key]['reserved_quantity'], self.decimal_limit)
                self.final_picklist_records_dict[unique_key]['picklist_quantity'] = truncate_float(self.final_picklist_records_dict[unique_key]['picklist_quantity'], self.decimal_limit)
                open_order_quantity = truncate_float(open_order_quantity, self.decimal_limit)
                self.stock_df.loc[self.stock_df['id'] == stock_item_record['id'], 'available_quantity'] = truncate_float(stock_item_record['available_quantity'] - min_qty, self.decimal_limit)

            serials = order_data.get('serial_numbers', [])
            if serials:
                order_data['serial_numbers'] = serials[int(min_qty):]
                self.create_serial_transaction_details(picklist_number, serials[:int(min_qty)], order_data, unique_key)

            self.order_detail_df.loc[self.order_detail_df['id'] == order_obj.id, 'quantity'] = open_order_quantity
            order_object = self.order_detail_df.loc[self.order_detail_df['id'] == order_obj.id, 'object'].values[0]
            order_object.quantity -= min_qty
            if self.decimal_limit:
                order_object.quantity = truncate_float(order_object.quantity, self.decimal_limit)
            if order_object.quantity <= 0:
                order_object.status = '0'
            self.updated_order_ids.append(order_obj.id)
            self.order_detail_df.loc[self.order_detail_df['id'] == order_obj.id, 'object'] = order_object

            if open_order_quantity <= 0:
                break

    def create_serial_transaction_details(self, picklist_number, serial_numbers, order_data, unique_key):
        """
        Create serial transaction details for a given picklist number.

        Args:
            picklist_number (str): The picklist number.
            serial_numbers (list): List of serial numbers.
            order_data (dict): Data related to the order.
            unique_key (str): Unique key for the item.

        Returns:
            None
        """
        if not self.serial_transaction_details.get(picklist_number):
            self.serial_transaction_details[picklist_number] = {
                'reference_number': picklist_number,
                'reference_type': 'so_picking',
                'items': {}
            }

        if unique_key not in self.serial_transaction_details[picklist_number]['items']:
            self.serial_transaction_details[picklist_number]['items'][unique_key] = {
                'serial_numbers': [],
                'sku_code': order_data.get('sku_code', ''),
                'batch_number': order_data.get('batch_number', ''),
                'location': order_data.get('location', ''),
                'zone': order_data.get('zone', ''),
                'status': 2,
                'lpn_number': order_data.get('lpn_number', ''),
                'serial_status': 3
            }

        for serial_number in serial_numbers:
            if serial_number not in self.serial_transaction_details[picklist_number]['items'][unique_key]['serial_numbers']:
                self.serial_transaction_details[picklist_number]['items'][unique_key]['serial_numbers'].append(serial_number)

    def create_and_update_order_picklist_details(self):
        '''
        Create and update order picklist details
        '''
        updated_order_objects = []

        if self.updated_order_ids and not self.order_detail_df.empty and self.order_detail_df['object'].tolist():
            for index, order_df in self.order_detail_df.iterrows():
                if order_df['id'] in self.updated_order_ids:
                    updated_order_objects.append(order_df['object'])

        self.picklist_ids, self.picklist_numbers = [], []
        with transaction.atomic('default'):
            if updated_order_objects:
                OrderDetail.objects.bulk_update_with_rounding(updated_order_objects, ['quantity', 'status'])

            picklist_entries = list(self.final_picklist_records_dict.values())
            if picklist_entries:
                picklist_ids = Picklist.objects.bulk_create([Picklist(**entry) for entry in picklist_entries])
                for picklist_id in picklist_ids:
                    self.picklist_ids.append(picklist_id.id)
                    self.picklist_numbers.append(picklist_id.picklist_number)

        if self.picklist_ids:
            self.create_task_master_data(self.picklist_ids)

    def create_task_master_data(self, picklist_ids):
        '''
        Create task master data
        '''
        created_picklist_objects = list(Picklist.objects.filter(user=self.warehouse.id, picklist_number__in=self.picklist_numbers, status='open', id__in=picklist_ids).
                                        values('id', 'order_type', 'user', 'picklist_number', 'order__trip_id', 'order__forward_time', 'order_id',
                                               'order__shipment_date', 'order__creation_date', 'order__estimated_dispatch_time', 'stock_id',
                                               'location__pick_sequence', 'location__zone__zone'))
        task_master_data = []
        for picklist in created_picklist_objects:
            task_master_data = self.prepare_task_data(picklist, picklist['order_id'], 'false', task_master_data)
            picklist_number = picklist.get('picklist_number')
            unique_key = (picklist_number, picklist.get('order_id'), picklist.get('stock_id'))
            self.serial_transaction_id_mapping(picklist_number, unique_key, picklist['id'])

        if task_master_data:
            TaskMaster.objects.bulk_create(task_master_data)

        if self.serial_transaction_details:
            self.create_serial_transaction()

    def serial_transaction_id_mapping(self, picklist_number, unique_key, picklist_id):
        """
        Maps the picklist ID to the corresponding item in the serial transaction details.

        Args:
            picklist_number (int): The picklist number.
            unique_key (str): The unique key of the item.
            picklist_id (int): The ID of the picklist.

        Returns:
            None
        """
        if not self.serial_transaction_details.get(picklist_number):
            return

        if self.serial_transaction_details[picklist_number].get('items', {}).get(unique_key):
            self.serial_transaction_details[picklist_number]['items'][unique_key]['transact_id'] = picklist_id

    def create_serial_transaction(self):
        """
        Creates a serial transaction based on the serial transaction details.

        Returns:
            None
        """
        sntd_request_data = []
        for _picklist_number, picklist_data in self.serial_transaction_details.items():
            picklist_data['items'] = list(picklist_data['items'].values())
            sntd_request_data.append(picklist_data)

        sntd_mixin_objs = SerialNumberTransactionMixin(self.warehouse, self.warehouse, sntd_request_data, False)
        self.final_response = sntd_mixin_objs.create_update_sn_transaction()

    def prepare_task_data(self, picklist, order, picklist_priority, task_master_data):
        '''
        Prepare task master data creation
        '''
        if order:
            if picklist_priority in ['trip_id', 'od_creation_time']:
                priority = picklist['order__forward_time']
                task_eta = picklist['order__creation_date']
            elif picklist_priority == 'picklist_generation':
                priority = picklist['order__forward_time']
                task_eta = datetime.now()
            elif picklist_priority == 'exp_delivery':
                priority = picklist['order__forward_time']
                task_eta = picklist['order__shipment_date']
            else:
                priority = picklist['order__forward_time']
                task_eta = picklist['order__estimated_dispatch_time']
        else:
            priority = picklist['location__pick_sequence']
            task_eta = None
        task_master_data.append(TaskMaster(**{
            "warehouse_id": picklist['user'],
            "task_ref_type": 'Picklist',
            "task_ref_id": picklist['id'],
            "group_type": picklist['location__zone__zone'],
            "order_type": picklist['order_type'],
            "reference_number": picklist['picklist_number'],
            "priority": priority,
            "eta": task_eta
        }
        ))
        return task_master_data
