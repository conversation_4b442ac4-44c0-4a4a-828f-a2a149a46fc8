#package imports
import re
from json import loads, dumps
from collections import defaultdict
from copy import deepcopy
from datetime import datetime
import traceback
import json

#django imports
from django.db.models import Max, F, Sum
from django.test.client import RequestFactory
from django.utils import timezone
from django.http import JsonResponse
from django.db import transaction

#wms imports
from wms.celery import app
from wms_base.models import User
from wms_base.wms_utils import init_logger

#core imports
from core.models import TempJson
from core_operations.views.common.main import (
    get_multiple_misc_values, get_misc_options_list, get_warehouse, get_financial_year,
    get_local_date_known_timezone, get_user_time_zone
)
from core_operations.views.services.packing_service import PackingService

#lms imports
from lms.models import TaskMaster

#inventory imports
from inventory.models import SKUPackMaster, StockDetail, LocationMaster
from inventory.views.cycle_count.cycle_count import generate_cycle_count
from inventory.views.serial_numbers.serial_number_transaction import SerialNumberTransactionMixin
from inventory.views.serial_numbers.serial_number import SerialNumberMixin

from inventory.models.locator import INVENTORY_CHOICES
status_choices = dict(INVENTORY_CHOICES)

#outbound imports
from outbound.models import (
    Picklist, OrderTypeZoneMapping, StockAllocation, OrderDetail, 
    SellerOrderSummary, Order, StagingInfo
)
from .validation import get_picklist_number

log = init_logger('logs/picklist_helpers.log')

DATEFORMAT = "%Y-%m-%d"

@app.task
def update_eta_async(update_eta_dict):
    try:
        pick_ids = list(Picklist.objects.filter(order__in = update_eta_dict['order_id']).exclude(status='cancelled').values_list('id', flat = True))
        pick_eta = update_eta_dict['order_eta']
        picklist_priority = update_eta_dict['picklist_priority']
        trip_id_num = 0
        if update_eta_dict.get('trip_id',''):
            try:
                trip_id = update_eta_dict['trip_id']
                if type(trip_id) == str:
                    trip_id_num =  re.findall(r'\d+', trip_id_num)[0]
                    trip_id_num = int(trip_id_num)
                else:
                    trip_id_num = int(trip_id)
            except Exception:
                trip_id_num = 0
        forward_time = update_eta_dict.get('forward_time',0)
        picker_unassigned = TaskMaster.objects.filter(task_ref_id__in = pick_ids,status=False,employee=None)
        updated_pick_ids =  list(picker_unassigned.values_list('task_ref_id',flat=True))
        updated_pick_ids = [int(pick_id) for pick_id in updated_pick_ids]
        if picklist_priority == 'eta' and pick_eta:
            picker_unassigned.update(eta=pick_eta,priority=forward_time)
        elif picklist_priority == 'trip_id':
            picker_unassigned.update(eta=None,priority=trip_id_num)
        not_updated_pick_ids = list(set(pick_ids) - set(updated_pick_ids))
        return not_updated_pick_ids
    except Exception as e:
        log.error(f"Error updating ETA asynchronously: {str(e)}")
    return  []


def get_saved_picklist_serial_number(model_ids, model_name='picklist'):
    temp_json_obj = list(TempJson.objects.filter(model_id__in=model_ids, model_name=model_name).values('model_id','model_json'))
    picklist_serial_dict={}
    picklist_serial_numbers = []
    all_stock_numbers=[]
    serial_stock_qty_dict={}
    if temp_json_obj:
        for each_temp in temp_json_obj:
            serial_stock_list=[]
            existed_serial_dict = loads(each_temp.get('model_json'))
            for each_stock,serial_list in existed_serial_dict.items():
                if int(each_stock) not in  all_stock_numbers:
                    all_stock_numbers.append(int(each_stock))
                if serial_stock_qty_dict.get(each_stock,[]):
                    serial_stock_qty_dict[each_stock].extend(serial_list) 
                else:
                    serial_stock_qty_dict[each_stock]=serial_list
                serial_stock_list.extend(serial_list)
                serial_stock_list =list(set(serial_stock_list))
                picklist_serial_numbers.extend(serial_stock_list)
            picklist_serial_dict[str(each_temp.get('model_id'))]=len(serial_stock_list)
    return all_stock_numbers, serial_stock_qty_dict,list(set(picklist_serial_numbers)), picklist_serial_dict

def create_cycle_count_short_pick(request, warehouse: User, stock, quantity, order_type, reason, extra_params=None):

    if extra_params is None:
        extra_params = {}
    if extra_params.get('request_user_id'):
        request_user = User.objects.get(id=extra_params.get('request_user_id'))
    else:
        request_user = request.user

    if not stock:
        return 0
    misc_types = ['create_cycle_count_for_short_close_joborder', 'create_cycle_count_for_short_close_batosa', 'create_cycle_count_for_short_close', 'job_order_types']
    misc_details = get_multiple_misc_values(misc_types, warehouse.id)
    jo_types = misc_details.get('job_order_types',"")
    source = ''
    if jo_types:
        jo_types = jo_types.split(',')
    if jo_types and order_type in jo_types:
        cycle_count = misc_details.get('create_cycle_count_for_short_close_joborder', False)
        source = 'Job Order'
    elif order_type in ['BA_TO_SA', 'NTE']:
        cycle_count = misc_details.get('create_cycle_count_for_short_close_batosa', False)
        source = order_type
    else:
        cycle_count = misc_details.get('create_cycle_count_for_short_close', False)
        source = 'Sale Order'
    if cycle_count != 'true':
        return 0
    inv_request = RequestFactory
    inv_request.method= 'POST'
    inv_request.warehouse = warehouse
    inv_request.user = request_user

    payload = [{
        'sku_code' : stock.sku.sku_code,
        'zone': stock.location.zone.zone,
        'location' : stock.location.location,
        'quantity' : stock.quantity - quantity,
        'cycle_type': 'short pick',
        'short_pick_reason': reason,
        'priority' : 1,
        'id' : stock.id,
        'sku_id': stock.sku.id,
        'location_id': stock.location.id,
        'stock_status': status_choices.get(stock.status),
        'batch_detail_id': stock.batch_detail_id,
        'lpn_number': stock.lpn_number,
        'source': source
    }]
    inv_request.body = dumps(payload)
    response = generate_cycle_count(inv_request)
    if response.status_code == 400:
        error_data = json.loads(response.content)
        log.info('Cycle count creation failed for short pick for stock %s and error is %s' % (str(stock.id), str(error_data)))
        for error_msg in error_data.get('message', []):
            if 'run_type: short pick' in error_msg:
                break
        else:
            return 0
    return (stock.sku_id, stock.location_id, stock.batch_detail_id, stock.lpn_number)

def get_pack_sizes_for_sku(warehouse, sku_codes):
    '''
    Get pack sixe for sku
    '''
    sku_pack_details = dict(SKUPackMaster.objects.filter(sku__user=warehouse.id, sku__sku_code__in=sku_codes, status=1).values(
        sku_code=F('sku__sku_code')).annotate(pack_qty=Max('pack_quantity')).values_list('sku_code', 'pack_qty'))
    return sku_pack_details

def get_picklist_tolerance(warehouse, sku_codes, misc_data=None):
    '''
    Get picklist tolerance
    '''
    tolerance_percentage, tolerance_type, sku_pack_details = 0, '', {}
    misc_data = misc_data or {}
    tolerance_type_config = misc_data.get('picklist_tolerance_type', '')
    tolerance_percentage = misc_data.get('picklist_tolerance')
    if tolerance_type_config not in ['false', False, '', 'null', 'None']:
        tolerance_type = tolerance_type_config

    if tolerance_type == 'default' and tolerance_percentage not in ['false', '', 'None', 'null', None]:
        tolerance_percentage = float(tolerance_percentage)
    else:
        tolerance_percentage = 0

    if tolerance_type == 'pack_size_tolarance':
        sku_pack_details = get_pack_sizes_for_sku(warehouse, sku_codes)

    return tolerance_type, tolerance_percentage, sku_pack_details

def get_picklist_tolerance_cal_extra_quantity(item_quantity, sku_code, tolerance_type, tolerance_percenetage, sku_pack_details={}, is_total=True):
    '''
    Get picklist_tolerance quantity
    '''
    tolerance_quantity = 0
    if tolerance_type == 'pack_size_tolarance':
        sku_max_qty = sku_pack_details.get(sku_code, '')
        if sku_max_qty:
            remaining_pack_qty = item_quantity%sku_max_qty
            if remaining_pack_qty > 0:
                tolerance_quantity = sku_max_qty - remaining_pack_qty
    elif tolerance_percenetage:
        tolerance_quantity = (item_quantity/100)*tolerance_percenetage
    if is_total:
        tolerance_quantity += item_quantity
    return tolerance_quantity

def get_picklist_configurations(warehouse):
    '''
    Get all outbound picklist configurations
    '''
    misc_types = [
        'short_close_order', 'block_expired_batches_picklist', 'stock_allocate',
        'mandate_tripid_for_picklist', 'create_cycle_count_for_short_close', 'customer_shelf_life',
        'picklist_priority', 'no_stock_switch', 'zone_mandatory_for_picklist_generation', 'enable_sales_uom',
        'order_expiration_date', 'restricts_number_of_skus_in_a_lpn', 'user_sub_zone_mapping',
        'create_cycle_count_for_short_close_batosa', 'create_cycle_count_for_short_close_joborder',
        'restrict_picklist_on_cycle_count_creation_options', 'restrict_picklist_on_cycle_count_pending_approval_options',
        'override_picklist_priority', 'allocation_chunk_size', 'order_hold_options', 'restrict_expired_orders',
        'manual_stock_selection_for_picklist', 'mrp_tolerance', 'zone_mandatory_for_jo_picklist_generation'
    ]
    misc_option_keys = [
        'allow_partial_picklist', 'order_all_items_picklist_generation','warehouse_notifications', 'manual_assignment',
        'cancel_open_order_at_picklist_generation', 'mrp_based_picking'
    ]

    switch_values = get_multiple_misc_values(
        misc_types, warehouse.id)
    misc_options_details = get_misc_options_list(
        misc_option_keys, warehouse)
    switch_values.update(misc_options_details)
    return switch_values

def get_order_type_master_stock_and_sale_uom_details(warehouse: User, order_type: str) -> list:
    '''
    Get stock status from order type master changes
    '''
    stock_status, sales_uom_enabled = [], False

    order_type_master_details_list = list(OrderTypeZoneMapping.objects.filter(user_id=warehouse.id, order_type=order_type, status=1).values('json_data', 'receipt_type', 'sales_uom'))
    for order_type_record in order_type_master_details_list:
        sales_uom_enabled = sales_uom_enabled or order_type_record.get('sales_uom', 0)
        if order_type_record.get('receipt_type', '') != 'PICKLIST':
            continue
        order_type_json_data = order_type_record.get('json_data', {}) or {}
        stock_status_list = order_type_json_data.get('stock_status', []) or []
        if stock_status_list:
            stock_status.extend(stock_status_list)
    return stock_status, sales_uom_enabled

def fetch_serial_data(warehouse, reference_number, transact_ids, serial_skus):
    """
    Fetch serial numbers for the given serial skus and reference number
    
    Args:
        warehouse : User
        reference_number : str
        serial_skus : list
        
    Returns:
        dict : serial_number_data
    """

    serial_filters = {
        'reference_type' : 'so_picking',
        'reference_number' : reference_number,
        'status' : 2,
        'sku_code__in' : serial_skus,
        'transact_id__in' : transact_ids
    }
    serial_data = get_serial_transaction_data(warehouse=warehouse, filters=serial_filters)
    serial_number_data = defaultdict(list)
    for serial in serial_data.get('data', []):
        item_key = (serial['sku_code'], serial['location'], serial.get('batch_number') or '', serial.get('lpn_number') or '')
        serial_number_data[item_key].extend(serial['serial_numbers'])
    
    return serial_number_data

def get_serial_transaction_data(user=None, warehouse=None, filters=None, excludes=None, values=None):
    """
    Fetch serial numbers for the given filters and values
    
    Args:
        warehouse : User
        filters : dict
        excludes : dict
        values : list
        
    Returns:
        dict : serial_number_data
    """
    filters = filters or {}
    excludes = excludes or {}
    values = values or []
    serial_data = SerialNumberTransactionMixin(user, warehouse, {'filters' : filters, 'exclude_filters' : excludes, 'values' : values}).get_sntd_details()
    
    return serial_data

def get_serial_data(user=None, warehouse=None, filters=None, excludes=None, values=None):
    """
    Fetch serial numbers for the given filters and values
    
    Args:
        warehouse : User
        filters : dict
        excludes : dict
        values : list
        
    Returns:
        dict : serial_number_data
    """
    filters = filters or {}
    excludes = excludes or {}
    values = values or []
    
    serial_data = SerialNumberMixin(None, warehouse, {'filters' : filters, 'excludes' : excludes, 'values' : values}).get_serial_numbers()
    
    return serial_data

def validate_serial_numbers(warehouse, serial_data, reference_number):
    """
    Validate and Reserve serial numbers for the given serial data
    
    Args:
        warehouse : User
        serial_data : dict
        reference_number : str
        
    Returns:
        bool : True if success else False
    """
    
    serial_numbers, sku_codes, locations, picklist_ids, from_lpn_numbers, lpn_numbers = [], set(), set(), set(), set(), set()
    item_serial_mapping, transact_serial_mapping, serial_mapping_data, reserved_serial_mapping = defaultdict(set), defaultdict(set), defaultdict(list), defaultdict(set)
    transaction_item_mapping = {}
    for serials in serial_data:
        serial_nums = serials['serial_numbers']
        sku_code = serials['sku_code']
        from_lpn = serials.get('from_lpn') or ''
        lpn_number = serials.get('lpn_number') or ''
        if lpn_number:
            lpn_numbers.add(lpn_number)
        serial_numbers.extend(serial_nums)
        sku_codes.add(sku_code)
        locations.add(serials['location'])
        if from_lpn:
            from_lpn_numbers.add(from_lpn)
        transact_id = serials['id']
        picklist_ids.add(transact_id)
        item_key = (sku_code, serials['location'], serials.get('batch_number') or '', from_lpn)
        item_serial_mapping[item_key].update(serial_nums)
        serial_mapping_data[item_key].append(serials)
        transaction_item_mapping[transact_id] = (item_key[0], item_key[1], item_key[2], lpn_number)
    
    avl_serial_mapping = fetch_available_serial_data(warehouse, serial_numbers, sku_codes, locations, from_lpn_numbers)
    not_avl_serials = set()
    missing_serials, reserve_serial_data = defaultdict(set), []
    for key, serials in item_serial_mapping.items():
        unavl_serials = serials - avl_serial_mapping.get(key, set())
        not_avl_serials.update(unavl_serials)
        missing_serials[key].update(unavl_serials)

    invalid_serials = set()
    if not_avl_serials:
        serial_filters = {
            'reference_type' : 'so_picking',
            'reference_number' : reference_number,
            'status' : 2,
            'transact_id__in' : picklist_ids,
            'serial_number__in' : list(not_avl_serials),
        }
        if from_lpn_numbers:
            serial_filters['lpn_number__in'] = from_lpn_numbers
        
        serial_data = get_serial_transaction_data(warehouse=warehouse, filters=serial_filters)
        reserved_serials = set()
        for serial in serial_data.get('data', []):
            transaction_id = serial['transact_id']
            item_key = transaction_item_mapping[transaction_id]
            reserved_serial_mapping[item_key].update(serial['serial_numbers'])
            transact_serial_mapping[transaction_id].update(serial['serial_numbers'])
            reserved_serials.update(serial['serial_numbers'])
        
        invalid_serials = not_avl_serials - reserved_serials

    if invalid_serials:
        return invalid_serials, [], transact_serial_mapping
    
    for key, serials in avl_serial_mapping.items():
        ser_data = serial_mapping_data[key]
        reserve_serial_data.extend(ser_data)
        for serial in ser_data:
            transact_serial_mapping[serial['id']].update(serial['serial_numbers'])
    
    return [], reserve_serial_data, transact_serial_mapping

def fetch_available_serial_data(warehouse, serial_numbers, sku_codes, locations, lpn_numbers):
    """
    Fetch available serial numbers for the given serial numbers, sku codes and locations
    
    Args:
        warehouse : User
        serial_numbers : list
        sku_codes : list
        locations : list
        
    Returns:
        dict : available_serial_data
    """
    
    serial_filters = {
        'serial_number__in' : serial_numbers,
        'sku__sku_code__in' : sku_codes,
        'location__location__in' : locations,
        'status' : 1
    }
    if lpn_numbers:
        serial_filters['lpn_number__in'] = lpn_numbers
    values = ['serial_number', 'sku__sku_code', 'location__location', 'batch_detail__batch_no', 'lpn_number']
    available_serial_data = get_serial_data(warehouse=warehouse, filters=serial_filters, values=values)
    avl_serial_mapping = defaultdict(set)
    for serial_dat in available_serial_data.get('data', []):
        serial_num = serial_dat['serial_number']
        sku_code = serial_dat['sku__sku_code']
        location = serial_dat['location__location']
        batch_no = serial_dat['batch_detail__batch_no'] or ''
        lpn_number = serial_dat['lpn_number'] or ''
        avl_serial_mapping[(sku_code, location, batch_no, lpn_number)].add(serial_num)
    
    return avl_serial_mapping

def update_serial_number_transaction_data(warehouse, picked_details, serial_number_mapping):
    """
    Update serial number transaction data for the given picked details
    
    Args:
        warehouse : User
        picked_details : dict
        transaction_wise_serials : dict
        
    Returns:
        None
    """
    serial_data = []
    for pick in picked_details:
        json_data = pick['json_data']
        pack_data = json_data['packing_data']
        if json_data.get('combo_flag') or pick['quantity'] <=0:
            continue
        data = {
            'picklist_number' : str(pick['transact_number']),
            'id' : pick['receipt_number'],
            'sku_code' : pack_data.get('sku_code', ''),
            'serial_numbers' : json_data.get('serial_numbers', []),
            'location' : pack_data.get('location', ''),
            'batch_number' : pack_data.get('batch_details', {}).get('batch_number', ''),
        }
        lpn_number = pick.get('lpn_number', '')
        if lpn_number:
            data['lpn_number'] = lpn_number
        serial_data.append(data)
    
    if serial_data:
        errors = update_serials_status(warehouse, serial_data, 'reserve', serial_number_mapping)
        log.info("Serial Number Updation Errors: %s", errors)

def update_serials_status(warehouse, serial_data, transaction_status=None, serial_number_mapping=True):
    """
    Update serials status for the given serials and status
    
    Args:
        warehouse : User
        serials : list
        status : int
        
    Returns:
        bool : True if success else False
    
    """
    if not serial_data:
        return []
    picklist_number = serial_data[0]['picklist_number']
    request_data = {
        'reference_number': picklist_number,
        'reference_type': 'so_picking',
        'items': []
    }
    for serial in serial_data:
        transact_id = serial['id']
        item_data = {
            'transact_type': '',
            'transact_id': 0,
            'serial_numbers': list(serial['serial_numbers']),
            'sku_code': serial['sku_code'],
            'location': serial['location'],
            'batch_number' : serial.get('batch_number', ''),
            'lpn_number' : serial.get('lpn_number', '')
        }
        if serial_number_mapping:
            item_data['transact_id'] = transact_id
        if transaction_status in ['reserve', 'delete']:
            if transaction_status == 'reserve':
                serial_status, serial_transaction_status = 2, 1
            elif transaction_status == 'delete':
                serial_status, serial_transaction_status = 1, 5
            item_data['status'] = serial_transaction_status
            item_data['serial_status'] = serial_status
        request_data['items'].append(item_data)
    if not request_data['items']:
        return []
    resp_dict = SerialNumberTransactionMixin(None, warehouse, request_data, False).create_update_sn_transaction()
    return resp_dict.get('errors')

def prepare_serial_data(pick_data):
    """
    Prepare serial data for the given pick data
    
    Args:
        pick_data : dict
    
    Returns:
        Formatted serial data : list
    """
    
    serial_data = []
    packed_serial_data = defaultdict(dict)
    packing_data = pick_data.get('packing_data')
    items = pick_data['items']
    if packing_data:
        id_wise_qty = defaultdict(int)
        for item in items:
            id_wise_qty[item['id']] += item['reserved_quantity']
        for pack in packing_data:
            serials = pack.get('serial_numbers', [])
            if not serials:
                continue
            picklist_ids = pack['picklist_id'].split(",")
            for pick_id in picklist_ids:
                qty = int(min(len(serials), id_wise_qty[int(pick_id)]))
                serial_nums = serials[:qty]
                serials = serials[qty:]
                packed_serial_data[pick_id].setdefault(pack['package_reference'], set()).update(serial_nums)
    for item in items:
        lpn_serial_data, serial_numbers = {}, []
        serial_item = {
            'picklist_number': str(item['picklist_number']),
            'id': item['id'],
            'sku_code': item['sku_code'],
            'location': item['location'],
            'zone' : item['zone'],
            'batch_number': item.get('batchno', ''),
            'from_lpn' : item.get('stock_lpn', ''),
        }
        if packing_data:
            lpn_serial_data = packed_serial_data.get(str(item['id']), {})
            if not lpn_serial_data:
                continue
            for lpn_number, serial_numbers in lpn_serial_data.items():
                lpn_serial_item = deepcopy(serial_item)
                lpn_serial_item['lpn_number'] = lpn_number
                lpn_serial_item['serial_numbers'] = serial_numbers
                serial_data.append(lpn_serial_item)
        else:
            serial_numbers = item.get('serial_numbers')
            if not serial_numbers:
                continue
            serial_item['serial_numbers'] = serial_numbers
            serial_data.append(serial_item)
    
    return serial_data

def short_close_subsequent_child_picklists_of_combo(picklist_number, order_id, warehouse):
    """
    Short close subsequent child picklists of a combo.

    This function updates the status of open picklists associated with a given combo to 'cancelled' 
    and updates their JSON data with a reason for the short close. It also updates the status of 
    associated tasks in the TaskMaster to True.

    Args:
        picklist_number (str): The picklist number of the combo.
        order_id (int): The order ID associated with the picklists.
        combo_order_picked_dict (dict): A dictionary containing the order ID and combo data.
        warehouse (str): The warehouse identifier.

    Returns:
        None
    """

    picklist_ids = set()
    combo_open_picklists = list(Picklist.objects
        .filter(picklist_number=picklist_number, order_id=order_id, status='open', user=warehouse)
    )

    for combo_open_picklist in combo_open_picklists:
        if combo_open_picklist.id not in picklist_ids:
            picklist_ids.add(combo_open_picklist.id)
        combo_open_picklist.status = 'cancelled'
        json_data = combo_open_picklist.json_data or {}
        json_data.update({'short_close_reason': 'Other child sku of combo has picked zero quantity'})
        combo_open_picklist.json_data = json_data

    if combo_open_picklists:
        Picklist.objects.bulk_update_with_rounding(combo_open_picklists, ['status', 'json_data'])
        TaskMaster.objects.filter(task_ref_id__in=picklist_ids, task_ref_type='Picklist', status=False, warehouse=warehouse).update(status=True, updation_date=timezone.now())

def get_reserved_stock_data(warehouse_id, stock_ids):
    """Retrieve picklist data for the duplicate stock IDs."""
    pick_filters = { 'user_id': warehouse_id, 'status': 'open', 'stock_id__in': stock_ids, 'reserved_quantity__gt': 0 }
    alloc_filters = { 'warehouse_id': warehouse_id, 'status': 1, 'stock_id__in': stock_ids}
    
    picklist_stock_data = defaultdict(int)
    allocated_data = StockAllocation.objects.filter(**alloc_filters).values('stock_id').annotate(allocated_qty=Sum('quantity'))
    reserved_data = Picklist.objects.filter(**pick_filters).values('stock_id').annotate(reserved_qty=Sum('reserved_quantity'))
    
    for data in allocated_data.iterator():
        picklist_stock_data[data['stock_id']] += data['allocated_qty']
    for data in reserved_data.iterator():
        picklist_stock_data[data['stock_id']] += data['reserved_qty']
    
    return picklist_stock_data

@get_warehouse
def get_active_waves(request, warehouse):
    """
    Get active waves for the given warehouse.
    
    Args:
        request : Request
        warehouse : User
        
    Returns:
        JsonResponse : Active waves data
    """
    wave_data = list(Picklist.objects.filter(user_id=warehouse.id, status='open', json_data__wave_reference__isnull=False).values_list('json_data__wave_reference', flat=True).distinct())
    return JsonResponse({"active_waves" : wave_data}, status=200)

@app.task(soft_time_limit=3500, time_limit=3600)
def auto_order_process(user_id, user, warehouse_id, warehouse, order_reference, stock_data, extra_params):
    if not (order_reference and stock_data):
        return JsonResponse({"status": "error", "message": "Order reference and stock data are mandatory"}, status=400)
    
    if not user:
        user_data = User.objects.prefetch_related('userprofile').in_bulk([user_id, warehouse_id])
        if not user_data:
            return JsonResponse({"status": "error", "message": "User not found"}, status=404)
        
        user = user_data.get(user_id)
        warehouse = user_data.get(warehouse_id)
    
    stock_data = prepare_stock_data(warehouse_id, stock_data)
    
    try:
        log.info("auto process order for %s with stock %s" % (str(order_reference), str(stock_data)))
        prepare_and_insert_data(user, warehouse, stock_data, order_reference, extra_params)
    except Exception as e:
        log.debug(traceback.format_exc())
    

def prepare_stock_data(warehouse_id, stock_data):
    batch_data = {
        'location_ids' : set(),
        'batch_detail_ids' : set(),
        'lpn_numbers' : set(),
        'sku_ids' : set(),
    }
    non_batch_data = {
        'location_ids' : set(),
        'lpn_numbers' : set(),
        'sku_ids' : set()
    }
    for stock in stock_data:
        if stock.get('batch_detail_id'):
            batch_data['location_ids'].add(stock['location_id'])
            batch_data['batch_detail_ids'].add(stock['batch_detail_id'])
            batch_data['lpn_numbers'].add(stock['lpn_number'])
            batch_data['sku_ids'].add(stock['sku_id'])
        else:
            
            non_batch_data['location_ids'].add(stock['location_id'])
            non_batch_data['lpn_numbers'].add(stock['lpn_number'])
            non_batch_data['sku_ids'].add(stock['sku_id'])
    
    filters = {
        'location_id__in' : batch_data['location_ids'],
        'batch_detail_id__in' : batch_data['batch_detail_ids'],
        'lpn_number__in' : batch_data['lpn_numbers'],
        'sku_id__in' : batch_data['sku_ids'],
        'sku__user' : warehouse_id,
    }
    batch_stock_data = list(StockDetail.objects.filter(**filters).values('sku_id', 'location_id', 'lpn_number', 'quantity', 'id'))
    filters = {
        'location_id__in' : non_batch_data['location_ids'],
        'lpn_number__in' : non_batch_data['lpn_numbers'],
        'sku_id__in' : non_batch_data['sku_ids'],
        'sku__user' : warehouse_id,
    }
    non_batch_stock_data = list(StockDetail.objects.filter(**filters).values('sku_id', 'location_id', 'lpn_number', 'quantity', 'id'))
    sku_data_dict = defaultdict(list)
    for stock in batch_stock_data:
        sku_data_dict[stock['sku_id']].append({
            'location_id' : stock['location_id'],
            'quantity' : stock['quantity'],
            'stock_id' : stock['id'],
            'lpn_number' : stock['lpn_number'],
        })
    
    for stock in non_batch_stock_data:
        sku_data_dict[stock['sku_id']].append({
            'location_id' : stock['location_id'],
            'quantity' : stock['quantity'],
            'stock_id' : stock['id'],
            'lpn_number' : stock['lpn_number'],
        })
    return sku_data_dict

def prepare_and_insert_data(user, warehouse, stock_data, order_reference, extra_params):
    with transaction.atomic('default'):
        picklist_data, customer_ref, picklist_number = insert_picklist_data(user, warehouse, order_reference, stock_data)
        sos_detail_data = insert_sos_data(picklist_data)
        packing_data_dict, lpn_numbers, location_id = insert_staging_stock(warehouse, sos_detail_data)
        if packing_data_dict:
            create_packing_data(user, warehouse, str(picklist_number), str(customer_ref), packing_data_dict, extra_params)
        if lpn_numbers:
            drop_lpns(warehouse, location_id, lpn_numbers, picklist_number, order_reference)
    
def insert_picklist_data(user, warehouse, order_reference, stock_data):
    picklist_objects_list = []
    filters = {
        'user' : warehouse.id,
        'order_reference' : order_reference,
        'status' : 1
    }
    stock_ids = set(stock['stock_id'] for sku_stock in stock_data.values() for stock in sku_stock)
    StockDetail.objects.filter(id__in=stock_ids).update(quantity=0)
    order_data = list(OrderDetail.objects.filter(**filters).values('id', 'sku_id', 'quantity', 'order_type', 'customer_identifier__customer_reference'))
    customer_ref = ''
    picklist_number = get_picklist_number(warehouse) + 1
    picked_time = get_local_date_known_timezone(warehouse.userprofile.timezone, timezone.now(), send_date=True).strftime('%Y-%m-%d %H:%M:%S')
    for order in order_data:
        sku_id = order['sku_id']
        order_quantity = order['quantity']
        customer_ref = order['customer_identifier__customer_reference']
        for stock in stock_data.get(sku_id, []):
            pick_qty = min(stock['quantity'], order_quantity)
            order_quantity -= pick_qty
            stock['quantity'] -= pick_qty
            picklist_data = {
                'sku_id': sku_id,
                'user_id': warehouse.id,
                'order_id' : order['id'],
                'stock_id' : stock['stock_id'],
                'location_id' : stock['location_id'],
                'reference_id': order['id'],
                'reference_number': order_reference,
                'picklist_quantity': pick_qty,
                'reserved_quantity': pick_qty,
                'picked_quantity': pick_qty,
                'order_type': order['order_type'],
                'pick_type': 'auto_picking',
                'status': 'picked',
                'account_id': warehouse.userprofile.id,
                'json_data': {
                    'generated_by' : user.username,
                    'picked_lpn' : stock['lpn_number'],
                    'confirmation_time' : picked_time,
                    'picker' : user.username,
                },
                'reference_model': 'OrderDetail',
                'picklist_number' : picklist_number,
            }
            picklist_objects_list.append(Picklist(**picklist_data))

    OrderDetail.objects.filter(order_reference=order_reference, user=warehouse.id).update(status=5, quantity=0)
    Order.objects.filter(order_reference=order_reference, warehouse_id=warehouse.id).update(status=5)
    picklist_data = Picklist.objects.bulk_create_with_rounding(picklist_objects_list)
    
    return picklist_data, customer_ref, picklist_number

def insert_sos_data(picklist_data):
    sos_objects_list = []
    financial_year = get_financial_year(datetime.now())
    for picklist in picklist_data:
        sos_data = {
            'order_id' : picklist.order_id,
            'picklist_id' : picklist.id,
            'order_status_flag' : 'processed_orders',
            'quantity' : picklist.picked_quantity,
            'account_id' : picklist.account_id,
            'financial_year': financial_year,
        }
        sos_objects_list.append(SellerOrderSummary(**sos_data))
        
    sos_data = SellerOrderSummary.objects.bulk_create_with_rounding(sos_objects_list)
    sos_ids = [sos.id for sos in sos_data]
    sos_detail_data = SellerOrderSummary.objects.select_related('order','picklist__stock__batch_detail', 'picklist__location').filter(id__in=sos_ids)
    return sos_detail_data

def insert_staging_stock(warehouse, sos_detail_data):
    staging_stock_objects_list, packing_data_dict, lpn_numbers = [], defaultdict(list), set()
    zone_filters = {
        'status' : 1, 
        'zone__user' : warehouse.id, 
        'zone__segregation' : 'outbound_staging', 
        'zone__storage_type' : 'pre_invoice'
    }
    location_id = LocationMaster.objects.filter(**zone_filters).values_list('id', flat=True).order_by('fill_sequence').first()
    if not location_id:
        raise Exception("Location not Found")
    for sos_object in sos_detail_data:
        picklist_object = sos_object.picklist
        order_object = sos_object.order
        stock_object = picklist_object.stock
        batch_detail = stock_object.batch_detail
        manufactured_date, expiry_date, batch_detail_dict = None, None, {}
        if batch_detail:
            timezone = get_user_time_zone(warehouse)
            try:
                # get user timezone (set in django middleware)
                manufactured_date = get_local_date_known_timezone(timezone, batch_detail.manufactured_date, send_date=True).strftime(DATEFORMAT)
            except Exception:
                pass
            try:
                expiry_date = get_local_date_known_timezone(timezone, batch_detail.expiry_date, send_date=True).strftime(DATEFORMAT)
            except Exception:
                pass
            batch_detail_dict = {
                "batch_number": batch_detail.batch_no,
                "batch_reference": batch_detail.batch_reference,
                "expiry_date": expiry_date,
                "manufactured_date": manufactured_date,
            }
        staging_stock = {
            'receipt_number': sos_object.id,
            'receipt_type': 'so_picking',
            'sku_id': picklist_object.sku.id,
            'batch_detail_id': stock_object.batch_detail_id,
            'quantity': sos_object.quantity,
            'original_quantity': sos_object.quantity,
            'grn_number': order_object.order_reference,
            'status': stock_object.status,
            'transact_number': picklist_object.picklist_number,
            'location_id' : location_id,
            'receipt_date': datetime.now(),
            'json_data': {
                'picklist_id': str(picklist_object.id)
            },
            'account_id': picklist_object.account_id,
        }
        
        picklist_json_data = picklist_object.json_data
        lpn_number = picklist_json_data.get('picked_lpn')
        if lpn_number:
            staging_stock['lpn_number'] = lpn_number
            staging_stock['json_data']['picked_lpn'] = True
            packing_data = {
                'transaction_id': sos_object.id,
                'sku_code': picklist_object.sku.sku_code,
                'sku_description': picklist_object.sku.sku_desc,
                'location' : picklist_object.location.location if picklist_object.location else '',
                'mrp': order_object.mrp,
                'unit_price': order_object.unit_price,
                'batch_details': batch_detail_dict,
                'json_data': {
                    'pack_uom_quantity': 1
                },
                'packed_quantity' : sos_object.quantity, 
            }
            lpn_numbers.add(lpn_number)
            packing_data_dict[lpn_number].append(packing_data)
        staging_stock_objects_list.append(StockDetail(**staging_stock))
    StockDetail.objects.bulk_create_with_rounding(staging_stock_objects_list)
    return packing_data_dict, lpn_numbers, location_id
    
def create_packing_data(user, warehouse, picklist_number, customer_ref, lpn_pack_details, extra_params):
    from outbound.views.picklist.picklist_confirmation import create_lpn_based_packing
    scan_lpn_payload = {
            "warehouse": warehouse.username,
            "lpn_numbers": list(lpn_pack_details.keys()),
            "transaction_number": str(picklist_number),
            "transaction_type": "so_packing",
            "json_data": {
                "created_by": user.username
            },
            "customer_reference": customer_ref,
            "user": user.username,
            "allow_multiple_transactions" : True
        }
    
    packing_service_instance = PackingService(extra_params, user, warehouse)
    scan_lpn_response, packing_service_errors = packing_service_instance.scan_lpn(scan_lpn_payload)
    log.info(f"Scan LPN response: {scan_lpn_response}")
    if packing_service_errors:
        raise Exception(str(packing_service_errors))
    if scan_lpn_response:
        extra_params['headers'] = extra_params['request_headers']
        create_lpn_based_packing.apply_async(args = [warehouse.username, user.username, picklist_number, lpn_pack_details, customer_ref, extra_params, 'auto_pick'])
    else:
        raise Exception(str(scan_lpn_response.get('message', 'Error')))
    
def drop_lpns(warehouse, location_id, lpn_numbers, picklist_number, order_reference):
    """
    Drop LPNS for the given LPNS
    
    Args:
        warehouse : User
        location_id : str
        lpn_numbers : list
        picklist_number : str
        order_reference : str
        
    Returns:
        None
    """
    staging_info_objs = []
    for lpn_number in lpn_numbers:
        staging_data = {
            'account_id' : warehouse.userprofile.id,
            'user_id' : warehouse.id,
            'location_id': location_id,
            'segregation': 'outbound_staging',
            'carton_no': lpn_number,
            'order_reference': order_reference,
            'invoice_number': '',
            'picklist_number': picklist_number,
        }
        staging_info_objs.append(StagingInfo(**staging_data))
    
    StagingInfo.objects.bulk_create_with_rounding(staging_info_objs)

def generate_picklist_with_stock(request_user_id, warehouse_id, order_reference, picklist_data):
    
    from outbound.views.picklist.main import SOPicklistSet
    request_data = {
        "order_references" : [order_reference],
        "pick_type" : "pack_while_pick",
        "items" : list()
    }
    order_data = list(OrderDetail.objects.filter(user=warehouse_id, order_reference=order_reference, status=1).values('id', 'sku__sku_code', 'line_reference', 'quantity'))
    order_item_mapping = { (order['sku__sku_code'], order['line_reference']) : order for order in order_data }
    for item_key, stock_data in picklist_data.items():
        order = order_item_mapping.get(item_key)
        if not order:
            continue
        item = {
            "id" : order['id'],
            "line_reference" : order['line_reference'],
            "order_reference" : order_reference,
            "quantity" : order['quantity'],
            "batch_details" : list()
        }
        for stock in stock_data:
            item['batch_details'].append({
                "batch_number" : stock.get('batch_number'),
                "quantity" : stock.get('quantity'),
                "lpn_number" : stock.get('lpn_number'),
                "location" : stock.get('location'),
            })
        request_data['items'].append(item)
    
    sos_picklist_set = SOPicklistSet()
    sos_picklist_set.prepare_and_generate_picklist_process(request_data, warehouse_id=warehouse_id, request_user_id=request_user_id)
    
