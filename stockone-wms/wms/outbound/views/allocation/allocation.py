#package imports
import traceback
from collections import defaultdict
from copy import deepcopy
from datetime import datetime, timedelta
from time import time
import ast
import pandas as pd

#django imports
from django.db.models import F, Case, When, IntegerField, Sum
from django.utils import timezone
from django.db import transaction
from django.core.cache import cache

#outbound imports
from outbound.models import StockAllocation, OrderTypeZoneMapping, OrderDetail, Picklist
from outbound.views.picklist.picklist_generation import PicklistMixin
from outbound.views.orders.utils import validate_transaction_lock
from outbound.views.allocation.constants import ALLOCATION_TYPES, MANDATORY_FIELDS, ITEMS_MANDATORY_FIELDS
from outbound.views.picklist.helpers import get_picklist_configurations, get_misc_options_list

#core operations imports
from core_operations.views.common.main import (
    init_logger, WMSListView
)
from core_operations.views.common.validation import update_order_header_status
from core_operations.views.integration.integration import webhook_integration_3p
from core.models import SKUMaster

#wms base imports
from wms_base.models import User

#inventory imports
from inventory.models import StockDetail, CycleCount

log = init_logger('logs/common_allocation.log')

class AllocationProcess(PicklistMixin):
    def __init__(self, user_id, warehouse_id, user, warehouse, request_data, extra_params=None):
        self.request_data = request_data
        self.extra_params = extra_params or {}
        self.user = user or User.objects.get(id=user_id)
        self.warehouse = warehouse or User.objects.get(id=warehouse_id)
        self.pick_type = ''
        self.errors = []
        self.skip_allocation = False
        
        self.batch_size = 500
        self.current_date = pd.Timestamp(datetime.now(timezone.utc))
        self.get_misc_values()
        self.get_decimal_limit()
    
    def process_allocation(self):
        """
        This method is used to allocate the orders.
        """
        try:
            log.info("Allocation Started for warehouse %s" % self.warehouse.username)
            self.validate_and_prepare_request_data()
            if self.errors:
                return {'errors': self.errors}
            log.info('Allocation Request Data for warehouse %s by user %s, params %s' % (self.warehouse.username, self.user.username, self.reference_numbers))
            self.allocate()
            
            response_data = {
                'errors': self.errors,
                'allocated_orders': list(self.allocated_orders),
                'reference_allocation_details': self.reference_id_picklist_details,
                'reference_ids': self.reference_ids,
            }
            
            return response_data
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('Error in AllocationSet post method: %s' % str(e))
            return {'errors': ['Allocation Failed']}
        
    def validate_and_prepare_request_data(self):
        """
        This method is used to prepare the request data.
        """
        self.reference_order_type_mapping, self.reference_numbers, self.allocated_orders, order_types = {}, set(), set(), set()
        self.allocation_data = self.request_data.get('data', [])
        self.allocation_type = self.request_data.get('allocation_type')
        
        if not self.allocation_data:
            self.errors.append('Allocation Data is mandatory')
        
        if self.allocation_type not in ALLOCATION_TYPES:
            self.errors.append('Invalid Allocation Type')
        
        for data in self.allocation_data:
            for field in MANDATORY_FIELDS:
                if not data.get(field):
                    self.errors.append('%s is mandatory' % field)
                if field == 'order_type':
                    order_types.add(data.get(field))
                elif field == 'order_reference':
                    self.reference_numbers.add(data['reference_number'])
            if not self.errors:
                for item in data['items']:
                    for field in ITEMS_MANDATORY_FIELDS:
                        if item.get(field) is None or not str(item.get(field)):
                            self.errors.append('%s is mandatory in Items' % field)
        
        if len(order_types) > 1:
            self.errors.append('Multiple Order Types are not allowed')
    
    def allocate(self):
        """
        This method is used to allocate the orders.
        """
        self.reference_id_picklist_details, self.reference_ids, self.processed_order_ids = {}, set(), set()
        extra_params = deepcopy(self.extra_params)
        extra_params.update(extra_params.pop('switch_values', {}))
        if self.allocation_data:
            extra_params['reference_model'] = self.allocation_data[0].get('items', [{}])[0].get('reference_model', '')
        restricted_orders_dict = validate_transaction_lock(self.warehouse, list(self.reference_numbers), extra_params=extra_params, transaction = 'picklist')
        
        # restrict hold/expired orders for picklist generation, if not wave picklist
        if restricted_orders_dict:
            self.errors.extend(list(restricted_orders_dict.values()))
            return
        
        self.allocation_id = int(time())
        self.allocation_objects_list, self.pick_sku_codes, self.sku_zones_dict = [], set(), defaultdict(list)
        
        self.prepare_stock_filter(self.allocation_data)
        if not self.get_stock_details():
            self.errors.append('Insufficient stock. Cannot process the picklist')
            return
        self.assign_stock_and_prepare_picklist(self.allocation_data, allocation=True)
        
        self.insert_allocation_details()
        
        self.allocation_callback()

    def prepare_picklist_data(self, item, stock, quantity, is_combo_sku: bool = False, combo_sku_qty: float = 0):
        """
        This method is used to prepare the picklist data.
        """
        self.prepare_allocation_data(item, stock, quantity, is_combo_sku, combo_sku_qty)
    
    def prepare_allocation_data(self, item, stock, quantity, is_combo_sku: bool = False, combo_sku_qty: float = 0):
        """
        This method is used to prepare the allocation data.
        """
        picklist_json_data = self.prepare_picklist_json_data(item)
        reference_id = item.get('id')
        self.pick_sku_codes.add(stock['sku_code'])
        if stock['sku_code'] not in self.sku_zones_dict:
            self.sku_zones_dict[stock['zone_id']].append(stock['sku_code'])
        
        picklist_json_data.update({
            'generated_by': self.user.username,
            'sku_id': item.get('sku_id'),
            'location_id': stock.get('location_id'),
            'zone' : stock.get('zone'),
            'sub_zone' : stock.get('sub_zone'),
            'order_type' : item.get('order_type'),
            'allocation_type' : self.allocation_type,
            'pack_uom_quantity' : item.get('pack_uom_quantity'),
            'combo_sku_qty' : combo_sku_qty,
        })
        if self.extra_params.get('wave_reference'):
            picklist_json_data['wave_reference'] = self.extra_params.get('wave_reference')
        if self.decimal_limit:
            quantity = round(quantity, self.decimal_limit)

        if quantity == 0:return

        allocation_data = {
            'allocation_id' : self.allocation_id,
            'warehouse_id': self.warehouse.id,
            'reference_id': reference_id,
            'reference_number': self.reference_number,
            'quantity': quantity,
            'status': 1,
            'account_id': self.warehouse.userprofile.id,
            'json_data': picklist_json_data,
            'reference_model': item.get('reference_model', ''),
            'stock_id' : stock.get('id'),
        }

        self.allocation_objects_list.append(StockAllocation(**allocation_data))
        self.prepare_reference_qty_dict(item, quantity, is_combo_sku, combo_sku_qty)

    def insert_allocation_details(self):
        """
        This method is used to insert the allocation details.
        """
        allocated_objs = StockAllocation.objects.bulk_create_with_rounding(self.allocation_objects_list, batch_size=self.batch_size)
        for obj in allocated_objs:
            self.allocated_orders.add(obj.reference_number)
    
    def allocation_callback(self):
        """
        This method is used to call the allocation callback.
        """
        if not self.allocated_orders:
            return
        filters = {
            "order_references" : list(self.allocated_orders),
            "sku_codes" : list(self.pick_sku_codes),
            "zones_data" : self.sku_zones_dict
        }
        webhook_integration_3p(self.warehouse.id, "allocation", filters=filters)
    
    def deallocation(self):
        """
        This method is used for deallocation.
        """
        reference_numbers = self.request_data.get('order_references')
        reference_model = self.request_data.get('reference_model', 'OrderDetail')
        reference_details, error = self.fetch_and_update_allocation_details(reference_numbers, reference_model)
        response = {
            'errors': [error],
            'message': 'Deallocation Failed' if error else 'Deallocation Successful',
            'reference_details' : reference_details,
            'user' : self.user.username
        }
        
        return response, []
    
    def fetch_and_update_allocation_details(self, reference_numbers, reference_model):
        """
        This method is used to get the allocated details.
        """
        filters = {
            'warehouse_id' : self.warehouse.id,
            'reference_number__in' : reference_numbers,
            'status' : 1,
            'reference_model' : reference_model
        }
        allocated_data = dict(StockAllocation.objects.filter(**filters).values_list('reference_id').annotate(Sum('quantity')))
        if not allocated_data:
            return {}, 'No Allocated Orders Found'
        
        self.update_allocation_details(filters)
        return allocated_data, ''
        
    def update_allocation_details(self, filters):
        """
        This method is used to update the allocation details.
        """
        StockAllocation.objects.filter(**filters).update(status=3, updation_date = datetime.now(), updated_by_id=self.user.id)

class ConstrainedAllocation(WMSListView):
    """
    Constrained Allocation class to handle the distribution of inventory to stores
    in scenarios where inventory is limited (constrained), following the business rules.
    """
    
    def __init__(self, order_types, user, warehouse, wave_reference, extra_params=None):
        """
        Initializes the Constrained Allocation process with necessary parameters.
        
        :param order_types: List of order types that need to be allocated.
        :param user: User who is initiating the allocation process.
        :param warehouse: Warehouse where the allocation is to be done.
        :param wave_reference: Reference identifier for the wave of orders.
        :param extra_params: Additional parameters like batch size or configurations.
        """
        self.order_types = order_types
        self.user = user
        self.warehouse = warehouse
        self.wave_reference = wave_reference
        self.extra_params = extra_params or {}
        self.allocation_id = int(time())
        self.batch_size = self.extra_params.get('batch_size') or 500
        self.full_open_order = self.extra_params.get('full_open_order')
        self.order_date = self.extra_params.get('order_date')
        self.allocated_orders, self.pick_sku_codes, self.sku_zones_dict = set(), set(), defaultdict(list)
        
        self.cycle_count_stock = set()
        
        self.get_misc_values()
    
    def get_misc_values(self):
        """
        Retrieves all the picklist configuration values for this warehouse.
        
        This includes settings like MRP-based picking, sales UOM, cycle count, and more.
        """
        self.switch_values = get_picklist_configurations(self.warehouse)

        self.mrp_based_picking = []
        if self.switch_values.get('mrp_based_picking'):
            self.mrp_based_picking = self.switch_values.get('mrp_based_picking') or []

        self.enable_sales_uom = False
        if self.switch_values.get('enable_sales_uom') not in ['', None, 'false', False]:
            self.enable_sales_uom = True

        self.restrict_stock_cycle_count_creation_types = self.switch_values.get('restrict_picklist_on_cycle_count_creation_options', '').split(',')
        self.restrict_stock_cycle_count_pending_approval_types = self.switch_values.get('restrict_picklist_on_cycle_count_pending_approval_options', '').split(',')
        
        self.block_expired_batches_picklist = False
        if self.switch_values.get('block_expired_batches_picklist', 'false') not in ['false', False, '', None]:
            self.block_expired_batches_picklist = True
        
        self.allocation_chunk_size = self.switch_values.get('allocation_chunk_size') or 100

        misc_options_list = ['FIFO','FEFO','LEFO','LIFO','Get Sequence','customer_shelf_life']
        self.stock_selection_strategy_ordertypes = get_misc_options_list(misc_options_list, self.warehouse)
        self.customer_shelf_life_ordertypes = self.stock_selection_strategy_ordertypes.pop('customer_shelf_life', [])
        if self.stock_selection_strategy_ordertypes:
            for key, value in self.stock_selection_strategy_ordertypes.items():
                self.stock_selection_strategy_ordertypes[key] = ast.literal_eval(value[0])

    def allocate(self):
        """
        Main allocation function that processes the entire allocation flow for the given warehouse and order types.
        Follows the constrained allocation logic and handles prioritization and inventory allocation.
        """
        full_allocation_start_time = time()
        log.info("Constrained Allocation started for warehouse %s" % (str(self.warehouse.username)))
        self.get_order_type_priority()
        try:
            cache_status = cache.add('Picklist_Generation' + str(self.warehouse.username), "True", timeout=60*60)
            if not cache_status:
                log.info("Order is already in progress for warehouse %s" % (str(self.warehouse.username)))
                return
            for order_type in self.order_types:
                order_type_allocation_start_time = time()
                log.info("Constrained Allocation started for order_type %s" % (str(order_type)))
                self.prepare_order_filters(order_type)
                self.fetch_unique_skus(order_type)
                self.sku_shelf_life = {}
                if order_type.lower() in self.customer_shelf_life_ordertypes or 'all' in self.customer_shelf_life_ordertypes:
                    self.fetch_customer_shelf_life()
                if self.restrict_stock_cycle_count_creation_types or self.restrict_stock_cycle_count_pending_approval_types:
                    self.fetch_cycle_count_data()
                for sku_range in range(0, len(self.total_skus), self.allocation_chunk_size):
                    sku_chunk_allocation_start_time = time()
                    self.process_skus = self.total_skus[sku_range:sku_range+self.allocation_chunk_size]
                    self.final_allocate_data = {}
                    self.fetch_reserved_data()
                    self.fetch_stock_data()
                    if not self.final_stock_data:
                        continue
                    self.fetch_order_data()
                    self.prepare_allocation_data()
                    self.assing_stock_and_prepare_allocation_objects(order_type)
                    self.insert_allocation_details()
                    log.info("Constrained Allocation Completed for order type %s for %s skus, time taken is %s" % (str(order_type), str(len(self.process_skus)), str(time()-sku_chunk_allocation_start_time)))
                log.info("Constrained Allocation completed for order_type %s, time taken is %s" % (str(order_type), str(time()-order_type_allocation_start_time)))
            self.allocation_callback()
            log.info("Constrained Allocation completed for warehouse %s, time taken is %s" % (str(self.warehouse.username), str(time()-full_allocation_start_time)))
            cache.delete('Picklist_Generation' + str(self.warehouse.username))
        except Exception as e:
            log.debug(traceback.format_exc())
            log.info('Error in Constrained Allocation for warehouse %s, order_types %s: %s' % (str(self.warehouse.username), str(self.order_types), str(e)))
            cache.delete('Picklist_Generation' + str(self.warehouse.username))
        
    def get_order_type_priority(self):
        """
        Retrieves the priority of the order types for the warehouse.
        
        The order types are ordered by priority to ensure the constrained allocation follows the correct order.
        """
        self.order_types = list(OrderTypeZoneMapping.objects.filter(user_id=self.warehouse.id, order_type__in=self.order_types, status=1).values_list('order_type',flat=True).distinct().order_by('order_type_priority'))
    
    def prepare_order_filters(self, order_type):
        """
        Prepares the filters for the order types.
        """
        self.order_filters = {
            'order_type': order_type,
            'user': self.warehouse.id
        }
        if self.order_date:
            self.order_filters['creation_date__gte'] = self.order_date
        if self.full_open_order:
            self.order_refs = list(OrderDetail.objects.filter(**self.order_filters).values('order_reference').\
            annotate(total_qty = Sum('original_quantity'), open_cancelled_qty=Sum('quantity') + Sum('cancelled_quantity')).\
            filter(total_qty=F('open_cancelled_qty')).values_list('order_reference', flat=True).distinct())
            self.order_filters['order_reference__in'] = self.order_refs
        self.order_filters['status'] = 1
        log.info("Order filters for order_type %s: %s" % (str(order_type), str(self.order_filters)))
    
    def fetch_unique_skus(self, order_type):
        """
        Fetches the unique SKUs associated with the given order type.
        
        This helps in determining which SKUs need to be allocated.
        """
        order_data = list(OrderDetail.objects.filter(**self.order_filters).values_list('sku_id', 'order_reference').distinct())
        self.total_skus, order_refs = set(), set()
        for sku_id, order_reference in order_data:
            self.total_skus.add(sku_id)
            order_refs.add(order_reference)
        self.total_skus = list(self.total_skus)
        
        log.info("Orders fetched for order_type %s: %s" % (str(order_type), str(order_refs)))
        
    def fetch_customer_shelf_life(self):
        """
        Fetches the customer shelf life for the SKUs.
        
        This helps in determining the expiry date for the SKUs.
        """
        self.sku_shelf_life = dict(SKUMaster.objects.filter(user=self.warehouse.id, id__in=self.total_skus, customer_shelf_life__gt=timedelta(days=0), status=1).values_list('id', 'customer_shelf_life'))
        current_date = datetime.now(timezone.utc)
        for sku_id, customer_shelf_life in self.sku_shelf_life.items():
            shelf_life_filter = current_date + customer_shelf_life
            timestamp = datetime.fromisoformat(str(shelf_life_filter))
            shelf_life_filter = timestamp.astimezone(timezone.utc)
            self.sku_shelf_life[sku_id] = shelf_life_filter
        
    def fetch_cycle_count_data(self):
        '''
        Fetch cycle count records based on the run type filters.
        
        This helps in excluding the SKUs that are part of cycle count records from the allocation process.
        '''
        restrict_stock_cycle_count_creation_types,  restrict_stock_cycle_count_pending_approval_types = set(), set()

        cycle_type_run_type_mapping = {
            'short_cycle_count': ['short pick'],
            'scheduled_cycle_count': ['scheduled', 'Scheduled'],
            'unscheduled_cycle_count': ['unscheduled', 'Unscheduled', 'UnScheduled'],
            'audit_cycle_count': ['Audit'],
            'adhoc_cycle_count': ['adhoc'],
        }

        # Get cycle count run types based on the restrict options
        for cycle_type in self.restrict_stock_cycle_count_creation_types:
            if cycle_type not in cycle_type_run_type_mapping:
                continue
            restrict_stock_cycle_count_creation_types.update(cycle_type_run_type_mapping[cycle_type])
        
        for cycle_type in self.restrict_stock_cycle_count_pending_approval_types:
            if cycle_type not in cycle_type_run_type_mapping:
                continue
            restrict_stock_cycle_count_pending_approval_types.update(cycle_type_run_type_mapping[cycle_type])

        if not (restrict_stock_cycle_count_creation_types or restrict_stock_cycle_count_pending_approval_types):
            return

        # Fetch cycle count records based on run type filters
        if restrict_stock_cycle_count_creation_types:
            self.cycle_count_stock = set(CycleCount.objects
                .filter(run_type__in= restrict_stock_cycle_count_creation_types, sku_id__in=self.total_skus, status__in= [1,2])
                .values_list('sku_id', 'location_id')
                .annotate(batch_id=Case(When(batch_detail__isnull=False,then=F('batch_detail_id')), default=0, output_field=IntegerField()))
                .distinct()
            )
        
        if restrict_stock_cycle_count_pending_approval_types:
            self.cycle_count_stock.update(set(CycleCount.objects
                .filter(run_type__in= restrict_stock_cycle_count_pending_approval_types, sku_id__in=self.total_skus, status=2)
                .values_list('sku_id', 'location_id')
                .annotate(batch_id=Case(When(batch_detail__isnull=False,then=F('batch_detail_id')), default=0, output_field=IntegerField()))
                .distinct()
            ))

    def fetch_reserved_data(self):
        """
        Fetches the reserved and allocated data for the SKUs.
        
        This helps in determining the available stock for allocation.
        """
        self.reserved_data = dict(Picklist.objects.filter(sku_id__in=self.process_skus, user_id=self.warehouse.id, status='open').values_list('stock_id').annotate(reserved_quantity = Sum('reserved_quantity')))
        self.allocated_data = dict(StockAllocation.objects.filter(stock__sku_id__in=self.process_skus, warehouse_id=self.warehouse.id, status=1).values_list('stock_id').annotate(allocated_quantity = Sum('quantity')))
        
    def fetch_stock_data(self):
        """
        Fetches the stock data for the SKUs.
        
        This helps in determining the available stock for allocation.
        """
        stock_filters = {
            'sku__user' : self.warehouse.id,
            'sku_id__in' : self.process_skus,
            'quantity__gt' : 0,
            'location__zone__segregation' : 'sellable',
            'location__status' : 1
        }
        if self.block_expired_batches_picklist:
            stock_filters['batch_detail__expiry_date__gte'] = timezone.now()
        values_list = ['id', 'sku_id', 'location_id', 'quantity', 'batch_detail_id', 'batch_detail__expiry_date', 'location__zone__zone', 'location__sub_zone__zone', 'location__zone_id', 'sku__sku_code']
        stock_data = StockDetail.objects.filter(**stock_filters).values(*values_list)
        self.final_stock_data, self.stock_data_dict = defaultdict(int), defaultdict(list)
        for stock in stock_data.iterator():
            stock_key = (stock['sku_id'], stock['location_id'], stock['batch_detail_id'] or 0)
            if stock_key in self.cycle_count_stock:
                continue
            shelf_life = self.sku_shelf_life.get(stock['sku_id'])
            if shelf_life and stock['batch_detail__expiry_date'] and stock['batch_detail__expiry_date'] <= shelf_life:
                continue
            reserved_quantity = self.reserved_data.get(stock_key, 0)
            allocated_quantity = self.allocated_data.get(stock['id'], 0)
            stock['quantity'] = max(stock['quantity'] - reserved_quantity - allocated_quantity, 0)
            self.stock_data_dict[stock['sku_id']].append(stock)
            self.final_stock_data[stock['sku_id']] += stock['quantity']
        
        self.process_skus = set(self.final_stock_data.keys())
    
    def fetch_order_data(self):
        """
        Fetches the order data for the SKUs.
        Prepares the order-wise and SKU-wise store quantity data for allocation.
        """
        order_data = OrderDetail.objects.filter(**self.order_filters, sku_id__in=self.process_skus).values('id', 'sku_id', 'order_reference','customer_identifier_id', 'quantity','json_data').order_by('id')
        order_references = {order['order_reference'] for order in order_data}
        extra_params = deepcopy(self.switch_values)
        extra_params['reference_model'] = 'OrderDetail'
        restricted_orders_dict = validate_transaction_lock(self.warehouse, order_references, extra_params=self.extra_params, transaction = 'picklist')
        self.order_wise_store_data, self.order_qty_dict, self.order_data_dict, self.order_id_mapping = defaultdict(dict), defaultdict(int), defaultdict(dict), {}
        for order in order_data.iterator():
            order_reference = order['order_reference']
            if order_reference in restricted_orders_dict:
                log.info("Order %s is restricted for allocation: %s" % (order_reference, restricted_orders_dict[order_reference]))
                continue
            sku_id = order['sku_id']
            customer_id = order['customer_identifier_id']
            json_data = order['json_data'] or {}
            if not int(json_data.get('target_stock', 0)):
                continue
            self.order_wise_store_data[(order_reference, sku_id, customer_id)]['current_stock'] = int(json_data.get('current_stock', 0) + json_data.get('intransit_stock', 0))
            self.order_wise_store_data[(order_reference, sku_id, customer_id)]['target_stock'] = int(json_data.get('target_stock', 0))
            self.order_qty_dict[sku_id] += order['quantity']
            self.order_data_dict.setdefault(sku_id,{
                'quantity': 0,
                'orders': [],
                'customers': set()
            })
            self.order_data_dict[sku_id]['quantity'] += order['quantity']
            self.order_data_dict[sku_id]['orders'].append(order)
            self.order_data_dict[sku_id]['customers'].add(customer_id)
            self.order_data_dict[sku_id].setdefault(customer_id, {}).setdefault('orders', []).append(order)
            self.order_id_mapping[order['id']] = order
        
        #Preparing SKU Wise and Customer Wise Store stock data
        self.sku_wise_store_data, self.customer_sku_wise_store_data = defaultdict(dict), defaultdict(dict)
        for (order_reference, sku_id, customer_id), store_qty in self.order_wise_store_data.items():
            self.sku_wise_store_data[sku_id].setdefault('current_stock', 0)
            self.sku_wise_store_data[sku_id].setdefault('target_stock', 0)
            self.customer_sku_wise_store_data[(customer_id, sku_id)].setdefault('current_stock', 0)
            self.customer_sku_wise_store_data[(customer_id, sku_id)].setdefault('target_stock', 0)
            
            self.sku_wise_store_data[sku_id]['current_stock'] += store_qty['current_stock']
            self.sku_wise_store_data[sku_id]['target_stock'] += store_qty['target_stock']
            
            self.customer_sku_wise_store_data[(customer_id, sku_id)]['current_stock'] += store_qty['current_stock']
            self.customer_sku_wise_store_data[(customer_id, sku_id)]['target_stock'] += store_qty['target_stock']
    
    def prepare_allocation_data(self):
        """
        Calculates the allocation data for the orders based on the stock availability and store requirements
        Prepares Final allocation quantity for each order.
        """
        self.full_allocated_orders = set()
        for sku_id, sku_order_data in self.order_data_dict.items():
            sku_order_qty = self.order_qty_dict[sku_id]
            avl_qty = self.final_stock_data.get(sku_id) or 0
            if not avl_qty:
                continue
            if avl_qty>sku_order_qty:
                for order in sku_order_data['orders']:
                    self.final_allocate_data[order['id']] = order['quantity']
                    self.full_allocated_orders.add(order['id'])
                continue
            coal = 0
            while True:
                prev_coal = coal
                coal = int(((self.sku_wise_store_data[sku_id]['current_stock'] + avl_qty)/self.sku_wise_store_data[sku_id]['target_stock'])*100)
                if prev_coal and abs(coal-prev_coal)<1:
                    break
                break_flag=True
                customers_list = set()
                for customer_id in sku_order_data['customers']:
                    stal = int(((self.customer_sku_wise_store_data[(customer_id,sku_id)]['current_stock'])/self.customer_sku_wise_store_data[(customer_id, sku_id)]['target_stock'])*100)
                    if stal>=coal:
                        self.sku_wise_store_data[sku_id]['current_stock'] -= self.customer_sku_wise_store_data[(customer_id, sku_id)]['current_stock']
                        self.sku_wise_store_data[sku_id]['target_stock'] -= self.customer_sku_wise_store_data[(customer_id, sku_id)]['target_stock']
                        break_flag=False
                        continue
                    customers_list.add(customer_id)
                if break_flag or self.sku_wise_store_data[sku_id]['target_stock'] == 0:
                    break
                sku_order_data['customers'] = customers_list
            
            for customer_id in sku_order_data['customers']:
                stal = int(((self.customer_sku_wise_store_data[(customer_id,sku_id)]['current_stock'])/self.customer_sku_wise_store_data[(customer_id, sku_id)]['target_stock'])*100)
                allocation_gap = max(int(((coal - stal)/100) * self.customer_sku_wise_store_data[(customer_id, sku_id)]['target_stock']), 0)
                for order in sku_order_data[(customer_id)]['orders']:
                    required_qty = max(self.order_wise_store_data[(order['order_reference'], sku_id, customer_id)]['target_stock'] - self.order_wise_store_data[(order['order_reference'], sku_id, customer_id)]['current_stock'], 0)
                    allocate_qty = min(allocation_gap, avl_qty, required_qty, order['quantity'])
                    self.final_allocate_data[order['id']] = allocate_qty
                    avl_qty -= allocate_qty
                    allocation_gap -= allocate_qty
                    required_qty -= allocate_qty
                    if avl_qty<=0 or allocation_gap<=0 or required_qty<=0:
                        break
                
                if avl_qty<=0:
                    break

    def assing_stock_and_prepare_allocation_objects(self, order_type):
        """
        Assigns the stock to the orders and prepares the allocation objects for the orders
        Updated the order details with the allocated quantity and status
        """
        self.allocation_objects_list, self.order_updation_objects_list, self.updated_orders = [], [], set()
        self.order_wise_allocated_qty, self.order_id_qty_dict = defaultdict(int), defaultdict(int)
        for order_id, allocate_quantity in self.final_allocate_data.items():
            order_data = self.order_id_mapping[order_id]
            sku_id = order_data['sku_id']
            stock_data = self.stock_data_dict[sku_id]
            for stock in stock_data:
                if not allocate_quantity:
                    break
                quantity = max(min(allocate_quantity, stock['quantity']), 0)
                stock['quantity'] -= quantity
                allocate_quantity -= quantity
                if not quantity:
                    continue
                allocation_json_data = {
                    'generated_by': self.user.username,
                    'sku_id': sku_id,
                    'location_id': stock['location_id'],
                    'zone' : stock['location__zone__zone'],
                    'sub_zone' : stock['location__sub_zone__zone'],
                    'order_type' : order_type,
                    'allocation_type' : 'constrained_allocation',
                    'pack_uom_quantity' : 1,
                }
                if self.wave_reference:
                    allocation_json_data['wave_reference'] = self.wave_reference
                allocation_data = {
                    'allocation_id' : self.allocation_id,
                    'warehouse_id': self.warehouse.id,
                    'reference_id': order_id,
                    'reference_number': order_data['order_reference'],
                    'quantity': quantity,
                    'status': 1,
                    'account_id': self.warehouse.userprofile.id,
                    'json_data': allocation_json_data,
                    'reference_model': 'OrderDetail',
                    'stock_id' : stock['id'],
                }

                self.allocation_objects_list.append(StockAllocation(**allocation_data))
                self.order_id_qty_dict[order_id] += quantity
                self.pick_sku_codes.add(stock['sku__sku_code'])
                if stock['sku__sku_code'] not in self.sku_zones_dict:
                    self.sku_zones_dict[stock['location__zone_id']].append(stock['sku__sku_code'])
                self.allocated_orders.add(order_data['order_reference'])
        
        if not self.order_id_qty_dict:
            return
        
        order_objects = OrderDetail.objects.filter(id__in=self.order_id_qty_dict, user=self.warehouse.id)
        for order in order_objects.iterator():
            allocated_qty = self.order_id_qty_dict[order.id]
            order.quantity = max(order.quantity - allocated_qty, 0)
            self.order_wise_allocated_qty[order.order_reference] += allocated_qty
            current_stock = self.order_wise_store_data[(order.order_reference, order.sku_id, order.customer_identifier_id)]['current_stock'] + self.order_id_qty_dict[order.id]
            final_coverage = (current_stock/self.order_wise_store_data[(order.order_reference, order.sku_id, order.customer_identifier_id)]['target_stock'])*100
            if not order.quantity:
                order.status = 20
                self.updated_orders.add(order.order_reference)
            json_data = order.json_data or {}
            json_data['final_coverage'] = final_coverage
            if order.id in self.full_allocated_orders:
                json_data['full_inventory'] = True
            order.json_data = json_data
            self.order_updation_objects_list.append(order)
    
    def insert_allocation_details(self):
        """
        Inserts the allocation details in the database.
        Updates the order details with the allocated quantity and status.
        Updates the order header status based on the updated order details.
        """
        with transaction.atomic('default'):
            StockAllocation.objects.bulk_create_with_rounding(self.allocation_objects_list, batch_size=self.batch_size)
            OrderDetail.objects.bulk_update_with_rounding(self.order_updation_objects_list, ['quantity', 'status', 'json_data'], batch_size=self.batch_size)
            
            if self.updated_orders:
                extra_params = {"allocation_details": self.order_wise_allocated_qty}
                update_order_header_status(self.warehouse, self.updated_orders, extra_params=extra_params)
    
    def allocation_callback(self):
        """
        Calls the allocation callback for the orders.
        """
        if not self.allocated_orders:
            return
        filters = {
            "order_references" : list(self.allocated_orders),
            "sku_codes" : list(self.pick_sku_codes),
            "zones_data" : self.sku_zones_dict
        }
        webhook_integration_3p(self.warehouse.id, "allocation", filters=filters)
