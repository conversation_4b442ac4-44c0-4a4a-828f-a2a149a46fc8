Boolean checkApprover(String approver, String approversList) {
    def names =  approversList.split(",")
    return names.contains(approver)
}

def getDeploymentInfo(key) {
    def deploymentInfo = [
      'NEO': [
        'API_CLUSTER': 'neo-prod-cluster-fg',
        'CELERY_CLUSTER': 'neo-prod-cluster-fg',
        'API_SERVICE_NAME': 'neo-prod-api-service',
        'CELERY_SERVICE_NAME': 'neo-prod-celery-service',
        'BEAT_SERVICE_NAME': 'neo-prod-beat-service',
        'FLOWER_SERVICE_NAME': 'neo-pro-flower-service',
        'API_TASK_NAME': 'prod-neo-api-task-fg',
        'CELERY_TASK_NAME': 'prod-neo-celery-task-fg',
        'BEAT_TASK_NAME': 'prod-neo-beat-task-fg',
        'FLOWER_TASK_NAME': 'prod-neo-flower-task-fg',
        'AWS_REGION': 'ap-south-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.ap-south-1.amazonaws.com',
        'PROFILE_NAME': 'default',
        'IMAGE_NAME': 'neo-api',
      ],
      'WELLNESS': [
        'API_CLUSTER': 'wf-stockone-dev',
        'CELERY_CLUSTER': 'wf-stockone-dev',
        'API_SERVICE_NAME': 'wms-api-dev-service',
        'CELERY_SERVICE_NAME': 'wms-celery-dev-service',
        'BEAT_SERVICE_NAME': 'wms-beat-dev-service',
        'FLOWER_SERVICE_NAME': 'wms-neo-flower-dev-service',
        'API_TASK_NAME': 'wms-neo-api-dev',
        'CELERY_TASK_NAME': 'wms-neo-celery-dev',
        'BEAT_TASK_NAME': 'wms-neo-beat-dev',
        'FLOWER_TASK_NAME': 'wms-neo-flower-dev',
        'AWS_REGION': 'ap-south-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.ap-south-1.amazonaws.com',
        'PROFILE_NAME': 'wellness',
        'IMAGE_NAME': 'wf-warehouse-stockone-ecr',
      ],
      'GO': [
        'API_CLUSTER': 'WMSNeoApiProdUAE',
        'CELERY_CLUSTER': 'WMSNeoApiProdUAE',
        'API_SERVICE_NAME': 'WMSNeoApiUaeProdService',
        'CELERY_SERVICE_NAME': 'WMSNeoCeleryProdUAE',
        'BEAT_SERVICE_NAME': 'WMSNeoBeatProdUAE',
        'FLOWER_SERVICE_NAME': 'WMSNeoFlowerProdUAE',
        'API_TASK_NAME': 'wms-neo-uae-api-prod-task',
        'CELERY_TASK_NAME': 'wms-neo-uae-celery-task',
        'BEAT_TASK_NAME': 'wms-neo-uae-beat-prod-task',
        'FLOWER_TASK_NAME': 'wms-neo-uae-flower-task',
        'AWS_REGION': 'me-central-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.me-central-1.amazonaws.com',
        'PROFILE_NAME': 'default',
        'IMAGE_NAME': 'neo-api',
      ],
      'UAT': [
        'API_CLUSTER': 'neo-uat-cluster',
        'CELERY_CLUSTER': 'neo-uat-cluster',
        'API_SERVICE_NAME': 'neo-uat-api-service',
        'CELERY_SERVICE_NAME': 'neo-uat-celery-service',
        'BEAT_SERVICE_NAME': 'neo-uat-beat-service',
        'FLOWER_SERVICE_NAME': 'neo-uat-flower-service',
        'API_TASK_NAME': 'uat-neo-api-task',
        'CELERY_TASK_NAME': 'uat-neo-celery-task',
        'BEAT_TASK_NAME': 'uat-neo-beat-task',
        'FLOWER_TASK_NAME': 'uat-neo-flower-task',
        'AWS_REGION': 'ap-south-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.ap-south-1.amazonaws.com',
        'PROFILE_NAME': 'default',
        'IMAGE_NAME': 'neo-api',
      ],
      'WF_PROD': [
        'API_CLUSTER': 'wf-stockone-prod',
        'CELERY_CLUSTER': 'wf-stockone-prod',
        'API_SERVICE_NAME': 'wf-wms-api-prod-service',
        'CELERY_SERVICE_NAME': 'wf-wms-celery-prod-service',
        'BEAT_SERVICE_NAME': 'wf-wms-beat-prod-service',
        'FLOWER_SERVICE_NAME': 'wf-wms-flower-prod-service',
        'API_TASK_NAME': 'wf-wms-api-prod',
        'CELERY_TASK_NAME': 'wf-wms-celery-prod',
        'BEAT_TASK_NAME': 'wf-wms-beat-prod',
        'FLOWER_TASK_NAME': 'wf-wms-flower-prod',
        'AWS_REGION': 'ap-south-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.ap-south-1.amazonaws.com',
        'PROFILE_NAME': 'wf-prod',
        'IMAGE_NAME': 'wf-warehouse-stockone-ecr',
      ], 
      'RZN1': [
        'API_CLUSTER': 'rzn-prod-cluster',
        'CELERY_CLUSTER': 'rzn-prod-cluster',
        'API_SERVICE_NAME': 'prod-rzn-api-task-service-n3uxqq3j',
        'CELERY_SERVICE_NAME': 'prod-rzn-celery-service',
        'BEAT_SERVICE_NAME': 'prod-rzn-beat-service',
        'FLOWER_SERVICE_NAME': 'prod-rzn-flower-service',
        'API_TASK_NAME': 'prod-rzn-api-task',
        'CELERY_TASK_NAME': 'prod-rzn-celery-task',
        'BEAT_TASK_NAME': 'prod-rzn-beat-task',
        'FLOWER_TASK_NAME': 'prod-rzn-flower-task',
        'AWS_REGION': 'ap-south-1',
        'AWS_ACCOUNT_ID': '************',
        'ECR_REGISTRY': '************.dkr.ecr.ap-south-1.amazonaws.com',
        'PROFILE_NAME': 'default',
        'IMAGE_NAME': 'neo-api',
      ]
    ]
    

    def fullJobName = env.JOB_NAME
    def jobNameSegments = fullJobName.split('/')
    def jobName = jobNameSegments[-2]

    def segments = jobName.split('-')
    def envKey = segments[1]

    // Validate and fetch deployment info
    if (deploymentInfo.containsKey(envKey)) {
        return deploymentInfo[envKey][key]
    } else {
        error "Environment '${envKey}' not found in deploymentInfo."
    }
}


pipeline {
  agent none
  options {
    buildDiscarder(logRotator(numToKeepStr: '5'))
  }
  environment {
    BRANCH = "${env.BRANCH_NAME}"
    NEO_SIMULATION_DEPLOYMENT_MACHINE = credentials("NEO_SIMULATION_DEPLOYMENT_MACHINE")
    NEO_SIMULATION_DEPLOYMENT_PATH = '/var/www/html/stockone-neo/'
    NEO_CELERY_SIMULATION_DEPLOYMENT_PATH = '/var/www/html/stockone-neo-celery/'
    SIMULATION_INPUT = 'No'
    SIMULATION_USER = "platform"
    MASTER_DEPLOYMENT_FILE = "prod-deploy-master-simulation.yml"
    CELERY_DEPLOYMENT_FILE = "prod-celery-flower-deploy-simulation.yml"
    PLATFORM_TEAM = credentials('PLATFORM_TEAM')

    API_CLUSTER = getDeploymentInfo("API_CLUSTER")
    CELERY_CLUSTER = getDeploymentInfo("CELERY_CLUSTER")

    API_SERVICE_NAME = getDeploymentInfo("API_SERVICE_NAME")
    CELERY_SERVICE_NAME = getDeploymentInfo("CELERY_SERVICE_NAME")
    BEAT_SERVICE_NAME = getDeploymentInfo("BEAT_SERVICE_NAME")
    FLOWER_SERVICE_NAME = getDeploymentInfo("FLOWER_SERVICE_NAME")

    API_TASK_NAME = getDeploymentInfo("API_TASK_NAME")
    CELERY_TASK_NAME = getDeploymentInfo("CELERY_TASK_NAME")
    BEAT_TASK_NAME = getDeploymentInfo("BEAT_TASK_NAME")
    FLOWER_TASK_NAME = getDeploymentInfo("FLOWER_TASK_NAME")

    AWS_REGION = getDeploymentInfo("AWS_REGION")
    AWS_ACCOUNT_ID = getDeploymentInfo("AWS_ACCOUNT_ID")

    ECR_REGISTRY = getDeploymentInfo("ECR_REGISTRY")
    IMAGE_NAME = getDeploymentInfo("IMAGE_NAME")
    PROFILE_NAME = getDeploymentInfo("PROFILE_NAME")

    IMAGE_URL = "${ECR_REGISTRY}/${IMAGE_NAME}:${BRANCH}"

  }
  stages {

    stage('Simulation Input') {
      steps{
        script {
          TEST_INPUT = input(
            message: "Simulate Current Version ${BRANCH} Deployment",
            submitterParameter: 'submitter',
            parameters: [
                    [$class: 'ChoiceParameterDefinition',
                    choices: ['No','Yes'].join('\n'),
                    name: 'input',
                    description: 'Menu - select box option']
            ])
        }
      }
    }

    stage('Simulate Deployment') {
      agent {
          label 'slave2'
      }
      steps{
          script {
              cleanWs()
              checkout scm
              sh "echo  Current Version ${BRANCH}"
              echo "${TEST_INPUT.submitter} Approved Simulation Deployment"
              echo "The answer is: ${TEST_INPUT}"
              SIMULATION_INPUT = "${TEST_INPUT.input}"
              CHECK_APPROVER = checkApprover(TEST_INPUT.submitter, PLATFORM_TEAM)
              sh('echo ${CHECK_APPROVER}')

              if( "${TEST_INPUT.input}" == "Yes" && CHECK_APPROVER){
                  sh "echo  Deploying ${BRANCH} Branch"

                  // deploying application
                  sh "echo Deploying App"
                  sh "chmod +x scripts/prod_application_deployment.sh"
                  sh "sudo sh scripts/prod_application_deployment.sh ${NEO_SIMULATION_DEPLOYMENT_MACHINE} ${SIMULATION_USER} ${NEO_SIMULATION_DEPLOYMENT_PATH} ${BRANCH} ${MASTER_DEPLOYMENT_FILE} ${AWS_ACCOUNT_ID} ${AWS_REGION} ${PROFILE_NAME} ${IMAGE_URL}"

                  // deploying celery
                  sh "echo Deploying Celery"
                  sh "chmod +x scripts/prod_celery_deployment.sh"
                  sh "sudo sh scripts/prod_celery_deployment.sh ${NEO_SIMULATION_DEPLOYMENT_MACHINE} ${SIMULATION_USER} ${NEO_CELERY_SIMULATION_DEPLOYMENT_PATH} ${BRANCH} ${CELERY_DEPLOYMENT_FILE} ${AWS_ACCOUNT_ID} ${AWS_REGION} ${PROFILE_NAME} ${IMAGE_URL}"

                  sh "echo deployed to TEST"
              } else {
                  sh "echo Not deployed to simulation"
              }
          }
      }
    }

    stage("Updating Version to Firebase"){
        agent {
            label 'slave2'
        }
        steps {
            script {
               if( "${TEST_INPUT.input}" == "Yes" && CHECK_APPROVER) {
                  //  updating version to firebase
                  try {
                      dir('/root/') {
                          sh "ls -lhrt"
                          sh "chmod +x /root/update_firebase_backendneo.sh"
                          sh "/root/update_firebase_backendneo.sh ${BRANCH}"
                      }
                  } catch (any) {
                      echo "version update failed"
                  }
               } else {
                  sh "echo Not Updated to Firebase"
              }
            }
        }
    }

    stage('API Deployment Input') {
      steps{
        script {
          API_INPUT = input(
            message: "Deploy API Version ${BRANCH}",
            submitterParameter: 'submitter',
            parameters: [
                    [$class: 'ChoiceParameterDefinition',
                    choices: ['No','Yes'].join('\n'),
                    name: 'input',
                    description: 'Menu - select box option']
            ])
        }
      }
    }

    stage('API Deployment') {
      agent {
          label 'slave2'
      }
      steps{
          script {
              echo "The answer is: ${API_INPUT}"
              echo "${API_INPUT.submitter} Approved API Deployment"
              CHECK_APPROVER = checkApprover(API_INPUT.submitter, PLATFORM_TEAM)
              sh('echo ${CHECK_APPROVER}')

              if( "${API_INPUT.input}" == "Yes" && CHECK_APPROVER){
                  // deploying application
                  sh "chmod +x scripts/prod_ecs_deployment.sh"
                  sh "sudo sh scripts/prod_ecs_deployment.sh ${API_TASK_NAME} ${API_SERVICE_NAME} ${IMAGE_URL} ${API_CLUSTER} ${AWS_REGION} true ${PROFILE_NAME} ${BRANCH}"

                  sh "echo API deployed"
              } else {
                  sh "echo API NOT Deployed"
              }
          }
      }
    }

    stage('CELERY Deployment Input') {
      steps{
        script {
          CELERY_INPUT = input(
            message: "Deploy CELERY Version ${BRANCH}",
            submitterParameter: 'submitter',
            parameters: [
                    [$class: 'ChoiceParameterDefinition',
                    choices: ['No','Yes'].join('\n'),
                    name: 'input',
                    description: 'Menu - select box option']
            ])
        }
      }
    }

    stage('CELERY Deployment') {
      agent {
          label 'slave2'
      }
      steps{
          script {
              echo "The answer is: ${CELERY_INPUT}"
              echo "${CELERY_INPUT.submitter} Approved CELERY Deployment"
              CHECK_APPROVER = checkApprover(CELERY_INPUT.submitter, PLATFORM_TEAM)
              sh('echo ${CHECK_APPROVER}')

              if( "${CELERY_INPUT.input}" == "Yes" && CHECK_APPROVER){
                  sh "echo CELERY started"
                  parallel (
                    "Celery Wroker" : {
                      // deploying celery worker
                      sh "chmod +x scripts/prod_ecs_deployment.sh"
                      sh "sudo sh scripts/prod_ecs_deployment.sh ${CELERY_TASK_NAME} ${CELERY_SERVICE_NAME} ${IMAGE_URL} ${CELERY_CLUSTER} ${AWS_REGION} false ${PROFILE_NAME} ${BRANCH}"
                    },
                    "Celery Beat" : {
                        // deploying celer beat
                      sh "chmod +x scripts/prod_ecs_deployment.sh"
                      sh "sudo sh scripts/prod_ecs_deployment.sh ${BEAT_TASK_NAME} ${BEAT_SERVICE_NAME} ${IMAGE_URL} ${CELERY_CLUSTER} ${AWS_REGION} false ${PROFILE_NAME} ${BRANCH}"
                    },
                  )
                  sh "echo CELERY deployed"
              } else {
                  sh "echo CELERY NOT Deployed"
              }
          }
      }
    }

    stage('FLOWER Deployment Input') {
      steps{
        script {
          FLOWER_INPUT = input(
            message: "Deploy Flower Version ${BRANCH}",
            submitterParameter: 'submitter',
            parameters: [
                    [$class: 'ChoiceParameterDefinition',
                    choices: ['No','Yes'].join('\n'),
                    name: 'input',
                    description: 'Menu - select box option']
            ])
        }
      }
    }

    stage('FLower Deployment') {
      agent {
          label 'slave2'
      }
      steps{
          script {
              echo "The answer is: ${FLOWER_INPUT}"
              echo "${FLOWER_INPUT.submitter} Approved API Deployment"
              CHECK_APPROVER = checkApprover(FLOWER_INPUT.submitter, PLATFORM_TEAM)
              sh('echo ${CHECK_APPROVER}')

              if("${FLOWER_INPUT.input}" == "Yes" && CHECK_APPROVER){
                  // deploying flower
                  sh "chmod +x scripts/prod_ecs_deployment.sh"
                  sh "sudo sh scripts/prod_ecs_deployment.sh ${FLOWER_TASK_NAME} ${FLOWER_SERVICE_NAME} ${IMAGE_URL} ${CELERY_CLUSTER} ${AWS_REGION} true ${PROFILE_NAME} ${BRANCH}"

                  sh "echo FLOWER deployed"
              } else {
                  sh "echo FLOWER NOT Deployed"
              }
          }
      }
    }


  }
  post {
    always {
      node(label: 'slave2') {
        script {
          sh 'docker logout'
        }
      }
    }
  }
}