#!/usr/bin/env python3
"""
Debug script to test SKU creation API and identify why SKUs are not being created
despite returning success response.
"""

import json
import requests
import sys
import os

# Test data from the user's request
test_data = {
    "warehouse": "TEST_WH1",
    "skus": [
        {
            "sku_code": "ROZ14940-PISTA-XXL",
            "sku_type": "FG",
            "sku_desc": "Men's round Pista-XL",
            "sku_category_name": "Apparel",
            "sub_category": "Men Bottomwear",
            "sku_brand": "Kai",
            "scan_picking": True,
            "price": 712,
            "cost_price": "500.00",
            "mrp": "750.00",
            "sku_class": "classic",
            "style_name": "card",
            "hsn_code": "85624596322",
            "gst_slab": 5,
            "sku_size": "20",
            "size_type": "meter",
            "ean_number": "21516cvs",
            "threshold_quantity": "0",
            "product_shelf_life": "300",
            "customer_shelf_life": "200",
            "length": "20.50",
            "breadth": "30.50",
            "height": "60.50",
            "weight": "120.00",
            "pick_group": "pharma",
            "batch_based": True,
            "image_url": "https://djpw4cfh60y52.cloudfront.net/uploads/products/photos/U2rQ54bOzJCT1dyoTtUiDieYvMMPUrj1sWH3ifwH.png",
            "status": 1,
            "measurement_type": "Each",
            "tax_type": "TAX-5",
            "json_data": []
        }
    ]
}

def test_sku_creation():
    """Test the SKU creation API"""
    
    # You'll need to update these with actual values
    BASE_URL = "http://localhost:8000"  # Update with your server URL
    API_ENDPOINT = f"{BASE_URL}/core_operations/products/"
    
    # You'll need to add authentication headers
    headers = {
        'Content-Type': 'application/json',
        # Add authentication headers here
        # 'Authorization': 'Bearer your_token_here',
        # 'X-Warehouse': 'TEST_WH1',
    }
    
    print("Testing SKU Creation API...")
    print(f"Endpoint: {API_ENDPOINT}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            API_ENDPOINT,
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2)}")
        except json.JSONDecodeError:
            print(f"Response Text: {response.text}")
            
        # If successful, test if SKU actually exists
        if response.status_code == 200:
            print("\n=== API returned success, now checking if SKU exists ===")
            # You would need to implement a check here to verify if the SKU
            # actually exists in the database
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

def analyze_logs():
    """Analyze the application logs for debugging information"""
    
    log_files = [
        "logs/sku_master.log",
        "logs/sku_upload.log",
        # Add other relevant log files
    ]
    
    print("\n=== Analyzing Log Files ===")
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\nAnalyzing {log_file}:")
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    # Get last 50 lines
                    recent_lines = lines[-50:] if len(lines) > 50 else lines
                    
                    for line in recent_lines:
                        if any(keyword in line.lower() for keyword in 
                               ['debug:', 'error', 'exception', 'roz14940-pista-xxl']):
                            print(line.strip())
            except Exception as e:
                print(f"Error reading {log_file}: {e}")
        else:
            print(f"Log file not found: {log_file}")

def check_database_directly():
    """Check database directly for the SKU"""
    
    print("\n=== Direct Database Check ===")
    print("To check if SKU exists in database, run this SQL query:")
    print("SELECT * FROM core_skumaster WHERE sku_code = 'ROZ14940-PISTA-XXL';")
    print("\nOr use Django shell:")
    print("python manage.py shell")
    print("from core.models import SKUMaster")
    print("sku = SKUMaster.objects.filter(sku_code='ROZ14940-PISTA-XXL')")
    print("print(sku.exists())")
    print("if sku.exists(): print(sku.first().__dict__)")

if __name__ == "__main__":
    print("SKU Creation Debug Script")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_sku_creation()
        elif sys.argv[1] == "logs":
            analyze_logs()
        elif sys.argv[1] == "db":
            check_database_directly()
        else:
            print("Usage: python debug_sku_creation.py [test|logs|db]")
    else:
        print("Available commands:")
        print("  python debug_sku_creation.py test  - Test the API")
        print("  python debug_sku_creation.py logs  - Analyze logs")
        print("  python debug_sku_creation.py db    - Show DB check commands")
